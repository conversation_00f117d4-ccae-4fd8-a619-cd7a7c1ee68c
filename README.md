# 依思SUP

> 一个基于Python Django框架开发的商城SUP系统

## 🚀快速开始

### 环境要求

- Python 3.8+
- Django 5.2+
- NodeJS 16+
- MySQL 8.0+

### 宝塔快速部署

#### 1.克隆项目

```bash
# 在需要保存的目录终端运行
git clone https://github.com/A-YiS/python-django-yisisup.git
# 或者使用Gitee(码云)拉取
git clone https://gitee.com/labixiaoqiu/python-django-yisisup.git
```

#### 2.创建环境与项目

导航栏网站 -> Python项目 -> Python版本管理 -> 选择对应及以上版本安装

添加Python项目 -> 选择对应路径与Python版本

```bash
# 启动命令
python run.py runserver # 默认8000端口启动
# 或者 接收指定端口启动
python run.py runserver 8080 # 占用8080端口运行django
# 注意不能占用9000端口启动django命令 9000端口为固定插件交互端口
```

确认创建后点击终端操作

````bash
# Python环境检查
# 如果添加项目时没有自动检测到依赖需要手动运行
pip install -r requirements.txt

# 安装node.js依赖
cd frontend
npm install
cd ..
````

#### 3.数据库/服务器/后台管理/邮件配置

导航栏 -> 数据库 -> MySQL-> 添加数据库

> 注意MySQL版本要求8.0+ 配合 Django 5.2+

返回项目目录 点击deploy_config.yaml进入编辑，按照实际情况填写所有配置信息

```bash
# 如果没有找到deploy_config.yaml文件可执行
python deploy.py production

# 编辑完成deploy_config.yaml文件后保存
python deploy.py production # 需要执行创建运行系统环境
```



#### 4. 启动服务

再次回到宝塔网站页面，点击启动对应的项目就可以啦！

#### 其他说明

