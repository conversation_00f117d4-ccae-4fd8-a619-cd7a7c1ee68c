"""
Django settings for ShoppingDjango project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""
import os
import datetime
from pathlib import Path
import random
import string
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 环境配置
DJANGO_ENV = os.getenv('DJANGO_ENV', 'development')

# 安全配置
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-yd1znj3rc5@rx-6qfid2c&*ef6a@0p6lyq1gu_v_*5l6q_q)5p')

# 允许访问的主机
ALLOWED_HOSTS_STR = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1')
ALLOWED_HOSTS = [host.strip() for host in ALLOWED_HOSTS_STR.split(',') if host.strip()]

# 生产环境安全检查
if DJANGO_ENV == 'production':
    if DEBUG:
        raise ValueError("生产环境不能开启DEBUG模式")
    if SECRET_KEY == 'django-insecure-yd1znj3rc5@rx-6qfid2c&*ef6a@0p6lyq1gu_v_*5l6q_q)5p':
        raise ValueError("生产环境必须设置安全的SECRET_KEY")
    if not ALLOWED_HOSTS or ALLOWED_HOSTS == ['*']:
        raise ValueError("生产环境必须明确设置ALLOWED_HOSTS，不能使用通配符")


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    "app.apps.AppAppConfig",
    "api.apps.ApiConfig",
    'user.apps.UserConfig',
    'rest_framework',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'api.middleware.VisitStatisticsMiddleware'
    # 'api.middleware.RequestLoggingMiddleware',
]

ROOT_URLCONF = 'ShoppingDjango.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ShoppingDjango.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# 数据库配置
DB_ENGINE = os.getenv('DB_ENGINE', 'django.db.backends.mysql')

# 基础数据库配置
DATABASES = {
    'default': {
        'ENGINE': DB_ENGINE,
        'NAME': os.getenv('DB_NAME', 'shopping_db'),
        'USER': os.getenv('DB_USER', 'root'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'Qiu125822'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '3306'),
    },

    'AdminConfig': {
        'username': os.getenv('ADMIN_USERNAME', 'admin'),
        'password': os.getenv('ADMIN_PASSWORD', 'admin'),
    }
}

# 根据数据库引擎添加特定配置
if 'mysql' in DB_ENGINE:
    DATABASES['default']['OPTIONS'] = {
        'charset': 'utf8mb4',
    }
elif 'sqlite3' in DB_ENGINE:
    # SQLite特定配置（如果需要）
    DATABASES['default']['OPTIONS'] = {
        'timeout': 20,
    }


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'  # 修改为中文

TIME_ZONE = 'Asia/Shanghai'  # 修改为中国时区

USE_I18N = True

USE_TZ = True

# 静态文件配置
STATIC_URL = os.getenv('STATIC_URL', '/static/')

# 静态文件目录配置（所有环境都需要，用于collectstatic收集）
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
    # 添加Vue构建文件目录到静态文件路径
    os.path.join(BASE_DIR, 'templates', 'vue'),
]

# 静态文件查找器配置
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# 根据环境配置静态文件处理
if DEBUG:
    # 开发环境：Django直接从STATICFILES_DIRS提供静态文件服务
    pass  # STATICFILES_DIRS已在上面设置
else:
    # 生产环境：收集静态文件到指定目录
    STATIC_ROOT = os.getenv('STATIC_ROOT', os.path.join(BASE_DIR, 'staticfiles'))


# 媒体文件配置
MEDIA_URL = os.getenv('MEDIA_URL', '/media/')
MEDIA_ROOT = os.getenv('MEDIA_ROOT', os.path.join(BASE_DIR, 'media'))

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 用户数据模型
AUTH_USER_MODEL = 'user.User'

# 认证后端配置
AUTHENTICATION_BACKENDS = [
    'user.backends.SaltedEmailBackend',  # 自定义加盐邮箱认证后端
    'django.contrib.auth.backends.ModelBackend',  # Django默认认证后端（备用）
]

# REST框架配置
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
}

# JWT 配置
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', ''.join(random.SystemRandom().choice(string.ascii_letters + string.digits) for _ in range(50)))
JWT_ALGORITHM = 'HS256'
JWT_ACCESS_TOKEN_LIFETIME = datetime.timedelta(days=7)
JWT_REFRESH_TOKEN_LIFETIME = datetime.timedelta(days=7)

# 邮件配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.qq.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', '465'))
EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'True').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', 'lfzrbxmhmoapbibd')
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

# 环境配置信息输出（仅在启动时显示）
if __name__ != '__main__':
    print("Django 环境配置:")
    print(f"   环境模式: {DJANGO_ENV}")
    print(f"   调试模式: {DEBUG}")
    print(f"   允许主机: {ALLOWED_HOSTS}")
    print(f"   数据库: {DATABASES['default']['ENGINE']} - {DATABASES['default']['NAME']}")
    print(f"   静态文件: {STATIC_URL}")
    if not DEBUG:
        print(f"   静态文件根目录: {STATIC_ROOT}")
    print("=" * 50)

# WebSocket配置
WEBSOCKET_ENABLED = True  # 是否启用WebSocket客户端功能
WEBSOCKET_HOST = 'localhost'  # WebSocket服务器地址
WEBSOCKET_PORT = 9000  # WebSocket服务器端口
WEBSOCKET_AUTO_RECONNECT = True  # 是否启用自动重连
WEBSOCKET_RECONNECT_INTERVAL = 5  # 重连间隔（秒）
WEBSOCKET_MAX_RECONNECT_ATTEMPTS = 10  # 最大重连次数，0表示无限重连
WEBSOCKET_CONNECTION_TIMEOUT = 10  # 连接超时时间（秒）

# Vue混合渲染系统配置
VUE_SETTINGS = {
    # Vue开发环境配置
    'DEVELOPMENT': {
        'VITE_SERVER_URL': 'http://localhost:5173',
        'HMR_PORT': 5174,
        'AUTO_RELOAD': True,
        'SOURCE_MAPS': True
    },
    
    # Vue生产环境配置
    'PRODUCTION': {
        'PRECOMPILED': True,
        'CACHE_ENABLED': True,
        'CACHE_TIMEOUT': 3600,  # 1小时
        'MINIFY': True
    },
    
    # Vue编译器配置
    'COMPILER': {
        'NODE_EXECUTABLE': 'node',  # Node.js可执行文件路径
        'COMPILE_TIMEOUT': 30,  # 编译超时时间（秒）
        'MAX_COMPILE_WORKERS': 4,  # 最大并行编译数
        'CACHE_DIR': os.path.join(BASE_DIR, 'cache', 'vue'),
        'TEMP_DIR': os.path.join(BASE_DIR, 'temp', 'vue')
    },
    
    # Vue模板配置
    'TEMPLATES': {
        'VUE_DIR': os.path.join(BASE_DIR, 'templates', 'vue'),
        'SOURCE_DIR': os.path.join(BASE_DIR, 'frontend', 'src'),
        'COMPONENTS_DIR': 'components',
        'PAGES_DIR': 'pages',
        'DELIMITERS': ['[[', ']]']  # Vue模板分隔符
    },
    
    # 安全配置
    'SECURITY': {
        'ALLOWED_EXTENSIONS': ['.vue'],
        'MAX_FILE_SIZE': 1024 * 1024,  # 1MB
        'SANITIZE_HTML': True,
        'VALIDATE_SYNTAX': True
    }
}

# 创建Vue缓存目录
VUE_CACHE_DIR = VUE_SETTINGS['COMPILER']['CACHE_DIR']
VUE_TEMP_DIR = VUE_SETTINGS['COMPILER']['TEMP_DIR']

if not os.path.exists(VUE_CACHE_DIR):
    os.makedirs(VUE_CACHE_DIR, exist_ok=True)
    
if not os.path.exists(VUE_TEMP_DIR):
    os.makedirs(VUE_TEMP_DIR, exist_ok=True)
