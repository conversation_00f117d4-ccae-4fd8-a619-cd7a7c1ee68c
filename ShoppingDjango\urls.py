
from django.contrib import admin
from django.urls import path, include
from django.shortcuts import render
from django.conf import settings
from django.conf.urls.static import static
from django.utils import timezone
import socket
import subprocess
import os

def index(request):
    return render(request, 'index.html')

def health_check(request):
    """
    健康检查端点
    用于服务器启动时的状态检查，避免使用根路径进行健康检查
    """
    from django.http import JsonResponse
    from django.db import connection
    
    try:
        # 检查数据库连接
        connection.ensure_connection()
        
        return JsonResponse({
            'status': 'healthy',
            'message': 'Django服务器运行正常',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy', 
            'message': f'服务器存在问题: {str(e)}',
            'timestamp': timezone.now().isoformat()
        }, status=503)

def check_domain_configuration():
    """
    检查是否配置了域名
    通过检查部署配置文件或环境变量来判断是否有有效的域名配置

    Returns:
        bool: True表示已配置域名，False表示未配置域名
    """
    try:
        # 检查环境变量中的域名配置
        domain_env = os.getenv('DOMAIN', '').strip()
        if domain_env and domain_env not in ['localhost', '127.0.0.1', '0.0.0.0']:
            return True

        # 检查部署配置文件
        config_file = 'deploy_config.yaml'
        if os.path.exists(config_file):
            try:
                import yaml
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                if 'production' in config:
                    domain = config['production'].get('domain', '').strip()
                    if domain and domain not in ['localhost', '127.0.0.1', '0.0.0.0']:
                        return True
            except Exception:
                pass

        # 检查Django settings中的ALLOWED_HOSTS
        from django.conf import settings
        import re
        if hasattr(settings, 'ALLOWED_HOSTS'):
            allowed_hosts = settings.ALLOWED_HOSTS
            ip_pattern = re.compile(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$')

            for host in allowed_hosts:
                if (host not in ['localhost', '127.0.0.1', '0.0.0.0', '*']
                    and not host.startswith('192.168.')
                    and not host.startswith('10.0.')
                    and not host.startswith('172.')
                    and not ip_pattern.match(host)
                    and '.' in host
                    and not host.replace('.', '').isdigit()):
                    return True

        return False

    except Exception:
        return False

def detect_nginx():
    """
    直接检测域名配置来决定静态文件处理方式

    简化逻辑：
    - 无域名配置 = 强制Django处理静态文件
    - 有域名配置 = 允许Nginx处理静态文件（如果Nginx正确配置）

    Returns:
        bool: True表示应该由Nginx处理静态文件，False表示由Django处理
    """
    try:
        # 检查环境变量强制设置
        force_django_static = os.getenv('FORCE_DJANGO_STATIC', '').lower() in ['true', '1', 'yes']
        if force_django_static:
            return False

        # 检查域名配置
        domain_configured = check_domain_configuration()
        if not domain_configured:
            return False

        # 检查Nginx是否运行
        try:
            result = subprocess.run(['pgrep', 'nginx'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    except Exception:
        return False

urlpatterns = [
    path('health/', health_check, name='health_check'),  # 健康检查端点
    path('admin/backend/', include('app.urls')),
    path('api/', include('api.urls')),
    path('', include('user.urls')),
]

# 直接的静态文件服务配置
nginx_detected = detect_nginx()

if settings.DEBUG:
    # 开发环境：始终由Django提供静态文件服务
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0] if hasattr(settings, 'STATICFILES_DIRS') and settings.STATICFILES_DIRS else settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
else:
    # 生产环境：根据域名配置决定静态文件处理方式
    if not nginx_detected:
        # Django处理静态文件
        if hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
            # Django 5.1.4生产环境下static()函数返回空列表，需要手动创建URL模式
            static_patterns = static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

            if len(static_patterns) == 0:
                # 手动创建静态文件URL模式
                from django.urls import re_path
                from django.views.static import serve

                static_url_pattern = settings.STATIC_URL.strip('/')
                manual_static_pattern = re_path(
                    rf'^{static_url_pattern}/(?P<path>.*)$',
                    serve,
                    {'document_root': settings.STATIC_ROOT}
                )
                urlpatterns.append(manual_static_pattern)
            else:
                urlpatterns.extend(static_patterns)
        elif hasattr(settings, 'STATICFILES_DIRS') and settings.STATICFILES_DIRS:
            static_patterns = static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])

            if len(static_patterns) == 0:
                from django.urls import re_path
                from django.views.static import serve

                static_url_pattern = settings.STATIC_URL.strip('/')
                manual_static_pattern = re_path(
                    rf'^{static_url_pattern}/(?P<path>.*)$',
                    serve,
                    {'document_root': settings.STATICFILES_DIRS[0]}
                )
                urlpatterns.append(manual_static_pattern)
            else:
                urlpatterns.extend(static_patterns)

        # 媒体文件服务
        if hasattr(settings, 'MEDIA_ROOT') and settings.MEDIA_ROOT:
            media_patterns = static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

            if len(media_patterns) == 0:
                from django.urls import re_path
                from django.views.static import serve

                media_url_pattern = settings.MEDIA_URL.strip('/')
                manual_media_pattern = re_path(
                    rf'^{media_url_pattern}/(?P<path>.*)$',
                    serve,
                    {'document_root': settings.MEDIA_ROOT}
                )
                urlpatterns.append(manual_media_pattern)
            else:
                urlpatterns.extend(media_patterns)

# 启动信息
if not settings.DEBUG and not nginx_detected:
    print("✅ Django静态文件服务已启用")
