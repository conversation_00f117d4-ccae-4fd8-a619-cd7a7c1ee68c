
import jwt
import time
import json
import hmac
import hashlib
import secrets
import uuid
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, Tuple, Optional, Any
from django.http import JsonResponse
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
import logging
# from .timezone_utils import TimezoneConsistencyChecker  # 暂时注释掉，避免导入错误

# 配置日志记录器
logger = logging.getLogger(__name__)

# 管理员JWT配置常量
ADMIN_JWT_SECRET_KEY = getattr(settings, 'ADMIN_JWT_SECRET_KEY', 'admin_jwt_secret_key_2025')
ADMIN_JWT_ALGORITHM = 'HS256'
ADMIN_JWT_ACCESS_TOKEN_LIFETIME = 2 * 60 * 60
ADMIN_JWT_REFRESH_TOKEN_LIFETIME = 7 * 24 * 60 * 60

# 签名验证配置
ADMIN_SIGNATURE_SECRET = getattr(settings, 'ADMIN_SIGNATURE_SECRET', 'admin_signature_secret_2025')
SIGNATURE_TIMESTAMP_TOLERANCE = 300
NONCE_CACHE_TIMEOUT = 600

# 安全级别枚举
class SecurityLevel:
    BASIC = 'basic'      # 基础级别：仅JWT验证
    STANDARD = 'standard'  # 标准级别：JWT + 签名验证
    HIGH = 'high'        # 高级别：JWT + 签名 + 操作确认


class AdminAuthError(Exception):
    """管理员认证异常基类"""
    pass


class TokenExpiredError(AdminAuthError):
    """Token过期异常"""
    pass


class InvalidTokenError(AdminAuthError):
    """无效Token异常"""
    pass


class SignatureVerificationError(AdminAuthError):
    """签名验证失败异常"""
    pass


class AdminJWTManager:
    """管理员JWT令牌管理器"""
    
    @staticmethod
    def generate_admin_tokens(admin_info: Dict[str, Any]) -> Tuple[str, str]:

        current_time = timezone.now()

        # 访问令牌载荷
        access_payload = {
            'admin_id': admin_info.get('admin_id', 'admin'),
            'admin_name': admin_info.get('admin_name', 'Administrator'),
            'token_type': 'access',
            'iat': current_time,
            'exp': current_time + timedelta(seconds=ADMIN_JWT_ACCESS_TOKEN_LIFETIME),
            'jti': str(uuid.uuid4())  # JWT ID，用于令牌撤销
        }

        # 刷新令牌载荷
        refresh_payload = {
            'admin_id': admin_info.get('admin_id', 'admin'),
            'token_type': 'refresh',
            'iat': current_time,
            'exp': current_time + timedelta(seconds=ADMIN_JWT_REFRESH_TOKEN_LIFETIME),
            'jti': str(uuid.uuid4())
        }



        
        # 生成令牌
        access_token = jwt.encode(access_payload, ADMIN_JWT_SECRET_KEY, algorithm=ADMIN_JWT_ALGORITHM)
        refresh_token = jwt.encode(refresh_payload, ADMIN_JWT_SECRET_KEY, algorithm=ADMIN_JWT_ALGORITHM)
        
        # 确保令牌为字符串类型（兼容不同版本的PyJWT）
        if isinstance(access_token, bytes):
            access_token = access_token.decode('utf-8')
        if isinstance(refresh_token, bytes):
            refresh_token = refresh_token.decode('utf-8')
            
        return access_token, refresh_token
    
    @staticmethod
    def verify_admin_token(token: str) -> Tuple[bool, Dict[str, Any]]:

        try:
            # 记录验证开始时间用于调试
            current_time = timezone.now()

            # 解码JWT令牌
            payload = jwt.decode(token, ADMIN_JWT_SECRET_KEY, algorithms=[ADMIN_JWT_ALGORITHM])

            # 记录token信息用于调试
            token_iat = payload.get('iat')
            token_exp = payload.get('exp')
            if token_iat and token_exp:
                # 将时间戳转换为datetime对象进行比较，确保时区一致性
                if isinstance(token_iat, datetime):
                    # 如果已经是datetime对象，确保它是时区感知的
                    iat_time = token_iat if token_iat.tzinfo else timezone.make_aware(token_iat)
                else:
                    # 从时间戳创建时区感知的datetime对象
                    iat_time = timezone.datetime.fromtimestamp(token_iat, tz=timezone.get_current_timezone())

                if isinstance(token_exp, datetime):
                    # 如果已经是datetime对象，确保它是时区感知的
                    exp_time = token_exp if token_exp.tzinfo else timezone.make_aware(token_exp)
                else:
                    # 从时间戳创建时区感知的datetime对象
                    exp_time = timezone.datetime.fromtimestamp(token_exp, tz=timezone.get_current_timezone())

                # 确保current_time也是时区感知的
                if not current_time.tzinfo:
                    current_time = timezone.make_aware(current_time)

                # 检查是否过期
                remaining_time = exp_time - current_time
                if remaining_time.total_seconds() <= 0:
                    logger.warning("令牌已过期")

            # 验证令牌类型
            if payload.get('token_type') != 'access':
                logger.warning(f"令牌类型错误: {payload.get('token_type')}")
                return False, {'error': '令牌类型错误', 'code': 'INVALID_TOKEN_TYPE'}

            # 检查令牌是否在黑名单中（可选功能）
            jti = payload.get('jti')
            if jti and cache.get(f'blacklist_token_{jti}'):
                logger.warning(f"令牌已被撤销: {jti}")
                return False, {'error': '令牌已被撤销', 'code': 'TOKEN_REVOKED'}


            return True, payload

        except jwt.ExpiredSignatureError as e:
            logger.warning(f"令牌已过期: {str(e)}")
            return False, {'error': '令牌已过期', 'code': 'TOKEN_EXPIRED'}
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的令牌: {str(e)}")
            return False, {'error': '无效的令牌', 'code': 'INVALID_TOKEN'}
        except Exception as e:
            logger.error(f"令牌验证异常: {str(e)}")
            return False, {'error': f'令牌验证失败: {str(e)}', 'code': 'VERIFICATION_ERROR'}
    
    @staticmethod
    def refresh_admin_token(refresh_token: str) -> Tuple[bool, Dict[str, Any]]:

        try:


            # 验证刷新令牌
            payload = jwt.decode(refresh_token, ADMIN_JWT_SECRET_KEY, algorithms=[ADMIN_JWT_ALGORITHM])

            if payload.get('token_type') != 'refresh':
                logger.warning(f"刷新令牌类型错误: {payload.get('token_type')}")
                return False, {'error': '刷新令牌类型错误'}

            # 生成新的令牌对
            admin_info = {
                'admin_id': payload.get('admin_id'),
                'admin_name': payload.get('admin_name', 'Administrator')
            }


            new_access_token, new_refresh_token = AdminJWTManager.generate_admin_tokens(admin_info)

            return True, {
                'access_token': new_access_token,
                'refresh_token': new_refresh_token
            }
            
        except jwt.ExpiredSignatureError:
            return False, {'error': '刷新令牌已过期'}
        except jwt.InvalidTokenError:
            return False, {'error': '无效的刷新令牌'}
        except Exception as e:
            logger.error(f"令牌刷新异常: {str(e)}")
            return False, {'error': f'令牌刷新失败: {str(e)}'}


class EnhancedSignatureVerifier:
    """增强型签名验证器"""
    
    @staticmethod
    def generate_signature(data: Dict[str, Any], timestamp: int, nonce: str) -> str:

        # 排除签名字段，按键排序
        clean_data = {k: v for k, v in data.items() if k not in ['signature', 'timestamp', 'nonce']}
        
        # 转换为JSON字符串
        json_str = json.dumps(clean_data, separators=(',', ':'), sort_keys=True, ensure_ascii=False)
        
        # 构建待签名字符串：timestamp + nonce + json_data
        sign_string = f"{timestamp}{nonce}{json_str}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            ADMIN_SIGNATURE_SECRET.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    @staticmethod
    def verify_signature(data: Dict[str, Any]) -> Tuple[bool, str]:

        try:
            # 提取签名相关字段
            signature = data.get('signature')
            timestamp = data.get('timestamp')
            nonce = data.get('nonce')
            
            if not all([signature, timestamp, nonce]):
                return False, '缺少必要的签名字段'
            
            # 验证时间戳
            current_time = int(time.time())
            if abs(current_time - int(timestamp)) > SIGNATURE_TIMESTAMP_TOLERANCE:
                return False, '请求时间戳超出允许范围'
            
            # 验证nonce（防重放攻击）
            nonce_key = f'nonce_{nonce}'
            if cache.get(nonce_key):
                return False, '检测到重放攻击，nonce已被使用'
            
            # 缓存nonce
            cache.set(nonce_key, True, NONCE_CACHE_TIMEOUT)
            
            # 计算预期签名
            expected_signature = EnhancedSignatureVerifier.generate_signature(data, int(timestamp), nonce)
            
            # 比较签名
            if not hmac.compare_digest(signature, expected_signature):
                return False, '签名验证失败'
            
            return True, ''
            
        except Exception as e:
            logger.error(f"签名验证异常: {str(e)}")
            return False, f'签名验证异常: {str(e)}'
    
    @staticmethod
    def generate_nonce() -> str:
        """生成随机nonce"""
        return secrets.token_hex(16)


class AdminAuditLogger:
    """管理员操作审计日志记录器"""

    @staticmethod
    def safe_json_serialize(obj):

        def json_serializer(o):

            if isinstance(o, bytes):
                try:
                    # 尝试将bytes解码为字符串
                    return o.decode('utf-8')
                except UnicodeDecodeError:
                    # 如果解码失败，返回base64编码
                    import base64
                    return f"<bytes:{base64.b64encode(o).decode('ascii')}>"
            elif hasattr(o, 'isoformat'):
                # 处理datetime对象
                return o.isoformat()
            elif hasattr(o, '__dict__'):
                # 处理自定义对象
                return f"<object:{o.__class__.__name__}>"
            else:
                # 其他不可序列化对象
                return f"<{type(o).__name__}:{str(o)}>"

        try:
            return json.dumps(obj, ensure_ascii=False, default=json_serializer)
        except Exception as e:
            # 如果仍然失败，返回错误信息
            return json.dumps({
                'serialization_error': str(e),
                'object_type': type(obj).__name__
            }, ensure_ascii=False)

    @staticmethod
    def parse_request_data(request):

        try:
            if hasattr(request, 'body') and request.body:
                # 尝试解析JSON数据
                try:
                    import json
                    return json.loads(request.body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    # 如果不是JSON或解码失败，返回原始数据的安全表示
                    return {
                        'raw_data': request.body.decode('utf-8', errors='replace')[:1000],  # 限制长度
                        'content_type': request.content_type,
                        'method': request.method
                    }
            elif request.method in ['GET', 'DELETE']:
                # GET/DELETE请求，返回查询参数
                return dict(request.GET.items())
            else:
                # POST/PUT请求，尝试获取表单数据
                return dict(request.POST.items()) if hasattr(request, 'POST') else {}
        except Exception as e:
            return {
                'parse_error': str(e),
                'method': getattr(request, 'method', 'unknown'),
                'content_type': getattr(request, 'content_type', 'unknown')
            }

    @staticmethod
    def log_admin_operation(admin_id: str, operation: str, resource: str,
                          request_data: Optional[Dict] = None, result: str = 'success',
                          ip_address: Optional[str] = None, user_agent: Optional[str] = None):

        log_entry = {
            'timestamp': timezone.now().isoformat(),
            'admin_id': admin_id,
            'operation': operation,
            'resource': resource,
            'result': result,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'request_data': request_data
        }
        
        # 记录到日志文件
        logger.info(f"管理员操作审计: {AdminAuditLogger.safe_json_serialize(log_entry)}")
        
        # 可选：记录到数据库（需要创建相应的模型）
        # AdminAuditLog.objects.create(**log_entry)


def get_client_ip(request) -> str:
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def extract_admin_token(request) -> Optional[str]:

    # 优先从X-Admin-Access-Token头获取（新的标准方式）
    access_token = request.META.get('HTTP_X_ADMIN_ACCESS_TOKEN')
    if access_token:
        return access_token

    # 从Authorization头获取（标准Bearer方式）
    auth_header = request.META.get('HTTP_AUTHORIZATION')
    if auth_header and auth_header.startswith('Bearer '):
        return auth_header[7:]

    # 从Admin-Token头获取（兼容旧方式）
    admin_token = request.META.get('HTTP_ADMIN_TOKEN')
    if admin_token:
        return admin_token

    # 从Cookie获取
    admin_token = request.COOKIES.get('admin_token')
    if admin_token:
        return admin_token

    # 从GET/POST参数获取（不推荐，但提供兼容性）
    if hasattr(request, 'GET'):
        admin_token = request.GET.get('admin_token')
        if admin_token:
            return admin_token

    if hasattr(request, 'POST'):
        admin_token = request.POST.get('admin_token')
        if admin_token:
            return admin_token

    return None


def admin_auth_required(level=SecurityLevel.BASIC,
                       operation=None,
                       resource=None,
                       require_confirmation=False):

    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:


                # 获取客户端信息
                client_ip = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')

                # 第一层：JWT令牌验证
                admin_token = extract_admin_token(request)


                if not admin_token:
                    logger.warning("未提供管理员认证令牌")
                    AdminAuditLogger.log_admin_operation(
                        admin_id='unknown',
                        operation=operation or view_func.__name__,
                        resource=resource or request.path,
                        result='failed_no_token',
                        ip_address=client_ip,
                        user_agent=user_agent
                    )
                    return JsonResponse({
                        'code': 401,
                        'msg': '未提供管理员认证令牌',
                        'error_code': 'NO_ADMIN_TOKEN'
                    }, status=401)



                # 验证JWT令牌
                is_valid, token_result = AdminJWTManager.verify_admin_token(admin_token)
                if not is_valid:
                    error_code = token_result.get("code", "unknown")
                    error_msg = token_result.get('error', '令牌验证失败')
                    logger.warning(f"管理员令牌验证失败: {error_msg} (错误代码: {error_code})")

                    AdminAuditLogger.log_admin_operation(
                        admin_id='unknown',
                        operation=operation or view_func.__name__,
                        resource=resource or request.path,
                        result=f'failed_invalid_token_{error_code}',
                        ip_address=client_ip,
                        user_agent=user_agent
                    )
                    return JsonResponse({
                        'code': 401,
                        'msg': error_msg,
                        'error_code': error_code
                    }, status=401)

                # 提取管理员信息
                admin_id = token_result.get('admin_id', 'unknown')
                admin_name = token_result.get('admin_name', 'Administrator')


                # 将管理员信息添加到请求对象
                request.admin_info = {
                    'admin_id': admin_id,
                    'admin_name': admin_name,
                    'token_payload': token_result
                }

                # 第二层：根据安全级别进行额外验证
                if level in [SecurityLevel.STANDARD, SecurityLevel.HIGH]:
                    # 标准级别和高级别需要签名验证
                    if request.method == 'POST':
                        try:
                            request_data = json.loads(request.body.decode('utf-8'))
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            AdminAuditLogger.log_admin_operation(
                                admin_id=admin_id,
                                operation=operation or view_func.__name__,
                                resource=resource or request.path,
                                result='failed_invalid_json',
                                ip_address=client_ip,
                                user_agent=user_agent
                            )
                            return JsonResponse({
                                'code': 400,
                                'msg': '无效的JSON数据格式',
                                'error_code': 'INVALID_JSON'
                            }, status=400)

                        # 检测签名格式并选择相应的验证方式
                        has_enhanced_signature = all(key in request_data for key in ['signature', 'timestamp', 'nonce'])
                        has_legacy_signature = 'sign' in request_data

                        if has_enhanced_signature:
                            # 使用新的增强型签名验证

                            is_signature_valid, signature_error = EnhancedSignatureVerifier.verify_signature(request_data)
                            if not is_signature_valid:
                                AdminAuditLogger.log_admin_operation(
                                    admin_id=admin_id,
                                    operation=operation or view_func.__name__,
                                    resource=resource or request.path,
                                    request_data=request_data,
                                    result=f'failed_enhanced_signature_{signature_error}',
                                    ip_address=client_ip,
                                    user_agent=user_agent
                                )
                                return JsonResponse({
                                    'code': 403,
                                    'msg': f'增强型签名验证失败: {signature_error}',
                                    'error_code': 'ENHANCED_SIGNATURE_VERIFICATION_FAILED'
                                }, status=403)
                        elif has_legacy_signature:
                            pass
                        else:
                            # 既没有新签名也没有旧签名
                            AdminAuditLogger.log_admin_operation(
                                admin_id=admin_id,
                                operation=operation or view_func.__name__,
                                resource=resource or request.path,
                                request_data=request_data,
                                result='failed_no_signature',
                                ip_address=client_ip,
                                user_agent=user_agent
                            )
                            return JsonResponse({
                                'code': 403,
                                'msg': '缺少签名信息',
                                'error_code': 'MISSING_SIGNATURE'
                            }, status=403)

                # 第三层：高级别安全验证
                if level == SecurityLevel.HIGH and require_confirmation:
                    # 检查操作确认码（可根据需要实现）
                    confirmation_code = request.META.get('HTTP_CONFIRMATION_CODE')
                    if not confirmation_code:
                        AdminAuditLogger.log_admin_operation(
                            admin_id=admin_id,
                            operation=operation or view_func.__name__,
                            resource=resource or request.path,
                            result='failed_no_confirmation',
                            ip_address=client_ip,
                            user_agent=user_agent
                        )
                        return JsonResponse({
                            'code': 403,
                            'msg': '高风险操作需要确认码',
                            'error_code': 'CONFIRMATION_REQUIRED'
                        }, status=403)

                # 执行原始视图函数
                response = view_func(request, *args, **kwargs)

                AdminAuditLogger.log_admin_operation(
                    admin_id=admin_id,
                    operation=operation or view_func.__name__,
                    resource=resource or request.path,
                    request_data=AdminAuditLogger.parse_request_data(request),
                    result='success',
                    ip_address=client_ip,
                    user_agent=user_agent
                )



                return response

            except Exception as e:
                # 记录异常
                logger.error(f"管理员认证装饰器异常: {str(e)}", exc_info=True)
                AdminAuditLogger.log_admin_operation(
                    admin_id=getattr(request, 'admin_info', {}).get('admin_id', 'unknown'),
                    operation=operation or view_func.__name__,
                    resource=resource or request.path,
                    result=f'error_{str(e)}',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                return JsonResponse({
                    'code': 500,
                    'msg': '服务器内部错误',
                    'error_code': 'INTERNAL_SERVER_ERROR'
                }, status=500)

        return wrapper
    return decorator


def admin_auth_basic(operation=None, resource=None):
    """基础级别管理员认证装饰器（仅JWT验证）"""
    return admin_auth_required(
        level=SecurityLevel.BASIC,
        operation=operation,
        resource=resource
    )


def admin_auth_standard(operation=None, resource=None):
    """标准级别管理员认证装饰器（JWT + 签名验证）"""
    return admin_auth_required(
        level=SecurityLevel.STANDARD,
        operation=operation,
        resource=resource
    )


def admin_auth_high(operation=None, resource=None, require_confirmation=False):
    """高级别管理员认证装饰器（JWT + 签名 + 确认码）"""
    return admin_auth_required(
        level=SecurityLevel.HIGH,
        operation=operation,
        resource=resource,
        require_confirmation=require_confirmation
    )
