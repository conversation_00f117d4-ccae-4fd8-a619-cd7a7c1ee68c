import os
import json
import sys
import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('main')

# 当前目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))

# 配置文件路径
CONFIG_DIR = os.path.join(current_dir, "config")
DATABASE_CONFIG_PATH = os.path.join(CONFIG_DIR, "database.json")

def get_database_config():
    """
    从database.json读取数据库配置信息
    
    Returns:
        dict: 包含数据库配置信息的字典，如果文件不存在或读取失败则返回None
    """
    try:
        if not os.path.exists(DATABASE_CONFIG_PATH):
            logger.error(f"数据库配置文件不存在: {DATABASE_CONFIG_PATH}")
            return None
            
        with open(DATABASE_CONFIG_PATH, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        if not config.get("status", False):
            logger.warning("数据库配置状态为未初始化")
            return None
            
        required_fields = ["database_address", "database_user", "database_password", "database_name"]
        for field in required_fields:
            if field not in config:
                logger.error(f"数据库配置缺少必要字段: {field}")
                return None
                
        logger.info("成功读取数据库配置")
        return config
    except Exception as e:
        logger.error(f"读取数据库配置失败: {str(e)}")
        return None

def create_connection(use_dict_cursor=False):
    """
    创建数据库连接
    
    Args:
        use_dict_cursor (bool): 是否使用字典游标(返回结果为字典格式)
    
    Returns:
        tuple: (connection, cursor) 数据库连接和游标对象
        None: 如果连接失败
    """
    config = get_database_config()
    if not config:
        return None
        
    try:
        connection = mysql.connector.connect(
            host=config.get('database_address'),
            user=config.get('database_user'),
            password=config.get('database_password'),
            database=config.get('database_name'),
            charset=config.get('database_encode', 'utf8mb4')
        )
        
        if connection.is_connected():
            cursor = connection.cursor(dictionary=use_dict_cursor)
            logger.info(f"成功连接到MySQL数据库: {config.get('database_name')}")
            return connection, cursor
        
    except Error as e:
        logger.error(f"连接MySQL数据库失败: {str(e)}")
    
    return None

class DatabaseConnection:
    """数据库连接上下文管理器，用于with语句"""
    def __init__(self, use_dict_cursor=False):
        self.use_dict_cursor = use_dict_cursor
        self.connection = None
        self.cursor = None
        
    def __enter__(self):
        result = create_connection(self.use_dict_cursor)
        if result:
            self.connection, self.cursor = result
            return self.connection, self.cursor
        raise Exception("无法创建数据库连接")
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            if exc_type is None:  # 如果没有异常发生
                self.connection.commit()
            else:
                self.connection.rollback()
            self.connection.close()
            logger.info("数据库连接已关闭")

def execute_query(query, params=None, fetch_all=True, use_dict_cursor=False):

    try:
        with DatabaseConnection(use_dict_cursor) as (connection, cursor):
            cursor.execute(query, params or ())
            
            if fetch_all:
                return cursor.fetchall()
            else:
                return cursor.fetchone()
                
    except Exception as e:
        logger.error(f"执行SQL查询失败: {str(e)}")
        logger.error(f"SQL查询: {query}, 参数: {params}")
        return None

def execute_update(query, params=None):

    try:
        with DatabaseConnection() as (connection, cursor):
            cursor.execute(query, params or ())
            
            # 如果是INSERT操作，返回最后插入的ID
            if query.strip().upper().startswith("INSERT"):
                last_id = cursor.lastrowid
                return {"affected_rows": cursor.rowcount, "last_id": last_id}
            
            return {"affected_rows": cursor.rowcount}
            
    except Exception as e:
        logger.error(f"执行SQL更新操作失败: {str(e)}")
        logger.error(f"SQL更新: {query}, 参数: {params}")
        return None

def table_exists(table_name):

    config = get_database_config()
    if not config:
        return False
        
    query = """
    SELECT COUNT(*) as count 
    FROM information_schema.tables 
    WHERE table_schema = %s 
    AND table_name = %s
    """
    
    result = execute_query(query, (config.get('database_name'), table_name), fetch_all=False)
    
    if result and result[0] > 0:
        return True
    return False

def create_tables():

    try:
        # 创建分类表
        if not table_exists("categories"):
            category_table_query = """
            CREATE TABLE categories (
                id VARCHAR(36) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                image VARCHAR(255),
                sort INT DEFAULT 0,
                parent_id VARCHAR(36) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_parent (parent_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """
            
            result = execute_update(category_table_query)
            if not result:
                logger.error("创建分类表失败")
                return False
            logger.info("成功创建分类表")
        
        # 这里可以添加更多表的创建逻辑
        
        return True
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {str(e)}")
        return False
