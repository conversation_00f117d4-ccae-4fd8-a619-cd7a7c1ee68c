#!/usr/bin/env python
# -*- coding: utf-8 -*-

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
import logging
import sys

# 配置日志
logger = logging.getLogger(__name__)

# 确保日志配置
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.INFO)
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


class Command(BaseCommand):
    """
    取消过期订单的Django管理命令
    
    用法:
        python manage.py cancel_expired_orders --hours=1
        python manage.py cancel_expired_orders --hours=2 --verbosity=2
        python manage.py cancel_expired_orders --hours=1 --dry-run
    """
    
    help = '取消指定小时数之前创建的过期订单，并释放相关资源'
    
    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument(
            '--hours',
            type=int,
            default=1,
            help='订单过期时间（小时），默认为1小时'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='干运行模式，只显示将要处理的订单，不实际执行操作'
        )
        
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='批处理大小，默认每次处理100个订单'
        )
    
    def handle(self, *args, **options):
        """命令主处理函数"""
        try:
            # 获取命令参数
            hours = options['hours']
            dry_run = options['dry_run']
            batch_size = options['batch_size']
            verbosity = options['verbosity']
            
            # 参数验证
            if hours <= 0:
                raise CommandError('过期时间必须大于0小时')
            
            if batch_size <= 0:
                raise CommandError('批处理大小必须大于0')
            
            # 输出开始信息
            self.stdout.write(
                self.style.SUCCESS(
                    f'开始处理过期订单 - 过期时间: {hours}小时, '
                    f'批处理大小: {batch_size}, '
                    f'模式: {"干运行" if dry_run else "实际执行"}'
                )
            )
            
            # 执行过期订单处理
            result = self._process_expired_orders(hours, dry_run, batch_size, verbosity)
            
            # 输出结果统计
            self._output_results(result, dry_run)
            
        except Exception as e:
            logger.error(f'处理过期订单时发生错误: {str(e)}', exc_info=True)
            raise CommandError(f'命令执行失败: {str(e)}')
    
    def _process_expired_orders(self, hours, dry_run, batch_size, verbosity):
        """处理过期订单的核心逻辑"""
        from api.models import Order, CardKey
        
        # 计算过期时间点
        expiry_time = timezone.now() - timedelta(hours=hours)
        
        if verbosity >= 2:
            self.stdout.write(f'查找 {expiry_time} 之前创建的订单...')
        
        # 查找过期订单 - 这里需要根据实际业务逻辑调整查询条件
        # 假设我们需要处理的是未完成支付或未处理的订单
        expired_orders = Order.objects.filter(
            created_at__lt=expiry_time
            # 这里可以添加更多的过滤条件，比如订单状态等
            # 由于Order模型中没有明确的状态字段，我们先处理所有过期订单
        ).order_by('created_at')
        
        total_found = expired_orders.count()
        
        if verbosity >= 1:
            self.stdout.write(f'发现 {total_found} 个过期订单')
        
        if total_found == 0:
            return {
                'total_found': 0,
                'total_processed': 0,
                'total_failed': 0,
                'card_keys_released': 0,
                'errors': []
            }
        
        # 初始化结果统计
        result = {
            'total_found': total_found,
            'total_processed': 0,
            'total_failed': 0,
            'card_keys_released': 0,
            'errors': []
        }
        
        # 分批处理订单
        processed_count = 0
        for i in range(0, total_found, batch_size):
            batch_orders = expired_orders[i:i + batch_size]
            
            if verbosity >= 2:
                self.stdout.write(f'处理第 {i//batch_size + 1} 批订单 ({len(batch_orders)} 个)...')
            
            batch_result = self._process_order_batch(batch_orders, dry_run, verbosity)
            
            # 累计结果
            result['total_processed'] += batch_result['processed']
            result['total_failed'] += batch_result['failed']
            result['card_keys_released'] += batch_result['card_keys_released']
            result['errors'].extend(batch_result['errors'])
            
            processed_count += len(batch_orders)
            
            if verbosity >= 1:
                self.stdout.write(f'已处理 {processed_count}/{total_found} 个订单')
        
        return result
    
    def _process_order_batch(self, batch_orders, dry_run, verbosity):
        """处理一批订单"""
        from api.models import CardKey, Goods
        
        batch_result = {
            'processed': 0,
            'failed': 0,
            'card_keys_released': 0,
            'errors': []
        }
        
        for order in batch_orders:
            try:
                if verbosity >= 2:
                    self.stdout.write(f'处理订单: {order.id} (创建时间: {order.created_at})')
                
                if dry_run:
                    # 干运行模式，只显示将要处理的内容
                    self._show_order_details(order, verbosity)
                    batch_result['processed'] += 1
                else:
                    # 实际处理订单
                    with transaction.atomic():
                        released_cards = self._cancel_order_and_release_resources(order, verbosity)
                        batch_result['card_keys_released'] += released_cards
                        batch_result['processed'] += 1
                        
                        if verbosity >= 2:
                            self.stdout.write(f'订单 {order.id} 处理完成，释放卡密 {released_cards} 个')
                
            except Exception as e:
                error_msg = f'处理订单 {order.id} 时发生错误: {str(e)}'
                batch_result['errors'].append(error_msg)
                batch_result['failed'] += 1
                logger.error(error_msg, exc_info=True)
                
                if verbosity >= 1:
                    self.stdout.write(self.style.ERROR(error_msg))
        
        return batch_result
    
    def _show_order_details(self, order, verbosity):
        """显示订单详情（干运行模式）"""
        if verbosity >= 2:
            self.stdout.write(f'  订单ID: {order.id}')
            self.stdout.write(f'  用户ID: {order.user}')
            self.stdout.write(f'  订单类型: {order.get_type_display()}')
            self.stdout.write(f'  创建时间: {order.created_at}')
            
            # 检查是否有关联的卡密
            from api.models import CardKey
            card_keys = CardKey.objects.filter(order=order)
            if card_keys.exists():
                self.stdout.write(f'  关联卡密: {card_keys.count()} 个')
    
    def _cancel_order_and_release_resources(self, order, verbosity):
        """取消订单并释放相关资源"""
        from api.models import CardKey, Goods
        
        released_card_count = 0
        
        try:
            # 1. 处理关联的卡密 - 将已售出的卡密重置为未售出状态
            card_keys = CardKey.objects.filter(order=order, status=1)  # status=1表示已售出
            
            if card_keys.exists():
                # 重置卡密状态
                updated_count = card_keys.update(
                    status=0,  # 重置为未售出
                    order=None,  # 清除订单关联
                    sold_at=None  # 清除售出时间
                )
                released_card_count = updated_count
                
                if verbosity >= 2:
                    self.stdout.write(f'  重置卡密状态: {updated_count} 个')
                
                # 2. 更新相关商品的库存（如果是卡密商品）
                for card_key in card_keys:
                    if card_key.library and card_key.library.goods.exists():
                        # 找到使用此卡库的商品
                        goods = card_key.library.goods.filter(type='1')  # type='1'表示卡密商品
                        for good in goods:
                            # 增加商品库存
                            good.stock = good.stock + 1
                            good.save()
                            
                            # 如果商品之前是售罄状态，现在有库存了，更新状态
                            if good.status == '3':  # status='3'表示售罄
                                good.status = '1'  # 重新上架
                                good.save()
                            
                            if verbosity >= 2:
                                self.stdout.write(f'  更新商品 {good.name} 库存: +1')
            
            # 3. 这里可以添加其他清理逻辑，比如：
            # - 退还用户余额（如果有预扣费）
            # - 发送取消通知
            # - 记录取消日志等
            
            # 注意：由于Order模型中没有明确的状态字段，我们不删除订单记录
            # 而是通过其他方式标记订单已被处理（比如在订单数据中添加标记）
            if hasattr(order, 'data') and isinstance(order.data, dict):
                order.data['cancelled_at'] = timezone.now().isoformat()
                order.data['cancelled_reason'] = 'expired'
                order.save()
            
        except Exception as e:
            logger.error(f'释放订单 {order.id} 资源时发生错误: {str(e)}', exc_info=True)
            raise
        
        return released_card_count
    
    def _output_results(self, result, dry_run):
        """输出处理结果"""
        mode_text = "【干运行模式】" if dry_run else "【实际执行】"
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n{mode_text} 过期订单处理完成:'
            )
        )
        
        self.stdout.write(f'  发现过期订单: {result["total_found"]} 个')
        self.stdout.write(f'  成功处理: {result["total_processed"]} 个')
        
        if result['total_failed'] > 0:
            self.stdout.write(
                self.style.WARNING(f'  处理失败: {result["total_failed"]} 个')
            )
        
        if result['card_keys_released'] > 0:
            self.stdout.write(f'  释放卡密: {result["card_keys_released"]} 个')
        
        # 输出错误信息
        if result['errors']:
            self.stdout.write(self.style.ERROR('\n处理过程中的错误:'))
            for error in result['errors']:
                self.stdout.write(self.style.ERROR(f'  - {error}'))