
import logging
from django.utils.deprecation import MiddlewareMixin
from django.db import transaction
from django.db.models import F
from django.core.cache import cache
import time

logger = logging.getLogger(__name__)


class VisitStatisticsMiddleware(MiddlewareMixin):
    """
    网站访问量统计中间件
    统计每个HTTP请求，更新今日网站访问量
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response
        # 缓存键，用于批量更新访问量
        self.cache_key = 'daily_visit_count'
        # 批量更新的阈值
        self.batch_threshold = 10
        # 上次更新时间的缓存键
        self.last_update_key = 'last_visit_update_time'
    
    def process_request(self, request):

        try:
            # 过滤掉不需要统计的请求
            if self._should_skip_request(request):
                return None
            
            # 使用缓存进行批量更新，提高性能
            current_count = cache.get(self.cache_key, 0)
            current_count += 1
            cache.set(self.cache_key, current_count, timeout=3600)  # 缓存1小时
            
            # 当达到批量更新阈值或距离上次更新超过5分钟时，更新数据库
            last_update_time = cache.get(self.last_update_key, 0)
            current_time = time.time()
            
            if (current_count >= self.batch_threshold or 
                current_time - last_update_time > 300):  # 5分钟
                self._update_visit_statistics(current_count)
                # 重置缓存
                cache.set(self.cache_key, 0, timeout=3600)
                cache.set(self.last_update_key, current_time, timeout=3600)
                
        except Exception as e:
            return None
        
        return None
    
    def _should_skip_request(self, request):
        """
        判断是否应该跳过此请求的统计
        """
        # 跳过静态文件请求
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return True
        
        # 跳过favicon请求
        if request.path == '/favicon.ico':
            return True
        
        # 跳过API健康检查等请求
        if request.path.startswith('/health/') or request.path.startswith('/ping/'):
            return True
        
        # 跳过管理后台的某些请求（可选）
        if request.path.startswith('/admin/jsi18n/'):
            return True
        
        return False
    
    def _update_visit_statistics(self, visit_count):
        """
        更新访问量统计到数据库
        """
        try:
            # 延迟导入避免循环导入
            from api.models import dataStatistics
            
            # 获取或创建今日统计记录
            today_stats, created = dataStatistics.get_or_create_today_stats()
            
            # 使用事务确保数据一致性
            with transaction.atomic():
                # 使用F表达式原子性更新访问量
                dataStatistics.objects.filter(date=today_stats.date).update(
                    daily_visits=F('daily_visits') + visit_count
                )
            
            logger.info(f"批量更新访问量统计: +{visit_count}")
            
        except Exception as e:
            logger.error(f"更新访问量统计到数据库失败: {str(e)}", exc_info=True)


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    请求日志中间件
    记录重要的请求信息用于调试和分析
    """
    
    def process_request(self, request):
        """记录请求开始时间"""
        request.start_time = time.time()
        return None
    
    def process_response(self, request, response):
        """记录请求处理时间和状态"""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 只记录耗时较长的请求或错误请求
            if duration > 1.0 or response.status_code >= 400:
                logger.info(
                    f"Request: {request.method} {request.path} - "
                    f"Status: {response.status_code} - "
                    f"Duration: {duration:.3f}s - "
                    f"IP: {self._get_client_ip(request)}"
                )
        
        return response
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip