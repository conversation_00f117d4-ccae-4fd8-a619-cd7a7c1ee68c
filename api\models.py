from django.db import models
import uuid
from datetime import datetime
from django.utils import timezone
from decimal import Decimal

class Category(models.Model):
    """
    商品分类模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, null=False, verbose_name='分类名称')
    level = models.IntegerField(default=1, verbose_name='分类层级')  # 1: 一级分类, 2: 二级分类
    image = models.TextField(blank=True, null=True, verbose_name='分类图片URL')
    parent_id = models.CharField(max_length=36, blank=True, null=True, verbose_name='父分类ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')\

    class Meta:
        db_table = 'category'
        verbose_name = '商品分类'
        verbose_name_plural = '商品分类'

    def __str__(self):
        return self.name

class MemberLevel(models.Model):
    """
    会员等级模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, null=False, verbose_name='等级名称')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'memberlevel'
        verbose_name = '会员等级'
        verbose_name_plural = '会员等级'
        ordering = ['-created_at']

class PriceTemplate(models.Model):
    """
    价格模板模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, null=False, verbose_name='模板名称')
    type = models.CharField(max_length=10, null=False, verbose_name='模板类型')  # 1: 固定金额加价, 2: 百分比加价
    data_json = models.TextField(null=False, verbose_name='加价比例数据')  # 存储JSON格式的加价比例数据
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    def __str__(self):
        return self.name
    
    class Meta:
        db_table = 'price_templates'
        verbose_name = '价格模板'
        verbose_name_plural = '价格模板'

class CardLibrary(models.Model):
    id = models.CharField(max_length=36, primary_key=True)
    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'card_library'
        verbose_name = '卡库'
        verbose_name_plural = '卡库'

class CardKey(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    content = models.CharField(max_length=255, verbose_name='卡密内容')
    library = models.ForeignKey(CardLibrary, on_delete=models.CASCADE, related_name='cards', verbose_name='所属卡库')
    status = models.IntegerField(default=0, verbose_name='卡密状态', help_text='0: 未售出, 1: 已售出')
    order = models.ForeignKey('Order', on_delete=models.SET_NULL, null=True, blank=True, related_name='card_keys', verbose_name='关联订单')
    sold_at = models.DateTimeField(null=True, blank=True, verbose_name='售出时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'card_key'
        verbose_name = '卡密'
        verbose_name_plural = '卡密'
        indexes = [
            models.Index(fields=['library']),
            models.Index(fields=['status']),
            models.Index(fields=['order']),
        ]

class DockingSite(models.Model):
    """对接社区模型"""
    id = models.CharField(max_length=36, primary_key=True)
    name = models.CharField(max_length=100, verbose_name='对接社区名称')
    url = models.CharField(max_length=255, verbose_name='对接地址')
    appid = models.CharField(max_length=100, verbose_name='对接APPID')
    type = models.CharField(max_length=50, verbose_name='对接社区类型')
    key = models.CharField(max_length=255, verbose_name='对接密钥')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), null=True, blank=True, verbose_name='账户余额')
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        verbose_name = '对接社区'
        verbose_name_plural = '对接社区'
        db_table = 'docking_site'

    def __str__(self):
        return self.name

class PaymentMethod(models.Model):
    """
    支付列表模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    interface_type = models.CharField(max_length=100, null=False, verbose_name='接口类型')
    name = models.CharField(max_length=255, null=False, verbose_name='渠道名称')
    fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), verbose_name='手续费')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    data_json = models.TextField(blank=True, null=True, verbose_name='数据配置')  # 存储JSON格式的配置数据
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.interface_type

    class Meta:
        db_table = 'payment_method'
        verbose_name = '支付列表'
        verbose_name_plural = '支付列表'
        ordering = ['-created_at']

class Goods(models.Model):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, null=False, verbose_name='商品名称')
    price = models.DecimalField(max_digits=10, decimal_places=2, null=False, verbose_name='商品价格')
    image_type = models.CharField(max_length = 10, null=True, verbose_name='读取图片方式') # 1: 本地读取, 2: 链接读取 默认2
    image = models.TextField(blank=True, null=True, verbose_name='商品图片地址') # image_type==1时为本地图片地址, 2: 对接图片地址
    price_template = models.ForeignKey(PriceTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='goods', verbose_name='价格模板')
    type = models.CharField(max_length=10, null=False, verbose_name='商品类型')  # 1: 卡密商品, 2: 虚拟商品 3：对接商品
    status = models.CharField(max_length=10, default='1', verbose_name='商品状态')  # 1: 上架, 2: 下架 3: 售罄
    stock = models.IntegerField(default=0, verbose_name='商品库存')
    sales_count = models.IntegerField(default=0, verbose_name='商品销量', help_text='记录商品的总销售数量')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, related_name='goods', verbose_name='商品分类')
    card_library = models.ForeignKey(CardLibrary, on_delete=models.SET_NULL, null=True, blank=True, related_name='goods', verbose_name='卡库')
    info = models.TextField(blank=True, null=True, verbose_name='商品详情')
    attach = models.TextField(blank=True, null=True, verbose_name='商品参数')
    operation_data = models.JSONField(blank=True, null=True, verbose_name='后置操作数据', help_text='商品订单后置操作配置')
    notice = models.TextField(blank=True, null=True, verbose_name='弹窗公告', help_text='商品详情页面弹窗公告内容')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    docking_site = models.ForeignKey(DockingSite, on_delete=models.SET_NULL, null=True, blank=True, related_name='docking_goods', verbose_name='对接社区ID')
    docking_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='对接商品ID')


    def __str__(self):
        return self.name

    class Meta:
        db_table = 'goods'
        verbose_name = '商品'
        verbose_name_plural = '商品'
        ordering = ['-created_at']


class Coupon(models.Model):
    """
    卡券模型
    """
    code = models.CharField(max_length=32, primary_key=True, verbose_name="卡券代码")
    discount_type = models.CharField(max_length=20, choices=[
        ('fixed', '固定金额'),
        ('percentage', '百分比折扣')
    ], verbose_name="优惠类型")
    discount_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="优惠值")
    min_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), verbose_name="最低消费金额")
    description = models.TextField(blank=True, null=True, verbose_name="卡券描述")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    expiry_date = models.DateTimeField(verbose_name="过期时间")
    is_used = models.BooleanField(default=False, verbose_name="是否已使用")
    used_time = models.DateTimeField(blank=True, null=True, verbose_name="使用时间")
    used_by = models.CharField(max_length=36, blank=True, null=True, verbose_name="使用者ID")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")

    class Meta:
        db_table = 'coupon'
        verbose_name = '卡券'
        verbose_name_plural = '卡券管理'
        ordering = ['-create_time']
        indexes = [
            models.Index(fields=['discount_type']),
            models.Index(fields=['is_used']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['create_time']),
        ]

    @property
    def status(self):
        """计算卡券状态"""
        if self.is_used:
            return 'used'
        elif self.expiry_date < timezone.now():
            return 'expired'
        else:
            return 'active'


class CouponUsageLog(models.Model):
    """
    卡券使用记录模型
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    coupon_code = models.CharField(max_length=32, verbose_name="卡券代码")
    user_id = models.CharField(max_length=36, verbose_name="使用者ID")
    order_id = models.CharField(max_length=36, blank=True, null=True, verbose_name="关联订单号")
    usage_time = models.DateTimeField(auto_now_add=True, verbose_name="使用时间")
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="实际优惠金额")

    class Meta:
        db_table = 'coupon_usage_log'
        verbose_name = '卡券使用记录'
        verbose_name_plural = '卡券使用记录'
        ordering = ['-usage_time']
        indexes = [
            models.Index(fields=['coupon_code']),
            models.Index(fields=['user_id']),
            models.Index(fields=['usage_time']),
        ]

    def __str__(self):
        return f"{self.coupon_code} - {self.user_id}"


class Order(models.Model):

    id = models.CharField(max_length=50, primary_key=True, verbose_name='系统订单号')
    order = models.CharField(max_length=50, verbose_name='支付平台订单号')
    user = models.CharField(max_length=50, verbose_name='下单用户ID')
    is_guest_order = models.BooleanField(default=False, verbose_name='是否为游客订单')
    data = models.JSONField(verbose_name='订单数据')
    type = models.CharField( max_length=10, default='2', choices=[ ('1', '充值余额订单'), ('2', '购买商品订单') ], verbose_name='订单类型')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '正式订单'
        verbose_name_plural = '正式订单'
        ordering = ['-created_at']
        db_table = 'orders'
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['user']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.id


class dataStatistics(models.Model):

    # 时间字段作为主键，按日期统计
    date = models.DateField(primary_key=True, verbose_name='统计日期')
    
    # 全局累计数据
    total_orders = models.IntegerField(default=0, verbose_name='总订单量')
    total_sales_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, verbose_name='总销售金额')
    total_profit = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, verbose_name='总利润')
    total_users = models.IntegerField(default=0, verbose_name='总用户量')
    total_sites = models.IntegerField(default=0, verbose_name='总分站数')
    
    # 当日数据统计
    daily_orders = models.IntegerField(default=0, verbose_name='今日订单量')
    daily_sales_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日订单金额')
    daily_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日订单成本')
    daily_profit = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日订单利润')
    daily_new_users = models.IntegerField(default=0, verbose_name='今日新增注册用户量')
    daily_visits = models.IntegerField(default=0, verbose_name='今日网站访问量')
    daily_recharge_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日充值余额量')
    daily_withdraw_requests = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日申请提现量')
    daily_new_sites = models.IntegerField(default=0, verbose_name='今日新分站量')
    daily_site_withdrawals = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日分站提现量')
    daily_site_commissions = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='今日分站分成量')
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'data_statistics'
        verbose_name = '数据统计'
        verbose_name_plural = '数据统计'
        ordering = ['-date']
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.date} 数据统计"

    @classmethod
    def get_or_create_today_stats(cls):
        """获取或创建今日统计记录"""
        from django.utils import timezone
        today = timezone.now().date()
        stats, created = cls.objects.get_or_create(date=today)
        return stats, created

    @classmethod
    def get_stats_by_date(cls, date):
        """根据日期获取统计数据"""
        try:
            return cls.objects.get(date=date)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_stats_by_year(cls, year):
        """获取指定年份的统计数据"""
        return cls.objects.filter(date__year=year).order_by('-date')

    @classmethod
    def get_stats_by_month(cls, year, month):
        """获取指定年月的统计数据"""
        return cls.objects.filter(date__year=year, date__month=month).order_by('-date')

    @classmethod
    def get_stats_by_date_range(cls, start_date, end_date):
        """获取指定日期范围的统计数据"""
        return cls.objects.filter(date__range=[start_date, end_date]).order_by('-date')


