import aiohttp
import asyncio
import concurrent.futures
import threading
import logging
import time
from django.db.models.lookups import EndsWith
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from asgiref.sync import sync_to_async
from django.views import View
from django.utils.decorators import method_decorator
from django.conf import settings
import json
import time
import os
import base64
import sys
import io
import atexit
import threading
import signal
import hashlib
import uuid
import requests
from datetime import datetime
from django.utils import timezone
from pydantic import BaseModel
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import api_view
from rest_framework import status




# 导入管理员认证模块
from .admin_auth import (
    admin_auth_basic,
    admin_auth_standard,
    admin_auth_high,
    SecurityLevel,
    AdminJWTManager,
    AdminAuditLogger
)
from ShoppingDjango.settings import DATABASES
from django.db import connection, connections, transaction
from django.db.models import F
from django.db.utils import OperationalError, IntegrityError
from api.models import Category, MemberLevel, PriceTemplate, CardLibrary, CardKey, Goods, DockingSite, PaymentMethod, Coupon, CouponUsageLog, Order, dataStatistics  # 导入PaymentMethod模型和卡券模型
from user.models import User
from alipay import AliPay


# 计算字符串的MD5
def get_md5(text):
    # 如果是字符串，需要先编码为字节
    if isinstance(text, str):
        text = text.encode('utf-8')
    m = hashlib.md5()
    m.update(text)
    return m.hexdigest()


def verify_jubijia_push_sign(data, merchant_key):

    try:
        # 获取接收到的签名
        received_sign = data.get('sign')
        if not received_sign:
            logger.warning("聚比价推送数据缺少sign字段")
            return False

        # 参与签名的必推字段（按聚比价文档要求）
        sign_fields = ['goodsid', 'goodsname', 'goodsprice', 'goodsstock',
                      'goodsstatus', 'update_time', 'timestamp']

        # 提取参与签名的字段
        sign_data = {}
        for field in sign_fields:
            if field in data:
                sign_data[field] = str(data[field])

        # 按ASCII码排序（Python字典默认按key排序）
        sorted_keys = sorted(sign_data.keys())

        # 拼接签名字符串：key1=value1&key2=value2格式
        sign_str_parts = []
        for key in sorted_keys:
            sign_str_parts.append(f"{key}={sign_data[key]}")

        # 组成签名字符串并加上密钥
        sign_str = '&'.join(sign_str_parts) + merchant_key

        # 计算MD5签名
        calculated_sign = get_md5(sign_str)

        # 对比签名（不区分大小写）
        is_valid = calculated_sign.lower() == received_sign.lower()

        if not is_valid:
            logger.warning(f"聚比价推送签名验证失败 - 计算签名: {calculated_sign}, 接收签名: {received_sign}, 签名字符串: {sign_str}")
        else:
            logger.info(f"聚比价推送签名验证成功 - 商品ID: {data.get('goodsid', 'unknown')}")

        return is_valid

    except Exception as e:
        logger.error(f"聚比价推送签名验证异常: {str(e)}")
        return False


async def process_jubijia_product_update_async(push_data):

    try:
        # 获取商品ID（聚比价的goodsid对应本地的docking_id）
        goods_id = push_data.get('goodsid')
        if not goods_id:
            logger.warning("聚比价推送数据缺少goodsid字段")
            return {'code': -1, 'msg': '商品ID不能为空'}

        # 根据docking_id查找本地商品（jubijia类型的对接商品）
        try:
            # 查找对接商品：type=3且docking_id匹配且对接站点类型为jubijia
            good = await sync_to_async(
                Goods.objects.select_related('docking_site').get
            )(type='3', docking_id=goods_id, docking_site__type='jubijia')
        except Goods.DoesNotExist:
            logger.info(f"本地未找到对接商品 goodsid: {goods_id}，忽略此推送")
            return {'code': 0, 'msg': '本地未对接该商品，忽略推送'}

        # 记录更新的字段
        updated_fields = []

        # 获取当前商品信息用于对比
        current_name = await sync_to_async(lambda: good.name)()
        current_price = await sync_to_async(lambda: good.price)()
        current_stock = await sync_to_async(lambda: good.stock)()
        current_status = await sync_to_async(lambda: good.status)()
        current_info = await sync_to_async(lambda: good.info)()

        # 更新商品名称
        new_name = push_data.get('goodsname')
        if new_name and new_name != current_name:
            await sync_to_async(setattr)(good, 'name', new_name)
            updated_fields.append('name')

        # 更新商品价格
        new_price = push_data.get('goodsprice')
        if new_price is not None and str(new_price) != str(current_price):
            await sync_to_async(setattr)(good, 'price', new_price)
            updated_fields.append('price')

        # 更新商品库存
        new_stock = push_data.get('goodsstock')
        if new_stock is not None and new_stock != current_stock:
            await sync_to_async(setattr)(good, 'stock', new_stock)
            updated_fields.append('stock')

        # 更新商品状态（需要状态映射）
        new_status_raw = push_data.get('goodsstatus')
        if new_status_raw is not None:
            # 聚比价状态映射：1=出售中->1(上架), 0=下架中->2(下架), 404=已删除->2(下架)
            if new_status_raw == 1:
                new_status = '1'  # 上架
            elif new_status_raw in [0, 404]:
                new_status = '2'  # 下架
            else:
                new_status = '2'  # 默认下架

            if new_status != current_status:
                await sync_to_async(setattr)(good, 'status', new_status)
                updated_fields.append('status')

        # 更新商品详情
        new_detail = push_data.get('goodsdetail')
        if new_detail and new_detail != current_info:
            await sync_to_async(setattr)(good, 'info', new_detail)
            updated_fields.append('info')

        # 处理商品图片更新
        new_image_url = push_data.get('goodsimgurl')
        if new_image_url:
            current_image = await sync_to_async(lambda: good.image)()
            image_type = await sync_to_async(lambda: good.image_type)()

            # 只有图片URL不同时才更新
            if new_image_url != current_image:
                if image_type == '2':
                    # 链接读取方式，直接更新图片URL
                    await sync_to_async(setattr)(good, 'image', new_image_url)
                    updated_fields.append('image')
                elif image_type == '1':
                    # 本地读取方式，下载图片到本地
                    try:
                        good_id = await sync_to_async(lambda: good.id)()
                        image_url = await sync_to_async(download_image)(new_image_url, f'product_{good_id}')
                        await sync_to_async(setattr)(good, 'image', image_url)
                        updated_fields.append('image')
                    except Exception as e:
                        logger.warning(f"聚比价推送：商品 {goods_id} 图片下载失败: {str(e)}")

        # 只有当有字段更新时才保存到数据库
        if updated_fields:
            await sync_to_async(good.save)()
            logger.info(f"聚比价推送：商品 {goods_id} 更新成功，更新字段: {updated_fields}")
        else:
            logger.debug(f"聚比价推送：商品 {goods_id} 数据无变化，跳过数据库保存")

        return {
            'code': 200,
            'msg': '商品信息更新成功',
            'goods_id': goods_id,
            'updated_fields': updated_fields
        }

    except Exception as e:
        logger.error(f"聚比价推送：处理商品更新异常 - {str(e)}")
        return {'code': -1, 'msg': f'处理商品更新失败: {str(e)}'}


@csrf_exempt
async def jubijia_product_push_callback(request):

    if request.method != 'POST':
        logger.warning(f"聚比价推送回调：不支持的请求方法 {request.method}")
        return HttpResponse('error: method not allowed', content_type='text/plain')

    try:
        # 解析JSON数据
        try:
            push_data = json.loads(request.body.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            logger.error(f"聚比价推送回调：JSON解析失败 - {str(e)}")
            return HttpResponse('error: invalid json', content_type='text/plain')

        # 记录推送数据（用于调试）
        logger.info(f"聚比价推送回调：接收到推送数据 - goodsid: {push_data.get('goodsid', 'unknown')}")
        logger.debug(f"聚比价推送回调：完整推送数据 - {push_data}")

        # 获取商品ID，用于查找对应的对接站点
        goods_id = push_data.get('goodsid')
        if not goods_id:
            logger.warning("聚比价推送回调：推送数据缺少goodsid字段")
            return HttpResponse('error: missing goodsid', content_type='text/plain')

        # 查找对应的对接站点以获取密钥进行签名验证
        try:
            # 先查找商品以获取对接站点信息
            good = await sync_to_async(
                Goods.objects.select_related('docking_site').get
            )(type='3', docking_id=goods_id, docking_site__type='jubijia')

            docking_site = await sync_to_async(lambda: good.docking_site)()
            if docking_site is None:
                logger.error(f"聚比价推送回调：商品 {goods_id} 没有关联的对接站点")
                return HttpResponse('ok', content_type='text/plain')
            merchant_key = await sync_to_async(lambda: docking_site.key)()

        except Goods.DoesNotExist:
            # 本地未对接该商品，但仍需返回ok避免聚比价重试
            logger.info(f"聚比价推送回调：本地未对接商品 {goods_id}，返回ok避免重试")
            return HttpResponse('ok', content_type='text/plain')
        except Exception as e:
            logger.error(f"聚比价推送回调：查找对接站点失败 - {str(e)}")
            return HttpResponse('ok', content_type='text/plain')

        # 验证签名
        if not verify_jubijia_push_sign(push_data, merchant_key):
            logger.warning(f"聚比价推送回调：签名验证失败 - goodsid: {goods_id}")
            return HttpResponse('ok', content_type='text/plain')

        # 异步处理商品信息更新
        try:
            update_result = await process_jubijia_product_update_async(push_data)

            if update_result['code'] == 200:
                logger.info(f"聚比价推送回调：商品 {goods_id} 处理成功 - {update_result.get('msg', '')}")
            elif update_result['code'] == 0:
                # 商品未对接，正常情况
                logger.info(f"聚比价推送回调：{update_result.get('msg', '')}")
            else:
                # 处理失败，但仍返回ok避免无限重试
                logger.error(f"聚比价推送回调：商品 {goods_id} 处理失败 - {update_result.get('msg', '')}")

        except Exception as e:
            logger.error(f"聚比价推送回调：异步处理异常 - {str(e)}")
            # 即使处理异常，也返回ok避免聚比价无限重试

        # 按照聚比价要求，必须返回纯文本"ok"
        return HttpResponse('ok', content_type='text/plain')

    except Exception as e:
        logger.error(f"聚比价推送回调：处理异常 - {str(e)}")
        return HttpResponse('ok', content_type='text/plain')

# 配置日志记录器
logger = logging.getLogger(__name__)

# 创建线程池执行器用于异步任务
async_task_executor = concurrent.futures.ThreadPoolExecutor(
    max_workers=5,
    thread_name_prefix='async_payment_task'
)

def run_async_task_in_thread(async_func, *args, **kwargs):
    def run_async():
        try:

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:                
                return loop.run_until_complete(async_func(*args, **kwargs))
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"异步任务执行失败: {str(e)}", exc_info=True)
            return None

    # 投递线程池
    future = async_task_executor.submit(run_async)
    return future

# 验证传入的sign签名是否正确
def verify_sign(data):

    if 'sign' not in data:
        # print("DEBUG: 签名验证失败 - 数据中缺少sign字段")
        return False
    
    # 保存原始签名
    sign = data.pop('sign')
    # print(f"DEBUG: 接收到的签名: {sign}")
    
    try:
        # 打印原始数据（排除sign字段后）
        # print(f"DEBUG: 用于签名的数据: {data}")
        
        # 将数据转换为JSON字符串，ensure_ascii=False确保Unicode字符不被转义，json字符串中的字段按照ASCII码排序
        json_str = json.dumps(data, separators=(',', ':'), sort_keys=True, ensure_ascii=False)
        # print(f"DEBUG: JSON字符串: {json_str}")
        
        # 对JSON字符串进行Base64编码
        json_bytes = json_str.encode('utf-8')
        # print(f"DEBUG: UTF-8编码后的字节: {json_bytes}")
        
        base64_str = base64.b64encode(json_bytes).decode('utf-8')
        # print(f"DEBUG: Base64编码后的字符串: {base64_str}")
        
        # 计算Base64编码后字符串的MD5哈希值
        calculated_sign = get_md5(base64_str)
        # print(f"DEBUG: 计算出的签名: {calculated_sign}")
        
        # 对比计算出的签名和传入的签名
        is_valid = sign == calculated_sign
        # print(f"DEBUG: 签名验证结果: {is_valid}")
        
        # if not is_valid:
            # print(f"DEBUG: 签名不匹配 - 接收: {sign}, 计算: {calculated_sign}")
            
        return is_valid
    finally:
        # 无论验证结果如何，确保sign值被添加回数据
        data['sign'] = sign

@csrf_exempt
def administrator_login(request):
    # 处理OPTIONS请求
    if request.method == 'OPTIONS':
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, X-User-ID, X-Admin-Access-Token, X-Admin-ID, X-Admin-Role, Authorization'
        return response
    
    # 原有的POST请求处理
    elif request.method == 'POST':

        data = json.loads(request.body)
        account = data.get('account')
        password = data.get('password')
        sign = data.get('sign')
        timestamp = data.get('timestamp')
        true_account = DATABASES['AdminConfig']['username']
        true_password = DATABASES['AdminConfig']['password']

        headers = {
            'Access-Control-Allow-Origin': '*',
        }
        if not account or not password or not sign or not timestamp:
            return JsonResponse({'code': -1, 'message': '缺少参数[Account, password, sign, timestamp]'}, status=200, headers=headers)
        
        if get_md5(account + password) != sign:
            return JsonResponse({'code': -1, 'message': '签名错误'}, status=200, headers=headers)
        elif get_md5(true_account + true_password) != sign:
            return JsonResponse({'code': -1, 'message': '签名错误'}, status=200, headers=headers)

        if time.time() - int(timestamp) > 60 * 1 :
            return JsonResponse({'code': -1, 'message': '请求超时'}, status=200, headers=headers)
        
        if account == true_account and password == true_password:
            # 登录成功，生成JWT token
            try:
                # 生成JWT token
                admin_data = {
                    'admin_id': 'admin_001',  # 管理员ID
                    'username': account,
                    'role': 'super_admin',
                    'permissions': ['read', 'write', 'delete', 'admin']
                }

                # 生成JWT令牌对（访问令牌和刷新令牌）
                access_token, refresh_token = AdminJWTManager.generate_admin_tokens(admin_data)

                # 记录登录日志
                AdminAuditLogger.log_admin_operation(
                    admin_id='admin_001',
                    operation='administrator_login',
                    resource='admin',
                    request_data={'login_time': time.time(), 'ip': request.META.get('REMOTE_ADDR', 'unknown')},
                    result='success'
                )

                # 将token信息添加到响应头中
                response_headers = {
                    'Access-Control-Allow-Origin': '*',
                    'X-Admin-Access-Token': access_token,
                    'X-Admin-Refresh-Token': refresh_token,
                    'X-Admin-ID': 'admin_001',
                    'X-Admin-Role': 'super_admin',
                    'Access-Control-Expose-Headers': 'X-Admin-Access-Token,X-Admin-Refresh-Token,X-Admin-ID,X-Admin-Role'
                }

                return JsonResponse({
                    'code': 200,
                    'message': '登录成功',
                    'data': {
                        'admin_info': {
                            'admin_id': 'admin_001',
                            'username': account,
                            'role': 'super_admin',
                            'permissions': ['read', 'write', 'delete', 'admin'],
                            'login_time': int(time.time())
                        }
                    }
                }, status=200, headers=response_headers)

            except Exception as e:
                # JWT生成失败，记录错误日志
                AdminAuditLogger.log_admin_operation(
                    admin_id='unknown',
                    operation='administrator_login_error',
                    resource='admin',
                    request_data={'error': str(e), 'ip': request.META.get('REMOTE_ADDR', 'unknown')},
                    result='failure'
                )

                return JsonResponse({
                    'code': -1,
                    'message': 'JWT token生成失败'
                }, status=200, headers=headers)
        else:
            # 登录失败，记录审计日志
            AdminAuditLogger.log_admin_operation(
                admin_id='unknown',
                operation='administrator_login_failed',
                resource='admin',
                request_data={'account': account, 'ip': request.META.get('REMOTE_ADDR', 'unknown')},
                result='failure'
            )

            return JsonResponse({'code': -1, 'message': '账号或密码错误'}, status=200, headers=headers)
    else:
        return JsonResponse({'code': -1, 'message': '请求方法错误'}, status=200)


# 获取分类列表的功能
@csrf_exempt
@admin_auth_basic(operation='get_category_list', resource='category')
def get_category_list(request):
    """
    获取分类列表API
    GET /api/get_category_list
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 使用CategoryService获取分类数据
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.category.category_service import CategoryService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用分类服务
        category_service = CategoryService(context)
        categories = category_service.get_category_list(include_children=True, format_type='admin')
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': categories
        })
    
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_standard(operation='add_category', resource='category')
def add_category(request):
    """
    添加分类API
    POST /api/add_category
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 使用CategoryService创建分类
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.category.category_service import CategoryService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用分类服务
        category_service = CategoryService(context)
        
        # 准备分类数据
        category_data = {
            'name': data.get('name', ''),
            'image': data.get('image', ''),
            'level': data.get('level', 1),
            'parent_id': data.get('parent_id', '')
        }
        
        # 创建分类
        created_category = category_service.create_category(category_data)
        
        # 获取更新后的分类列表
        categories = category_service.get_category_list(include_children=True, format_type='admin')
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'id': created_category['id'],
            'data': categories
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_high(operation='delete_category', resource='category')
def delete_category(request):
    """
    删除分类API
    POST /api/delete_category
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        category_id = data.get('id', '')
        
        if not category_id:
            return JsonResponse({'code': -1, 'msg': '分类ID不能为空'})
        
        # 使用CategoryService删除分类
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.category.category_service import CategoryService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用分类服务
        category_service = CategoryService(context)
        
        # 删除分类
        category_service.delete_category(category_id)
        
        # 获取更新后的分类列表
        categories = category_service.get_category_list(include_children=True, format_type='admin')
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '删除分类成功',
            'data': categories
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_standard(operation='update_category', resource='category')
def update_category(request):
    """
    更新分类API
    POST /api/update_category
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        category_id = data.get('id', '')
        
        if not category_id:
            return JsonResponse({'code': -1, 'msg': '分类ID不能为空'})
        
        # 使用CategoryService更新分类
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.category.category_service import CategoryService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用分类服务
        category_service = CategoryService(context)
        
        # 准备更新数据
        update_data = {}
        if 'name' in data and data['name'] is not None:
            update_data['name'] = data['name']
        if 'image' in data and data['image'] is not None:
            update_data['image'] = data['image']
        if 'parent_id' in data and data['parent_id'] is not None:
            update_data['parent_id'] = data['parent_id']
        
        # 更新分类
        category_service.update_category(category_id, update_data)
        
        # 获取更新后的分类列表
        categories = category_service.get_category_list(include_children=True, format_type='admin')
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '更新分类成功',
            'data': categories
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

# 会员等级相关API
@csrf_exempt
@admin_auth_standard(operation='add_member_level', resource='member_level')
def add_member_level(request):
    """
    添加会员等级API
    POST /api/AddMemberLevel
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        original_sign = data.get('sign', '')
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        name = data.get('name', '')
        
        # 参数验证
        if not name:
            return JsonResponse({'code': -1, 'msg': '会员等级名称不能为空'})
        
        # 开始事务处理
        with transaction.atomic():
            # 生成新会员等级ID
            level_id = str(uuid.uuid4())
            
            # 创建新会员等级
            member_level = MemberLevel(
                id=level_id,
                name=name
            )
            member_level.save()
            
            # 构建返回响应
            return JsonResponse({
                'code': 200,
                'msg': '添加成功',
                'id': level_id
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_member_level', resource='member_level')
def delete_member_level(request):
    """
    删除会员等级API
    POST /api/DelMemberLevel
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        original_sign = data.get('sign', '')
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        level_id = data.get('id', '')
        
        # 参数验证
        if not level_id:
            return JsonResponse({'code': -1, 'msg': '会员等级ID不能为空'})
        
        # 开始事务处理
        with transaction.atomic():
            try:
                # A. 查找要删除的会员等级
                member_level = MemberLevel.objects.get(id=level_id)
                
                # B. 在此可以添加其他业务逻辑检查，例如检查该会员等级是否被使用
                
                # C. 执行删除操作
                member_level.delete()
                
            except MemberLevel.DoesNotExist:
                return JsonResponse({'code': -1, 'msg': '会员等级不存在或已被删除'})
            
            # 构建返回响应
            return JsonResponse({
                'code': 200,
                'msg': '删除成功'
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_member_level_list', resource='member_level')
def get_member_level_list(request):
    """
    获取会员等级列表API
    GET /api/GetMemberLevelList
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 获取所有会员等级
        member_levels = MemberLevel.objects.all().order_by('-created_at')
        
        # 构建响应数据
        result = []
        for level in member_levels:
            result.append({
                'id': level.id,
                'name': level.name,
                'create_time': level.created_at.isoformat() if level.created_at else '',
                'update_time': level.updated_at.isoformat() if level.updated_at else ''
            })
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })
    
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

# 价格模板相关API

@csrf_exempt
@admin_auth_standard(operation='add_price_template', resource='price_template')
def add_price_template(request):
    """
    添加价格模板API
    POST /api/AddPriceTemplate
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        name = data.get('name', '')
        type_value = data.get('type', '')
        template_data = data.get('data', {})
        
        # 参数验证
        if not name:
            return JsonResponse({'code': -1, 'msg': '模板名称不能为空'})
        
        if type_value not in ['1', '2']:
            return JsonResponse({'code': -1, 'msg': '模板类型必须为1(固定金额)或2(百分比)'})
        
        if not isinstance(template_data, dict) or 'NormalUser' not in template_data:
            return JsonResponse({'code': -1, 'msg': '模板数据格式不正确，必须包含NormalUser字段'})
        
        # 开始事务处理
        with transaction.atomic():
            # 检查是否是编辑模式
            template_id = data.get('id', '')
            if template_id:
                # 编辑现有模板
                try:
                    template = PriceTemplate.objects.get(id=template_id)
                    template.name = name
                    template.type = type_value
                    template.data_json = json.dumps(template_data)
                    template.save()
                    
                    return JsonResponse({
                        'code': 200,
                        'msg': '修改成功'
                    })
                except PriceTemplate.DoesNotExist:
                    return JsonResponse({'code': -1, 'msg': '模板不存在'})
            else:
                # 创建新模板
                template_id = str(uuid.uuid4())
                
                # 创建新价格模板
                template = PriceTemplate(
                    id=template_id,
                    name=name,
                    type=type_value,
                    data_json=json.dumps(template_data)
                )
                template.save()
                
                # 构建返回响应
                return JsonResponse({
                    'code': 200,
                    'msg': '添加成功',
                    'id': template_id
                })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_price_template', resource='price_template')
def delete_price_template(request):
    """
    删除价格模板API
    POST /api/DelPriceTemplate
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        template_id = data.get('id', '')
        
        # 参数验证
        if not template_id:
            return JsonResponse({'code': -1, 'msg': '模板ID不能为空'})
        
        # 开始事务处理
        with transaction.atomic():
            try:
                # 查找要删除的价格模板
                template = PriceTemplate.objects.get(id=template_id)
                
                # 执行删除操作
                template.delete()
                
                # 构建返回响应
                return JsonResponse({
                    'code': 200,
                    'msg': '删除成功'
                })
            except PriceTemplate.DoesNotExist:
                return JsonResponse({'code': -1, 'msg': '模板不存在或已被删除'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_price_template_list', resource='price_template')
def get_price_template_list(request):
    """
    获取价格模板列表API
    GET /api/GetPriceTemplateList
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 获取所有价格模板
        templates = PriceTemplate.objects.all().order_by('-created_at')
        
        # 构建响应数据
        result = []
        for template in templates:
            try:
                # 解析JSON数据
                data_dict = json.loads(template.data_json)
            except json.JSONDecodeError:
                data_dict = {'NormalUser': 0}
            
            result.append({
                'id': template.id,
                'name': template.name,
                'type': template.type,  # 1: 固定金额加价, 2: 百分比加价
                'data': data_dict,
                'create_time': template.created_at.isoformat() if template.created_at else '',
                'update_time': template.updated_at.isoformat() if template.updated_at else ''
            })
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })
    
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='update_price_template', resource='price_template')
def update_price_template(request):
    """
    更新价格模板API
    POST /api/UpdatePriceTemplate
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        template_id = data.get('id', '')
        name = data.get('name', None)
        type_value = data.get('type', None)
        template_data = data.get('data', None)
        
        # 参数验证
        if not template_id:
            return JsonResponse({'code': -1, 'msg': '模板ID不能为空'})
        
        # 如果没有提供任何可更新的字段，返回错误
        if name is None and type_value is None and template_data is None:
            return JsonResponse({'code': -1, 'msg': '没有提供可更新的字段'})
        
        # 开始事务处理
        with transaction.atomic():
            try:
                # 查找要更新的价格模板
                template = PriceTemplate.objects.get(id=template_id)
                
                # 更新字段
                if name is not None:
                    template.name = name
                
                if type_value is not None:
                    if type_value not in ['1', '2']:
                        return JsonResponse({'code': -1, 'msg': '模板类型必须为1(固定金额)或2(百分比)'})
                    template.type = type_value
                
                if template_data is not None:
                    if not isinstance(template_data, dict) or 'NormalUser' not in template_data:
                        return JsonResponse({'code': -1, 'msg': '模板数据格式不正确，必须包含NormalUser字段'})
                    template.data_json = json.dumps(template_data)
                
                # 保存更新
                template.save()
                
                # 构建返回响应
                return JsonResponse({
                    'code': 200,
                    'msg': '修改成功'
                })
            except PriceTemplate.DoesNotExist:
                return JsonResponse({'code': -1, 'msg': '模板不存在'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_card_library_list', resource='card_library')
def get_card_library_list(request):
    """
    获取卡库列表API
    GET /api/GetCardLibraryList
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 获取所有卡库
        libraries = CardLibrary.objects.all().order_by('-created_at')
        
        # 构建响应数据
        result = []
        for library in libraries:
            # 获取卡库中的卡密数量
            card_count = CardKey.objects.filter(library=library).count()
            
            result.append({
                'id': library.id,
                'name': library.name,
                'create_time': library.created_at.isoformat() if library.created_at else '',
                'update_time': library.updated_at.isoformat() if library.updated_at else '',
                'count': card_count
            })
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })
    except Exception as e:
        return JsonResponse({
            'code': -1,
            'msg': f'获取卡库列表失败：{str(e)}'
        })

@csrf_exempt
@admin_auth_standard(operation='add_card_library', resource='card_library')
def add_card_library(request):
    """
    添加卡库API
    POST /api/AddCardLibrary
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        name = data.get('name', '')
        
        # 参数验证
        if not name:
            return JsonResponse({'code': -1, 'msg': '卡库名称不能为空'})
        
        # 生成新卡库ID
        new_id = str(uuid.uuid4())
        
        # 创建卡库
        with transaction.atomic():
            library = CardLibrary.objects.create(id=new_id, name=name)
            
            return JsonResponse({
                'code': 200,
                'msg': '添加成功',
                'id': library.id
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON格式'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'添加卡库失败：{str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_card_library', resource='card_library')
def delete_card_library(request):
    """
    删除卡库API
    POST /api/DelCardLibrary
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        library_id = data.get('id')
        if not library_id:
            return JsonResponse({'code': -1, 'msg': '卡库ID不能为空'})
        
        # 删除卡库
        try:
            library = CardLibrary.objects.get(id=library_id)
            
            # 使用事务保证一致性
            with transaction.atomic():
                # 删除卡库下的所有卡密
                CardKey.objects.filter(library=library).delete()
                
                # 删除卡库
                library.delete()
                
                return JsonResponse({
                    'code': 200,
                    'msg': '删除成功'
                })
        except CardLibrary.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON格式'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'删除卡库失败：{str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='update_card_library', resource='card_library')
def update_card_library(request):
    """
    更新卡库信息API
    POST /api/UpdateCardLibrary
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        library_id = data.get('id')
        name = data.get('name')
        
        if not library_id:
            return JsonResponse({'code': -1, 'msg': '卡库ID不能为空'})
        
        if not name:
            return JsonResponse({'code': -1, 'msg': '卡库名称不能为空'})
        
        # 更新卡库
        try:
            library = CardLibrary.objects.get(id=library_id)
            library.name = name
            library.save()
            
            return JsonResponse({
                'code': 200,
                'msg': '更新成功'
            })
        except CardLibrary.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON格式'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'更新卡库失败：{str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_card_library_card_list', resource='card_library')
def get_card_library_card_list(request):
    """
    获取卡库中的卡密列表API
    GET /api/GetCardLibraryCardList?id=卡库ID
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 获取卡库ID
        library_id = request.GET.get('id')
        
        if not library_id:
            return JsonResponse({'code': -1, 'msg': '卡库ID不能为空'})
        
        # 查找卡库
        try:
            library = CardLibrary.objects.get(id=library_id)
        except CardLibrary.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
        
        # 获取卡库中的卡密
        cards = CardKey.objects.filter(library=library).order_by('-created_at')
        
        # 构建响应数据
        result = []
        for card in cards:
            result.append(card.content)
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })
    
    except Exception as e:
        return JsonResponse({
            'code': -1,
            'msg': f'获取卡密列表失败：{str(e)}'
        })

@csrf_exempt
@admin_auth_standard(operation='add_card_library_card', resource='card_library')
def add_card_library_card(request):
    """
    向卡库添加卡密API
    POST /api/AddCardLibraryCard
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        library_id = data.get('id')
        card_keys = data.get('card', [])
        
        if not library_id:
            return JsonResponse({'code': -1, 'msg': '卡库ID不能为空'})
        
        if not card_keys or not isinstance(card_keys, list):
            return JsonResponse({'code': -1, 'msg': '卡密列表不能为空且必须是数组'})
        
        # 查找卡库
        try:
            library = CardLibrary.objects.get(id=library_id)
        except CardLibrary.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
        
        # 添加卡密 - 重构后的逻辑：为每张卡密生成唯一UUID，允许重复内容
        with transaction.atomic():
            added_count = 0

            for key in card_keys:
                # 为每张卡密生成唯一的UUID作为主键
                card_id = str(uuid.uuid4())

                # 创建新卡密记录，每张卡密都有唯一ID
                CardKey.objects.create(
                    id=card_id,
                    library=library,
                    content=key,
                    status=0  # 默认状态：未售出
                )
                added_count += 1

            return JsonResponse({
                'code': 200,
                'msg': '添加成功',
                'data': {
                    'added_count': added_count,
                    'duplicate_count': 0  # 不再检查重复，每张卡密都有唯一ID
                }
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON格式'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'添加卡密失败：{str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_card_library_card', resource='card_library')
def delete_card_library_card(request):
    """
    删除卡库中的卡密API
    POST /api/DelCardLibraryCard
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        library_id = data.get('id')
        card_keys = data.get('card', [])
        
        if not library_id:
            return JsonResponse({'code': -1, 'msg': '卡库ID不能为空'})
        
        if not card_keys or not isinstance(card_keys, list):
            return JsonResponse({'code': -1, 'msg': '卡密列表不能为空且必须是数组'})
        
        # 查询卡库是否存在
        try:
            library = CardLibrary.objects.get(id=library_id)
        except CardLibrary.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
        
        # 删除卡密 - 重构后的逻辑：按新的模型结构处理删除
        with transaction.atomic():
            deleted_count = 0

            # 获取属于该卡库的所有卡密记录
            card_records = CardKey.objects.filter(library=library)

            for card_record in card_records:
                # 检查该卡密的内容是否在要删除的列表中
                if card_record.content in card_keys:
                    # 检查卡密是否已售出，已售出的卡密不能删除
                    if card_record.status == 1:
                        continue  # 跳过已售出的卡密

                    # 删除未售出的卡密
                    card_record.delete()
                    deleted_count += 1

            return JsonResponse({
                'code': 200,
                'msg': f'成功删除{deleted_count}个卡密',
                'data': {
                    'deleted_count': deleted_count
                }
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON格式'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'删除卡密失败：{str(e)}'})

# ==============================================================================================
# 重新获取对接商品的对接成本价格 更新数据库模型数据

@csrf_exempt
def update_docking_good_info(good_id: str):
    try:

        try:
            good = Goods.objects.get(id=good_id)
        except Goods.DoesNotExist:
            return {'code': -1, 'msg': '商品不存在'}

        # 判断商品类型
        if good.type != '3':
            return {'code': -1, 'msg': '商品非对接商品'}

        # 检查对接信息是否完整
        if not good.docking_site or not good.docking_id:
            return {'code': -1, 'msg': '对接商品信息不完整'}

        # 获取对接商品的对接源与对接ID
        docking_site_id = good.docking_site.id
        docking_product_id = good.docking_id

        # 获取对接站点信息
        try:
            docking_site = DockingSite.objects.get(id=docking_site_id)
        except DockingSite.DoesNotExist:
            return {'code': -1, 'msg': '对接货源站点不存在'}

        docking_url = docking_site.url
        docking_appid = docking_site.appid
        docking_key = docking_site.key
        docking_type = docking_site.type

        # ========== 注释：jubijia平台处理分支 ==========
        # 原因：改为使用聚比价平台的商品信息变更推送机制，不再主动请求获取商品信息
        # 保留此代码供其他需要主动请求的对接平台参考
        # 推送回调接口：/api/jubijia_product_push_callback
        # ===============================================
        if docking_type == 'jubijia':
            # 跳过jubijia平台的主动请求处理，改为监听推送
            return {
                'code': 200,
                'msg': 'jubijia平台商品信息通过推送机制更新，无需主动请求',
                'data': {
                    'id': good.id,
                    'name': good.name,
                    'price': str(good.price),
                    'stock': good.stock,
                    'updated_at': good.updated_at.isoformat() if good.updated_at else ''
                }
            }
        else:
            return {'code': -1, 'msg': f'不支持的对接类型: {docking_type}'}

        # 以下为原jubijia处理逻辑，已注释但保留供参考
        """
        if docking_type == 'jubijia':
            api_url = 'http://jubijia.com/api/open/product/detail'

            # 构建签名字符串
            sign = get_md5(f"id={docking_product_id}&userId={docking_appid}{docking_key}")

            # 构建请求参数
            request_data = {
                'id': docking_product_id,
                'userId': docking_appid,
                'sign': sign
            }

            # 发送POST请求
            try:
                response = requests.post(api_url, data=request_data, timeout=30)
                if response.status_code != 200:
                    return {
                        'code': -1,
                        'msg': f'网络请求失败，状态码: {response.status_code}'
                    }

                # 解析返回数据
                response_data = response.json()

                # 检查API返回状态
                if response_data.get('code') != 200:
                    return {
                        'code': -1,
                        'msg': f'API返回错误: {response_data.get("msg", "未知错误")}'
                    }

                # 获取商品详情数据
                data = response_data.get('data', {})

                # 更新商品信息
                if 'details' in data:
                    good.info = data['details']

                if 'name' in data:
                    good.name = data['name']

                if 'price' in data:
                    good.price = data['price']

                if 'quota' in data:
                    good.stock = data['quota']

                # 处理图片更新
                if 'image' in data and data['image']:
                    if good.image_type == '2':
                        # 链接读取方式，直接更新图片URL
                        good.image = data['image']
                    elif good.image_type == '1':
                        # 本地读取方式，下载图片到本地
                        try:
                            image_url = download_image(data['image'], f'product_{good_id}')
                            good.image = image_url
                        except Exception as e:
                            # 图片下载失败，记录错误但不影响其他信息更新
                            print(f"图片下载失败: {str(e)}")

                # 处理attach字段格式转换
                if 'attach' in data and data['attach']:
                    try:
                        attach_data = data['attach']
                        if isinstance(attach_data, list):
                            # 为每个attach项添加tip字段
                            processed_attach = []
                            for item in attach_data:
                                if isinstance(item, dict) and 'name' in item:
                                    processed_item = item.copy()
                                    processed_item['tip'] = f"请输入{item['name']}"
                                    processed_attach.append(processed_item)
                                else:
                                    processed_attach.append(item)

                            good.attach = json.dumps(processed_attach, ensure_ascii=False)
                    except Exception as e:
                        print(f"处理attach字段失败: {str(e)}")

                # 保存更新
                good.save()

                return {
                    'code': 200,
                    'msg': '商品信息更新成功',
                    'data': {
                        'id': good.id,
                        'name': good.name,
                        'price': str(good.price),
                        'stock': good.stock,
                        'updated_at': good.updated_at.isoformat() if good.updated_at else ''
                    }
                }

            except requests.RequestException as e:
                return {'code': -1, 'msg': f'网络请求异常: {str(e)}'}
            except json.JSONDecodeError:
                return {'code': -1, 'msg': 'API返回数据格式错误'}
        """

    except Exception as e:
        return {'code': -1, 'msg': f'更新失败: {str(e)}'}


# ==============================================================================================
# 异步版本的对接商品信息更新函数 - 支持高性能并发处理

async def update_docking_good_info_async(session, good_id: str):
    """
    异步更新单个对接商品信息
    Args:
        session: aiohttp.ClientSession 实例
        good_id: 商品ID
    Returns:
        dict: 更新结果
    """
    try:
        # 使用sync_to_async包装数据库查询，包含外键关系
        good = await sync_to_async(Goods.objects.select_related('docking_site').get)(id=good_id)

        # 判断商品类型
        if good.type != '3':
            return {'code': -1, 'msg': '商品非对接商品', 'good_id': good_id}

        # 检查对接信息是否完整 - 使用sync_to_async包装外键访问
        docking_site = await sync_to_async(lambda: good.docking_site)()
        docking_id = await sync_to_async(lambda: good.docking_id)()

        if not docking_site or not docking_id:
            return {'code': -1, 'msg': '对接商品信息不完整', 'good_id': good_id}

        # 获取对接商品的对接源与对接ID
        docking_product_id = docking_id

        # 获取对接站点信息 - 使用sync_to_async包装属性访问
        docking_url = await sync_to_async(lambda: docking_site.url)()
        docking_appid = await sync_to_async(lambda: docking_site.appid)()
        docking_key = await sync_to_async(lambda: docking_site.key)()
        docking_type = await sync_to_async(lambda: docking_site.type)()

        # ========== 注释：jubijia平台异步处理分支 ==========
        # 原因：改为使用聚比价平台的商品信息变更推送机制，不再主动请求获取商品信息
        # 保留此代码供其他需要主动请求的对接平台参考
        # 推送回调接口：/api/jubijia_product_push_callback
        # ===============================================
        if docking_type == 'jubijia':
            # 跳过jubijia平台的主动请求处理，改为监听推送
            good_data = {
                'id': await sync_to_async(lambda: good.id)(),
                'name': await sync_to_async(lambda: good.name)(),
                'price': str(await sync_to_async(lambda: good.price)()),
                'stock': await sync_to_async(lambda: good.stock)(),
            }

            return {
                'code': 200,
                'msg': 'jubijia平台商品信息通过推送机制更新，无需主动请求',
                'good_id': good_id,
                'updated_fields': [],
                'data': good_data
            }
        else:
            return {'code': -1, 'msg': f'不支持的对接类型: {docking_type}', 'good_id': good_id}

        # 以下为原jubijia异步处理逻辑，已注释但保留供参考
        """
        if docking_type == 'jubijia':
            api_url = 'http://jubijia.com/api/open/product/detail'

            # 构建签名字符串
            sign = get_md5(f"id={docking_product_id}&userId={docking_appid}{docking_key}")

            # 构建请求参数
            request_data = {
                'id': docking_product_id,
                'userId': docking_appid,
                'sign': sign
            }

            # 发送异步POST请求
            try:
                async with session.post(api_url, data=request_data, timeout=3) as response:
                    if response.status != 200:
                        return {
                            'code': -1,
                            'msg': f'网络请求失败，状态码: {response.status}',
                            'good_id': good_id
                        }

                    # 解析返回数据
                    response_data = await response.json()

                    # 检查API返回状态
                    if response_data.get('code') != 200:
                        return {
                            'code': -1,
                            'msg': f'API返回错误: {response_data.get("msg", "未知错误")}',
                            'good_id': good_id
                        }

                    # 获取商品详情数据
                    data = response_data.get('data', {})

                    # 更新商品信息 - 添加数据对比，只更新变化的字段
                    updated_fields = []

                    # 获取当前商品信息用于对比
                    current_info = await sync_to_async(lambda: good.info)()
                    current_name = await sync_to_async(lambda: good.name)()
                    current_price = await sync_to_async(lambda: good.price)()
                    current_stock = await sync_to_async(lambda: good.stock)()

                    # 对比并更新info字段
                    if 'details' in data and data['details'] != current_info:
                        await sync_to_async(setattr)(good, 'info', data['details'])
                        updated_fields.append('info')

                    # 对比并更新name字段
                    if 'name' in data and data['name'] != current_name:
                        await sync_to_async(setattr)(good, 'name', data['name'])
                        updated_fields.append('name')

                    # 对比并更新price字段
                    if 'price' in data and str(data['price']) != str(current_price):
                        await sync_to_async(setattr)(good, 'price', data['price'])
                        updated_fields.append('price')

                    # 对比并更新stock字段
                    if 'quota' in data and data['quota'] != current_stock:
                        await sync_to_async(setattr)(good, 'stock', data['quota'])
                        updated_fields.append('stock')

                    # 处理图片更新 - 添加对比逻辑
                    if 'image' in data and data['image']:
                        # 获取当前图片信息用于对比
                        current_image = await sync_to_async(lambda: good.image)()
                        image_type = await sync_to_async(lambda: good.image_type)()

                        # 只有图片URL不同时才更新
                        if data['image'] != current_image:
                            if image_type == '2':
                                # 链接读取方式，直接更新图片URL
                                await sync_to_async(setattr)(good, 'image', data['image'])
                                updated_fields.append('image')
                            elif image_type == '1':
                                # 本地读取方式，下载图片到本地
                                try:
                                    # 使用sync_to_async包装同步的download_image函数
                                    image_url = await sync_to_async(download_image)(data['image'], f'product_{good_id}')
                                    await sync_to_async(setattr)(good, 'image', image_url)
                                    updated_fields.append('image')
                                except Exception as e:
                                    # 图片下载失败，记录错误但不影响其他信息更新
                                    logger.warning(f"异步更新商品 {good_id} 图片下载失败: {str(e)}")
                        else:
                            logger.debug(f"商品 {good_id} 图片未变化，跳过更新")

                    # 处理attach字段格式转换
                    if 'attach' in data and data['attach']:
                        try:
                            attach_data = data['attach']
                            if isinstance(attach_data, list):
                                # 为每个attach项添加tip字段
                                processed_attach = []
                                for item in attach_data:
                                    if isinstance(item, dict) and 'name' in item:
                                        processed_item = item.copy()
                                        processed_item['tip'] = f"请输入{item['name']}"
                                        processed_attach.append(processed_item)
                                    else:
                                        processed_attach.append(item)

                                await sync_to_async(setattr)(good, 'attach', json.dumps(processed_attach, ensure_ascii=False))
                                updated_fields.append('attach')
                        except Exception as e:
                            logger.warning(f"异步更新商品 {good_id} 处理attach字段失败: {str(e)}")

                    # 只有当有字段更新时才保存到数据库
                    if updated_fields:
                        await sync_to_async(good.save)()
                        logger.info(f"异步更新商品 {good_id} 成功，更新字段: {updated_fields}")
                    else:
                        logger.debug(f"商品 {good_id} 数据无变化，跳过数据库保存")

                    # 使用sync_to_async包装属性访问
                    good_data = {
                        'id': await sync_to_async(lambda: good.id)(),
                        'name': await sync_to_async(lambda: good.name)(),
                        'price': str(await sync_to_async(lambda: good.price)()),
                        'stock': await sync_to_async(lambda: good.stock)(),
                    }

                    return {
                        'code': 200,
                        'msg': '商品信息更新成功',
                        'good_id': good_id,
                        'updated_fields': updated_fields,
                        'data': good_data
                    }

            except asyncio.TimeoutError:
                return {'code': -1, 'msg': '请求超时', 'good_id': good_id}
            except Exception as e:
                return {'code': -1, 'msg': f'网络请求异常: {str(e)}', 'good_id': good_id}
        """

    except Goods.DoesNotExist:
        return {'code': -1, 'msg': '商品不存在', 'good_id': good_id}
    except DockingSite.DoesNotExist:
        return {'code': -1, 'msg': '对接货源站点不存在', 'good_id': good_id}
    except Exception as e:
        logger.error(f"异步更新商品 {good_id} 失败: {str(e)}")
        return {'code': -1, 'msg': f'更新失败: {str(e)}', 'good_id': good_id}


async def batch_update_docking_goods_async(goods_list):
    """
    批量异步更新对接商品信息
    Args:
        goods_list: 商品对象列表
    Returns:
        list: 更新结果列表
    """
    if not goods_list:
        return []

    # 过滤出对接商品
    docking_goods = [good for good in goods_list if good.type == '3']

    if not docking_goods:
        return []

    # 配置HTTP连接池
    connector = aiohttp.TCPConnector(
        limit=100,  # 总连接池大小
        limit_per_host=50,  # 每个主机的连接数
        ttl_dns_cache=300,  # DNS缓存时间
        use_dns_cache=True,
    )

    timeout = aiohttp.ClientTimeout(total=3, connect=1)

    try:
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'ShoppingDjango/1.0'}
        ) as session:

            # 分批处理，每批50个商品
            batch_size = 50
            all_results = []

            for i in range(0, len(docking_goods), batch_size):
                batch = docking_goods[i:i+batch_size]
                logger.info(f"开始处理第 {i//batch_size + 1} 批对接商品，数量: {len(batch)}")

                # 创建异步任务
                tasks = [
                    update_docking_good_info_async(session, good.id)
                    for good in batch
                ]

                # 并发执行任务
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理结果
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"批量更新异常: {str(result)}")
                        all_results.append({
                            'code': -1,
                            'msg': f'处理异常: {str(result)}',
                            'good_id': 'unknown'
                        })
                    else:
                        all_results.append(result)

                # 批次间短暂延迟，避免过度压力
                if i + batch_size < len(docking_goods):
                    await asyncio.sleep(0.1)

            # 统计结果
            success_count = sum(1 for r in all_results if r.get('code') == 200)
            total_count = len(all_results)

            logger.info(f"批量异步更新完成: 成功 {success_count}/{total_count}")

            return all_results

    except Exception as e:
        logger.error(f"批量异步更新失败: {str(e)}")
        return [{'code': -1, 'msg': f'批量更新失败: {str(e)}', 'good_id': good.id} for good in docking_goods]


@csrf_exempt
@admin_auth_basic(operation='get_goods_list', resource='goods')
def get_goods_list(request):
    """
    获取商品列表API - 支持分页和搜索筛选
    GET /api/GetGoodsList?page=1&page_size=20&keyword=&primary_category=&secondary_category=&product_type=&product_status=
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 使用ProductService获取商品列表
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 获取分页参数
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 100)  # 最大100条
        
        # 构建过滤条件
        filters = {}
        
        # 获取搜索筛选参数
        keyword = request.GET.get('keyword', '').strip()
        if keyword:
            filters['name'] = keyword
        
        primary_category = request.GET.get('primary_category', '').strip()
        secondary_category = request.GET.get('secondary_category', '').strip()
        category_id = request.GET.get('category_id', '').strip()  # 兼容旧版本
        
        # 分类筛选逻辑
        if secondary_category:
            filters['category_id'] = secondary_category
        elif primary_category:
            # 获取该一级分类下的所有二级分类ID
            try:
                secondary_categories = Category.objects.filter(
                    level=2, parent_id=primary_category
                ).values_list('id', flat=True)
                if secondary_categories:
                    filters['category_id__in'] = list(secondary_categories)
                else:
                    filters['category_id'] = 'nonexistent'  # 返回空结果
            except Exception as e:
                logger.warning(f"处理一级分类筛选时出错: {str(e)}")
        elif category_id:
            filters['category_id'] = category_id
        
        product_type = request.GET.get('product_type', '').strip()
        if product_type:
            filters['type'] = product_type
        
        product_status = request.GET.get('product_status', '').strip()
        if product_status:
            filters['status'] = product_status
        
        # 分页参数
        pagination = {
            'page': page,
            'page_size': page_size
        }
        
        # 获取商品列表
        result = product_service.get_product_list(
            filters=filters,
            pagination=pagination,
            format_type='admin'
        )
        
        # 转换为原有的响应格式
        items = []
        for product in result['list']:
            # 处理图片URL逻辑（保持原有逻辑）
            image_url = product.get('image', '')
            product_id = product.get('id', '')
            
            # 这里需要获取原始商品数据来判断image_type
            try:
                from api.models import Goods
                goods = Goods.objects.get(id=product_id)
                if goods.image_type == '1' and goods.image:
                    image_url = f'/api/get_local_image_product?id={goods.id}'
                elif goods.image_type == '2':
                    image_url = goods.image
                
                items.append({
                    'id': product.get('id'),
                    'name': product.get('name'),
                    'price': product.get('price', '').replace('¥', ''),
                    'image': image_url,
                    'image_type': goods.image_type or '2',
                    'PriceTemplate': product.get('price_template', ''),
                    'type': product.get('type'),
                    'sales_count': getattr(goods, 'sales_count', 0),
                    'status': product.get('status'),
                    'stock': product.get('stock'),
                    'category_id': product.get('category_id', ''),
                    'notice': getattr(goods, 'notice', ''),
                    'operation_data': getattr(goods, 'operation_data', {})
                })
            except:
                # 如果获取原始数据失败，使用默认值
                items.append({
                    'id': product.get('id'),
                    'name': product.get('name'),
                    'price': product.get('price', '').replace('¥', ''),
                    'image': image_url,
                    'image_type': '2',
                    'PriceTemplate': product.get('price_template', ''),
                    'type': product.get('type'),
                    'sales_count': 0,
                    'status': product.get('status'),
                    'stock': product.get('stock'),
                    'category_id': product.get('category_id', ''),
                    'notice': '',
                    'operation_data': {}
                })
        
        # 构建分页响应
        pagination_data = {
            'current_page': result.get('page', 1),
            'page_size': result.get('page_size', 20),
            'total': result.get('total', 0),
            'total_pages': result.get('total_pages', 1),
            'has_next': result.get('has_next', False),
            'has_prev': result.get('has_prev', False)
        }
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': {
                'items': items,
                'pagination': pagination_data
            }
        })

    except ValueError as e:
        return JsonResponse({'code': -1, 'msg': f'参数错误: {str(e)}'})
    except Exception as e:
        logger.error(f"获取商品列表失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_goods_detail', resource='goods')
def get_goods_detail(request):
    """
    获取商品详情
    POST /api/GetGoodsDetail
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        goods_id = data.get('id', '')
        
        if not goods_id:
            return JsonResponse({'code': -1, 'msg': '商品ID不能为空'})
        
        # 使用ProductService获取商品详情
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 获取商品详情
        product_data = product_service.get_product_detail(goods_id, format_type='admin')
        
        # 获取原始商品数据以保持兼容性
        from api.models import Goods
        try:
            goods = Goods.objects.get(id=goods_id)
            
            # 判断是否为对接商品，如果是则更新商品信息
            if goods.type == '3':
                try:
                    # 使用ProductService的对接商品更新功能
                    product_service.update_docking_product(goods_id)
                    # 重新获取商品信息
                    goods.refresh_from_db()
                    product_data = product_service.get_product_detail(goods_id, format_type='admin')
                except Exception as e:
                    logger.error(f"商品详情：更新对接商品 {goods.id} 信息失败: {str(e)}")
            
            # 处理图片URL
            image_url = product_data.get('image', '')
            if goods.image_type == '1' and goods.image:
                image_url = f'/api/get_local_image_product?id={goods.id}'
            elif goods.image_type == '2':
                image_url = goods.image
            
            # 检查图片URL是否为空，如果为空则使用占位符API
            if not image_url or not image_url.strip():
                image_url = f'/api/get_local_image_product?id={goods.id}&placeholder=true'
            
            # 解析attach字段
            attach_list = []
            if goods.attach:
                try:
                    attach_data = json.loads(goods.attach)
                    if isinstance(attach_data, list):
                        attach_list = attach_data
                except json.JSONDecodeError:
                    pass
            
            # 构建响应（保持原有格式）
            response_data = {
                'id': goods.id,
                'name': product_data.get('name'),
                'price': product_data.get('price', '').replace('¥', ''),
                'image': image_url,
                'image_type': goods.image_type or '2',
                'PriceTemplate': goods.price_template.id if goods.price_template else '',
                'type': product_data.get('type'),
                'card_library': goods.card_library.id if goods.card_library else '',
                'status': product_data.get('status'),
                'stock': product_data.get('stock'),
                'category_id': product_data.get('category_id', ''),
                'create_time': product_data.get('created_at', ''),
                'update_time': product_data.get('updated_at', ''),
                'info': product_data.get('info', ''),
                'attach': attach_list,
                'notice': getattr(goods, 'notice', ''),
                'operation_data': getattr(goods, 'operation_data', {})
            }
            
        except Goods.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '商品不存在'})
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': response_data
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='add_goods', resource='goods')
def add_goods(request):
    """
    添加商品API
    POST /api/AddGoods
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 使用ProductService创建商品
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 准备商品数据
        product_data = {
            'name': data.get('name', ''),
            'price': data.get('price', ''),
            'image': data.get('image', ''),
            'type': data.get('type', ''),
            'category_id': data.get('category_id', ''),
            'stock': data.get('stock', 0),
            'description': data.get('info', ''),
            'attach_data': data.get('attach', [])
        }
        
        # 添加额外的业务字段（这些字段在ProductService中可能不直接支持，需要特殊处理）
        price_template_id = data.get('PriceTemplate', '')
        card_library_id = data.get('card_library', '')
        notice = data.get('notice', '')
        order_operation = data.get('order_operation', {})
        
        # 业务逻辑验证（保持原有逻辑）
        goods_type = product_data['type']
        if goods_type not in ['1', '2']:
            return JsonResponse({'code': -1, 'msg': '商品类型必须为1(卡密商品)或2(虚拟商品)'})
        
        if goods_type == '1' and not card_library_id:
            return JsonResponse({'code': -1, 'msg': '卡密商品必须指定卡库ID'})
        
        # 创建商品
        created_product = product_service.create_product(product_data)
        
        # 处理ProductService不直接支持的字段，包括图片类型设置
        try:
            from api.models import Goods, PriceTemplate, CardLibrary
            goods = Goods.objects.get(id=created_product['id'])
            
            # 处理图片类型设置
            image_url = data.get('image', '')
            if image_url:
                # 检查是否为URL格式的图片（通过上传API获得的图片）
                if image_url.startswith('/media/product_images/') or image_url.startswith('http'):
                    goods.image_type = '2'  # 链接读取方式
                elif image_url.startswith('data:image/'):
                    goods.image_type = '1'  # 本地读取方式（base64数据）
                else:
                    goods.image_type = '2'  # 默认为链接读取方式
            
            # 处理价格模板
            if price_template_id:
                try:
                    price_template = PriceTemplate.objects.get(id=price_template_id)
                    goods.price_template = price_template
                except PriceTemplate.DoesNotExist:
                    pass
            
            # 处理卡库
            if card_library_id:
                try:
                    card_library = CardLibrary.objects.get(id=card_library_id)
                    goods.card_library = card_library
                except CardLibrary.DoesNotExist:
                    pass
            
            # 处理其他字段
            if notice:
                goods.notice = notice
            if order_operation:
                goods.operation_data = order_operation
            
            goods.save()
            
        except Exception as e:
            logger.warning(f"处理额外字段时出错: {str(e)}")
        
        # 返回成功响应
        return JsonResponse({
            'code': 200,
            'msg': '添加成功',
            'id': created_product['id']
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_goods', resource='goods')
def delete_goods(request):
    """
    删除商品API
    POST /api/deleteGood
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        goods_id = data.get('id', '')
        
        if not goods_id:
            return JsonResponse({'code': -1, 'msg': '商品ID不能为空'})
        
        # 使用ProductService删除商品
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 删除商品
        success = product_service.delete_product(goods_id)
        
        if success:
            return JsonResponse({
                'code': 200,
                'msg': '删除成功'
            })
        else:
            return JsonResponse({'code': -1, 'msg': '删除失败'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='update_goods', resource='goods')
def update_goods(request):
    """
    修改商品API
    POST /api/UpdateGoods
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        goods_id = data.get('id', '')
        
        # 参数验证
        if not goods_id:
            return JsonResponse({'code': -1, 'msg': '商品ID不能为空'})
        
        # 使用ProductService更新商品
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 准备更新数据
        update_data = {}
        
        # 基本字段
        if 'name' in data and data['name'] is not None:
            update_data['name'] = data['name']
        
        if 'price' in data and data['price'] is not None:
            update_data['price'] = data['price']
        
        if 'image' in data and data['image'] is not None:
            update_data['image'] = data['image']
        
        if 'info' in data and data['info'] is not None:
            update_data['description'] = data['info']
        
        if 'attach' in data and data['attach'] is not None:
            update_data['attach_data'] = data['attach']
        
        if 'category_id' in data and data['category_id'] is not None:
            update_data['category_id'] = data['category_id']
        
        if 'stock' in data and data['stock'] is not None:
            update_data['stock'] = data['stock']
        
        if 'status' in data and data['status'] is not None:
            update_data['status'] = data['status']
        
        # 更新商品
        updated_product = product_service.update_product(goods_id, update_data)
        
        # 处理ProductService不直接支持的字段
        price_template_id = data.get('PriceTemplate', None)
        goods_type = data.get('type', None)
        card_library_id = data.get('card_library', None)
        notice = data.get('notice', None)
        order_operation = data.get('order_operation', None)
        
        if any([price_template_id is not None, goods_type is not None, 
                card_library_id is not None, notice is not None, 
                order_operation is not None]):
            try:
                from api.models import Goods, PriceTemplate, CardLibrary, Category
                goods = Goods.objects.get(id=goods_id)
                
                # 处理价格模板
                if price_template_id is not None:
                    if price_template_id:
                        try:
                            price_template = PriceTemplate.objects.get(id=price_template_id)
                            goods.price_template = price_template
                        except PriceTemplate.DoesNotExist:
                            return JsonResponse({'code': -1, 'msg': '价格模板不存在'})
                    else:
                        goods.price_template = None
                
                # 处理商品类型
                if goods_type is not None:
                    goods.type = goods_type
                
                # 处理卡库
                if card_library_id is not None:
                    if card_library_id:
                        try:
                            card_library = CardLibrary.objects.get(id=card_library_id)
                            goods.card_library = card_library
                        except CardLibrary.DoesNotExist:
                            return JsonResponse({'code': -1, 'msg': '卡库不存在'})
                    else:
                        goods.card_library = None
                
                # 处理其他字段
                if notice is not None:
                    goods.notice = notice
                
                if order_operation is not None:
                    goods.operation_data = order_operation
                
                goods.save()
                
            except Exception as e:
                logger.warning(f"处理额外字段时出错: {str(e)}")
        
        # 返回成功响应
        return JsonResponse({
            'code': 200,
            'msg': '修改成功'
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

# 对接中心相关API
@csrf_exempt
@admin_auth_standard(operation='add_docking', resource='docking')
def add_docking(request):
    """
    添加对接社区API
    POST /api/addDocking
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        name = data.get('name', '')
        url = data.get('url', '')
        appid = data.get('appid', '')
        site_type = data.get('type', '')
        key = data.get('key', '')
        
        # 参数验证
        if not name:
            return JsonResponse({'code': -1, 'msg': '对接社区名称不能为空'})
        
        if not url:
            return JsonResponse({'code': -1, 'msg': '对接社区地址不能为空'})
        
        if not appid:
            return JsonResponse({'code': -1, 'msg': '对接社区APPID不能为空'})
        
        if not site_type:
            return JsonResponse({'code': -1, 'msg': '对接社区类型不能为空'})
        
        if not key:
            return JsonResponse({'code': -1, 'msg': '对接社区密钥不能为空'})
        
        # 开始事务处理
        with transaction.atomic():
            # 生成新ID
            new_id = str(uuid.uuid4())
            
            # 创建新对接社区
            docking_site = DockingSite(
                id=new_id,
                name=name,
                url=url,
                appid=appid,
                type=site_type,
                key=key
            )
            docking_site.save()
            
            # 获取所有对接社区信息
            all_sites = DockingSite.objects.all().order_by('-created_at')
            
            # 构建返回数据
            sites_list = []
            for site in all_sites:
                sites_list.append({
                    'id': site.id,
                    'name': site.name,
                    'url': site.url,
                    'appid': site.appid,
                    'type': site.type,
                    'key': site.key
                })
            
            return JsonResponse({
                'code': 200,
                'msg': '添加成功',
                'data': {
                    'id': new_id,
                    'list': sites_list
                }
            })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_docking_list', resource='docking')
def get_docking_list(request):
    """
    获取对接社区列表
    GET /api/getDockingList
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 获取所有对接社区
        sites = DockingSite.objects.all().order_by('-created_at')
        
        # 构建响应数据
        sites_list = []
        for site in sites:
            sites_list.append({
                'id': site.id,
                'name': site.name,
                'url': site.url,
                'appid': site.appid,
                'type': site.type,
                'key': site.key
            })
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': {
                'list': sites_list
            }
        })
    
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_docking', resource='docking')
def delete_docking(request):
    """
    删除对接社区
    POST /api/deletetDocking
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        site_id = data.get('id', '')
        
        if not site_id:
            return JsonResponse({'code': -1, 'msg': '对接社区ID不能为空'})
        
        # 开始事务处理
        with transaction.atomic():
            try:
                # 查找要删除的对接社区
                site = DockingSite.objects.get(id=site_id)
                
                # 执行删除
                site.delete()
                
                # 获取更新后的对接社区列表
                sites = DockingSite.objects.all().order_by('-created_at')
                
                # 构建返回数据
                sites_list = []
                for site in sites:
                    sites_list.append({
                        'id': site.id,
                        'name': site.name,
                        'url': site.url,
                        'appid': site.appid,
                        'type': site.type,
                        'key': site.key
                    })
                
                return JsonResponse({
                    'code': 200,
                    'msg': '删除成功',
                    'data': {
                        'list': sites_list
                    }
                })
            except DockingSite.DoesNotExist:
                return JsonResponse({'code': -1, 'msg': '对接社区不存在或已被删除'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='update_docking', resource='docking')
def update_docking(request):
    """
    修改对接社区信息
    POST /api/updateDocking
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        site_id = data.get('id', '')
        name = data.get('name', None)
        url = data.get('url', None)
        appid = data.get('appid', None)
        site_type = data.get('type', None)
        key = data.get('key', None)
        
        if not site_id:
            return JsonResponse({'code': -1, 'msg': '对接社区ID不能为空'})
        
        # 如果没有提供任何可更新的字段，返回错误
        if name is None and url is None and appid is None and site_type is None and key is None:
            return JsonResponse({'code': -1, 'msg': '没有提供可更新的字段'})
        
        # 开始事务处理
        with transaction.atomic():
            try:
                # 查找要更新的对接社区
                site = DockingSite.objects.get(id=site_id)
                
                # 更新字段
                if name is not None:
                    site.name = name
                
                if url is not None:
                    site.url = url
                
                if appid is not None:
                    site.appid = appid
                
                if site_type is not None:
                    site.type = site_type
                
                if key is not None:
                    site.key = key
                
                # 保存更新
                site.save()
                
                # 获取更新后的对接社区列表
                sites = DockingSite.objects.all().order_by('-created_at')
                
                # 构建返回数据
                sites_list = []
                for site in sites:
                    sites_list.append({
                        'id': site.id,
                        'name': site.name,
                        'url': site.url,
                        'appid': site.appid,
                        'type': site.type,
                        'key': site.key
                    })
                
                return JsonResponse({
                    'code': 200,
                    'msg': '修改成功',
                    'data': {
                        'list': sites_list
                    }
                })
            except DockingSite.DoesNotExist:
                return JsonResponse({'code': -1, 'msg': '对接社区不存在'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
def BatchAddDockingProduct(request):
    """
    批量添加对接商品
    POST /api/BatchAddDockingProduct
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        docking_site_id = data.get('docking_site_id', '') # 对接社区ID
        category_id = data.get('category_id', '') # 添加至分类ID
        pricing_template_id = data.get('pricing_template', '') # 定价模板
        image_save_mode = data.get('image_save_mode', '') # 图片保存方式 source为对接地址，local为下载至本地
        goods_list = data.get('goods_list', []) # 批量对接商品列表(传入上级的商品ID)
        docking_site_type = data.get('docking_site_type', '') # 对接社区类型
        
        # 参数验证
        if not docking_site_id:
            return JsonResponse({'code': -1, 'msg': '对接社区ID不能为空'})
        if not category_id:
            return JsonResponse({'code': -1, 'msg': '添加至分类ID不能为空'})
        if not pricing_template_id:
            return JsonResponse({'code': -1, 'msg': '定价模板不能为空'})
        if not image_save_mode:
            return JsonResponse({'code': -1, 'msg': '图片保存方式不能为空'})
        if not goods_list:
            return JsonResponse({'code': -1, 'msg': '批量对接商品列表不能为空'})

        # 统计结果
        result = {
            'success_count': 0,
            'fail_count': 0,
            'fail_items': []
        }

        with transaction.atomic():
            # 获取定价模板
            price_template = None
            if pricing_template_id:
                try:
                    price_template = PriceTemplate.objects.get(id=pricing_template_id)
                except PriceTemplate.DoesNotExist:
                    return JsonResponse({'code': -1, 'msg': '定价模板不存在'})
            
            # 获取分类信息
            category = None
            if category_id:
                try:
                    category = Category.objects.get(id=category_id)
                except Category.DoesNotExist:
                    return JsonResponse({'code': -1, 'msg': '分类不存在'})
            
            # 获取对接社区信息
            docking_site = None
            if docking_site_id:
                try:
                    docking_site = DockingSite.objects.get(id=docking_site_id)
                except DockingSite.DoesNotExist:
                    return JsonResponse({'code': -1, 'msg': '对接社区不存在'})
            
            # 获取对接社区的ID和密钥
            if docking_site is None:
                return JsonResponse({'code': -1, 'msg': '对接社区信息不存在'})

            appid = docking_site.appid
            key = docking_site.key

            # 遍历处理每个商品
            for goods_id in goods_list:
                try:
                    if docking_site_type.lower() == 'jubijia':
                        # 获取商品详情
                        url = 'http://jubijia.com/api/open/product/detail'
                        data_str = f'id={goods_id}&userId={appid}'
                        # 计算签名
                        sign = get_md5(data_str + key)
                        request_data = data_str + '&sign=' + sign
                        
                        # 发送请求获取商品详情
                        res = requests.post(url+"?"+request_data)
                        
                        # 检查请求是否成功
                        if res.status_code != 200:
                            result['fail_count'] += 1
                            result['fail_items'].append({
                                'id': goods_id,
                                'reason': f'网络请求失败，状态码: {res.status_code}'
                            })
                            continue
                        
                        # 检查API返回是否成功
                        response_data = res.json()
                        if response_data['code'] != 200:
                            result['fail_count'] += 1
                            result['fail_items'].append({
                                'id': goods_id,
                                'reason': f'API返回错误: {response_data.get("msg", "未知错误")}'
                            })
                            continue
                        
                        # 获取商品详情数据
                        goods_data = response_data['data']
                        
                        good_id_local = str(uuid.uuid4())
                        # 处理商品图片
                        image_url = goods_data['image']
                        # 默认设置image_type
                        image_type = 2  # 默认为外部链接
                        
                        if image_save_mode == 'local':
                            image_type = 1  # 本地图片
                            # 下载图片到本地
                            try:
                                image_url = download_image(image_url, f'product_{good_id_local}')
                            except Exception as e:
                                # 如果下载失败，仍使用原始URL
                                pass
                        
                        # 获取商品状态，默认为1（上架）
                        status = goods_data.get('state', '1')
                        if 'status' in goods_data:
                            status = goods_data['status']
                            
                        # 安全获取商品价格
                        price = goods_data.get('price', '0')
                        
                        # 处理attach字段，为每个JSON成员添加tip字段
                        attach_str = goods_data.get('attach', '')
                        processed_attach = attach_str
                        if attach_str:
                            try:
                                # 尝试解析JSON
                                attach_array = json.loads(attach_str)
                                if isinstance(attach_array, list):
                                    # 为每个JSON对象添加tip字段
                                    for item in attach_array:
                                        if isinstance(item, dict) and 'name' in item:
                                            item['tip'] = f"请输入{item['name']}"
                                    # 将处理后的数组转回JSON字符串
                                    processed_attach = json.dumps(attach_array)
                            except json.JSONDecodeError:
                                pass
                            except Exception as e:
                                pass
                        
                        # 创建商品记录
                        Goods.objects.create(
                            id=good_id_local,
                            name=goods_data['name'],
                            image=image_url,
                            image_type=image_type,
                            status=status,
                            price=price,
                            attach=processed_attach,
                            docking_id=goods_id,
                            docking_site=docking_site,
                            category=category,
                            price_template=price_template,
                            stock=goods_data.get('quota', 0),
                            info=goods_data.get('details'),
                            type=3
                        )
                        
                        # 增加成功计数
                        result['success_count'] += 1
                    
                    elif docking_site_type.lower() == 'other_type':
                        # 这里添加其他类型对接网站的处理逻辑
                        pass
                    
                    else:
                        # 不支持的对接网站类型
                        result['fail_count'] += 1
                        result['fail_items'].append({
                            'id': goods_id,
                            'reason': f'不支持的对接网站类型: {docking_site_type}'
                        })
                
                except Exception as e:
                    # 记录详细的错误信息
                    print(f"处理商品 {goods_id} 时发生错误: {str(e)}")
                    print(f"错误类型: {type(e).__name__}")
                    # 添加更详细的错误描述
                    error_message = str(e)
                    if isinstance(e, KeyError):
                        error_message = f"缺少必要字段: {str(e)}"
                    elif isinstance(e, TypeError):
                        error_message = f"数据类型错误: {str(e)}"
                    
                    result['fail_count'] += 1
                    result['fail_items'].append({
                        'id': goods_id,
                        'reason': error_message
                    })
        
        # 所有商品处理完成后，返回结果
        return JsonResponse({
            'code': 200,
            'msg': f'批量对接商品处理完成，成功: {result["success_count"]}，失败: {result["fail_count"]}',
            'data': result
        })
    
    except Exception as e:
        # 捕获整个过程中的异常
        return JsonResponse({'code': -1, 'msg': f'处理失败: {str(e)}'})

def download_image(url, filename_prefix):
    try:
        # 获取图片文件扩展名
        ext = os.path.splitext(url)[1]
        if not ext:
            ext = '.jpg'
        
        # 生成文件名
        filename = f"{filename_prefix}{ext}"
        filepath = os.path.join(settings.MEDIA_ROOT, 'product_images', filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 下载图片
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            
            # 返回相对于MEDIA_URL的路径
            return os.path.join(settings.MEDIA_URL, 'product_images', filename)
        else:
            raise Exception(f"下载图片失败，状态码: {response.status_code}")
    except Exception as e:
        raise Exception(f"处理图片时出错: {str(e)}")

# 支付渠道相关API
@csrf_exempt
@admin_auth_standard(operation='add_payment_method', resource='payment_method')
def add_payment_method(request):
    """
    添加支付渠道API
    POST /api/add_payment_method
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 使用PaymentService创建支付方式
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 准备支付方式数据
        payment_data = {
            'name': data.get('name', ''),
            'interface_type': data.get('type', ''),
            'fee': data.get('serviceCharge', 0),
            'is_active': data.get('is_active', True),
            'config_data': data.get('data', {})
        }
        
        # 创建支付方式
        created_payment = payment_service.create_payment_method(payment_data)
        
        # 获取更新后的支付方式列表
        payment_list_data = payment_service.get_payment_methods(format_type='admin')
        
        # 转换为原有的响应格式
        payment_list = []
        for method in payment_list_data:
            payment_list.append({
                'id': method.get('id'),
                'name': method.get('name'),
                'fee': method.get('fee'),
                'type': method.get('interface_type'),
                'status': 'active' if method.get('is_active') else 'inactive',
                'create_time': method.get('created_at', ''),
                'update_time': method.get('updated_at', '')
            })
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '添加成功',
            'id': created_payment.get('id'),
            'list': payment_list
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_basic(operation='get_payment_method_list', resource='payment_method')
def get_payment_method_list(request):
    """
    获取支付渠道列表API
    GET /api/get_payment_method_list
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 使用PaymentService获取支付方式列表
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 获取支付方式列表
        payment_methods = payment_service.get_payment_methods(format_type='admin')
        
        # 转换为原有的响应格式
        result = []
        for method in payment_methods:
            result.append({
                'id': method.get('id'),
                'name': method.get('name'),
                'fee': method.get('fee'),
                'type': method.get('interface_type'),
                'status': 'active' if method.get('is_active') else 'inactive',
                'data': json.dumps(method.get('config_data', {})) if method.get('config_data') else '{}',
                'create_time': method.get('created_at', ''),
                'update_time': method.get('updated_at', '')
            })
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })
    
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_high(operation='delete_payment_method', resource='payment_method')
def delete_payment_method(request):
    """
    删除支付渠道API
    POST /api/delete_payment_method
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        payment_id = data.get('id', '')
        
        if not payment_id:
            return JsonResponse({'code': -1, 'msg': '支付渠道ID不能为空'})
        
        # 使用PaymentService删除支付方式
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 删除支付方式
        success = payment_service.delete_payment_method(payment_id)
        
        if success:
            # 获取更新后的支付方式列表
            payment_methods = payment_service.get_payment_methods(format_type='admin')
            
            # 转换为原有的响应格式
            payment_list = []
            for method in payment_methods:
                payment_list.append({
                    'id': method.get('id'),
                    'name': method.get('name'),
                    'fee': method.get('fee'),
                    'type': method.get('interface_type'),
                    'status': 'active' if method.get('is_active') else 'inactive',
                    'create_time': method.get('created_at', ''),
                    'update_time': method.get('updated_at', '')
                })
            
            # 构建返回响应
            return JsonResponse({
                'code': 200,
                'msg': '删除成功',
                'list': payment_list
            })
        else:
            return JsonResponse({'code': -1, 'msg': '删除失败'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
@admin_auth_standard(operation='update_payment_method', resource='payment_method')
def update_payment_method(request):
    """
    更新支付渠道API
    POST /api/update_payment_method
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        payment_id = data.get('id', '')
        
        if not payment_id:
            return JsonResponse({'code': -1, 'msg': '支付渠道ID不能为空'})
        
        # 使用PaymentService更新支付方式
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 准备更新数据
        update_data = {}
        
        if 'name' in data and data['name'] is not None:
            update_data['name'] = data['name']
        
        if 'type' in data and data['type'] is not None:
            update_data['interface_type'] = data['type']
        
        if 'serviceCharge' in data and data['serviceCharge'] is not None:
            update_data['fee'] = data['serviceCharge']
        
        if 'is_active' in data and data['is_active'] is not None:
            update_data['is_active'] = data['is_active']
        
        if 'data' in data and data['data'] is not None:
            update_data['config_data'] = data['data']
        
        # 更新支付方式
        updated_payment = payment_service.update_payment_method(payment_id, update_data)
        
        # 获取更新后的支付方式列表
        payment_methods = payment_service.get_payment_methods(format_type='admin')
        
        # 转换为原有的响应格式
        payment_list = []
        for method in payment_methods:
            payment_list.append({
                'id': method.get('id'),
                'name': method.get('name'),
                'fee': method.get('fee'),
                'type': method.get('interface_type'),
                'status': 'active' if method.get('is_active') else 'inactive',
                'create_time': method.get('created_at', ''),
                'update_time': method.get('updated_at', '')
            })
        
        # 构建返回响应
        return JsonResponse({
            'code': 200,
            'msg': '更新成功',
            'list': payment_list
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})

@csrf_exempt
def epay_notify(request):
    """
    易支付异步通知接口
    GET /api/epay_notify
    """
    if request.method != 'GET':
        return HttpResponse('FAIL')
    
    try:
        # 获取请求参数
        params = request.GET.dict()
        
        # 获取签名
        sign = params.get('sign', '')
        sign_type = params.get('sign_type', '')
        
        # 验证必要参数
        required_params = ['pid', 'trade_no', 'out_trade_no', 'type', 'name', 'money', 'trade_status']
        for param in required_params:
            if param not in params:
                return HttpResponse('FAIL')
        
        # 验证支付状态
        if params.get('trade_status') != 'TRADE_SUCCESS':
            return HttpResponse('FAIL')
        
        # 从数据库或配置中获取key
        try:
            order = Order.objects.get(id=params['out_trade_no'])
            payment_info = order.data.get('payment', {})
            payment_id = payment_info.get('id', '') if isinstance(payment_info, dict) else ''
            if payment_id:
                payment_method = PaymentMethod.objects.get(id=payment_id)
                payment_key = payment_method.data_json or ''
            else:
                payment_key = ''

        except (Order.DoesNotExist, ValueError, KeyError):
            payment_key = ''
        
        # 验证签名
        if verify_epay_sign(params, sign, payment_key):
            # 签名验证成功

            # 处理订单状态更新
            try:
                with transaction.atomic():
                    # 获取订单数据
                    order = Order.objects.get(id=params['out_trade_no'])
                    order_data = order.data

                    # 检查订单状态，只有pending状态的订单才能更新为paid
                    if order_data.get('status') != 'pending':
                        logger.warning(f"订单 {params['out_trade_no']} 状态不是pending，当前状态: {order_data.get('status')}")
                        return HttpResponse('FAIL')

                    # 更新订单状态从 pending 到 paid
                    order_data['status'] = 'paid'

                    # 在 payment 字段中添加支付信息
                    if 'payment' not in order_data:
                        order_data['payment'] = {}

                    order_data['payment']['payment_order'] = params['trade_no']  # 支付平台订单号
                    order_data['payment']['pay_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 支付时间
                    order_data['payment']['pay_platform'] = 'epay'  # 支付平台标识

                    # 根据订单类型进行不同处理
                    order_type = order_data.get('type', 2)  # 默认为商品订单
                    
                    if order_type == 2:
                    # 更新商品销量和库存
                        try:
                            product_id = order_data.get('product', {}).get('id', '')
                            if product_id:
                                # 获取商品信息以确定类型
                                try:
                                    goods = Goods.objects.get(id=product_id)
                                
                                    # 根据商品类型决定是否减少库存
                                    if goods.type in ['1', '2']:  # 卡密商品或虚拟商品
                                        # 同时更新销量和库存，确保库存不为负数
                                        updated_count = Goods.objects.filter(
                                            id=product_id,
                                            stock__gt=0  # 确保库存大于0
                                        ).update(
                                            sales_count=F('sales_count') + 1,
                                            stock=F('stock') - 1
                                        )
                                    
                                        if updated_count > 0:
                                            logger.info(f"易支付回调：商品 {product_id} 销量+1，库存-1")
                                        else:
                                            # 库存不足，只更新销量
                                            Goods.objects.filter(id=product_id).update(
                                                sales_count=F('sales_count') + 1
                                            )
                                        
                                    elif goods.type == '3':  # 对接商品
                                        # 只更新销量，不减少库存
                                        updated_count = Goods.objects.filter(id=product_id).update(
                                            sales_count=F('sales_count') + 1
                                        )
                                    
                                    else:
                                        logger.warning(f"易支付回调：商品 {product_id} 类型未知: {goods.type}")
                                    
                                except Goods.DoesNotExist:
                                    logger.error(f"易支付回调：商品 {product_id} 不存在")
                            else:
                                logger.warning(f"易支付回调：订单 {params['out_trade_no']} 缺少商品ID")
                        except Exception as sales_error:
                            # 销量和库存更新失败不影响支付回调成功，仅记录错误日志
                            logger.error(f"易支付回调：更新商品销量和库存失败 - 订单: {params['out_trade_no']}, 错误: {str(sales_error)}")

                    # 更新订单记录
                    order.order = params['trade_no']  # 更新支付平台订单号
                    order.data = order_data
                    order.save()

                    # 使用统一的异步处理函数
                    handle_payment_success_unified(params['out_trade_no'])

            except Order.DoesNotExist:
                logger.error(f"订单不存在: {params['out_trade_no']}")
                return HttpResponse('FAIL')
            except Exception as e:
                logger.error(f"处理epay支付回调失败: {str(e)}", exc_info=True)
                return HttpResponse('FAIL')

            # 返回成功响应
            return HttpResponse('SUCCESS')
        else:
            # 签名验证失败
            return HttpResponse('FAIL')

    except Exception as e:
        return HttpResponse('FAIL')

def verify_epay_sign(params, sign, key)->bool:
    """
    验证易支付异步通知签名
    """

    # 剔除sign与sign_type参数
    sign_params = params.copy()
    if 'sign' in sign_params:
        sign_params.pop('sign')
    if 'sign_type' in sign_params:
        sign_params.pop('sign_type')

    sorted_keys = sorted(sign_params.keys())
    
    string_list = []
    for k in sorted_keys:
        string_list.append(f"{k}={sign_params[k]}")
    
    # 待签名字符串
    string_to_sign = "&".join(string_list)
    string_to_sign += key
    
    calculated_sign = get_md5(string_to_sign)

    return calculated_sign.lower() == sign.lower()


async def process_payment_success_async(order_id):
    """
    异步处理支付成功后的后续操作
    增强版：添加重试机制、详细日志记录和数据库异步支持
    """
    max_retries = 2
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info(f"开始处理订单 {order_id} 的异步后续操作 (尝试 {retry_count + 1}/{max_retries})")

            # 使用sync_to_async包装数据库操作
            order = await sync_to_async(Order.objects.get)(id=order_id)
            
            # 获取订单类型
            order_type = order.type # 1为充值余额订单 2为商品下单订单

            # 充值订单
            if order_type == 1:
                
                try:
                    # 解析订单数据获取用户ID和充值金额
                    order_data = order.data
                    user_info = order_data.get('user', {})
                    price_info = order_data.get('price', {})
                    
                    if not isinstance(user_info, dict) or not isinstance(price_info, dict):
                        return
                    
                    user_id = user_info.get('id', '')
                    recharge_amount = price_info.get('final_price', '0')
                    
                    if not user_id:
                        return
                    
                    try:
                        recharge_amount = float(recharge_amount)
                    except (ValueError, TypeError):
                        return
                    
                    if recharge_amount <= 0:
                        return
                   
                    # 查找用户并更新余额
                    try:
                        user = await sync_to_async(User.objects.get)(user_id=user_id)
                        
                        # 使用F表达式原子性更新用户余额
                        await sync_to_async(
                            User.objects.filter(user_id=user_id).update
                        )(balance=F('balance') + recharge_amount)
                        
                        # 重新获取用户信息以记录日志
                        user = await sync_to_async(User.objects.get)(user_id=user_id)
                        
                    except User.DoesNotExist:
                        return
                    
                except Exception as e:
                    return
                
                return

            # 获取下单的商品ID和商品类型
            order_product_id = order.data.get('product', {}).get('id', '')
            order_product_type = order.data.get('product', {}).get('type', '')

            logger.info(f"订单 {order_id} 商品信息: ID={order_product_id}, 类型={order_product_type}")

            if order_product_type == '1':
                """卡密商品发货处理"""

                try:
                    goods = await sync_to_async(Goods.objects.get)(id=order_product_id)

                    if not goods.card_library:
                        logger.error(f"订单 {order_id} 的卡密商品未关联卡库")

                        order_data = order.data
                        order_data['status'] = 'failed'
                        if 'order' not in order_data:
                            order_data['order'] = {}
                        order_data['order']['msg'] = '卡密商品未关联卡库'

                        order_data = add_order_history(
                            order_data=order_data,
                            status='failed',
                            action='order_failed',
                            description='卡密商品发货失败：商品未关联卡库',
                            details={
                                'error_message': '卡密商品未关联卡库',
                                'fulfillment_type': '卡密商品',
                                'card_provided': False,
                                'goods_id': order_product_id,
                                'library_id': None
                            }
                        )

                        order.data = order_data
                        await sync_to_async(order.save)()
                        continue

                    available_card = await sync_to_async(
                        lambda: CardKey.objects.filter(
                            library=goods.card_library,
                            status=0
                        ).first()
                    )()

                    if not available_card:

                        order_data = order.data
                        order_data['status'] = 'failed'
                        if 'order' not in order_data:
                            order_data['order'] = {}
                        order_data['order']['msg'] = '卡库中没有可用的卡密'

                        order_data = add_order_history(
                            order_data=order_data,
                            status='failed',
                            action='order_failed',
                            description='卡密商品发货失败：没有可用的卡密',
                            details={
                                'error_message': '卡库中没有可用的卡密',
                                'fulfillment_type': '卡密商品',
                                'card_provided': False,
                                'goods_id': order_product_id,
                                'library_id': goods.card_library.id if goods.card_library else None
                            }
                        )

                        order.data = order_data
                        await sync_to_async(order.save)()
                        continue

                    # 获取订单数据
                    order_data = order.data

                    # 标记卡密为已售出
                    available_card.status = 1
                    # 设置外键关联，使用 setattr 避免类型检查问题
                    setattr(available_card, 'order', order)
                    available_card.sold_at = timezone.now()
                    await sync_to_async(available_card.save)()

                    # 添加订单历史记录
                    order_data = add_order_history(
                        order_data=order_data,
                        status='finish',
                        action='order_completed',
                        description='卡密订单已发货',
                        details={
                            'fulfillment_type': '卡密商品',
                            'upstream_message': '卡密订单已发货',
                            'upstream_order_number': available_card.content,
                            'card_provided': True
                        }
                    )

                    # 更新订单状态
                    order_data['status'] = 'finish'
                    order_data['order']['card'] = available_card.content
                    order_data['order']['fulfillment_type'] = '2'  # 卡密商品
                    order_data['order']['msg'] = '卡密订单已发货'
                    order.data = order_data
                    await sync_to_async(order.save)()

                    logger.info(f"订单 {order_id} 卡密商品发货成功，卡密ID: {available_card.id}")

                except Exception as e:
                    logger.error(f"订单 {order_id} 卡密商品发货失败: {str(e)}")
                    # 更新订单为失败状态
                    try:
                        order_data = order.data
                        order_data['status'] = 'failed'
                        if 'order' not in order_data:
                            order_data['order'] = {}
                        order_data['order']['msg'] = f'卡密商品发货失败: {str(e)}'

                        # 添加订单失败历史记录
                        order_data = add_order_history(
                            order_data=order_data,
                            status='failed',
                            action='order_failed',
                            description='卡密商品发货失败：系统异常',
                            details={
                                'error_message': str(e),
                                'fulfillment_type': '卡密商品',
                                'card_provided': False,
                                'goods_id': order_product_id,
                                'exception_type': type(e).__name__
                            }
                        )

                        order.data = order_data
                        await sync_to_async(order.save)()
                    except Exception as save_error:
                        logger.error(f"订单 {order_id} 保存失败状态时发生错误: {str(save_error)}")
                    continue

            elif order_product_type == '2':
                """自营虚拟商品 - 暂时无需特殊处理"""

            elif order_product_type == '3':
                """对接商品处理"""
                try:
                    # 异步获取商品信息
                    goods = await sync_to_async(Goods.objects.get)(id=order_product_id)
                    order_docking_site_product_id = goods.docking_id
                    order_docking_site = await sync_to_async(lambda: goods.docking_site)()

                    if order_docking_site is None:
                        logger.error(f"订单 {order_id} 的商品 {order_product_id} 没有关联的对接社区")
                        continue

                    order_docking_site_type = order_docking_site.type
                    order_docking_site_appid = order_docking_site.appid
                    order_attach = order.data.get('attach', [])

                    order_product_price = order.data.get('price', {}).get('original_price', '0.00')

                    # 防亏损机制：下单前更新对接商品价格并进行对比
                    logger.info(f"订单 {order_id} 开始执行防亏损检查，用户支付金额: {order_product_price}")

                    try:
                        connector = aiohttp.TCPConnector(limit=10)
                        timeout = aiohttp.ClientTimeout(total=10)

                        async with aiohttp.ClientSession(
                            connector=connector,
                            timeout=timeout
                        ) as session:
                            update_result = await update_docking_good_info_async(session, order_product_id)

                            if update_result.get('code') == 200:
                                # 获取最新的对接价格
                                goods = await sync_to_async(Goods.objects.get)(id=order_product_id)
                                current_docking_price = goods.price

                                # 价格对比
                                from decimal import Decimal, InvalidOperation
                                try:
                                    user_paid_amount = Decimal(str(order_product_price))
                                    docking_price_amount = Decimal(str(current_docking_price))

                                    logger.info(f"订单 {order_id} 放亏损机制价格对比 - 用户支付: {user_paid_amount}, 对接价格: {docking_price_amount}")

                                    # 如果用户支付金额小于对接价格，拦截下单
                                    if user_paid_amount < docking_price_amount:
                                        price_diff = docking_price_amount - user_paid_amount

                                        # 更新订单状态为失败
                                        order_data = order.data
                                        order_data['status'] = 'failed'

                                        # 添加防亏损拦截历史记录
                                        order_data = add_order_history(
                                            order_data=order_data,
                                            status='failed',
                                            action='anti_loss_blocked',
                                            description='防亏损机制拦截：用户支付金额低于当前对接价格',
                                            details={
                                                'user_paid_amount': str(user_paid_amount),
                                                'current_docking_price': str(docking_price_amount),
                                                'price_difference': str(price_diff),
                                                'blocked_reason': 'anti_loss_protection',
                                                'docking_site_type': order_docking_site_type,
                                                'product_id': order_product_id
                                            }
                                        )

                                        # 保存订单数据
                                        order.data = order_data
                                        await sync_to_async(order.save)()

                                        logger.error(f"订单 {order_id} 已被防亏损机制拦截，不执行货源API下单")
                                        raise Exception(f"防亏损拦截：支付金额({user_paid_amount})低于对接价格({docking_price_amount})")

                                    else:
                                        logger.info(f"订单 {order_id} 防亏损检查通过，继续执行下单流程")

                                except (InvalidOperation, ValueError) as e:
                                    logger.error(f"订单 {order_id} 价格转换失败: {str(e)}")
                                    # 价格转换失败时拦截下单
                                    order_data = order.data
                                    order_data['status'] = 'failed'
                                    order_data = add_order_history(
                                        order_data=order_data,
                                        status='failed',
                                        action='price_check_error',
                                        description='防亏损检查时价格转换失败，拦截下单',
                                        details={
                                            'error_message': str(e),
                                            'user_paid_amount': order_product_price,
                                            'current_docking_price': str(current_docking_price),
                                            'blocked_reason': 'price_conversion_failed'
                                        }
                                    )
                                    order.data = order_data
                                    await sync_to_async(order.save)()
                                    logger.error(f"订单 {order_id} 因价格转换失败被拦截，不执行货源API下单")
                                    raise Exception(f"防亏损拦截：价格转换失败 - {str(e)}")
                            else:
                                logger.warning(f"订单 {order_id} 对接商品价格更新失败: {update_result.get('msg')}")
                                # 价格更新失败时拦截下单
                                order_data = order.data
                                order_data['status'] = 'failed'
                                order_data = add_order_history(
                                    order_data=order_data,
                                    status='failed',
                                    action='price_update_failed',
                                    description='防亏损检查时价格更新失败，拦截下单',
                                    details={
                                        'update_error': update_result.get('msg', '未知错误'),
                                        'user_paid_amount': order_product_price,
                                        'blocked_reason': 'price_update_failed'
                                    }
                                )
                                order.data = order_data
                                await sync_to_async(order.save)()
                                logger.error(f"订单 {order_id} 因价格更新失败被拦截，不执行货源API下单")
                                raise Exception(f"防亏损拦截：价格更新失败 - {update_result.get('msg', '未知错误')}")

                    except Exception as price_check_error:
                        logger.error(f"订单 {order_id} 防亏损检查异常: {str(price_check_error)}")
                        # 如果是防亏损拦截异常，直接抛出
                        if "防亏损拦截" in str(price_check_error):
                            raise price_check_error
                        # 其他异常也需要拦截下单
                        order_data = order.data
                        order_data['status'] = 'failed'
                        order_data = add_order_history(
                            order_data=order_data,
                            status='failed',
                            action='anti_loss_check_error',
                            description='防亏损检查过程中发生异常，拦截下单',
                            details={
                                'error_message': str(price_check_error),
                                'user_paid_amount': order_product_price,
                                'blocked_reason': 'anti_loss_check_exception'
                            }
                        )
                        order.data = order_data
                        await sync_to_async(order.save)()
                        logger.error(f"订单 {order_id} 因防亏损检查异常被拦截，不执行货源API下单")
                        raise Exception(f"防亏损拦截：检查过程异常 - {str(price_check_error)}")

                    if order_docking_site_type == 'jubijia':

                        result = await jubijia_place_order(order_docking_site_appid, order_docking_site_product_id, order_product_price, order_attach, order_id)

                        if result and result.get('code') == 200:
                            logger.info(f"订单 {order_id} 聚比价下单成功: {result}")
                        else:
                            logger.warning(f"订单 {order_id} 聚比价下单失败: {result}")
                        
                            error_msg = result.get('msg', '未知错误') if result else '返回结果为空'
                            raise Exception(f"聚比价下单失败: {error_msg}")
                    else:
                        logger.warning(f"订单 {order_id} 未知的对接站点类型: {order_docking_site_type}")

                except Exception as e:
                    logger.error(f"订单 {order_id} 对接商品下单失败: {str(e)}", exc_info=True)
                    raise
            else:
                logger.warning(f"订单 {order_id} 未知商品类型: {order_product_type}")

            break

        except Order.DoesNotExist:
            break
        except Exception as e:

            if "防亏损拦截" in str(e):
                logger.error(f"订单 {order_id} 防亏损机制拦截，停止处理: {str(e)}")
                break

            # 其他异常进行正常的重试机制
            retry_count += 1
            logger.error(f"订单 {order_id} 异步处理失败 (尝试 {retry_count}/{max_retries}): {str(e)}", exc_info=True)

            if retry_count >= max_retries:
                logger.error(f"订单 {order_id} 异步处理达到最大重试次数，放弃处理")
                break
            else:
                # 等待一段时间后重试
                await asyncio.sleep(2 ** retry_count)

async def jubijia_place_order(user_id, product_id, price, attach_array: list[dict], out_trade_no:str):

    max_retries = 2
    retry_count = 0

    while retry_count < max_retries:
        try:
            logger.info(f"开始聚比价下单 (尝试 {retry_count + 1}/{max_retries}): 用户ID={user_id}, 商品ID={product_id}")

            # 使用sync_to_async包装数据库操作
            try:
                docking_site = await sync_to_async(DockingSite.objects.get)(appid=user_id)
                key = docking_site.key

            except Exception as e:
                print('聚比价下单时获取用户密钥失败')

            # 重构attach参数
            attach = []
            """
            attach_array传参如下:
            attach:[
                {
                    "name": "充值账号",
                    "value": "***********"
                }
            ]
            取value值按照顺序组成数组
            """
            for item in attach_array:
                attach.append(item.get('value', ''))

            body = {
                "price": price,
                "productId": product_id,
                "num": 1,
                "attach": attach,
                'externalOrderNumber': out_trade_no
            }

            sign = get_md5(f"userId={user_id}{key}")
            url = f"http://jubijia.com/api/open/purchase?userId={user_id}&sign={sign}"
            headers = {
                "Content-Type": "application/json"
            }

            logger.info(f"聚比价下单请求: URL={url}, Body={body}")

            # 设置超时时间
            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, headers=headers, json=body) as response:
                    response_text = await response.text()
                    logger.info(f"聚比价下单响应: 状态码={response.status}, 响应={response_text}")
                    
                    order = await sync_to_async(Order.objects.get)(id=out_trade_no)
                    order_data = order.data
                                
                    if 'order' not in order_data:
                        order_data['order'] = {}
                    order_data['order']['raw_response'] = response_text

                    if response.status == 200:
                        try:
                            response_data = await response.json()

                            if response_data.get('code') == 200:
                                result = {'code': 200, 'msg': '下单成功', 'data': response_text}
                                logger.info(f"聚比价下单成功: {result}")

                                # 解析数据
                                if response_data.get('data',{}).get('kmData') is None:
                                    fulfillment_type = '1' # 直冲商品
                                    order_data = add_order_history(
                                        order_data=order_data,
                                        status='processing',
                                        action='order_processing',
                                        description='已提交货源，订单处理中',
                                        details={
                                        'fulfillment_type': '直冲商品',
                                        'upstream_message': response_data.get('msg',''),
                                        'card_provided': False
                                    }
                                    )
                                    order_data['status'] = 'processing'
                                else:
                                    fulfillment_type = '2' # 卡密商品
                                    # 卡密商品提取卡密
                                    jubijia_card = response_data.get('data',{}).get('kmData','')
                                    order_data = add_order_history(
                                        order_data=order_data,
                                        status='finish',
                                        action='order_completed',
                                        description='卡密订单已发货',
                                        details={
                                            'fulfillment_type': '卡密商品',
                                            'upstream_message': response_data.get('msg',''),
                                            'upstream_order_number': response_data.get('data',{}).get('orderNumber',''),
                                            'card_provided': True
                                        }
                                    )
                                    
                                    order_data['status'] = 'finish'
                                    order_data['order']['card'] = jubijia_card
                                
                                # 更新订单数据
                                order_data['order']['fulfillment_type'] = fulfillment_type
                                order_data['order']['msg'] = response_data.get('msg','')
                                order_data['order']['upstream_order_number'] = response_data.get('data',{}).get('orderNumber','')

                                order.data = order_data
                                await sync_to_async(order.save)()
                                return result

                            else:
                                result = {'code': -1, 'msg': '调用接口失败', 'data': response_data.get('msg', '未知错误')}
                                logger.info(f"聚比价下单失败: {result}")
                                # 更新订单为失败状态
                                order_data['status'] = 'failed'
                                if 'order' not in order_data:
                                    order_data['order'] = {}
                                order_data['order']['msg'] = response_data.get('msg', '未知错误')
                                order_data['order']['upstream_order_number'] = response_data.get('data',{}).get('orderNumber','')

                                # 添加订单失败历史记录
                                order_data = add_order_history(
                                    order_data=order_data,
                                    status='failed',
                                    action='order_failed',
                                    description=f'货源API第{str(retry_count)}次调用失败',
                                    details={
                                        'error_code': response_data.get('code', '未知'),
                                        'error_message': response_data.get('msg', '未知错误'),
                                        'api_response': response_data,
                                        'retry_attempt': retry_count + 1,
                                        'max_retries': max_retries
                                    }
                                )

                                order.data = order_data
                                await sync_to_async(order.save)()
                                return result

                        except Exception as json_error:
                            # 处理JSON解析异常
                            logger.error(f"聚比价响应JSON解析失败: {str(json_error)}, 原始响应: {response_text}")
                            # 更新订单为失败状态
                            order_data['status'] = 'failed'
                            order_data['order']['msg'] = f"响应数据解析失败: {str(json_error)}"
                            order_data['order']['upstream_order_number'] = response_data.get('data',{}).get('orderNumber','')

                            # 添加订单失败历史记录
                            order_data = add_order_history(
                                order_data=order_data,
                                status='failed',
                                action='order_failed_json_error',
                                description='货源API响应JSON解析失败',
                                details={
                                    'error_message': str(json_error),
                                    'raw_response': response_text,
                                    'http_status': response.status,
                                    'retry_attempt': retry_count + 1,
                                    'max_retries': max_retries
                                }
                            )

                            order.data = order_data
                            await sync_to_async(order.save)()
                            raise Exception(f"JSON解析失败: {str(json_error)}")

                    else:
                        # 更新订单为失败状态
                        order_data['status'] = 'failed'
                        order_data['order']['msg'] = f"HTTP请求失败，响应状态码: {response.status}"

                        # 添加订单失败历史记录
                        order_data = add_order_history(
                            order_data=order_data,
                            status='failed',
                            action='order_failed_http_error',
                            description='货源API HTTP请求失败',
                            details={
                                'http_status': response.status,
                                'error_message': f"HTTP请求失败，响应状态码: {response.status}",
                                'retry_attempt': retry_count + 1,
                                'max_retries': max_retries
                            }
                        )

                        order.data = order_data
                        await sync_to_async(order.save)()
                        raise Exception(f"HTTP请求失败，响应状态码: {response.status}")

        except Exception as e:
            retry_count += 1
            logger.error(f"聚比价下单失败 (尝试 {retry_count}/{max_retries}): {str(e)}", exc_info=True)

            if retry_count >= max_retries:
                logger.error(f"聚比价下单达到最大重试次数，放弃处理")

                # 最终失败时更新订单状态并添加历史记录
                try:
                    order = await sync_to_async(Order.objects.get)(id=out_trade_no)
                    order_data = order.data

                    if 'order' not in order_data:
                        order_data['order'] = {}

                    # 更新订单为失败状态
                    order_data['status'] = 'failed'
                    order_data['order']['msg'] = f'货源下单失败，已达到最大重试次数: {str(e)}'

                    # 添加订单失败历史记录
                    order_data = add_order_history(
                        order_data=order_data,
                        status='failed',
                        action='order_failed_max_retries',
                        description='货源请求下单达到最大重试次数，最终失败',
                        details={
                            'final_error': str(e),
                            'total_retries': max_retries,
                            'failure_reason': '达到最大重试次数'
                        }
                    )

                    order.data = order_data
                    await sync_to_async(order.save)()
                except Exception as update_error:
                    logger.error(f"更新订单失败状态时出错: {str(update_error)}")

                return {'code': -1, 'msg': f'货源下单失败: {str(e)}'}
            else:
                # 等待一段时间后重试
                await asyncio.sleep(2 ** retry_count)

@csrf_exempt
def get_local_image_product(request):
    """
    获取本地商品图片API
    GET /api/get_local_image_product?id={{商品ID}}
    根据商品ID返回对应的本地存储图片
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 获取商品ID参数和占位符参数
        product_id = request.GET.get('id', '')
        is_placeholder = request.GET.get('placeholder', '').lower() == 'true'

        if not product_id:
            return JsonResponse({'code': -1, 'msg': '商品ID不能为空'})

        # 如果是占位符请求，直接返回占位符图片
        if is_placeholder:
            placeholder_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'product-item-placeholder.svg')
            if os.path.exists(placeholder_path):
                with open(placeholder_path, 'rb') as f:
                    image_data = f.read()
                response = HttpResponse(image_data, content_type='image/svg+xml')
                response['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时
                return response
            else:
                return JsonResponse({'code': -1, 'msg': '占位符图片不存在'})

        # 查询商品信息
        try:
            goods = Goods.objects.get(id=product_id)
        except Goods.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '商品不存在'})

        # 检查是否为本地图片
        if goods.image_type != '1':
            return JsonResponse({'code': -1, 'msg': '该商品不是本地图片存储'})

        # 检查图片路径是否存在
        if not goods.image:
            # 图片路径为空，返回占位符图片
            placeholder_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'product-item-placeholder.svg')
            if os.path.exists(placeholder_path):
                with open(placeholder_path, 'rb') as f:
                    image_data = f.read()
                response = HttpResponse(image_data, content_type='image/svg+xml')
                response['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时
                return response
            else:
                return JsonResponse({'code': -1, 'msg': '商品图片路径为空且无占位符图片'})

        # 构建完整的文件路径
        # goods.image 存储格式如: /media/product_images/product_xxx.png
        # 需要转换为实际的文件系统路径
        image_path = goods.image
        if image_path.startswith('/media/'):
            # 移除 /media/ 前缀，因为 MEDIA_ROOT 已经包含了基础路径
            relative_path = image_path[7:]  # 移除 '/media/'
            file_path = os.path.join(settings.MEDIA_ROOT, relative_path)
        else:
            # 如果不是标准格式，直接拼接
            file_path = os.path.join(settings.MEDIA_ROOT, image_path)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            # 文件不存在，返回默认占位图
            placeholder_path = os.path.join(settings.BASE_DIR, 'static', 'images', 'product-item-placeholder.svg')
            if os.path.exists(placeholder_path):
                with open(placeholder_path, 'rb') as f:
                    image_data = f.read()
                response = HttpResponse(image_data, content_type='image/svg+xml')
                response['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时
                return response
            else:
                return JsonResponse({'code': -1, 'msg': '图片文件不存在且无默认占位图'})

        # 读取图片文件
        with open(file_path, 'rb') as f:
            image_data = f.read()

        # 根据文件扩展名设置正确的Content-Type
        file_ext = os.path.splitext(file_path)[1].lower()
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml'
        }
        content_type = content_type_map.get(file_ext, 'image/jpeg')

        # 创建HTTP响应
        response = HttpResponse(image_data, content_type=content_type)
        response['Cache-Control'] = 'public, max-age=86400'  # 缓存24小时
        response['Content-Length'] = len(image_data)

        return response

    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'获取图片失败: {str(e)}'})

import random
import string
from datetime import timedelta
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Case, When, IntegerField


def generate_coupon_code(prefix='', length=28):
    """生成卡券代码"""
    if not prefix:
        prefix = ''  # 不使用前缀，直接生成28位随机字符

    # 生成随机字符串（大小写字母和数字）
    chars = string.ascii_uppercase + string.ascii_lowercase + string.digits
    random_part = ''.join(random.choice(chars) for _ in range(length - len(prefix)))

    return prefix + random_part


@csrf_exempt
@admin_auth_basic(operation='coupon_list', resource='coupon')
def coupon_list(request):
    """
    卡券列表查询API
    POST /api/coupons/list/
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)

        # 提取请求参数
        page = data.get('page', 1)
        page_size = data.get('pageSize', 20)
        search = data.get('search', '').strip()
        discount_type = data.get('discountType', '').strip()
        status = data.get('status', '').strip()

        # 参数验证
        if page_size > 100:
            page_size = 100

        # 构建查询条件
        queryset = Coupon.objects.all()

        # 搜索条件
        if search:
            queryset = queryset.filter(
                Q(code__icontains=search) |
                Q(description__icontains=search)
            )

        # 优惠类型筛选
        if discount_type:
            queryset = queryset.filter(discount_type=discount_type)

        # 状态筛选
        now = timezone.now()
        if status == 'active':
            queryset = queryset.filter(is_used=False, expiry_date__gt=now)
        elif status == 'used':
            queryset = queryset.filter(is_used=True)
        elif status == 'expired':
            queryset = queryset.filter(is_used=False, expiry_date__lte=now)

        # 排序
        queryset = queryset.order_by('-create_time')

        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 构建响应数据
        coupon_list = []
        for coupon in page_obj:
            coupon_list.append({
                'code': coupon.code,
                'discountType': coupon.discount_type,
                'discountValue': str(coupon.discount_value),
                'minOrderAmount': str(coupon.min_order_amount),
                'description': coupon.description or '',
                'createTime': coupon.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'expiryDate': coupon.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
                'isUsed': coupon.is_used,
                'usedTime': coupon.used_time.strftime('%Y-%m-%d %H:%M:%S') if coupon.used_time else None,
                'status': coupon.status
            })

        # 统计信息
        total_count = Coupon.objects.count()
        active_count = Coupon.objects.filter(is_used=False, expiry_date__gt=now).count()
        used_count = Coupon.objects.filter(is_used=True).count()
        expired_count = Coupon.objects.filter(is_used=False, expiry_date__lte=now).count()

        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': {
                'total': paginator.count,
                'page': page,
                'pageSize': page_size,
                'list': coupon_list,
                'stats': {
                    'total': total_count,
                    'active': active_count,
                    'used': used_count,
                    'expired': expired_count
                }
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
def coupon_generate(request):
    """
    批量生成卡券API
    POST /api/coupons/generate/
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)

        # 提取请求参数
        count = data.get('count', 1)
        discount_type = data.get('discountType', '')
        discount_value = data.get('discountValue', 0)
        valid_days = data.get('validDays', 30)
        min_order_amount = data.get('minOrderAmount', 0)
        description = data.get('description', '').strip()

        # 参数验证
        if not isinstance(count, int) or count <= 0 or count > 1000:
            return JsonResponse({'code': -1, 'msg': '生成数量必须在1-1000之间'})

        if discount_type not in ['fixed', 'percentage']:
            return JsonResponse({'code': -1, 'msg': '优惠类型必须为fixed或percentage'})

        if not isinstance(discount_value, (int, float)) or discount_value <= 0:
            return JsonResponse({'code': -1, 'msg': '优惠值必须大于0'})

        if discount_type == 'percentage' and discount_value > 100:
            return JsonResponse({'code': -1, 'msg': '百分比折扣不能超过100%'})

        if not isinstance(valid_days, int) or valid_days <= 0 or valid_days > 365:
            return JsonResponse({'code': -1, 'msg': '有效天数必须在1-365之间'})

        if not isinstance(min_order_amount, (int, float)) or min_order_amount < 0:
            return JsonResponse({'code': -1, 'msg': '最低消费金额不能小于0'})

        # 计算过期时间
        expiry_date = timezone.now() + timedelta(days=valid_days)

        # 批量生成卡券
        generated_coupons = []
        with transaction.atomic():
            for i in range(count):
                # 生成唯一的卡券代码
                while True:
                    code = generate_coupon_code()
                    if not Coupon.objects.filter(code=code).exists():
                        break

                # 创建卡券
                coupon = Coupon.objects.create(
                    code=code,
                    discount_type=discount_type,
                    discount_value=discount_value,
                    min_order_amount=min_order_amount,
                    description=description,
                    expiry_date=expiry_date
                )

                generated_coupons.append({
                    'code': coupon.code,
                    'expiryDate': coupon.expiry_date.strftime('%Y-%m-%d %H:%M:%S')
                })

        return JsonResponse({
            'code': 200,
            'msg': f'成功生成{count}张卡券',
            'data': {
                'generatedCount': count,
                'coupons': generated_coupons
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_basic(operation='coupon_detail', resource='coupon')
def coupon_detail(request):
    """
    卡券详情查询API
    POST /api/coupons/detail/?coupon={{实际的卡券码}}
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 获取卡券代码
        coupon_code = request.GET.get('coupon', '').strip()

        if not coupon_code:
            return JsonResponse({'code': -1, 'msg': '卡券代码不能为空'})

        # 查询卡券
        try:
            coupon = Coupon.objects.get(code=coupon_code)
        except Coupon.DoesNotExist:
            return JsonResponse({'code': -1, 'msg': '卡券不存在'})

        # 构建响应数据
        response_data = {
            'code': coupon.code,
            'discountType': coupon.discount_type,
            'discountValue': str(coupon.discount_value),
            'minOrderAmount': str(coupon.min_order_amount),
            'description': coupon.description or '',
            'createTime': coupon.create_time.strftime('%Y-%m-%d %H:%M:%S'),
            'expiryDate': coupon.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
            'isUsed': coupon.is_used,
            'usedTime': coupon.used_time.strftime('%Y-%m-%d %H:%M:%S') if coupon.used_time else None,
            'usedBy': coupon.used_by,
            'status': coupon.status
        }

        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': response_data
        })

    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_high(operation='coupon_delete', resource='coupon')
def coupon_delete(request):
    """
    批量删除卡券API
    POST /api/coupons/delete/
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)

        # 提取请求参数
        coupon_codes = data.get('couponCodes', [])

        # 参数验证
        if not isinstance(coupon_codes, list) or len(coupon_codes) == 0:
            return JsonResponse({'code': -1, 'msg': '卡券代码列表不能为空'})

        if len(coupon_codes) > 1000:
            return JsonResponse({'code': -1, 'msg': '单次删除数量不能超过1000张'})

        # 批量删除卡券
        with transaction.atomic():
            # 查找存在的卡券
            existing_coupons = Coupon.objects.filter(code__in=coupon_codes)
            existing_codes = set(existing_coupons.values_list('code', flat=True))

            # 删除卡券
            deleted_count = existing_coupons.delete()[0]

            # 找出不存在的卡券代码
            failed_codes = [code for code in coupon_codes if code not in existing_codes]

        return JsonResponse({
            'code': 200,
            'msg': f'成功删除{deleted_count}张卡券',
            'data': {
                'deletedCount': deleted_count,
                'failedIds': failed_codes
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


def add_order_history(order_data, status, action, description, details=None, request=None):

    from django.utils import timezone

    # 确保history字段存在
    if 'history' not in order_data:
        order_data['history'] = []

    # 构建历史记录
    history_entry = {
        'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': status,
        'action': action,
        'description': description
    }

    # 添加详细信息
    if details:
        history_entry['details'] = details
    else:
        history_entry['details'] = {}

    # 从request中提取信息
    if request:
        history_entry['details']['user_agent'] = request.META.get('HTTP_USER_AGENT', '')[:200]  # 限制长度
        history_entry['details']['ip_address'] = get_client_ip(request)

        # 判断设备类型
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        mobile_keywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
        device_type = 'mobile' if any(keyword in user_agent for keyword in mobile_keywords) else 'desktop'
        history_entry['details']['device_type'] = device_type

    # 添加到历史记录数组
    order_data['history'].append(history_entry)

    return order_data


def verify_jubijia_callback_signature(params, secret_key):
    """
    验证聚比价回调请求的签名
    """
    try:
        # 获取传递过来的签名
        received_sign = params.get('sign', '')
        if not received_sign:
            return False

        # 排除sign参数，获取其他所有参数
        filtered_params = {k: v for k, v in params.items() if k != 'sign'}

        # 按参数名排序
        sorted_keys = sorted(filtered_params.keys())

        # 构建签名字符串：key1=value1&key2=value2
        param_string = '&'.join([f"{k}={filtered_params[k]}" for k in sorted_keys])

        # 在尾部加上对接密钥
        param_string += secret_key

        # 生成MD5签名
        calculated_sign = get_md5(param_string)

        # 比较签名
        return calculated_sign.lower() == received_sign.lower()

    except Exception as e:
        return False


def process_jubijia_order_status_update(order_number, status, order_time, callback_params):
    """
    处理聚比价订单状态更新
    """
    try:
        from django.utils import timezone
        from datetime import datetime, timedelta

        # 状态映射关系
        STATUS_MAPPING = {
            "-1": "failed",      # 下单失败
            "0": "processing",   # 正在处理
            "1": "completed",    # 充值成功
            "2": "refunded",     # 退款成功
            "3": "failed"        # 未知状态
        }

        # 获取映射后的本地状态
        local_status = STATUS_MAPPING.get(status)
        if not local_status:
            return False

        # 时间处理：回调时间(东八区) -> UTC时间 -> 减70分钟 -> 最早创建时间
        try:
            # 解析回调时间 (假设格式为 'YYYY-MM-DD HH:MM:SS')
            callback_time = datetime.strptime(order_time, '%Y-%m-%d %H:%M:%S')
            # 转换为UTC时间 (东八区减8小时)
            callback_time_utc = callback_time - timedelta(hours=8)
            # 设置时区信息
            callback_time_utc = callback_time_utc.replace(tzinfo=timezone.utc)
            # 计算最早创建时间 (往前推70分钟)
            earliest_create_time = callback_time_utc - timedelta(minutes=70)

        except ValueError as e:
            return False

        # 查询符合时间条件的订单
        orders = Order.objects.filter(created_at__gte=earliest_create_time).order_by('-created_at')

        # 遍历订单，查找匹配的upstream_order_number
        for order in orders:
            try:
                order_data = order.data
                if not order_data:
                    continue

                # 检查是否有order字段和upstream_order_number
                order_info = order_data.get('order', {})
                upstream_order_number = order_info.get('upstream_order_number', '')

                if upstream_order_number == order_number:
                    # 更新订单状态
                    with transaction.atomic():
                        # 更新状态
                        order_data['status'] = local_status

                        # 创建订单历史记录
                        status_descriptions = {
                            'failed': '订单失败',
                            'processing': '订单处理中',
                            'completed': '订单完成',
                            'refunded': '订单退款'
                        }

                        order_data = add_order_history(
                            order_data=order_data,
                            status=local_status,
                            action='order_status_callback',
                            description=f'订单状态回调更新: {status_descriptions.get(local_status, "状态更新")}',
                            details={
                                'original_status': status,
                                'order_number': order_number,
                                'order_time': order_time,
                                'callback_params': callback_params
                            }
                        )

                        # 保存订单
                        order.data = order_data
                        order.save()

                        return True

            except Exception as e:
                continue

        return False

    except Exception as e:
        return False


@csrf_exempt
def jubijia_order_callback(request):
    """
    聚比价订单状态异步回调处理函数
    POST /api/jubijia_order_callback
    """
    if request.method != 'POST':
        return HttpResponse('error')

    try:
        # 获取回调参数
        params = {}
        for key, value in request.POST.items():
            params[key] = value

        # 提取关键参数
        merchant_id = params.get('merchantId', '')
        order_number = params.get('orderNumber', '')
        status = params.get('status', '')
        order_time = params.get('orderTime', '')

        # 参数验证
        if not all([merchant_id, order_number, status, order_time]):
            return HttpResponse('error')

        # 通过商户ID查询对接站点信息
        try:
            docking_site = DockingSite.objects.get(appid=merchant_id, type='jubijia')
            secret_key = docking_site.key
        except DockingSite.DoesNotExist:
            return HttpResponse('error')

        # 验证签名
        if not verify_jubijia_callback_signature(params, secret_key):
            return HttpResponse('error')

        # 实现订单时间校验和状态更新逻辑
        success = process_jubijia_order_status_update(order_number, status, order_time, params)

        if success:
            return HttpResponse('ok')
        else:
            return HttpResponse('error')

    except Exception as e:
        return HttpResponse('error')


def get_client_ip(request):

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    return ip


async def update_data_statistics_async(order_id):

    try:
        
        # 获取订单信息
        order = await sync_to_async(Order.objects.get)(id=order_id)
        order_data = order.data
        order_type = order.type
        
        # 获取或创建今日统计记录
        today_stats, created = await sync_to_async(dataStatistics.get_or_create_today_stats)()
        
        # 使用事务确保数据一致性
        async def update_stats():
            with transaction.atomic():
                # 重新获取最新的统计记录以避免并发问题
                stats = await sync_to_async(dataStatistics.objects.get)(date=today_stats.date)
                
                if order_type == '1':
                    
                    # 解析订单数据获取充值金额
                    recharge_amount = 0
                    try:
                        price_info = order_data.get('price', {})
                        if isinstance(price_info, dict):
                            final_price = price_info.get('final_price', '0')
                            recharge_amount = float(final_price)
                    except (ValueError, TypeError) as e:
                        recharge_amount = 0
                    
                    # 更新充值余额量
                    stats.daily_recharge_amount = F('daily_recharge_amount') + recharge_amount
                    await sync_to_async(stats.save)(update_fields=['daily_recharge_amount', 'updated_at'])
                    
                elif order_type == '2':

                    try:
                        # 解析订单数据
                        product_info = order_data.get('product', {})
                        price_info = order_data.get('price', {})
                        
                        if not isinstance(product_info, dict) or not isinstance(price_info, dict):
                            return
                        
                        # 获取商品成本价和最终支付价
                        product_price = float(product_info.get('price', '0'))  # 商品成本价
                        final_price = float(price_info.get('final_price', '0'))  # 最终支付价
                        
                        # 计算利润
                        profit = final_price - product_price
                        
                        # 更新统计数据
                        stats.daily_orders = F('daily_orders') + 1
                        stats.daily_sales_amount = F('daily_sales_amount') + final_price
                        stats.daily_cost = F('daily_cost') + product_price
                        stats.daily_profit = F('daily_profit') + profit
                        stats.total_orders = F('total_orders') + 1
                        stats.total_sales_amount = F('total_sales_amount') + final_price
                        stats.total_profit = F('total_profit') + profit
                        
                        await sync_to_async(stats.save)(update_fields=[
                            'daily_orders', 'daily_sales_amount', 'daily_cost', 'daily_profit',
                            'total_orders', 'total_sales_amount', 'total_profit', 'updated_at'
                        ])
                                
                    except (ValueError, TypeError, KeyError) as e:
                        return
                else:
                    return
        
        # 执行统计更新
        await sync_to_async(update_stats)()
       
    except Order.DoesNotExist:
        return
    except Exception as e:
        return


def handle_payment_success_unified(order_id):

    try:
        future1 = run_async_task_in_thread(process_payment_success_async, order_id)
        future2 = run_async_task_in_thread(process_order_operations, order_id) # 商品后置操作
        future3 = run_async_task_in_thread(update_data_statistics_async, order_id) # 数据统计更新
        return future1, future2, future3
    except Exception as e:
        return None


def cleanup_async_task_executor():
    """
    清理异步任务执行器
    """
    try:
        if async_task_executor:
            logger.info("正在关闭异步任务执行器...")
            async_task_executor.shutdown(wait=True)
            logger.info("异步任务执行器已关闭")
    except Exception as e:
        logger.error(f"关闭异步任务执行器时出错: {str(e)}")


import atexit
atexit.register(cleanup_async_task_executor)


class AlipayView:

    def __init__(self, payment_method_id, order_id, product_name, amount, device_type='desktop'):

        self.payment_method_id = payment_method_id
        self.order_id = order_id
        self.product_name = product_name
        self.amount = amount
        self.device_type = device_type
        self.alipay = None

        # 初始化支付宝配置
        self._init_alipay()

    def _init_alipay(self):

        try:
            # 根据支付渠道ID获取配置信息
            payment_method = PaymentMethod.objects.get(id=self.payment_method_id)

            # 二次验证接口类型
            if payment_method.interface_type != 'alipay':
                raise ValueError(f"支付渠道类型错误: {payment_method.interface_type}")

            # 解析配置数据
            if not payment_method.data_json:
                raise ValueError("支付方式配置数据为空")
            config_data = json.loads(payment_method.data_json)

            # 获取配置参数
            appid = config_data.get('appid', '')
            app_private_key = config_data.get('app_private_key', '')
            alipay_public_key = config_data.get('alipay_public_key', '')
            sandbox = config_data.get('sandbox', False)  # 获取沙箱模式配置

            if not all([appid, app_private_key, alipay_public_key]):
                raise ValueError("支付宝配置信息不完整")

            # 获取异步回调地址
            from user.views import get_site_base_url
            notify_url = f"{get_site_base_url()}/api/alipay_notify"

            # 根据沙箱模式配置决定debug参数
            debug_mode = bool(sandbox)

            # 初始化AliPay对象
            self.alipay = AliPay(
                appid=appid,
                app_notify_url=notify_url,
                app_private_key_string=app_private_key,
                alipay_public_key_string=alipay_public_key,
                sign_type="RSA2",
                debug=debug_mode
            )

            # 存储环境信息用于后续使用
            self.is_sandbox = debug_mode

        except PaymentMethod.DoesNotExist:
            raise ValueError(f"支付渠道不存在: {self.payment_method_id}")
        except json.JSONDecodeError:
            raise ValueError("支付渠道配置数据格式错误")
        except Exception as e:
            raise ValueError(f"初始化支付宝配置失败: {str(e)}")

    def _generate_quit_url(self):

        try:
            from user.views import get_site_base_url
            import urllib.parse
            import json

            # 读取订单数据
            order = Order.objects.get(id=self.order_id)
            order_data = order.data

            # 提取所需参数
            product_id = order_data.get('product', {}).get('id', '')
            notice_mail = order_data.get('user', {}).get('notice_mail', '')
            payment_id = order_data.get('payment', {}).get('id', '')
            payment_way = 'alipay'
            coupon = order_data.get('price', {}).get('coupon', '')
            attach = order_data.get('attach', [])

            params = {
                'id': product_id,
                'notice_mail': notice_mail,
                'payment_id': payment_id,
                'payment_way': payment_way,
                'coupon': coupon,
                'attach': json.dumps(attach, ensure_ascii=False)
            }

            # 移除空值参数
            params = {k: v for k, v in params.items() if v}

            # 构造完整的quit_url
            base_url = get_site_base_url()
            query_string = urllib.parse.urlencode(params, quote_via=urllib.parse.quote)
            quit_url = f"{base_url}/confirmOrder/?{query_string}"

            return quit_url

        except Exception as e:
            # 生成失败 -> 返回无信息的订单确认页面
            from user.views import get_site_base_url
            return f"{get_site_base_url()}/confirmOrder/?id={self.order_id}"

    def generate_payment_url(self):
        """
        生成支付宝支付链接
        """

        try:
            if not self.alipay:
                raise ValueError("支付宝配置未初始化")

            # 获取同步回调地址
            from user.views import get_site_base_url
            return_url = f"{get_site_base_url()}/payment/success/"

            # 构造quit_url（用于手机网站支付取消时的返回地址）
            quit_url = ""
            if self.device_type == 'mobile':
                quit_url = self._generate_quit_url()

            # 获取异步回调地址
            notify_url = f"{get_site_base_url()}/api/alipay_notify"

            # 格式化金额，确保精度正确（保留2位小数）
            formatted_amount = "{:.2f}".format(float(self.amount))

            # 根据设备类型选择不同的支付方式
            if self.device_type == 'mobile':
                # 手机网站支付
                order_string = self.alipay.api_alipay_trade_wap_pay(
                    out_trade_no=self.order_id,
                    total_amount=formatted_amount,
                    subject=self.product_name,
                    return_url=return_url,
                    notify_url=notify_url,
                    quit_url=quit_url
                )
            else:
                # 电脑网站支付
                order_string = self.alipay.api_alipay_trade_page_pay(
                    out_trade_no=self.order_id,
                    total_amount=formatted_amount,
                    subject=self.product_name,
                    return_url=return_url,
                    notify_url=notify_url
                )

            # 根据环境选择网关地址
            if self.is_sandbox:
                gateway_url = "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
            else:
                gateway_url = "https://openapi.alipay.com/gateway.do"

            # 生成支付链接
            pay_url = gateway_url + "?" + order_string

            return {
                'code': 200,
                'msg': '生成支付订单成功',
                'type': 'url',
                'data': {
                    'url': pay_url
                }
            }

        except Exception as e:
            return {
                'code': 401,
                'msg': f'生成支付链接失败: {str(e)}'
            }


@csrf_exempt
def alipay_notify(request):
    """
    支付宝异步回调处理函数
    POST /api/alipay_notify
    """
    if request.method != 'POST':
        return HttpResponse('fail')

    try:
        # 获取回调参数
        data = request.POST.dict()
        signature = data.pop('sign', None)

        if not signature:
            return HttpResponse('fail')

        # 获取商户订单号
        out_trade_no = data.get('out_trade_no', '')
        if not out_trade_no:
            return HttpResponse('fail')

        # 通过订单号获取订单数据
        try:
            order = Order.objects.get(id=out_trade_no)
            order_data = order.data
        except Order.DoesNotExist:
            logger.error(f"支付宝回调：订单不存在 {out_trade_no}")
            return HttpResponse('fail')

        # 获取支付渠道ID
        payment_method_id = order_data.get('payment', {}).get('id', '')
        if not payment_method_id:
            return HttpResponse('fail')

        # 获取支付渠道配置
        try:
            payment_method = PaymentMethod.objects.get(id=payment_method_id)
            if payment_method.interface_type != 'alipay':
                return HttpResponse('fail')

            # 解析配置数据
            if not payment_method.data_json:
                return HttpResponse('fail')
            config_data = json.loads(payment_method.data_json)
            appid = config_data.get('appid', '')
            app_private_key = config_data.get('app_private_key', '')
            alipay_public_key = config_data.get('alipay_public_key', '')
            sandbox = config_data.get('sandbox', False)

            if not all([appid, app_private_key, alipay_public_key]):
                return HttpResponse('fail')

        except PaymentMethod.DoesNotExist:
            return HttpResponse('fail')
        except json.JSONDecodeError:
            return HttpResponse('fail')

        debug_mode = bool(sandbox)
        alipay = AliPay(
            appid=appid,
            app_notify_url=None,
            app_private_key_string=app_private_key,
            alipay_public_key_string=alipay_public_key,
            sign_type="RSA2",
            debug=debug_mode
        )

        # 验证签名
        success = alipay.verify(data, signature)

        if success and data.get("trade_status") in ("TRADE_SUCCESS", "TRADE_FINISHED"):

            try:
                with transaction.atomic():
                    # 检查订单状态，只有pending状态的订单才能更新为paid
                    if order_data.get('status') != 'pending':
                        logger.warning(f"支付宝回调：订单 {out_trade_no} 状态不是pending，当前状态: {order_data.get('status')}")
                        return HttpResponse('fail')

                    # 更新订单状态
                    order_data['status'] = 'paid'

                    # 在 payment 字段中添加支付信息
                    if 'payment' not in order_data:
                        order_data['payment'] = {}

                    order_data['payment']['payment_order'] = out_trade_no  # 支付宝交易号
                    order_data['payment']['pay_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # 支付时间
                    order_data['payment']['pay_platform'] = 'alipay'

                    # 根据订单类型进行不同处理
                    order_type = order_data.get('type', 2)  # 默认为商品订单

                    # 更新商品销量和库存
                    try:
                        if order_type == 2:
                            product_id = order_data.get('product', {}).get('id', '')
                            if product_id:
                                # 获取商品信息以确定类型
                                try:
                                    goods = Goods.objects.get(id=product_id)
                                
                                    # 根据商品类型决定是否减少库存
                                    if goods.type in ['1', '2']:  # 卡密商品或虚拟商品
                                        # 同时更新销量和库存，确保库存不为负数
                                        updated_count = Goods.objects.filter(
                                            id=product_id,
                                            stock__gt=0  # 确保库存大于0
                                        ).update(
                                            sales_count=F('sales_count') + 1,
                                            stock=F('stock') - 1
                                        )
                                    
                                        if updated_count > 0:
                                            logger.info(f"支付宝回调：商品 {product_id} 销量+1，库存-1")
                                        else:
                                            # 库存不足，只更新销量
                                            Goods.objects.filter(id=product_id).update(
                                                sales_count=F('sales_count') + 1
                                            )
                                            logger.warning(f"支付宝回调：商品 {product_id} 库存不足，仅更新销量")
                                        
                                    elif goods.type == '3':  # 对接商品
                                        # 只更新销量，不减少库存
                                        updated_count = Goods.objects.filter(id=product_id).update(
                                            sales_count=F('sales_count') + 1
                                        )
                                        logger.info(f"支付宝回调：对接商品 {product_id} 仅更新销量+1")
                                    
                                except Goods.DoesNotExist:
                                    logger.error(f"支付宝回调：商品 {product_id} 不存在")
                            else:
                                logger.warning(f"支付宝回调：订单 {out_trade_no} 缺少商品ID，无法更新销量")
                    except Exception as sales_error:
                        # 销量和库存更新失败不影响支付回调成功，仅记录错误日志
                        logger.error(f"支付宝回调：更新商品销量和库存失败 - 订单: {out_trade_no}, 错误: {str(sales_error)}")

                    # 更新订单记录
                    order.order = out_trade_no  # 支付宝只返回out_trade_no参数
                    order.data = order_data
                    order.save()

                    # 使用统一的异步处理函数
                    handle_payment_success_unified(out_trade_no)

                    return HttpResponse('success')

            except Exception as e:
                logger.error(f"处理支付宝支付回调失败: {str(e)}", exc_info=True)
                return HttpResponse('fail')
        else:
            return HttpResponse('fail')

    except Exception as e:
        return HttpResponse('fail')


# 订单管理相关API
@csrf_exempt
@admin_auth_basic(operation='get_orders_list', resource='order')
def get_orders_list(request):
    """
    获取订单列表API
    GET /api/GetOrdersList
    """
    if request.method != 'GET':
        return JsonResponse({'code': 400, 'message': '请求方法错误'})

    try:
        # 使用OrderService获取订单数据
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.order.order_service import OrderService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用订单服务
        order_service = OrderService(context)
        
        # 获取查询参数
        keyword = request.GET.get('keyword', '').strip()
        status = request.GET.get('status', '').strip()
        order_source = request.GET.get('order_source', '').strip()
        product_type = request.GET.get('product_type', '').strip()
        
        # 构建过滤条件
        filters = {}
        if keyword:
            filters['search'] = keyword
        if status:
            filters['status'] = status
        if order_source:
            filters['order_source'] = order_source
        if product_type:
            filters['product_type'] = product_type
        
        # 获取订单列表
        result = order_service.get_order_list(
            filters=filters,
            format_type='admin'
        )
        
        # 转换为原有API格式
        orders_list = []
        for order in result['list']:
            order_obj = {
                'id': order['id'],
                'order_number': order['order_no'],
                'product_id': order.get('product_id', ''),
                'product_name': order.get('product_name', ''),
                'status': order['status'],
                'order_source': order.get('order_source', ''),
                'product_type': order.get('product_type', ''),
                'customer_name': order.get('customer_name', ''),
                'payment_order': order.get('payment_order', ''),
                'sale_price': float(order.get('sale_price', 0)),
                'purchase_price': float(order.get('purchase_price', 0)),
                'created_at': order['created_at']
            }
            
            # 计算利润
            order_obj['profit'] = order_obj['sale_price'] - order_obj['purchase_price']
            orders_list.append(order_obj)

        return JsonResponse({
            'code': 200,
            'message': '获取成功',
            'data': {
                'orders': orders_list
            }
        })

    except Exception as e:
        logger.error(f"获取订单列表失败: {str(e)}")
        return JsonResponse({
            'code': 500,
            'message': '服务器内部错误'
        })


def _determine_order_source(order_id):
    """根据订单号确定下单来源"""
    if not order_id:
        return 'web'

    if order_id.startswith('Web-'):
        return 'web'
    elif order_id.startswith('Robot-'):
        return 'robot'
    elif order_id.startswith('API-'):
        return 'API'
    else:
        return 'web'


def _extract_customer_name(user_info):
    """提取客户姓名"""
    if user_info.get('is_guest', False):
        return '游客用户'

    # 从邮箱中提取用户名部分作为客户名
    email = user_info.get('notice_mail', '')
    if email and '@' in email:
        return email.split('@')[0]

    return user_info.get('id', '未知用户')


def _should_include_order(order_obj, keyword, status, order_source, product_type):
    """判断订单是否应该包含在结果中"""
    # 关键字搜索
    if keyword:
        search_fields = [
            order_obj.get('id', ''),
            order_obj.get('order_number', ''),
            order_obj.get('product_name', ''),
            order_obj.get('customer_name', ''),
            order_obj.get('payment_order', '')
        ]

        keyword_lower = keyword.lower()
        if not any(keyword_lower in str(field).lower() for field in search_fields):
            return False

    # 状态筛选
    if status and order_obj.get('status') != status:
        return False

    # 下单来源筛选
    if order_source and order_obj.get('order_source') != order_source:
        return False

    # 商品类型筛选
    if product_type and order_obj.get('product_type') != product_type:
        return False

    return True


@csrf_exempt
def get_order_detail(request):
    """
    获取订单详情API
    GET /api/orders?order_id={order_id}
    """
    if request.method != 'GET':
        return JsonResponse({'code': 400, 'message': '请求方法错误'})

    try:
        # 获取订单ID参数
        order_id = request.GET.get('order_id', '').strip()

        if not order_id:
            return JsonResponse({
                'code': 400,
                'message': '订单ID不能为空'
            })

        # 使用OrderService获取订单详情
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.order.order_service import OrderService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用订单服务
        order_service = OrderService(context)
        
        try:
            # 获取订单详情
            order_detail = order_service.get_order_detail(order_id, format_type='admin')
            
            # 返回订单详情（保持原有格式兼容性）
            return JsonResponse({
                'code': 200,
                'message': '获取成功',
                'data': order_detail.get('detailed_data', order_detail)
            })
            
        except Exception as e:
            if 'not found' in str(e).lower() or '不存在' in str(e):
                return JsonResponse({
                    'code': 404,
                    'message': '订单不存在'
                })
            raise

    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        return JsonResponse({
            'code': 500,
            'message': '服务器内部错误'
        })


@csrf_exempt
@admin_auth_high(operation='batch_delete_orders', resource='order')
def batch_delete_orders(request):
    """
    批量删除订单API
    POST /api/orders/batch-delete
    """
    if request.method != 'POST':
        return JsonResponse({'code': 400, 'message': '请求方法错误'})

    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)

        # 提取订单ID列表
        order_ids = data.get('order_ids', [])

        if not order_ids or not isinstance(order_ids, list):
            return JsonResponse({
                'code': 400,
                'message': '订单ID列表不能为空且必须是数组'
            })

        # 使用OrderService批量删除订单
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.order.order_service import OrderService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用订单服务
        order_service = OrderService(context)
        
        success_count = 0
        failed_count = 0

        for order_id in order_ids:
            try:
                # 使用服务删除订单（这里简化为取消订单，因为直接删除可能影响数据完整性）
                order_service.cancel_order(order_id, reason='管理员批量删除')
                success_count += 1
            except Exception as e:
                failed_count += 1
                logger.error(f"删除订单 {order_id} 时出错: {str(e)}")

        return JsonResponse({
            'code': 200,
            'message': '删除订单成功',
            'data': {
                'success_count': success_count,
                'failed_count': failed_count
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'code': 400,
            'message': '无效的JSON数据'
        })
    except Exception as e:
        logger.error(f"批量删除订单失败: {str(e)}")
        return JsonResponse({
            'code': 500,
            'message': '服务器内部错误'
        })

async def process_order_operations(order_id):
    try:
        # 使用sync_to_async包装数据库操作
        order = await sync_to_async(Order.objects.get)(id=order_id)
        
        # 获取订单类型
        order_type = order.type # 1为充值余额订单 2为商品下单订单

        # 如果是充值订单，直接返回（余额更新已在支付回调中完成）
        if order_type == 1:
            return

        # 获取下单的商品ID
        order_product_id = order.data.get('product', {}).get('id', '')
        
        if not order_product_id:
            logger.warning(f"订单 {order_id} 缺少商品ID，跳过后置操作处理")
            return
        
        # 获取商品信息
        try:
            goods = await sync_to_async(Goods.objects.get)(id=order_product_id)
        except Goods.DoesNotExist:
            logger.error(f"订单 {order_id} 关联的商品 {order_product_id} 不存在")
            return
        
        # 获取商品的后置操作配置
        operation_data = await sync_to_async(lambda: goods.operation_data)()
        
        if not operation_data:
            logger.debug(f"订单 {order_id} 的商品 {order_product_id} 没有配置后置操作")
            return
        
        # 解析后置操作配置
        operation_type = operation_data.get('type', 0)
        
        # 如果type=0，直接返回不处理
        if operation_type == 0:
            logger.debug(f"订单 {order_id} 的商品后置操作类型为0，跳过处理")
            return
        
        # 如果type=1，处理URL请求
        if operation_type == 1:
            await process_url_request_operation(order, goods, operation_data)
        
        logger.info(f"订单 {order_id} 后置操作处理完成")
        
    except Order.DoesNotExist:
        logger.error(f"订单不存在: {order_id}")
    except Exception as e:
        logger.error(f"处理订单后置操作时出错: {str(e)}", exc_info=True)


async def process_url_request_operation(order, goods, operation_data):

    try:
        # 获取配置数据
        data_config = operation_data.get('data', {})
        url = data_config.get('url', '')
        request_method = data_config.get('requestMethod', 0)  # 0=GET, 1=POST
        headers = data_config.get('header', {})
        body = data_config.get('body', '')
        sign_config = data_config.get('sign', {})
        
        if not url:
            logger.warning(f"订单 {order.id} 的URL请求配置缺少URL")
            return
        
        # 获取订单数据用于变量替换
        order_data = await sync_to_async(lambda: order.data)()
        
        # 准备变量替换的数据
        variables = await prepare_variables(order, goods, sign_config)
        
        # 替换URL中的变量
        processed_url = replace_variables(url, variables)
        
        # 处理请求头
        processed_headers = {}
        if isinstance(headers, dict):
            for key, value in headers.items():
                processed_key = replace_variables(str(key), variables)
                processed_value = replace_variables(str(value), variables)
                processed_headers[processed_key] = processed_value
        
        # 处理请求体
        processed_body = replace_variables(body, variables) if body else ''
        
        # 发送请求
        await send_http_request(
            order.id,
            processed_url,
            request_method,
            processed_headers,
            processed_body
        )
        
    except Exception as e:
        logger.error(f"处理URL请求操作失败: {str(e)}", exc_info=True)


async def prepare_variables(order, goods, sign_config):
    
    # 生成时间戳（确保同个请求中采用相同时间戳）
    t = time.time()
    timestamp_10 = str(int(t))
    timestamp_13 = str(int(t * 1000))
    
    # 获取订单基本信息
    order_id = str(order.id)
    user_id = str(order.user.id) if order.user else ''
    product_id = str(goods.id)
    product_name = goods.name
    original_price = str(order.total_price)
    final_price = str(order.final_price)
    payment_way = order.payment_method or ''
    
    # 获取自定义字段作为attach参数
    custom_fields = {}
    if order.custom_fields:
        try:
            custom_fields = json.loads(order.custom_fields)
        except:
            logger.warning(f"订单 {order_id} 的自定义字段解析失败")
    
    # 从自定义字段中提取最多5个值作为参数
    attach_values = []
    if isinstance(custom_fields, dict):
        # 如果是字典，取前5个值
        for i, (key, value) in enumerate(custom_fields.items()):
            if i >= 5:
                break
            attach_values.append(str(value))
    elif isinstance(custom_fields, list):
        # 如果是列表，取前5个元素
        for i, item in enumerate(custom_fields):
            if i >= 5:
                break
            if isinstance(item, dict) and item:
                # 取字典的第一个值
                attach_values.append(str(next(iter(item.values()))))
            else:
                attach_values.append(str(item))
    
    # 确保有5个参数位置
    while len(attach_values) < 5:
        attach_values.append('')
    
    # 处理签名
    sign_value = ''
    if sign_config:
        sign_body = sign_config.get('body', '')
        encrypt_method = sign_config.get('encryptMethod', 1)
        output_method = sign_config.get('outputMethod', 1)
        
        if sign_body:
            # 先替换签名内容中的变量（除了签名信息本身）
            temp_variables = {
                '10位时间戳': timestamp_10,
                '13位时间戳': timestamp_13,
                '参数1': attach_values[0],
                '参数2': attach_values[1],
                '参数3': attach_values[2],
                '参数4': attach_values[3],
                '参数5': attach_values[4],
                '用户ID': user_id,
                '订单号': order_id,
                '支付方式': payment_way,
                '请求方式': 'GET' if sign_config.get('requestMethod', 0) == 0 else 'POST',
                '商品名称': product_name,
                '商品价格': original_price,
                '支付价格': final_price,
            }
            
            processed_sign_body = replace_variables(sign_body, temp_variables)
            
            # 根据加密方式处理
            if encrypt_method == 2:  # 内容小写后MD5加密
                processed_sign_body = processed_sign_body.lower()
            elif encrypt_method == 3:  # 内容大写后MD5加密
                processed_sign_body = processed_sign_body.upper()
            
            # MD5加密
            sign_value = get_md5(processed_sign_body)
            
            # 根据输出方式处理
            if output_method == 2:  # 转小写后输出
                sign_value = sign_value.lower()
            elif output_method == 3:  # 转大写后输出
                sign_value = sign_value.upper()
    
    # 构建变量字典
    variables = {
        '10位时间戳': timestamp_10,
        '13位时间戳': timestamp_13,
        '参数1': attach_values[0],
        '参数2': attach_values[1],
        '参数3': attach_values[2],
        '参数4': attach_values[3],
        '参数5': attach_values[4],
        '用户ID': user_id,
        '订单号': order_id,
        '支付方式': payment_way,
        '请求方式': 'GET' if sign_config.get('requestMethod', 0) == 0 else 'POST',
        '商品名称': product_name,
        '商品价格': original_price,
        '支付价格': final_price,
        '签名信息': sign_value,
    }
    
    return variables


def replace_variables(text, variables):
    """
    替换文本中的变量
    """
    if not text or not variables:
        return text
    
    result = text
    for var_name, var_value in variables.items():
        placeholder = f'<{var_name}>'
        result = result.replace(placeholder, str(var_value))
    
    return result


async def send_http_request(order_id, url, request_method, headers, body):
    """
    发送HTTP请求
    """
    try:
        # 设置默认请求头
        default_headers = {
            'User-Agent': 'OrderSystem/1.0',
            'Content-Type': 'application/json' if body else 'text/plain'
        }
        
        # 合并请求头
        final_headers = {**default_headers, **headers}
        
        # 创建HTTP客户端
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            if request_method == 0:  # GET请求
                async with session.get(url, headers=final_headers) as response:
                    response_text = await response.text()
                    logger.info(f"订单 {order_id} GET请求成功: {url}, 状态码: {response.status}")
                    logger.debug(f"订单 {order_id} 响应内容: {response_text[:200]}...")
            else:  # POST请求
                async with session.post(url, headers=final_headers, data=body) as response:
                    response_text = await response.text()
                    logger.info(f"订单 {order_id} POST请求成功: {url}, 状态码: {response.status}")
                    logger.debug(f"订单 {order_id} 响应内容: {response_text[:200]}...")

        # # 添加订单失败历史记录
        # order_data = add_order_history(
        #     order_data=order_data,
        #     status='processing',
        #     action='order_failed',
        #     description='卡密商品发货失败：系统异常',
        #     details={
        #         'error_message': str(e),
        #         'fulfillment_type': '卡密商品',
        #         'card_provided': False,
        #         'goods_id': order_product_id,
        #         'exception_type': type(e).__name__
        #         }
        #     )
        
    except asyncio.TimeoutError:
        logger.error(f"订单 {order_id} HTTP请求超时: {url}")
    except Exception as e:
        logger.error(f"订单 {order_id} HTTP请求失败: {url}, 错误: {str(e)}")

@csrf_exempt
def vue_test_api(request):
    """
    Vue混合渲染测试API
    用于测试Vue组件的API调用功能
    """
    if request.method == 'GET':
        try:
            # 返回测试数据
            test_data = {
                'code': 200,
                'message': 'Vue API测试成功',
                'data': {
                    'timestamp': timezone.now().isoformat(),
                    'server_info': {
                        'django_version': '5.2',
                        'python_version': '3.x',
                        'debug_mode': settings.DEBUG
                    },
                    'test_items': [
                        {'id': 1, 'name': '测试项目1', 'status': 'active'},
                        {'id': 2, 'name': '测试项目2', 'status': 'inactive'},
                        {'id': 3, 'name': '测试项目3', 'status': 'active'}
                    ],
                    'random_number': __import__('random').randint(1, 1000)
                }
            }
            
            return JsonResponse(test_data)
            
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'message': f'API测试失败: {str(e)}',
                'data': None
            })
    
    elif request.method == 'POST':
        try:
            # 处理POST请求
            data = json.loads(request.body) if request.body else {}
            
            response_data = {
                'code': 200,
                'message': 'POST请求处理成功',
                'data': {
                    'received_data': data,
                    'timestamp': timezone.now().isoformat(),
                    'echo': f"您发送的数据已收到: {data}"
                }
            }
            
            return JsonResponse(response_data)
            
        except Exception as e:
            return JsonResponse({
                'code': -1,
                'message': f'POST请求处理失败: {str(e)}',
                'data': None
            })
    
    else:
        return JsonResponse({
            'code': -1,
            'message': '不支持的请求方法',
            'data': None
        })
    
# 用户管理相关API
@csrf_exempt
@admin_auth_basic(operation='get_user_list', resource='user')
def get_user_list(request):
    """
    获取用户列表API
    GET /api/GetUserList?page=1&page_size=20&keyword=&status=
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 使用UserService获取用户列表
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.user.user_service import UserService
        from utils.shared_services.user.user_dto import UserListFilter
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用用户服务
        user_service = UserService(context)
        
        # 获取分页参数
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 20)), 100)  # 最大100条
        
        # 构建过滤条件
        filters = UserListFilter()
        
        # 获取搜索筛选参数
        keyword = request.GET.get('keyword', '').strip()
        if keyword:
            filters.keyword = keyword
        
        status = request.GET.get('status', '').strip()
        if status:
            filters.status = status
        
        membership_level = request.GET.get('membership_level', '').strip()
        if membership_level:
            filters.membership_level = membership_level
        
        # 获取用户列表
        result = user_service.get_user_list(
            filters=filters,
            page=page,
            page_size=page_size,
            format_type='admin'
        )
        
        # 转换为原有的响应格式
        users_list = []
        for user in result['users']:
            user_obj = {
                'user_id': user.get('user_id'),
                'email': user.get('email'),
                'username': user.get('username', ''),
                'membership_level': user.get('membership_level', 'NormalUser'),
                'balance': float(user.get('balance', 0)),
                'status': user.get('status', 'active'),
                'date_joined': user.get('date_joined', ''),
                'last_login': user.get('last_login', ''),
                'total_orders': user.get('total_orders', 0),
                'total_spent': float(user.get('total_spent', 0))
            }
            users_list.append(user_obj)
        
        # 构建分页响应
        pagination_data = result['pagination']
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': {
                'users': users_list,
                'pagination': pagination_data
            }
        })

    except ValueError as e:
        return JsonResponse({'code': -1, 'msg': f'参数错误: {str(e)}'})
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_basic(operation='get_user_detail', resource='user')
def get_user_detail(request):
    """
    获取用户详情API
    POST /api/GetUserDetail
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        user_id = data.get('user_id', '')
        
        if not user_id:
            return JsonResponse({'code': -1, 'msg': '用户ID不能为空'})
        
        # 使用UserService获取用户详情
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.user.user_service import UserService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用用户服务
        user_service = UserService(context)
        
        # 获取用户详情
        user_data = user_service.get_user_info(
            user_id=user_id, 
            format_type='admin',
            include_permissions=True
        )
        
        # 构建响应（保持原有格式）
        response_data = {
            'user_id': user_data.get('user_id'),
            'email': user_data.get('email'),
            'username': user_data.get('username', ''),
            'membership_level': user_data.get('membership_level', 'NormalUser'),
            'balance': float(user_data.get('balance', 0)),
            'status': user_data.get('status', 'active'),
            'date_joined': user_data.get('date_joined', ''),
            'last_login': user_data.get('last_login', ''),
            'profile': user_data.get('profile', {}),
            'permissions': user_data.get('permissions', []),
            'statistics': user_data.get('statistics', {})
        }
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': response_data
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_standard(operation='update_user', resource='user')
def update_user(request):
    """
    更新用户信息API
    POST /api/UpdateUser
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 提取请求参数
        user_id = data.get('user_id', '')
        
        # 参数验证
        if not user_id:
            return JsonResponse({'code': -1, 'msg': '用户ID不能为空'})
        
        # 使用UserService更新用户
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.user.user_service import UserService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用用户服务
        user_service = UserService(context)
        
        # 准备更新数据
        update_data = {}
        
        # 基本字段
        if 'username' in data and data['username'] is not None:
            update_data['username'] = data['username']
        
        if 'email' in data and data['email'] is not None:
            update_data['email'] = data['email']
        
        if 'membership_level' in data and data['membership_level'] is not None:
            update_data['membership_level'] = data['membership_level']
        
        if 'balance' in data and data['balance'] is not None:
            update_data['balance'] = data['balance']
        
        if 'status' in data and data['status'] is not None:
            update_data['status'] = data['status']
        
        # 更新用户
        updated_user = user_service.update_user_info(user_id, update_data)
        
        # 返回成功响应
        return JsonResponse({
            'code': 200,
            'msg': '修改成功'
        })
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        logger.error(f"更新用户失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_basic(operation='get_user_statistics', resource='user')
def get_user_statistics(request):
    """
    获取用户统计信息API
    GET /api/GetUserStatistics
    """
    if request.method != 'GET':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})

    try:
        # 使用UserService获取用户统计
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.user.user_service import UserService
        
        # 创建服务上下文
        context = AuthService.create_context_from_admin_request(request)
        
        # 使用用户服务
        user_service = UserService(context)
        
        # 获取日期范围参数
        start_date = request.GET.get('start_date', '')
        end_date = request.GET.get('end_date', '')
        
        date_range = {}
        if start_date:
            date_range['start'] = start_date
        if end_date:
            date_range['end'] = end_date
        
        # 获取用户统计
        stats = user_service.get_user_statistics(date_range if date_range else None)
        
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': stats
        })

    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'操作失败: {str(e)}'})


@csrf_exempt
@admin_auth_standard(operation='upload_product_image', resource='goods')
def upload_product_image(request):
    """
    上传商品图片API
    POST /api/upload_product_image
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 获取base64图片数据
        image_data = data.get('image', '')
        if not image_data:
            return JsonResponse({'code': -1, 'msg': '图片数据不能为空'})
        
        # 检查是否为base64格式的图片数据
        if not image_data.startswith('data:image/'):
            return JsonResponse({'code': -1, 'msg': '图片数据格式不正确'})
        
        try:
            # 解析base64数据
            # 格式: data:image/jpeg;base64,/9j/4AAQ...
            header, encoded = image_data.split(',', 1)
            
            # 获取图片格式
            mime_type = header.split(';')[0].split(':')[1]
            if mime_type not in ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']:
                return JsonResponse({'code': -1, 'msg': '不支持的图片格式'})
            
            # 获取文件扩展名
            extension_map = {
                'image/jpeg': '.jpg',
                'image/jpg': '.jpg', 
                'image/png': '.png',
                'image/gif': '.gif',
                'image/webp': '.webp'
            }
            file_extension = extension_map.get(mime_type, '.jpg')
            
            # 解码base64数据
            image_binary = base64.b64decode(encoded)
            
            # 检查文件大小 (限制为5MB)
            if len(image_binary) > 5 * 1024 * 1024:
                return JsonResponse({'code': -1, 'msg': '图片文件过大，请选择小于5MB的图片'})
            
            # 生成唯一的文件名
            file_uuid = str(uuid.uuid4())
            filename = f"product_{file_uuid}{file_extension}"
            
            # 确保目录存在
            upload_dir = os.path.join(settings.MEDIA_ROOT, 'product_images')
            os.makedirs(upload_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(upload_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(image_binary)
            
            # 生成访问URL
            image_url = f"/media/product_images/{filename}"
            
            # 记录日志
            logger.info(f"商品图片上传成功: {filename}, 大小: {len(image_binary)} bytes")
            
            return JsonResponse({
                'code': 200,
                'msg': '图片上传成功',
                'data': {
                    'image_url': image_url,
                    'filename': filename,
                    'size': len(image_binary)
                }
            })
            
        except Exception as decode_error:
            logger.error(f"图片数据解码失败: {str(decode_error)}")
            return JsonResponse({'code': -1, 'msg': '图片数据解码失败'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        logger.error(f"图片上传失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'图片上传失败: {str(e)}'})
        return JsonResponse({'code': -1, 'msg': f'图片上传失败: {str(e)}'})


@csrf_exempt
@admin_auth_standard(operation='upload_category_image', resource='category')
def upload_category_image(request):
    """
    上传分类图片API
    POST /api/upload_category_image
    """
    if request.method != 'POST':
        return JsonResponse({'code': -1, 'msg': '请求方法错误'})
    
    try:
        # 解析请求体JSON数据
        data = json.loads(request.body)
        
        # 验证签名
        if not verify_sign(data):
            return JsonResponse({'code': -1, 'msg': '签名验证失败'})
        
        # 获取base64图片数据
        image_data = data.get('image', '')
        if not image_data:
            return JsonResponse({'code': -1, 'msg': '图片数据不能为空'})
        
        # 检查是否为base64格式的图片数据
        if not image_data.startswith('data:image/'):
            return JsonResponse({'code': -1, 'msg': '图片数据格式不正确'})
        
        try:
            # 解析base64数据
            # 格式: data:image/jpeg;base64,/9j/4AAQ...
            header, encoded = image_data.split(',', 1)
            
            # 获取图片格式
            mime_type = header.split(';')[0].split(':')[1]
            if mime_type not in ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']:
                return JsonResponse({'code': -1, 'msg': '不支持的图片格式'})
            
            # 获取文件扩展名
            extension_map = {
                'image/jpeg': '.jpg',
                'image/jpg': '.jpg', 
                'image/png': '.png',
                'image/gif': '.gif',
                'image/webp': '.webp'
            }
            file_extension = extension_map.get(mime_type, '.jpg')
            
            # 解码base64数据
            image_binary = base64.b64decode(encoded)
            
            # 检查文件大小 (限制为5MB)
            if len(image_binary) > 5 * 1024 * 1024:
                return JsonResponse({'code': -1, 'msg': '图片文件过大，请选择小于5MB的图片'})
            
            # 生成唯一的文件名
            file_uuid = str(uuid.uuid4())
            filename = f"category_{file_uuid}{file_extension}"
            
            # 确保目录存在
            upload_dir = os.path.join(settings.MEDIA_ROOT, 'category_images')
            os.makedirs(upload_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(upload_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(image_binary)
            
            # 生成访问URL
            image_url = f"/media/category_images/{filename}"
            
            # 记录日志
            logger.info(f"分类图片上传成功: {filename}, 大小: {len(image_binary)} bytes")
            
            return JsonResponse({
                'code': 200,
                'msg': '图片上传成功',
                'data': {
                    'image_url': image_url,
                    'filename': filename,
                    'size': len(image_binary)
                }
            })
            
        except Exception as decode_error:
            logger.error(f"图片数据解码失败: {str(decode_error)}")
            return JsonResponse({'code': -1, 'msg': '图片数据解码失败'})
    
    except json.JSONDecodeError:
        return JsonResponse({'code': -1, 'msg': '无效的JSON数据'})
    except Exception as e:
        logger.error(f"分类图片上传失败: {str(e)}")
        return JsonResponse({'code': -1, 'msg': f'图片上传失败: {str(e)}'})