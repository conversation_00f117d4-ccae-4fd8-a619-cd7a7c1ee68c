from django.apps import AppConfig
import logging
import threading
import time

# 配置日志
logger = logging.getLogger(__name__)

class AppAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'app'

    def ready(self):
        """
        Django应用启动时的初始化方法
        在此处初始化WebSocket客户端连接
        """
        # 避免在Django迁移或其他管理命令时初始化WebSocket
        import sys
        if 'runserver' not in sys.argv and 'manage.py' in sys.argv:
            logger.info("检测到Django管理命令，跳过WebSocket初始化")
            return

        # 延迟初始化WebSocket，避免在Django完全启动前连接
        def delayed_websocket_init():
            """
            延迟初始化WebSocket连接
            """
            try:
                # 等待Django完全启动
                time.sleep(2)

                logger.info("开始初始化WebSocket客户端连接...")

                # 导入WebSocket管理器
                from utils.websocket_manager import init_websocket

                # 初始化WebSocket连接
                success = init_websocket()

                if success:
                    logger.info("WebSocket客户端连接初始化成功")
                else:
                    logger.warning("WebSocket客户端连接初始化失败，将在后台自动重试")

            except ImportError as e:
                logger.error(f"导入WebSocket模块失败: {e}")
            except Exception as e:
                logger.error(f"WebSocket初始化异常: {e}")

        # 在独立线程中执行延迟初始化，避免阻塞Django启动
        init_thread = threading.Thread(target=delayed_websocket_init, daemon=True)
        init_thread.start()

        logger.info("WebSocket初始化线程已启动")
