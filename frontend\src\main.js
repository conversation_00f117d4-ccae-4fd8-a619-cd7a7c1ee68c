import { createApp } from 'vue'

// Vue应用配置
const VueConfig = {
  // 自定义分隔符，避免与Django模板语法冲突
  delimiters: ['[[', ']]'],
  
  // 全局配置
  config: {
    productionTip: false,
    devtools: true
  }
}

// 全局工具函数
window.VueUtils = {
  // 获取Django传递的context数据
  getDjangoContext() {
    return window.__DJANGO_CONTEXT__ || {}
  },
  
  // 获取CSRF token
  getCSRFToken() {
    return window.__DJANGO_CONTEXT__?.csrf_token || 
           document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
           ''
  },
  
  // API请求辅助函数
  async apiRequest(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.getCSRFToken(),
        ...options.headers
      },
      credentials: 'same-origin',
      ...options
    }
    
    try {
      const response = await fetch(url, defaultOptions)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }
}

// 全局混入，为所有Vue组件提供Django数据访问
const GlobalMixin = {
  data() {
    return {
      djangoContext: window.__DJANGO_CONTEXT__ || {}
    }
  },
  
  methods: {
    // 获取Django context数据
    getDjangoData(key, defaultValue = null) {
      return this.djangoContext[key] || defaultValue
    },
    
    // API请求方法
    async $api(url, options = {}) {
      return await window.VueUtils.apiRequest(url, options)
    },
    
    // 获取CSRF token
    getCSRFToken() {
      return window.VueUtils.getCSRFToken()
    }
  }
}

// 导出Vue配置和工具
export { VueConfig, GlobalMixin }

// 如果在浏览器环境中，设置全局变量
if (typeof window !== 'undefined') {
  window.VueConfig = VueConfig
  window.GlobalMixin = GlobalMixin
}