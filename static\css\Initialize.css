:root {
            --primary-color: #ff7eb9;
            --light-pink: #ffe6f0;
            --white: #ffffff;
            --error-color: #ff4757;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', system-ui, sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--light-pink) 0%, var(--white) 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: var(--white);
            padding: 1.8rem 2.2rem;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(255, 126, 185, 0.08);
            width: 100%;
            max-width: 680px;
            margin: 20px auto;
            transform: translateY(20px);
            opacity: 0;
            animation: fadeInUp 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .step {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .step.active {
            background: var(--primary-color);
            color: white;
            transform: scale(1.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 1.8rem;
            font-weight: 400;
            font-size: 1.8rem;
            letter-spacing: -0.3px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.2rem;
        }

        .form-group {
            position: relative;
            pointer-events: auto;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
            pointer-events: auto;
        }

        input {
            width: 100%;
            padding: 0.7rem 1rem;
            border: 2px solid #eee;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            pointer-events: auto !important;
            z-index: 50;
            position: relative;
        }

        input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 126, 185, 0.15);
        }

        input.error {
            border-color: var(--error-color);
            animation: shake 0.4s ease;
        }

        /* 新增：自定义单选按钮样式 */
        .encode-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .encode-option {
            display: flex;
            align-items: center;
            position: relative;
            padding: 10px 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #eee;
        }

        .encode-option:hover {
            background-color: #f5f5f5;
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }

        .encode-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .radio-custom {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid #ddd;
            display: inline-block;
            position: relative;
            margin-right: 10px;
            flex-shrink: 0;
            transition: all 0.2s ease;
        }

        .encode-option input[type="radio"]:checked+.radio-custom {
            border-color: var(--primary-color);
            background-color: var(--white);
        }

        .encode-option input[type="radio"]:checked+.radio-custom:after {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-color);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: radioCheck 0.3s ease;
        }

        @keyframes radioCheck {
            0% {
                transform: translate(-50%, -50%) scale(0);
            }

            50% {
                transform: translate(-50%, -50%) scale(1.2);
            }

            100% {
                transform: translate(-50%, -50%) scale(1);
            }
        }

        .encode-name {
            font-weight: 600;
            margin-right: 8px;
            color: #444;
        }

        .encode-desc {
            font-size: 0.8rem;
            color: #888;
            margin-left: auto;
        }

        .encode-option input[type="radio"]:checked~.encode-name {
            color: var(--primary-color);
        }

        /* 媒体查询 - 在小屏幕上简化显示 */
        @media (max-width: 700px) {
            .encode-options {
                grid-template-columns: 1fr;
            }

            .encode-option {
                flex-wrap: wrap;
            }

            .encode-desc {
                width: 100%;
                margin-top: 4px;
                margin-left: 28px;
            }
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-4px);
            }

            75% {
                transform: translateX(4px);
            }
        }

        .error-message {
            color: var(--error-color);
            font-size: 0.8rem;
            position: absolute;
            bottom: -1.2rem;
            left: 0.2rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .error-message.show {
            opacity: 1;
        }

        .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            gap: 1rem;
        }

        button {
            padding: 0.8rem 1.6rem;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #ff6ba9;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 126, 185, 0.2);
        }

        .btn-secondary {
            background: #f8f8f8;
            color: #666;
        }

        .btn-secondary:hover {
            background: #eee;
        }

        .summary-box {
            background: var(--light-pink);
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 1.5rem;
        }

        .config-list {
            list-style: none;
            padding: 0;
        }

        .config-list li {
            padding: 0.6rem 0;
            border-bottom: 1px solid rgba(255, 126, 185, 0.1);
            color: #666;
            font-size: 0.95rem;
            display: flex;
            justify-content: space-between;
        }

        .config-list span {
            color: #333;
            font-weight: 500;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 100;
            pointer-events: none;
        }

        /* 当loading-overlay显示时激活pointer-events */
        .loading-overlay[style*="display: flex"] {
            pointer-events: auto;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--light-pink);
            border-top-color: var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .success-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            background: var(--white);
            padding: 2rem 3rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(255, 126, 185, 0.2);
            opacity: 0;
            z-index: 1000;
            transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            border: 2px solid var(--primary-color);
            pointer-events: none;
        }

        .success-modal.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
            pointer-events: auto;
        }

        .success-modal-content {
            text-align: center;
        }

        .success-icon {
            width: 60px;
            height: 60px;
            background: var(--light-pink);
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 2rem;
            animation: checkmark 0.6s ease;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
            }

            50% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1.2rem;
                max-width: 95%;
            }

            h1 {
                font-size: 1.6rem;
                margin-bottom: 1.5rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .success-modal {
                width: 90%;
                padding: 1.5rem;
            }

            .success-icon {
                width: 50px;
                height: 50px;
                font-size: 1.6rem;
            }
        }

        /* 自定义弹窗样式 */
        .custom-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .custom-modal.show {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        .custom-modal-content {
            background-color: var(--white);
            padding: 2rem;
            border-radius: 12px;
            max-width: 480px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(255, 126, 185, 0.2);
            text-align: center;
            transform: translateY(20px);
            transition: transform 0.3s ease;
            pointer-events: auto;
            position: relative;
            z-index: 201;
        }

        .custom-modal.show .custom-modal-content {
            transform: translateY(0);
        }

        .modal-title {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.4rem;
            font-weight: 500;
        }

        .modal-message {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            position: relative;
            z-index: 220;
        }

        .modal-btn {
            display: inline-block;
            margin: 0 8px;
            padding: 10px 20px;
            min-width: 100px;
            cursor: pointer;
            font-weight: bold;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            position: relative;
            z-index: 210;
            overflow: visible;
            pointer-events: auto;
        }

        .modal-btn-primary {
            background-color: #ff7eb9;
            color: white;
        }

        .modal-btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }