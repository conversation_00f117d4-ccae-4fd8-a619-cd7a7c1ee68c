/* ==================== 后台管理框架样式文件 ==================== */
/*
 * 文件名: admin-framework.css
 * 用途: 为admin-framework.html提供完整的样式支持
 * 前缀: af- (admin framework)
 * 设计原则: 现代化、响应式、用户友好
 */

/* ==================== CSS变量定义 ==================== */
:root {
    /* 框架布局变量 */
    --af-sidebar-width: 250px;
    --af-header-height: 60px;
    --af-mobile-breakpoint: 768px;

    /* 颜色系统 */
    --af-primary-color: #ff6b9d;
    --af-primary-light: #ffecf2;
    --af-primary-dark: #e55a8a;
    --af-secondary-color: #f8f9fa;
    --af-text-primary: #333333;
    --af-text-secondary: #666666;
    --af-text-muted: #999999;
    --af-border-color: #e9ecef;
    --af-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --af-shadow-medium: 0 4px 12px rgba(0,0,0,0.15);

    /* 背景颜色 */
    --af-bg-primary: #ffffff;
    --af-bg-secondary: #f8f9fa;
    --af-bg-tertiary: #e9ecef;

    /* 过渡动画 */
    --af-transition-fast: 0.2s ease;
    --af-transition-normal: 0.3s ease;
    --af-transition-slow: 0.5s ease;

    /* 圆角 */
    --af-radius-small: 4px;
    --af-radius-medium: 8px;
    --af-radius-large: 12px;
}

/* ==================== 基础样式重置 ==================== */
.af-body {
    margin: 0;
    padding: 0;
    font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', Roboto, sans-serif;
    background-color: var(--af-bg-secondary);
    color: var(--af-text-primary);
    line-height: 1.6;
    font-size: 14px;
    overflow: hidden;
    height: 100vh;
}

/* ==================== 页面加载动画 ==================== */
.af-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: all var(--af-transition-normal);
    backdrop-filter: blur(2px);
}

.af-loading-overlay.af-hidden {
    opacity: 0;
    visibility: hidden;
}

.af-loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--af-primary-color);
    border-radius: 50%;
    animation: af-spin 1s linear infinite;
}

@keyframes af-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 进度条样式 ==================== */
.af-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--af-primary-color), var(--af-primary-light));
    z-index: 10000;
    transition: width var(--af-transition-normal);
    width: 0%;
    box-shadow: 0 0 10px rgba(255, 107, 157, 0.5);
}

/* ==================== 头部样式 ==================== */
.af-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--af-header-height);
    background-color: var(--af-bg-primary);
    border-bottom: 1px solid var(--af-border-color);
    box-shadow: var(--af-shadow-light);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.af-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.af-header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* ==================== Logo区域 ==================== */
.af-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 18px;
    color: var(--af-text-primary);
}

.af-logo-icon {
    font-size: 24px;
    color: var(--af-primary-color);
}

.af-logo-text {
    font-weight: 700;
    background: linear-gradient(135deg, var(--af-primary-color), var(--af-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ==================== 菜单切换按钮 ==================== */
.af-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 24px;
    height: 18px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    transition: all var(--af-transition-fast);
}

.af-menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--af-primary-color);
    border-radius: 1px;
    transition: all var(--af-transition-fast);
}

.af-menu-toggle:hover span {
    background-color: var(--af-primary-dark);
}

.af-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.af-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.af-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ==================== 侧边栏样式 ==================== */
.af-sidebar {
    position: fixed;
    top: var(--af-header-height);
    left: 0;
    width: var(--af-sidebar-width);
    height: calc(100vh - var(--af-header-height));
    background-color: var(--af-bg-primary);
    border-right: 1px solid var(--af-border-color);
    box-shadow: var(--af-shadow-light);
    z-index: 999;
    overflow-y: auto;
    overflow-x: hidden;
    transition: transform var(--af-transition-normal);
    transform: translateX(0);
}

.af-sidebar.af-hidden {
    transform: translateX(-100%);
}

.af-sidebar-content {
    padding: 20px 0;
}

/* ==================== 导航列表样式 ==================== */
.af-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.af-nav-item {
    margin: 0;
}

.af-nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--af-text-primary);
    text-decoration: none;
    transition: all var(--af-transition-fast);
    border-left: 3px solid transparent;
}

.af-nav-link:hover {
    background-color: var(--af-primary-light);
    color: var(--af-primary-color);
    border-left-color: var(--af-primary-color);
}

.af-nav-item.af-active .af-nav-link {
    background-color: var(--af-primary-color);
    color: white;
    border-left-color: var(--af-primary-dark);
}

.af-nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
    font-size: 16px;
}

.af-nav-text {
    font-size: 14px;
    font-weight: 500;
}

/* ==================== 主内容区域 ==================== */
.af-main-content {
    margin-left: var(--af-sidebar-width);
    margin-top: var(--af-header-height);
    width: calc(100% - var(--af-sidebar-width));
    height: calc(100vh - var(--af-header-height));
    background-color: var(--af-bg-secondary);
    transition: all var(--af-transition-normal);
    overflow: hidden;
}

.af-main-content.af-expanded {
    margin-left: 0;
    width: 100%;
}

.af-page-content {
    padding: 30px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

/* ==================== 欢迎消息样式 ==================== */
.af-welcome-message {
    text-align: center;
    padding: 60px 20px;
    background-color: var(--af-bg-primary);
    border-radius: var(--af-radius-large);
    box-shadow: var(--af-shadow-light);
    margin: 0 auto;
    max-width: 600px;
}

.af-welcome-message h1 {
    color: var(--af-text-primary);
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 15px 0;
    background: linear-gradient(135deg, var(--af-primary-color), var(--af-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.af-welcome-message p {
    color: var(--af-text-secondary);
    font-size: 16px;
    margin: 0;
    line-height: 1.6;
}

/* ==================== 移动端遮罩层 ==================== */
.af-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--af-transition-normal);
    backdrop-filter: blur(2px);
}

.af-sidebar-overlay.af-active {
    opacity: 1;
    visibility: visible;
}

/* ==================== 滚动条样式 ==================== */
.af-sidebar::-webkit-scrollbar,
.af-page-content::-webkit-scrollbar {
    width: 6px;
}

.af-sidebar::-webkit-scrollbar-track,
.af-page-content::-webkit-scrollbar-track {
    background: var(--af-bg-tertiary);
    border-radius: 3px;
}

.af-sidebar::-webkit-scrollbar-thumb,
.af-page-content::-webkit-scrollbar-thumb {
    background: var(--af-primary-color);
    border-radius: 3px;
}

.af-sidebar::-webkit-scrollbar-thumb:hover,
.af-page-content::-webkit-scrollbar-thumb:hover {
    background: var(--af-primary-dark);
}

/* ==================== 响应式设计 ==================== */
/* 平板设备 */
@media (max-width: 1024px) {
    :root {
        --af-sidebar-width: 220px;
    }

    .af-page-content {
        padding: 20px;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    :root {
        --af-sidebar-width: 280px;
    }

    .af-sidebar {
        transform: translateX(-100%);
        box-shadow: var(--af-shadow-medium);
    }

    .af-sidebar.af-active {
        transform: translateX(0);
    }

    .af-main-content {
        margin-left: 0;
        width: 100%;
    }

    .af-page-content {
        padding: 15px;
    }

    .af-welcome-message {
        padding: 40px 15px;
    }

    .af-welcome-message h1 {
        font-size: 24px;
    }

    .af-welcome-message p {
        font-size: 14px;
    }
}

/* 小屏幕移动设备 */
@media (max-width: 480px) {
    :root {
        --af-header-height: 55px;
        --af-sidebar-width: 260px;
    }

    .af-header {
        padding: 0 15px;
    }

    .af-logo-text {
        font-size: 16px;
    }

    .af-page-content {
        padding: 10px;
    }

    .af-welcome-message {
        padding: 30px 10px;
    }

    .af-welcome-message h1 {
        font-size: 20px;
    }

    .af-nav-link {
        padding: 15px 20px;
    }

    .af-nav-text {
        font-size: 15px;
    }
}