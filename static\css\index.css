/* 认证提示模态框样式 (支持Token过期和会话超时) */
        .session-timeout-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .session-timeout-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .session-timeout-content {
            background-color: #fff;
            padding: 2rem;
            border-radius: 12px;
            max-width: 480px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .session-timeout-title {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.4rem;
        }

        .session-timeout-message {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .session-timeout-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #ff7eb9;
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
        }

        /* 用户头像样式 - 减小尺寸 */
        .user-avatar {
            width: 28px;
            height: 28px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #ff7eb9;
            color: white;
            border-radius: 50%;
        }

        /* 防止页面整体滚动 */
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        /* 进度条样式 */
        .nprogress-bar {
            height: 3px;
            background: #ff7eb9;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 9999;
            transition: width 0.4s;
        }
        
        /* 骨架屏样式 */
        .skeleton {
            background: #f0f0f0;
            border-radius: 4px;
            animation: pulse 1.5s infinite;
            margin-bottom: 15px;
        }
        
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }
        
        /* 页面过渡动画 */
        #page-content {
            position: relative;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .page-exit {
            opacity: 0;
            transform: translateY(10px);
        }
        
        .page-enter {
            opacity: 0;
            transform: translateY(-10px);
            animation: fadeIn 0.3s forwards;
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }