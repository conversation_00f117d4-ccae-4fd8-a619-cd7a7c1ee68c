:root {
            --primary-pink: #ff7eb9;
            --deep-pink: #ff5ca8;
            --light-pink: #ffecf7;
            --glass-bg: rgba(255, 255, 255, 0.95);
            --border-color: #ffe6f2;
            --shadow-color: rgba(255, 126, 185, 0.2);
            --success-color: #ff5ca8;
            --error-color: #ff3b30;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #fff5f8 0%, #ffecf2 100%);
            background-size: 400% 400%;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            animation: bgGradient 15s ease infinite;
            position: relative;
            overflow: hidden;
        }

        /* 背景装饰元素 */
        body::before, body::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            z-index: -1;
            opacity: 0.4;
        }

        body::before {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, #ff9ecb, #ff7eb9);
            top: -100px;
            right: -100px;
            animation: floatBubble 20s ease-in-out infinite;
        }

        body::after {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, #ffb8d9, #ff9ecb);
            bottom: -50px;
            left: -50px;
            animation: floatBubble 15s ease-in-out infinite 2s;
        }

        @keyframes floatBubble {
            0%, 100% {
                transform: translate(0, 0) rotate(0deg);
            }
            33% {
                transform: translate(50px, 50px) rotate(10deg);
            }
            66% {
                transform: translate(-30px, 20px) rotate(-8deg);
            }
        }

        @keyframes bgGradient {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        .admin-box {
            width: 100%;
            max-width: 380px;
            background: var(--glass-bg);
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 15px 35px var(--shadow-color);
            transform: translateY(0);
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            border: 2px solid var(--border-color);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 2;
        }

        .admin-box:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(255, 154, 193, 0.3);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
        }

        .logo {
            width: 75px;
            height: 75px;
            background: linear-gradient(135deg, var(--primary-pink), var(--deep-pink));
            border-radius: 18px;
            margin: 0 auto 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(255, 154, 193, 0.3);
            animation: logoFloat 4s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .logo::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 200%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(255, 255, 255, 0.3), 
                transparent);
            animation: logoShine 6s infinite;
        }

        @keyframes logoShine {
            100% {
                left: 100%;
            }
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0) scale(1);
            }
            50% {
                transform: translateY(-10px) scale(1.05);
            }
        }

        .logo svg {
            width: 40px;
            height: 40px;
            fill: white;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            z-index: 2;
        }

        h1 {
            color: #333;
            font-size: 1.8rem;
            letter-spacing: 0.5px;
            font-weight: 600;
            margin-top: 10px;
            background: linear-gradient(135deg, #333, #555);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            display: inline-block;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-pink), var(--deep-pink));
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .admin-box:hover h1::after {
            width: 80px;
        }

        .input-container {
            position: relative;
            margin-bottom: 1.8rem;
        }

        .input-field {
            width: 100%;
            padding: 1.1rem 1.1rem 1.1rem 48px;
            border: 2px solid var(--border-color);
            border-radius: 14px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 10px rgba(255, 154, 193, 0.1);
        }

        .input-field:focus {
            border-color: var(--primary-pink);
            box-shadow: 0 8px 15px rgba(255, 154, 193, 0.2);
            outline: none;
            transform: translateY(-3px);
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-pink);
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .input-field:focus + .input-icon {
            color: var(--deep-pink);
            transform: translateY(-50%) scale(1.1);
        }

        .login-btn {
            width: 100%;
            padding: 1.1rem;
            background: linear-gradient(135deg, var(--primary-pink), var(--deep-pink));
            border: none;
            border-radius: 14px;
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 8px 15px rgba(255, 154, 193, 0.25);
            margin-top: 10px;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 200%;
            height: 100%;
            background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.3),
                    transparent);
            animation: btnShine 4s infinite;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(255, 154, 193, 0.35);
        }

        .login-btn:active {
            transform: translateY(1px);
            box-shadow: 0 4px 8px rgba(255, 154, 193, 0.2);
        }

        @keyframes btnShine {
            100% {
                left: 100%;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s infinite linear;
            margin-right: 10px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .error-msg {
            color: var(--error-color);
            text-align: center;
            margin-top: 1.2rem;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.4s ease;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(255, 200, 210, 0.2);
            box-shadow: 0 4px 6px rgba(255, 81, 123, 0.1);
        }

        .error-show {
            opacity: 1;
            transform: translateY(0);
        }

        /* 初始化状态样式 */
        .init-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg);
            padding: 12px 18px;
            border-radius: 12px;
            font-size: 14px;
            color: #333;
            box-shadow: 0 8px 15px rgba(255, 154, 193, 0.15);
            opacity: 0;
            transform: translateY(-15px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 2px solid var(--border-color);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            z-index: 100;
        }

        .init-status.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* 重试按钮样式 */
        .retry-btn {
            display: block;
            margin: 1.2rem auto 0;
            padding: 0.8rem 1.2rem;
            background-color: #ffeef7;
            border: 2px solid #ffd6e6;
            border-radius: 10px;
            color: var(--primary-pink);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(255, 154, 193, 0.1);
        }

        .retry-btn:hover {
            background-color: #ffd6e6;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(255, 154, 193, 0.2);
        }

        /* 响应式样式调整 */
        @media (max-width: 480px) {
            .admin-box {
                padding: 2rem;
                border-radius: 20px;
            }

            .logo {
                width: 65px;
                height: 65px;
            }

            h1 {
                font-size: 1.5rem;
            }

            .input-field, .login-btn {
                padding: 1rem;
            }
        }