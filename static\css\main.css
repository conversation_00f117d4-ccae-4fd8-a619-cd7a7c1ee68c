/**
 * 主样式文件 - Main Stylesheet
 * 企业级前端设计系统的入口文件
 * 
 * 导入顺序:
 * 1. 设计令牌 (Design Tokens)
 * 2. 基础样式 (Base Styles)
 * 3. 组件样式 (Component Styles)
 * 4. 页面特定样式 (Page-specific Styles)
 * 5. 实用工具 (Utilities)
 */

/* ========================================
 * 设计系统核心文件导入
 * ======================================== */

/* 设计令牌 - 必须首先导入 */
@import url('./design-system/tokens.css');

/* 品牌令牌 - 品牌特定的设计令牌 */
@import url('./design-system/brand-tokens.css');

/* 语义化令牌 - 将基础令牌映射到具体使用场景 */
@import url('./design-system/semantic-tokens.css');

/* 基础样式 - CSS重置和全局样式 */
@import url('./design-system/base.css');

/* 组件样式 - 可复用组件 */
@import url('./design-system/components.css');

/* ========================================
 * 页面布局样式
 * ======================================== */

/* 主要布局容器 */
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  margin-top: var(--navbar-height);
  padding: var(--spacing-6) 0;
}

/* 页面标题区域 */
.page-header {
  background-color: var(--color-bg-secondary);
  border-bottom: var(--border-width-1) solid var(--color-border-secondary);
  padding: var(--spacing-8) 0;
  margin-bottom: var(--spacing-8);
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

/* 页脚 */
.footer {
  background-color: var(--color-bg-secondary);
  border-top: var(--border-width-1) solid var(--color-border-secondary);
  padding: var(--spacing-8) 0;
  margin-top: auto;
}

.footer__content {
  text-align: center;
  color: var(--color-text-secondary);
}

/* ========================================
 * 商品相关组件
 * ======================================== */

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
}

/* 商品卡片 */
.product-card {
  background-color: var(--color-bg-primary);
  border: var(--border-width-1) solid var(--color-border-secondary);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  transition: all var(--transition-duration-base) var(--transition-timing-function);
  box-shadow: var(--shadow-sm);
}

.product-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--color-primary);
}

.product-card__image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background-color: var(--color-bg-tertiary);
}

.product-card__content {
  padding: var(--spacing-4);
}

.product-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-tight);
}

.product-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-normal);
}

.product-card__price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-success);
  margin-bottom: var(--spacing-4);
}

.product-card__actions {
  display: flex;
  gap: var(--spacing-2);
}

/* ========================================
 * 筛选和排序组件
 * ======================================== */

.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-bg-primary);
  border: var(--border-width-1) solid var(--color-border-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.filter-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.sort-options {
  display: flex;
  gap: var(--spacing-2);
}

.sort-option {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: transparent;
  border: var(--border-width-1) solid var(--color-border-secondary);
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: all var(--transition-duration-base) var(--transition-timing-function);
}

.sort-option:hover {
  color: var(--color-primary);
  border-color: var(--color-primary);
  background-color: var(--color-primary-lighter);
}

.sort-option--active {
  color: var(--color-primary);
  border-color: var(--color-primary);
  background-color: var(--color-primary-lighter);
}

/* ========================================
 * 分页组件
 * ======================================== */

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin: var(--spacing-8) 0;
}

.pagination__item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background-color: var(--color-bg-primary);
  border: var(--border-width-1) solid var(--color-border-secondary);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-duration-base) var(--transition-timing-function);
  text-decoration: none;
}

.pagination__item:hover {
  color: var(--color-primary);
  border-color: var(--color-primary);
  background-color: var(--color-primary-lighter);
}

.pagination__item--active {
  color: var(--color-text-inverse);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.pagination__item--disabled {
  color: var(--color-text-disabled);
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination__item--disabled:hover {
  color: var(--color-text-disabled);
  border-color: var(--color-border-secondary);
  background-color: var(--color-bg-primary);
}

/* ========================================
 * 状态页面样式
 * ======================================== */

.status-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: var(--spacing-8);
}

.status-page__icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-4);
}

.status-page__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
}

.status-page__message {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-6);
  max-width: 600px;
}

.status-page__actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
  justify-content: center;
}

/* 成功状态 */
.status-page--success .status-page__icon {
  color: var(--color-success);
}

.status-page--success .status-page__title {
  color: var(--color-success);
}

/* 错误状态 */
.status-page--error .status-page__icon {
  color: var(--color-error);
}

.status-page--error .status-page__title {
  color: var(--color-error);
}

/* 警告状态 */
.status-page--warning .status-page__icon {
  color: var(--color-warning);
}

.status-page--warning .status-page__title {
  color: var(--color-warning);
}

/* ========================================
 * 响应式调整
 * ======================================== */

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4) 0;
  }
  
  .page-header {
    padding: var(--spacing-6) 0;
    margin-bottom: var(--spacing-6);
  }
  
  .page-title {
    font-size: var(--font-size-3xl);
  }
  
  .filter-bar {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  
  .sort-options {
    flex-wrap: wrap;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .status-page__actions {
    flex-direction: column;
    align-items: center;
  }
}
