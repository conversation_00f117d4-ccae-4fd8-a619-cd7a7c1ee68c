/*
 * 移动端响应式布局专用样式文件 - 布局层次结构重构版
 * 针对后台管理系统的移动端优化
 * 设计原则：布局层次结构 + 功能性突出 + 可读性保证
 * 使用 mr- 前缀确保样式隔离，避免与现有样式冲突
 */

/* ========== 移动端布局层次系统 ========== */
:root {
    /* 层次间距系统 - 通过间距创建视觉层级 */
    --mr-space-xs: 4px;    /* 最小间距 - 紧密相关元素 */
    --mr-space-sm: 8px;    /* 小间距 - 相关元素组 */
    --mr-space-md: 16px;   /* 中等间距 - 功能区块 */
    --mr-space-lg: 24px;   /* 大间距 - 主要区域分隔 */
    --mr-space-xl: 32px;   /* 超大间距 - 页面级分隔 */
    --mr-space-xxl: 48px;  /* 巨大间距 - 强调分隔 */

    /* 层次字体系统 - 通过字体大小创建信息层级 */
    --mr-text-xs: 0.75rem;   /* 12px - 辅助信息 */
    --mr-text-sm: 0.875rem;  /* 14px - 次要信息 */
    --mr-text-base: 1rem;    /* 16px - 基础文字 */
    --mr-text-lg: 1.125rem;  /* 18px - 重要信息 */
    --mr-text-xl: 1.25rem;   /* 20px - 标题级别 */
    --mr-text-2xl: 1.5rem;   /* 24px - 主标题 */
    --mr-text-3xl: 1.875rem; /* 30px - 页面标题 */

    /* 层次色彩系统 - 简洁的层级区分 */
    --mr-color-primary: #ff6b9d;      /* 主要强调色 */
    --mr-color-text-primary: #1a1a1a; /* 一级文字 - 最重要 */
    --mr-color-text-secondary: #4a4a4a; /* 二级文字 - 重要 */
    --mr-color-text-tertiary: #8a8a8a;  /* 三级文字 - 次要 */
    --mr-color-text-quaternary: #b8b8b8; /* 四级文字 - 辅助 */

    /* 层次背景系统 */
    --mr-bg-primary: #ffffff;    /* 主要内容背景 */
    --mr-bg-secondary: #fafafa;  /* 次要内容背景 */
    --mr-bg-tertiary: #f5f5f5;   /* 辅助内容背景 */

    /* 层次阴影系统 - 简洁的深度表现 */
    --mr-shadow-1: 0 1px 3px rgba(0, 0, 0, 0.1);  /* 轻微浮起 */
    --mr-shadow-2: 0 2px 6px rgba(0, 0, 0, 0.1);  /* 明显浮起 */
    --mr-shadow-3: 0 4px 12px rgba(0, 0, 0, 0.1); /* 强烈浮起 */

    /* 层次圆角系统 */
    --mr-radius-sm: 6px;
    --mr-radius-md: 8px;
    --mr-radius-lg: 12px;
}

/* 移动端媒体查询 - 768px及以下设备 */
@media (max-width: 768px) {

    /* ========== 全局移动端布局层次调整 ========== */

    /* 主内容区域 - 移除粉色背景，使用白色填充整个区域 */
    .af-main-content {
        background-color: var(--mr-bg-primary) !important;
        padding: 0 !important;
        margin: 0 !important;
        margin-top: var(--af-header-height) !important;
        height: calc(100vh - var(--af-header-height)) !important;
    }

    /* 页面内容容器 - 层次化间距设计 */
    .af-page-content {
        background-color: var(--mr-bg-primary) !important;
        padding: var(--mr-space-lg) var(--mr-space-md) !important;
        margin: 0 !important;
        height: 100% !important;
        overflow-y: auto !important;
        overflow-x: hidden !important; /* 防止页面横向滚动 */
        box-sizing: border-box !important;
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* ========== 页面容器层次结构 ========== */

    /* 所有页面的主容器 - 简洁布局 */
    #dashboard-container,
    #product-container,
    #category-container,
    #cardstock-container,
    #coupon-container,
    #price-template-container,
    #docking-container,
    #payment-container,
    #plugin-container,
    #usergroup-container {
        background-color: transparent !important;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        height: 100% !important;
        box-sizing: border-box !important;
        overflow-y: auto !important;
        overflow-x: hidden !important; /* 防止页面容器横向滚动 */
    }

    /* ========== 卡片容器层次结构 ========== */

    /* 主要功能卡片 - 美化设计 */
    .products-card,
    .categories-card,
    .cardstock-card,
    .coupon-card,
    .price-template-card,
    .docking-card,
    .payment-card,
    .plugin-card,
    .usergroup-card {
        background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important; /* 微妙渐变 */
        border-radius: var(--mr-radius-lg) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05) !important; /* 层次阴影 */
        padding: 24px 20px !important; /* 适中的内边距 */
        margin: 0 0 20px 0 !important;
        border: 1px solid rgba(255, 107, 157, 0.08) !important; /* 微妙的彩色边框 */
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    /* 卡片悬浮效果 */
    .products-card:hover,
    .categories-card:hover,
    .cardstock-card:hover,
    .coupon-card:hover,
    .price-template-card:hover,
    .docking-card:hover,
    .payment-card:hover,
    .plugin-card:hover,
    .usergroup-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08) !important;
    }

    /* 卡片顶部装饰条 */
    .products-card::before,
    .categories-card::before,
    .cardstock-card::before,
    .coupon-card::before,
    .price-template-card::before,
    .docking-card::before,
    .payment-card::before,
    .plugin-card::before,
    .usergroup-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--mr-color-primary) 0%, #ff8fab 50%, #ffa6d2 100%) !important;
        border-radius: var(--mr-radius-lg) var(--mr-radius-lg) 0 0;
    }

    /* 次要信息卡片 - 协调的美化设计 */
    .notice-card,
    .chart-card {
        background: linear-gradient(145deg, #fafafa 0%, #ffffff 100%) !important; /* 轻微渐变 */
        border-radius: var(--mr-radius-md) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04) !important; /* 轻微阴影 */
        padding: 18px !important; /* 适中的内边距 */
        margin: 0 0 16px 0 !important;
        border: 1px solid rgba(255, 107, 157, 0.05) !important; /* 极淡的彩色边框 */
        transition: all 0.2s ease !important;
    }

    .notice-card:hover,
    .chart-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    }
    
    /* ========== 首页数据展示层次结构 ========== */

    /* 统计卡片容器 - 一级层次：最重要的数据展示 */
    .stats-container {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: var(--mr-space-md) !important;
        margin-bottom: var(--mr-space-xxl) !important; /* 大间距突出重要性 */
        padding: 0 !important;
    }

    /* 统计卡片样式 - 美化设计 */
    .stat-card {
        background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important; /* 微妙渐变 */
        border-radius: var(--mr-radius-lg) !important;
        box-shadow: 0 4px 12px rgba(255, 107, 157, 0.12), 0 1px 3px rgba(0, 0, 0, 0.05) !important; /* 彩色阴影 */
        padding: 20px 16px !important; /* 适中的内边距 */
        border: 1px solid rgba(255, 107, 157, 0.1) !important; /* 微妙的彩色边框 */
        text-align: center !important;
        position: relative !important;
        overflow: hidden !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    /* 统计卡片悬浮效果 */
    .stat-card:active {
        transform: scale(0.98) translateY(1px) !important;
        box-shadow: 0 2px 8px rgba(255, 107, 157, 0.15), 0 1px 2px rgba(0, 0, 0, 0.08) !important;
    }

    /* 统计卡片装饰性背景 */
    .stat-card::after {
        content: '';
        position: absolute;
        top: -20px;
        right: -20px;
        width: 60px;
        height: 60px;
        background: radial-gradient(circle, rgba(255, 107, 157, 0.08) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
    }

    /* 数据值 - 最高层次：最大最突出 */
    .stat-card .card-value {
        font-size: var(--mr-text-3xl) !important; /* 30px - 最大字体 */
        font-weight: 800 !important;
        color: var(--mr-color-primary) !important;
        margin: var(--mr-space-sm) 0 !important;
        line-height: 1.2 !important;
    }

    /* 数据标题 - 二级层次：说明性文字 */
    .stat-card .card-title {
        font-size: var(--mr-text-sm) !important; /* 14px */
        font-weight: 500 !important;
        color: var(--mr-color-text-secondary) !important;
        margin-bottom: var(--mr-space-xs) !important;
    }

    /* 趋势信息 - 三级层次：辅助信息 */
    .stat-card .trend {
        font-size: var(--mr-text-xs) !important; /* 12px */
        color: var(--mr-color-text-tertiary) !important;
        margin-top: var(--mr-space-sm) !important;
    }

    /* 图标 - 视觉辅助，不抢夺数据焦点 */
    .stat-card .card-icon {
        width: 40px !important;
        height: 40px !important;
        margin-bottom: var(--mr-space-sm) !important;
        background-color: rgba(255, 107, 157, 0.1) !important;
        border-radius: var(--mr-radius-sm) !important;
    }

    /* 导航卡片容器 - 二级层次：功能导航 */
    .nav-cards-container {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: var(--mr-space-sm) !important;
        margin-bottom: var(--mr-space-xl) !important;
        padding: 0 !important;
    }

    /* 导航卡片样式 - 精致美化设计 */
    .quick-nav-card {
        background: linear-gradient(145deg, #fafafa 0%, #ffffff 100%) !important; /* 轻微渐变 */
        border-radius: var(--mr-radius-md) !important;
        box-shadow: 0 2px 6px rgba(255, 107, 157, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04) !important; /* 彩色阴影 */
        padding: 14px 10px !important; /* 适中的内边距 */
        border: 1px solid rgba(255, 107, 157, 0.06) !important; /* 极淡的彩色边框 */
        text-align: center !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    /* 导航卡片交互反馈 */
    .quick-nav-card:active {
        transform: scale(0.96) translateY(1px) !important;
        background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
        box-shadow: 0 4px 10px rgba(255, 107, 157, 0.12), 0 1px 3px rgba(0, 0, 0, 0.06) !important;
    }

    /* 导航卡片微妙的装饰效果 */
    .quick-nav-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 107, 157, 0.2) 50%, transparent 100%);
    }

    .quick-nav-card .nav-icon {
        width: 36px !important;
        height: 36px !important;
        margin-bottom: var(--mr-space-sm) !important;
        background-color: rgba(255, 107, 157, 0.08) !important;
        border-radius: var(--mr-radius-sm) !important;
    }

    .quick-nav-card .nav-icon i {
        color: var(--mr-color-primary) !important;
        font-size: var(--mr-text-lg) !important;
    }

    .quick-nav-card .nav-title {
        font-size: var(--mr-text-xs) !important; /* 12px */
        font-weight: 500 !important;
        color: var(--mr-color-text-secondary) !important;
    }

    /* 仪表板行 - 三级层次：详细信息区域 */
    .dashboard-row {
        display: flex !important;
        flex-direction: column !important;
        gap: var(--mr-space-lg) !important;
        margin-top: var(--mr-space-xl) !important;
    }
    
    /* ========== 表格移动端优化 - 可读性与滑动体验并重 ========== */

    /* 表格容器滑动优化 - 只在移动端生效 */
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        scrollbar-width: thin !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* 卡片容器不允许横向滚动 */
    .products-card,
    .categories-card,
    .cardstock-card,
    .coupon-card,
    .price-template-card,
    .docking-card,
    .payment-card,
    .plugin-card,
    .usergroup-card {
        overflow-x: hidden !important; /* 防止卡片横向滚动 */
        overflow-y: visible !important;
    }

    /* 简洁的滚动条样式 - 只针对表格容器 */
    .table-responsive::-webkit-scrollbar {
        height: 4px !important;
        width: 4px !important;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: rgba(255, 107, 157, 0.1) !important;
        border-radius: 2px !important;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: var(--mr-color-primary) !important;
        border-radius: 2px !important;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #ff5a8a !important;
    }

    /* 表格样式 - 保持完整可读性，通过滑动解决空间问题 */
    .products-table,
    .categories-table,
    .cardstock-table,
    .coupon-table,
    .price-template-table,
    .docking-table,
    .payment-table,
    .plugin-table,
    .usergroup-table {
        font-size: var(--mr-text-base) !important; /* 16px - 保持最佳可读性 */
        min-width: 900px !important; /* 足够的宽度确保内容不被压缩 */
        background-color: var(--mr-bg-primary) !important;
        border-radius: var(--mr-radius-md) !important;
        overflow: hidden !important;
        box-shadow: var(--mr-shadow-1) !important;
        border: 1px solid #e5e5e5 !important;
    }

    /* 表格头部 - 突出层次 */
    .products-table th,
    .categories-table th,
    .cardstock-table th,
    .coupon-table th,
    .price-template-table th,
    .docking-table th,
    .payment-table th,
    .plugin-table th,
    .usergroup-table th {
        background-color: var(--mr-color-primary) !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: var(--mr-text-base) !important; /* 16px - 与内容同等重要 */
        padding: var(--mr-space-md) var(--mr-space-sm) !important;
        border: none !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
        text-align: left !important;
    }

    /* 表格单元格 - 保持舒适的阅读体验 */
    .products-table td,
    .categories-table td,
    .cardstock-table td,
    .coupon-table td,
    .price-template-table td,
    .docking-table td,
    .payment-table td,
    .plugin-table td,
    .usergroup-table td {
        padding: var(--mr-space-md) var(--mr-space-sm) !important;
        font-size: var(--mr-text-base) !important; /* 16px - 保持可读性 */
        border-bottom: 1px solid #f0f0f0 !important;
        color: var(--mr-color-text-primary) !important;
        line-height: 1.5 !important;
    }

    /* 表格行交互反馈 */
    .products-table tbody tr:active,
    .categories-table tbody tr:active,
    .cardstock-table tbody tr:active,
    .coupon-table tbody tr:active,
    .price-template-table tbody tr:active,
    .docking-table tbody tr:active,
    .payment-table tbody tr:active,
    .plugin-table tbody tr:active,
    .usergroup-table tbody tr:active {
        background-color: var(--mr-bg-secondary) !important;
    }

    /* 商品图片 - 保持清晰可见 */
    .product-image {
        width: 60px !important; /* 足够大以保持清晰 */
        height: 60px !important;
        border-radius: var(--mr-radius-sm) !important;
        box-shadow: var(--mr-shadow-1) !important;
        object-fit: cover !important;
    }

    /* 操作按钮 - 符合移动端触摸标准 */
    .action-btn {
        width: 44px !important; /* 符合44px最小触摸面积标准 */
        height: 44px !important;
        border-radius: 50% !important;
        box-shadow: var(--mr-shadow-1) !important;
        transition: all 0.2s ease !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .action-btn:active {
        transform: scale(0.9) !important;
        box-shadow: var(--mr-shadow-2) !important;
    }

    .action-buttons {
        gap: var(--mr-space-sm) !important;
        justify-content: center !important;
        align-items: center !important;
    }

    /* ========== 表单和按钮层次结构 ========== */

    /* 主要操作按钮 - 合理美观的设计 */
    .btn-primary,
    .btn.btn-primary {
        font-size: 14px !important; /* 合理的字体大小 */
        font-weight: 500 !important;
        padding: 10px 20px !important; /* 适中的内边距 */
        border-radius: var(--mr-radius-md) !important;
        min-height: 40px !important; /* 适中的高度 */
        box-shadow: var(--mr-shadow-1) !important;
        border: none !important;
        transition: all 0.2s ease !important;
    }

    .btn-primary:active,
    .btn.btn-primary:active {
        transform: scale(0.98) !important;
        box-shadow: var(--mr-shadow-2) !important;
    }

    /* 次要操作按钮 - 协调的设计 */
    .btn-light,
    .btn-outline,
    .btn.btn-light,
    .btn.btn-outline {
        font-size: 13px !important; /* 稍小的字体 */
        font-weight: 400 !important;
        padding: 8px 16px !important; /* 紧凑的内边距 */
        border-radius: var(--mr-radius-sm) !important;
        min-height: 36px !important;
        box-shadow: var(--mr-shadow-1) !important;
        transition: all 0.2s ease !important;
    }

    .btn-light:active,
    .btn-outline:active,
    .btn.btn-light:active,
    .btn.btn-outline:active {
        transform: scale(0.98) !important;
        background-color: var(--mr-bg-secondary) !important;
    }

    /* 危险操作按钮 - 协调的设计 */
    .btn-danger,
    .btn.btn-danger {
        font-size: 13px !important;
        font-weight: 500 !important;
        padding: 8px 16px !important;
        border-radius: var(--mr-radius-sm) !important;
        min-height: 36px !important;
        box-shadow: var(--mr-shadow-1) !important;
        transition: all 0.2s ease !important;
    }

    /* 表单控件 - 保持良好的输入体验 */
    .form-control,
    input.form-control,
    textarea.form-control,
    select.form-control {
        font-size: var(--mr-text-base) !important; /* 16px - 防止iOS缩放 */
        padding: var(--mr-space-md) !important;
        border-radius: var(--mr-radius-md) !important;
        border: 2px solid #e5e5e5 !important;
        background-color: var(--mr-bg-primary) !important;
        color: var(--mr-color-text-primary) !important;
        min-height: 48px !important;
        transition: border-color 0.2s ease !important;
    }

    .form-control:focus,
    input.form-control:focus,
    textarea.form-control:focus,
    select.form-control:focus {
        border-color: var(--mr-color-primary) !important;
        box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1) !important;
        outline: none !important;
    }

    /* 表单标签 - 清晰的层次指导 */
    .form-label,
    label.form-label {
        font-size: var(--mr-text-base) !important; /* 16px */
        font-weight: 600 !important;
        color: var(--mr-color-text-primary) !important;
        margin-bottom: var(--mr-space-sm) !important;
        display: block !important;
    }

    /* 表单组在移动端的间距优化 */
    .form-group {
        margin-bottom: var(--mr-space-lg) !important;
    }

    /* 表单行在移动端垂直排列 */
    .form-row {
        flex-direction: column !important;
        gap: var(--mr-space-md) !important;
    }

    .form-row .form-group {
        width: 100% !important;
        margin-bottom: var(--mr-space-md) !important;
    }

    /* ========== 搜索和筛选区域移动端优化 ========== */

    /* 搜索栏在移动端的优化 */
    .search-container,
    .filter-container {
        flex-direction: column !important;
        gap: var(--mr-space-md) !important;
        margin-bottom: var(--mr-space-lg) !important;
    }

    .search-input,
    .filter-select {
        width: 100% !important;
        margin-bottom: var(--mr-space-sm) !important;
    }

    /* 搜索按钮在移动端全宽显示 */
    .search-btn {
        width: 100% !important;
        justify-content: center !important;
    }

    /* 筛选标签在移动端的优化 */
    .filter-tags {
        flex-wrap: wrap !important;
        gap: var(--mr-space-xs) !important;
        margin-top: var(--mr-space-sm) !important;
    }

    .filter-tag {
        font-size: var(--mr-text-sm) !important;
        padding: 4px 8px !important;
        border-radius: var(--mr-radius-sm) !important;
    }

    /* ========== 分页组件移动端优化 ========== */

    /* 分页容器在移动端的优化 */
    .pagination-container {
        flex-direction: column !important;
        align-items: center !important;
        gap: var(--mr-space-md) !important;
        margin-top: var(--mr-space-lg) !important;
    }

    /* 分页按钮在移动端的优化 */
    .pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: var(--mr-space-xs) !important;
    }

    .pagination .page-link {
        min-width: 40px !important;
        min-height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: var(--mr-text-sm) !important;
        border-radius: var(--mr-radius-sm) !important;
    }

    /* 分页信息在移动端的显示 */
    .pagination-info {
        font-size: var(--mr-text-sm) !important;
        text-align: center !important;
        color: var(--mr-color-text-secondary) !important;
    }

    /* 卡片标题 - 突出内容区域的重要性 */
    .card-title {
        font-size: var(--mr-text-2xl) !important; /* 24px - 页面级标题 */
        font-weight: 700 !important;
        color: var(--mr-color-text-primary) !important;
        margin-bottom: var(--mr-space-lg) !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: var(--mr-space-md) !important;
    }

    .card-actions {
        width: 100% !important;
        display: flex !important;
        justify-content: flex-start !important; /* 移动端左对齐更自然 */
        gap: var(--mr-space-sm) !important;
        margin-top: var(--mr-space-md) !important;
        flex-wrap: wrap !important; /* 允许按钮换行 */
    }

    /* 页面标题 - 最高层次 */
    .page-title {
        font-size: var(--mr-text-3xl) !important; /* 30px - 最大标题 */
        font-weight: 800 !important;
        color: var(--mr-color-text-primary) !important;
        margin-bottom: var(--mr-space-lg) !important;
    }

    /* 页面头部 - 重要功能区域的层次结构 */
    .page-header {
        background-color: var(--mr-bg-primary) !important;
        border-radius: var(--mr-radius-lg) !important;
        box-shadow: var(--mr-shadow-2) !important;
        padding: var(--mr-space-xl) var(--mr-space-lg) !important;
        margin-bottom: var(--mr-space-xl) !important;
        border: 1px solid #e5e5e5 !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: var(--mr-space-lg) !important;
    }

    .action-buttons {
        width: 100% !important;
        display: flex !important;
        justify-content: flex-start !important; /* 移动端左对齐 */
        gap: var(--mr-space-sm) !important;
        flex-wrap: wrap !important;
        margin-top: var(--mr-space-md) !important;
    }

    /* 主要操作按钮在移动端优先显示 */
    .action-buttons .btn-primary {
        order: -1 !important; /* 主要按钮排在前面 */
        margin-bottom: var(--mr-space-sm) !important;
    }

    /* ========== 模态框和弹窗美化设计 ========== */

    /* 模态框 - 等比例缩小，左右留空 */
    .modal-content {
        width: calc(100% - 32px) !important; /* 左右各留16px空余 */
        max-width: calc(100% - 32px) !important;
        height: calc(100vh - 80px) !important; /* 上下留40px空余 */
        max-height: calc(100vh - 80px) !important;
        margin: 40px 16px !important; /* 居中并留空 */
        border-radius: var(--mr-radius-lg) !important; /* 圆角美化 */
        border: none !important;
        background-color: var(--mr-bg-primary) !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important; /* 增强阴影 */
        overflow: hidden !important;
    }

    .modal-header {
        background: linear-gradient(135deg, var(--mr-color-primary) 0%, #ff8fab 100%) !important; /* 渐变美化 */
        color: white !important;
        padding: 20px !important; /* 适中的内边距 */
        border-bottom: none !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 100 !important;
    }

    .modal-title {
        font-size: 18px !important; /* 合理的标题大小 */
        font-weight: 600 !important;
        color: white !important;
    }

    .modal-body {
        padding: 20px !important; /* 适中的内边距 */
        max-height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
        background-color: var(--mr-bg-primary) !important;
    }

    .modal-footer {
        padding: 16px 20px !important; /* 适中的内边距 */
        border-top: 1px solid #e5e5e5 !important;
        background-color: #fafafa !important; /* 轻微的背景色区分 */
        position: sticky !important;
        bottom: 0 !important;
        gap: 12px !important;
    }

    /* 导航选项卡 - 移动端友好 */
    .nav-tabs {
        flex-wrap: wrap !important;
        border-bottom: 2px solid #e5e5e5 !important;
        margin-bottom: var(--mr-space-lg) !important;
    }

    .nav-tabs .nav-link {
        padding: var(--mr-space-md) var(--mr-space-lg) !important;
        font-size: var(--mr-text-base) !important;
        font-weight: 500 !important;
        margin-right: var(--mr-space-xs) !important;
        margin-bottom: var(--mr-space-xs) !important;
        border-radius: var(--mr-radius-md) var(--mr-radius-md) 0 0 !important;
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .nav-tabs .nav-link.active {
        background-color: var(--mr-color-primary) !important;
        color: white !important;
        border-color: var(--mr-color-primary) !important;
    }

    /* ========== 通知和反馈层次结构 ========== */

    /* 确认对话框 - 重要决策的突出显示 */
    .confirm-box {
        width: 90% !important;
        max-width: 400px !important;
        padding: var(--mr-space-xl) !important;
        border-radius: var(--mr-radius-lg) !important;
        background-color: var(--mr-bg-primary) !important;
        box-shadow: var(--mr-shadow-3) !important;
    }

    .confirm-title {
        font-size: var(--mr-text-xl) !important; /* 20px */
        font-weight: 700 !important;
        margin-bottom: var(--mr-space-lg) !important;
        color: var(--mr-danger-color) !important;
    }

    .confirm-message {
        font-size: var(--mr-text-base) !important;
        line-height: 1.6 !important;
        color: var(--mr-color-text-primary) !important;
        margin-bottom: var(--mr-space-xl) !important;
    }

    .confirm-actions {
        gap: var(--mr-space-md) !important;
        justify-content: flex-end !important;
    }

    /* 通知提示 - 适配移动端位置 */
    .notification {
        top: var(--mr-space-lg) !important;
        right: var(--mr-space-md) !important;
        left: var(--mr-space-md) !important;
        max-width: none !important;
        width: auto !important;
        padding: var(--mr-space-lg) !important;
        border-radius: var(--mr-radius-lg) !important;
        box-shadow: var(--mr-shadow-3) !important;
        font-size: var(--mr-text-base) !important;
    }

    /* ========== 特殊组件移动端优化 ========== */

    /* 类型卡片 - 垂直布局更适合移动端 */
    .type-card-container {
        flex-direction: column !important;
        gap: var(--mr-space-md) !important;
    }

    .type-card {
        padding: var(--mr-space-lg) !important;
        border-radius: var(--mr-radius-lg) !important;
        text-align: left !important;
        display: flex !important;
        align-items: center !important;
        gap: var(--mr-space-lg) !important;
        background-color: var(--mr-bg-secondary) !important;
        border: 2px solid transparent !important;
        transition: all 0.2s ease !important;
        min-height: 80px !important;
    }

    .type-card.selected {
        background-color: var(--mr-bg-primary) !important;
        border-color: var(--mr-color-primary) !important;
        box-shadow: var(--mr-shadow-2) !important;
    }

    .type-card i {
        font-size: var(--mr-text-2xl) !important; /* 24px */
        margin-bottom: 0 !important;
        color: var(--mr-color-primary) !important;
        flex-shrink: 0 !important;
    }

    .type-card h5 {
        font-size: var(--mr-text-lg) !important; /* 18px */
        font-weight: 600 !important;
        margin-bottom: var(--mr-space-xs) !important;
        color: var(--mr-color-text-primary) !important;
    }

    .type-card p {
        font-size: var(--mr-text-sm) !important; /* 14px */
        color: var(--mr-color-text-secondary) !important;
        margin: 0 !important;
    }

    /* ========== 图表和数据可视化移动端优化 ========== */

    /* 图表容器 - 适配移动端高度 */
    .chart-container {
        min-height: 280px !important;
        height: 280px !important;
        width: 100% !important;
    }

    .chart-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: var(--mr-space-md) !important;
        margin-bottom: var(--mr-space-lg) !important;
    }

    .chart-title {
        font-size: var(--mr-text-lg) !important; /* 18px */
        font-weight: 600 !important;
        color: var(--mr-color-text-primary) !important;
    }

    .chart-period {
        width: 100% !important;
        display: flex !important;
        justify-content: space-between !important;
        gap: var(--mr-space-xs) !important;
    }

    .period-btn {
        flex: 1 !important;
        text-align: center !important;
        padding: var(--mr-space-sm) var(--mr-space-xs) !important;
        font-size: var(--mr-text-sm) !important;
        border-radius: var(--mr-radius-md) !important;
        min-height: 36px !important;
    }

    /* ========== 滚动条全局优化 ========== */

    .af-page-content::-webkit-scrollbar {
        width: 4px !important;
    }

    .af-page-content::-webkit-scrollbar-track {
        background: transparent !important;
    }

    .af-page-content::-webkit-scrollbar-thumb {
        background-color: rgba(255, 107, 157, 0.3) !important;
        border-radius: 2px !important;
    }
}

/* ========== 平板设备优化 (769px - 1024px) ========== */
@media (min-width: 769px) and (max-width: 1024px) {

    /* 平板设备保持桌面端布局，但适当调整间距 */
    .stats-container {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: var(--mr-space-md) !important;
    }

    .nav-cards-container {
        grid-template-columns: repeat(5, 1fr) !important;
        gap: var(--mr-space-md) !important;
    }

    .dashboard-row {
        grid-template-columns: 1fr 1.8fr !important;
        gap: var(--mr-space-lg) !important;
    }
}

/* ========== 超小屏幕设备优化 (480px及以下) ========== */
@media (max-width: 480px) {

    /* 超小屏幕的层次结构进一步优化 */
    .af-page-content {
        padding: var(--mr-space-md) var(--mr-space-sm) !important;
    }

    /* 统计卡片 - 单列显示突出重要性 */
    .stats-container {
        grid-template-columns: 1fr !important;
        gap: var(--mr-space-md) !important;
        margin-bottom: var(--mr-space-xl) !important;
    }

    .stat-card .card-value {
        font-size: var(--mr-text-2xl) !important; /* 24px - 仍然突出 */
    }

    /* 导航卡片 - 2列布局保持功能可见性 */
    .nav-cards-container {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: var(--mr-space-sm) !important;
    }

    .quick-nav-card .nav-title {
        font-size: var(--mr-text-xs) !important; /* 12px */
    }

    /* 表格 - 进一步优化滑动体验 */
    .products-table,
    .categories-table,
    .cardstock-table,
    .coupon-table,
    .price-template-table,
    .docking-table,
    .payment-table,
    .plugin-table,
    .usergroup-table {
        min-width: 800px !important; /* 保持足够宽度 */
        font-size: var(--mr-text-sm) !important; /* 14px - 在超小屏幕上适当缩小 */
    }

    .products-table th,
    .products-table td,
    .categories-table th,
    .categories-table td,
    .cardstock-table th,
    .cardstock-table td,
    .coupon-table th,
    .coupon-table td,
    .price-template-table th,
    .price-template-table td,
    .docking-table th,
    .docking-table td,
    .payment-table th,
    .payment-table td,
    .plugin-table th,
    .plugin-table td,
    .usergroup-table th,
    .usergroup-table td {
        padding: var(--mr-space-sm) var(--mr-space-xs) !important;
        font-size: var(--mr-text-sm) !important; /* 14px */
    }

    /* 按钮和表单 - 保持触摸友好 */
    .btn {
        font-size: var(--mr-text-sm) !important;
        padding: var(--mr-space-sm) var(--mr-space-md) !important;
        min-height: 44px !important; /* 保持触摸标准 */
    }

    .form-control {
        font-size: var(--mr-text-base) !important; /* 16px - 防止缩放 */
        padding: var(--mr-space-sm) !important;
    }

    /* 标题层次 - 适配超小屏幕 */
    .page-title {
        font-size: var(--mr-text-2xl) !important; /* 24px */
    }

    .card-title {
        font-size: var(--mr-text-xl) !important; /* 20px */
    }

    /* 图表 - 适配超小屏幕 */
    .chart-container {
        min-height: 220px !important;
        height: 220px !important;
    }

    /* 模态框 - 保持全屏体验 */
    .modal-body {
        padding: var(--mr-space-md) !important;
    }

    .nav-tabs .nav-link {
        padding: var(--mr-space-sm) var(--mr-space-md) !important;
        font-size: var(--mr-text-sm) !important;
    }
}

/* ========== 电脑端样式覆盖 - 确保不影响桌面端布局 ========== */
@media (min-width: 1025px) {
    /* 电脑端禁用表格容器滚动 */
    .table-responsive {
        overflow-x: visible !important;
        overflow-y: visible !important;
        width: auto !important;
        max-width: none !important;
    }

    /* 电脑端表格恢复正常宽度 */
    .products-table,
    .categories-table,
    .cardstock-table,
    .coupon-table,
    .price-template-table,
    .docking-table,
    .payment-table,
    .plugin-table,
    .usergroup-table,
    .category-list {
        min-width: auto !important;
        width: 100% !important;
    }

    /* 电脑端移除移动端的特殊样式 */
    .af-main-content,
    .af-page-content,
    #dashboard-container,
    #product-container,
    #category-container,
    #cardstock-container,
    #coupon-container,
    #price-template-container,
    #docking-container,
    #payment-container,
    #plugin-container,
    #usergroup-container {
        overflow-x: visible !important;
    }
}
