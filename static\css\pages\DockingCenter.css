/* 对接中心页面样式 */
    .docking-center-container {
        padding: 20px;
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    /* 页面标题和添加按钮 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
    }

    /* 添加按钮容器样式 */
    .header-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .page-title {
        font-size: 24px;
        color: #333;
        margin: 0;
        font-weight: 600;
    }

    /* 插件注入点样式 */
    .plugin-injection-point {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    /* 默认的对接源容器 */
    .default-docking-source {
        width: 100%;
    }

    /* 顶部功能按钮注入点特殊样式 */
    #plugin-injection-docking-buttons {
        display: inline-flex;
        align-items: center;
    }

    .add-docking-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .add-docking-btn i {
        margin-right: 8px;
    }

    .add-docking-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    /* 数据列表样式 */
    .docking-list-container {
        overflow-x: auto;
    }

    .docking-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .docking-table thead {
        background-color: #fff2f8;
    }

    .docking-table th {
        color: #ff7eb9;
        font-weight: 600;
        padding: 15px;
        text-align: left;
        border-bottom: 2px solid #ffdfed;
    }

    .docking-table th i {
        margin-right: 8px;
        font-size: 0.9em;
        opacity: 0.8;
    }

    .docking-table td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #555;
    }

    .docking-table tbody tr:hover {
        background-color: #fff9fc;
    }

    .docking-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .purchase-btn {
        background-color: #9fe7ff;
        color: #0088cc;
    }

    .edit-btn {
        background-color: #ffe08a;
        color: #d19c00;
    }

    .delete-btn {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background-color: white;
        width: 450px;
        max-width: 85%;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
        overflow: hidden;
    }

    @keyframes modalSlideIn {
        from {
            transform: translateY(-50px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .modal-header {
        background-color: #ff7eb9;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: white;
        transition: all 0.2s;
    }

    .close-modal:hover {
        transform: scale(1.2);
    }

    .modal-body {
        padding: 20px;
        box-sizing: border-box; /* 确保padding不会增加总宽度 */
    }

    .form-group {
        margin-bottom: 20px;
        width: 100%;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }

    .form-group label i {
        color: #ff7eb9;
        margin-right: 6px;
    }

    .form-control {
        width: 95%;
        padding: 10px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 15px;
        transition: border 0.3s ease;
        box-sizing: border-box; /* 确保padding不会增加总宽度 */
    }

    .form-control:focus {
        border-color: #ff7eb9;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin-top: 25px;
    }

    .btn-cancel, .btn-confirm {
        padding: 10px 25px;
        border-radius: 25px;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-cancel i, .btn-confirm i {
        margin-right: 6px;
        font-size: 14px;
    }

    .btn-cancel {
        background-color: #f0f0f0;
        color: #666;
        border: none;
    }

    .btn-confirm {
        background-color: #ff7eb9;
        color: white;
        border: none;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .btn-cancel:hover {
        background-color: #e0e0e0;
    }

    .btn-confirm:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    /* 删除确认模态框样式 */
    .delete-modal-content {
        width: 480px;
        max-width: 90%;
        border-radius: 20px;
        background-color: #fff;
        box-shadow: 0 15px 35px rgba(255, 69, 95, 0.25);
        border: 3px solid rgba(255, 228, 241, 0.8);
        overflow: hidden;
        animation: deleteModalBounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
    }

    @keyframes deleteModalBounceIn {
        0% {
            transform: scale(0.3) rotate(-10deg);
            opacity: 0;
        }
        50% {
            transform: scale(1.05) rotate(2deg);
        }
        100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
    }

    .delete-modal-header {
        background: linear-gradient(135deg, #ff6b95 0%, #ff4757 100%);
        padding: 18px 24px;
        position: relative;
        overflow: hidden;
    }

    .delete-modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
            #ff4757 0%, #ff6b95 25%,
            #ff4757 50%, #ff6b95 75%,
            #ff4757 100%);
        animation: warningPulse 2s ease-in-out infinite;
    }

    @keyframes warningPulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
    }

    .delete-modal-header h2 {
        color: white;
        font-size: 20px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .delete-modal-header h2 i {
        margin-right: 12px;
        font-size: 18px;
        animation: warningShake 2s ease-in-out infinite;
    }

    @keyframes warningShake {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(-5deg); }
        75% { transform: rotate(5deg); }
    }

    .delete-modal-body {
        padding: 30px;
        background-color: #fff;
        text-align: center;
    }

    .delete-warning-icon {
        margin-bottom: 20px;
    }

    .delete-warning-icon i {
        font-size: 64px;
        color: #ff4757;
        animation: deleteIconPulse 2s ease-in-out infinite;
    }

    @keyframes deleteIconPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.1);
            opacity: 1;
        }
    }

    .delete-warning-text {
        margin-bottom: 25px;
    }

    .delete-warning-text h3 {
        color: #2c3e50;
        font-size: 22px;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    .delete-warning-text p {
        color: #7f8c8d;
        font-size: 14px;
        margin: 0;
        line-height: 1.5;
    }

    .delete-site-info {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #ff4757;
    }

    .site-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        text-align: left;
    }

    .site-info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        color: #5a6c7d;
        font-weight: 500;
        min-width: 100px;
        font-size: 14px;
    }

    .info-label i {
        margin-right: 6px;
        width: 16px;
        text-align: center;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 600;
        font-size: 14px;
        word-break: break-all;
    }

    .delete-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
    }

    .btn-delete-confirm {
        padding: 12px 28px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
        position: relative;
        overflow: hidden;
    }

    .btn-delete-confirm i {
        margin-right: 8px;
        font-size: 14px;
    }

    .btn-delete-confirm:hover {
        background: linear-gradient(135deg, #ff3742 0%, #ff2d3a 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.5);
    }

    .btn-delete-confirm:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(255, 71, 87, 0.3);
    }

    /* 通知样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999; /* 增加z-index值，确保显示在最上层 */
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        pointer-events: none; /* 允许点击穿透，不影响下方元素的交互 */
    }

    .toast {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        padding: 12px 16px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        transform: translateX(120%);
        transition: transform 0.3s ease, opacity 0.3s ease;
        max-width: 300px;
        pointer-events: auto; /* 恢复通知本身的点击能力 */
        position: relative; /* 确保相对定位 */
        opacity: 0;
        border-left: 4px solid #ccc;
    }

    .toast.show {
        transform: translateX(0);
        opacity: 1;
    }

    .toast-icon {
        margin-right: 12px;
        font-size: 1.2rem;
    }

    .toast-success {
        border-left: 4px solid #4cd964;
        box-shadow: 0 4px 15px rgba(76, 217, 100, 0.2);
    }
    
    .toast-success .toast-icon {
        color: #4cd964;
    }

    .toast-error {
        border-left: 4px solid #ff6b6b;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
    }
    
    .toast-error .toast-icon {
        color: #ff6b6b;
    }

    .toast-message {
        flex-grow: 1;
        font-size: 0.95rem;
        color: #333;
        font-weight: 500;
    }

    /* 加载中指示器 */
    .loading-indicator {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 1500;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .loading-indicator.show {
        display: flex;
    }

    .spinner {
        border: 4px solid rgba(255, 126, 185, 0.3);
        border-radius: 50%;
        border-top: 4px solid #ff7eb9;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    .loading-text {
        font-size: 16px;
        color: #ff7eb9;
        font-weight: 500;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 空状态样式 */
    .empty-state {
        display: none;
        padding: 50px 0;
        text-align: center;
    }

    .empty-state-icon {
        font-size: 60px;
        color: #ffdfed;
        margin-bottom: 20px;
    }

    .empty-state-text {
        font-size: 18px;
        color: #999;
    }

    /* 聚比价回调地址提示样式 */
    .jubijia-callback-tip {
        margin-top: 10px;
        padding: 12px 15px;
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border: 1px solid #bbdefb;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
        animation: tipSlideIn 0.3s ease-out;
    }

    @keyframes tipSlideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .tip-content {
        display: flex;
        align-items: flex-start;
        font-size: 14px;
        color: #1976d2;
        line-height: 1.5;
    }

    .tip-content i {
        margin-right: 8px;
        margin-top: 2px;
        color: #2196f3;
        font-size: 16px;
        flex-shrink: 0;
    }

    .tip-content span {
        flex: 1;
    }

    .tip-content strong {
        color: #0d47a1;
        font-weight: 600;
        background-color: rgba(33, 150, 243, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        word-break: break-all;
    }

    /* 进货模态框样式优化 */
    .purchase-modal-content {
        width: auto; /* 从固定百分比改为自适应内容宽度 */
        min-width: 80%; /* 添加最小宽度确保不会太窄 */
        max-width: 1400px; /* 保持最大宽度限制 */
        height: 85vh; /* 修改为视口高度的85% */
        max-height: 800px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(255, 182, 219, 0.3);
        border: 2px solid #ffe6f2;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        animation: modalPop 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .modal-header {
        background-color: #ff9ed2;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px dashed #fff;
        position: relative;
        flex-shrink: 0; /* 防止头部被压缩 */
        z-index: 20; /* 确保在滚动内容上方 */
    }

    .purchase-modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
        display: flex;
        flex-direction: column;
    }

    .purchase-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: #fff;
        border-top: 2px dashed #ffe6f2;
    }

    @keyframes modalPop {
        0% {
            transform: scale(0.9);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .modal-header h2 i {
        margin-right: 10px;
        font-size: 18px;
    }

    .close-modal {
        font-size: 28px;
        cursor: pointer;
        color: white;
        transition: all 0.3s;
        opacity: 0.8;
        line-height: 0.8;
        height: 28px;
        width: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .close-modal:hover {
        transform: rotate(90deg);
        opacity: 1;
        background-color: rgba(255, 255, 255, 0.2);
    }
    
    .purchase-container {
        display: flex;
        height: 100%;
        background-color: #fff;
    }
    
    /* 左侧分类树样式 */
    .category-tree-container {
        width: 240px; /* 从280px减少到240px，为表格腾出更多空间 */
        min-width: 240px; /* 从280px减少到240px */
        border-right: 2px solid #ffe6f2;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        overflow: hidden; /* 确保内容不溢出 */
    }
    
    .tree-search {
        padding: 15px;
        border-bottom: 2px solid #ffe6f2;
    }

    .category-tree {
        padding: 10px 0;
        flex: 1;
        overflow-y: auto; /* 添加垂直滚动条 */
        height: calc(100% - 65px); /* 计算高度，减去搜索框的高度 */
        scrollbar-width: thin; /* Firefox兼容 */
        scrollbar-color: #ff9ed2 #f8f8f8; /* Firefox兼容 */
        -webkit-overflow-scrolling: touch; /* 添加惯性滚动 */
    }

    /* 自定义滚动条样式 */
    .category-tree::-webkit-scrollbar,
    .product-table-container::-webkit-scrollbar {
        width: 8px;
    }

    .category-tree::-webkit-scrollbar-track,
    .product-table-container::-webkit-scrollbar-track {
        background: #f8f8f8;
        border-radius: 10px;
    }

    .category-tree::-webkit-scrollbar-thumb,
    .product-table-container::-webkit-scrollbar-thumb {
        background-color: #ff9ed2;
        border-radius: 10px;
        border: 2px solid #f8f8f8;
    }

    .tree-search .form-control {
        border-radius: 20px;
        border: 2px solid #ffe6f2;
        padding: 8px 12px 8px 36px;
        font-size: 14px;
        transition: all 0.3s ease;
        width: 85%;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23ff9ed2'%3E%3Cpath d='M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: 12px center;
        background-size: 16px 16px;
    }

    .tree-search .form-control:focus {
        border-color: #ff9ed2;
        box-shadow: 0 0 8px rgba(255, 158, 210, 0.3);
        outline: none;
    }
    
    .category-tree ul {
        list-style: none;
        padding: 0;
        margin: 0;
        padding-left: 18px; /* 增加子列表的缩进 */
    }
    
    .category-tree > ul {
        padding-left: 0; /* 顶级列表不需要缩进 */
    }
    
    .tree-item {
        margin-bottom: 3px;
        font-size: 14px;
    }
    
    .tree-item-header {
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.2s;
        background-color: transparent;
    }
    
    .tree-item-header:hover {
        background-color: rgba(255, 158, 210, 0.1);
    }
    
    .tree-item.selected > .tree-item-header {
        background-color: rgba(255, 158, 210, 0.2);
        color: #ff7eb9;
    }
    
    .tree-item-header i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
        font-size: 12px;
        transition: transform 0.2s;
    }
    
    .tree-item-header span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .tree-item.expanded > .tree-item-header > i.fa-caret-right {
        transform: rotate(90deg);
    }
    
    .tree-all-item {
        font-weight: 500;
        color: #ff7eb9;
    }
    
    .tree-item ul {
        padding-left: 20px;
        display: none;
    }
    
    .tree-item.expanded > ul {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* 右侧商品列表样式 */
    .product-list-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        height: 100%;
    }
    
    .product-filter {
        padding: 12px;
        background-color: #fff;
        border-bottom: 2px dashed #ffe6f2;
    }
    
    .filter-group {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .filter-group .form-control {
        max-width: 150px;
        border-radius: 20px;
        border: 2px solid #ffe6f2;
        padding: 8px 12px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .filter-group .form-control:focus {
        border-color: #ff9ed2;
        box-shadow: 0 0 8px rgba(255, 158, 210, 0.3);
        outline: none;
    }
    
    .search-btn {
        padding: 8px 20px;
        border-radius: 20px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        background-color: #ff9ed2;
        color: white;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .search-btn::after {
        content: '';
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 100px;
        height: 100px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.3s ease-out;
        pointer-events: none;
    }
    
    .search-btn:active::after {
        transform: translate(-50%, -50%) scale(1.5);
        transition: transform 0.4s ease-out;
        opacity: 0;
    }
    
    .search-btn:hover {
        background-color: #ff7bbd;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 158, 210, 0.3);
    }

    .search-btn i {
        margin-right: 6px;
    }
    
    .product-table-container {
        flex: 1;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #ff9ed2 #f8f8f8;
        -webkit-overflow-scrolling: touch; /* 添加惯性滚动 */
        padding: 0 10px; /* 添加一点内边距 */
        position: relative; /* 添加相对定位，使空状态能够绝对定位 */
    }

    .product-table-container::-webkit-scrollbar {
        width: 6px;
    }

    .product-table-container::-webkit-scrollbar-track {
        background: #f8f8f8;
        border-radius: 10px;
    }

    .product-table-container::-webkit-scrollbar-thumb {
        background-color: #ff9ed2;
        border-radius: 10px;
        border: 2px solid #f8f8f8;
    }

    /* 商品表格样式 */
    .product-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 10px;
        background-color: #fff;
        min-width: 1050px; /* 设置合适的最小宽度 */
        table-layout: fixed; /* 使用固定表格布局以更好地控制列宽 */
    }
    
    .product-table thead th {
        position: sticky;
        top: 0;
        background-color: #fff2f8;
        color: #ff7eb9;
        font-weight: 500;
        padding: 10px 15px;
        text-align: left;
        z-index: 10;
        border-bottom: 2px solid #ffd6eb;
        box-shadow: 0 2px 5px rgba(255, 158, 210, 0.1);
    }

    .product-table tbody td {
        padding: 10px 15px;
        border-bottom: 1px solid #ffd6eb;
        color: #666;
        font-size: 14px;
        vertical-align: middle;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 250px; /* 从200px增加到250px，允许更多内容显示 */
        white-space: nowrap;
    }

    /* 商品图片样式 */
    .product-image {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        overflow: hidden;
        background-color: #ffd6eb;
        box-shadow: 0 3px 6px rgba(255, 158, 210, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .placeholder-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffd6eb;
        color: #ff7bbd;
        font-weight: bold;
        font-size: 18px;
    }

    /* 商品状态样式 */
    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
    }

    .status-active {
        background-color: #d1f7c4;
        color: #5cb85c;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #dc3545;
    }

    /* 商品操作按钮样式 */
    .action-btn-small {
        padding: 4px 8px;
        border-radius: 12px;
        border: none;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 70px;
        text-align: center;
    }

    .not-docked {
        background-color: #ffd6eb;
        color: #ff7bbd;
    }

    .docked {
        background-color: #d1f7c4;
        color: #5cb85c;
    }

    /* 卡通复选框样式 */
    .kawaii-checkbox {
        position: relative;
        display: inline-block;
    }

    .kawaii-checkbox-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .kawaii-checkbox-label {
        display: inline-block;
        position: relative;
        cursor: pointer;
        height: 24px;
        width: 24px;
        background: white;
        border: 2px solid #ffcce5;
        border-radius: 6px;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        transform-origin: center;
    }

    /* 商品列表中使用较小的复选框 */
    .product-table .kawaii-checkbox-label {
        height: 20px;
        width: 20px;
    }
    
    /* 第一列减小宽度和内边距 */
    .product-table th:first-child,
    .product-table td:first-child {
        width: 30px;
        min-width: 30px;
        padding: 10px 2px 10px 5px;
        text-align: center;
    }
    
    /* 商品名称列增加左内边距 */
    .product-table th:nth-child(2),
    .product-table td:nth-child(2) {
        padding-left: 5px;
    }

    /* 默认状态-可爱表情 */
    .kawaii-checkbox-face {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .kawaii-checkbox-eyes {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-bottom: 2px;
    }

    .kawaii-checkbox-eyes:before,
    .kawaii-checkbox-eyes:after {
        content: '';
        width: 4px;
        height: 4px;
        background: #ff7eb9;
        border-radius: 50%;
    }

    /* 商品列表中的表情缩小 */
    .product-table .kawaii-checkbox-eyes:before,
    .product-table .kawaii-checkbox-eyes:after {
        width: 3px;
        height: 3px;
    }

    .kawaii-checkbox-mouth {
        width: 6px;
        height: 3px;
        border-radius: 3px 3px 6px 6px;
        background: #ff7eb9;
    }

    /* 商品列表中的表情嘴巴缩小 */
    .product-table .kawaii-checkbox-mouth {
        width: 5px;
        height: 2px;
    }

    /* 美化下拉框 */
    .page-size-select {
        border: 2px solid #ffd6eb;
        border-radius: 20px;
        padding: 6px 28px 6px 12px;
        font-size: 14px;
        color: #ff7eb9;
        background-color: #fff;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        cursor: pointer;
        transition: all 0.3s ease;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff7eb9'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 16px;
        box-shadow: 0 2px 4px rgba(255, 158, 210, 0.1);
    }

    .page-size-select:focus {
        outline: none;
        border-color: #ff9ed2;
        box-shadow: 0 0 0 3px rgba(255, 158, 210, 0.2);
    }

    .page-size-select:hover {
        border-color: #ff9ed2;
        background-color: #fff9fc;
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(255, 158, 210, 0.2);
    }

    /* 自定义下拉框选项样式 */
    .page-size-select option {
        background-color: white;
        color: #555;
        padding: 8px;
        font-size: 14px;
    }

    .page-info {
        font-size: 14px;
        color: #ff9ed2;
        font-weight: 500;
    }

    /* 商品列表中的表情嘴巴缩小 */
    .product-table .kawaii-checkbox-mouth {
        width: 5px;
        height: 2px;
    }
    
    .product-table .kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-mouth {
        width: 6px;
        height: 3px;
    }
    
    /* 选中状态样式 */
    .kawaii-checkbox-input:checked + .kawaii-checkbox-label {
        background: #ff7eb9;
        border-color: #ff7eb9;
        transform: scale(1.1);
    }

    .kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-face {
        opacity: 1;
    }

    .kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-eyes:before,
    .kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-eyes:after {
        background: white;
    }

    .kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-mouth {
        background: white;
        width: 8px;
        height: 4px;
        border-radius: 0 0 4px 4px;
    }

    /* 悬停效果 */
    .kawaii-checkbox-label:hover {
        transform: scale(1.05);
        box-shadow: 0 3px 6px rgba(255, 126, 185, 0.3);
        border-color: #ff9ec7;
    }

    .kawaii-checkbox-input:checked + .kawaii-checkbox-label:hover {
        transform: scale(1.15);
    }

    /* 动画效果 */
    @keyframes bounce {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .kawaii-checkbox-input:checked + .kawaii-checkbox-label {
        animation: bounce 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    }

    /* 添加复选框选中样式 */
    #selectAllDocking:checked, #selectAllProducts:checked, .docking-checkbox:checked, .product-checkbox:checked {
        background-color: #ff7eb9;
        position: relative;
    }
    
    #selectAllDocking:checked:after, #selectAllProducts:checked:after, .docking-checkbox:checked:after, .product-checkbox:checked:after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 4px;
        color: white;
        font-size: 14px;
    }

    /* 美化批量添加按钮 */
    .batch-btn {
        padding: 10px 20px;
        border-radius: 25px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
    }

    .batch-purchase-btn {
        background-color: #ff9ed2;
        color: white;
        border: 2px solid #ff9ed2;
        position: relative;
        z-index: 1;
        /* 添加可爱字体 */
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    /* 添加可爱卡通效果 */
    .batch-purchase-btn::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #ff9ed2, #ff85c8, #ff9ed2);
        border-radius: 27px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .batch-purchase-btn:hover::before {
        opacity: 1;
        animation: gradient-shift 3s ease infinite;
    }

    @keyframes gradient-shift {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .batch-btn i {
        margin-right: 8px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .batch-purchase-btn:hover {
        background-color: #ff7bbd;
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 15px rgba(255, 158, 210, 0.4);
    }

    .batch-purchase-btn:hover i {
        transform: rotate(-10deg) scale(1.2);
    }

    .batch-purchase-btn:active {
        transform: translateY(1px) scale(0.98);
        box-shadow: 0 2px 5px rgba(255, 158, 210, 0.3);
    }

    /* 添加按钮呼吸灯效果 */
    @keyframes pulse {
        0% {
            box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
        }
        50% {
            box-shadow: 0 4px 12px rgba(255, 158, 210, 0.4);
        }
        100% {
            box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
        }
    }

    .batch-purchase-btn {
        animation: pulse 2s infinite;
    }

    /* 添加按钮点击波纹效果 */
    .batch-btn::after {
        content: '';
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 100px;
        height: 100px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        pointer-events: none;
        opacity: 0.7;
    }

    .batch-btn:active::after {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }

    /* 美化分页控件 */
    .pagination {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .page-btn {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        border: 2px solid #ffcce5;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        color: #ff7eb9;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        box-shadow: 0 3px 6px rgba(255, 158, 210, 0.15);
        position: relative;
        overflow: hidden;
    }

    /* 添加卡通效果 - 背景渐变 */
    .page-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle, rgba(255,242,248,1) 0%, rgba(255,255,255,1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 50%;
    }

    .page-btn:hover::before {
        opacity: 1;
    }

    .page-btn:hover:not(.disabled) {
        background-color: #fff2f8;
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 6px 12px rgba(255, 158, 210, 0.3);
        border-color: #ff9ed2;
    }

    .page-btn:active:not(.disabled) {
        transform: translateY(1px) scale(0.95);
        box-shadow: 0 2px 4px rgba(255, 158, 210, 0.2);
    }

    .page-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f8f8f8;
        color: #ccc;
        border-color: #f0f0f0;
        box-shadow: none;
        animation: none;
    }

    .page-btn i {
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
    }

    .page-btn:hover:not(.disabled) i.fa-angle-left {
        transform: translateX(-3px);
        text-shadow: 0 0 3px rgba(255, 126, 185, 0.5);
    }

    .page-btn:hover:not(.disabled) i.fa-angle-right {
        transform: translateX(3px);
        text-shadow: 0 0 3px rgba(255, 126, 185, 0.5);
    }

    /* 添加按钮微弹跳动画 */
    @keyframes float {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-2px);
        }
        100% {
            transform: translateY(0);
        }
    }

    .page-btn:not(.disabled) {
        animation: float 2s ease-in-out infinite;
    }

    /* 添加点击涟漪效果 */
    .page-btn::after {
        content: '';
        position: absolute;
        background: rgba(255, 126, 185, 0.2);
        border-radius: 50%;
        width: 100%;
        height: 100%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.4s ease-out, opacity 0.4s ease-out;
        pointer-events: none;
        opacity: 0.7;
        z-index: 1;
    }

    .page-btn:active::after {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }

    /* 为当前页添加特殊样式 */
    .current-page {
        font-weight: 600;
        color: #ff7eb9;
        min-width: 24px;
        text-align: center;
        font-size: 16px;
        position: relative;
        padding: 0 3px;
    }

    .current-page::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #ff9ed2;
        border-radius: 2px;
    }

    /* 自定义表格列宽样式 */
    .product-table th:nth-child(4) {
        min-width: 130px; /* 增加商品编号列宽 */
    }

    .product-table th:nth-child(6),
    .product-table th:nth-child(7) {
        max-width: 80px; /* 减小商品类型和销售状态列宽 */
    }

    /* 确保内容溢出时显示省略号 */
    .product-table td:nth-child(6),
    .product-table td:nth-child(7) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
    }

    /* 增强商品编号列的显示 */
    .product-table td:nth-child(4) {
        /* 移除等宽字体，恢复默认字体 */
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .product-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 80%;
        max-width: 400px;
        padding: 40px 20px;
        text-align: center;
        color: #999;
        background-color: rgba(255, 255, 255, 0.95);
        z-index: 5;
        border-radius: 20px;
        box-shadow: 0 5px 25px rgba(255, 214, 235, 0.5);
        animation: fadeIn 0.5s ease-in-out;
        border: 2px dashed #ffd6eb;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -60%);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%);
        }
    }

    .product-empty i {
        font-size: 60px;
        color: #ff9ed2;
        margin-bottom: 20px;
        animation: float 3s ease-in-out infinite;
        background: linear-gradient(45deg, #ff7eb9, #ff9ed2);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 3px 10px rgba(255, 126, 185, 0.3);
    }

    .product-empty div {
        font-size: 18px;
        font-weight: 600;
        color: #ff7eb9;
        margin-bottom: 5px;
    }

    .product-empty .empty-hint {
        font-size: 14px;
        font-weight: normal;
        color: #999;
        margin-top: 10px;
    }
    
    /* 批量对接设置模态框样式 */
    .radio-group {
        display: flex;
        flex-direction: row;
        gap: 20px;
        margin-top: 8px;
    }
    
    /* 添加分类水平布局样式 */
    .category-row {
        display: flex;
        flex-direction: row;
        gap: 15px;
        width: 100%;
    }
    
    .category-col {
        flex: 1;
        min-width: 0;
    }
    
    .radio-item {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .radio-item input[type="radio"] {
        margin-right: 8px;
        cursor: pointer;
    }
    
    .radio-item label {
        cursor: pointer;
        color: #555;
    }
    
    .form-notice {
        margin-top: 20px;
        padding: 10px 15px;
        background-color: #fff9fc;
        border-radius: 8px;
        border-left: 3px solid #ff7eb9;
        display: flex;
        align-items: center;
    }
    
    .form-notice i {
        color: #ff7eb9;
        font-size: 16px;
        margin-right: 10px;
    }
    
    .form-notice span {
        color: #666;
        font-size: 14px;
    }
    
    .form-label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }
    
    .form-label i {
        color: #ff7eb9;
        margin-right: 6px;
    }

    /* 批量对接设置模态框样式 */
    .batch-modal-content {
        width: 500px;
        max-width: 90%;
        border-radius: 24px;
        background-color: #fff;
        box-shadow: 0 15px 30px rgba(255, 126, 185, 0.25);
        border: 3px solid rgba(255, 228, 241, 0.8);
        overflow: hidden;
        animation: modalBounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
    }
    
    @keyframes modalBounceIn {
        0% { transform: scale(0.5); opacity: 0; }
        70% { transform: scale(1.05); }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .batch-modal-header {
        background: linear-gradient(135deg, #ffa9d3 0%, #ff7eb9 100%);
        padding: 18px 24px;
        position: relative;
        overflow: hidden;
    }
    
    .batch-modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, 
            #ff9ed2 0%, #ffb6dd 10%, 
            #ff9ed2 20%, #ffb6dd 30%, 
            #ff9ed2 40%, #ffb6dd 50%, 
            #ff9ed2 60%, #ffb6dd 70%, 
            #ff9ed2 80%, #ffb6dd 90%, 
            #ff9ed2 100%);
        animation: shimmer 2s linear infinite;
        background-size: 200% 100%;
    }
    
    @keyframes shimmer {
        0% { background-position: 100% 0; }
        100% { background-position: -100% 0; }
    }
    
    .batch-modal-header h2 {
        color: white;
        font-size: 22px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        text-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    }
    
    .batch-modal-header h2 i {
        margin-right: 12px;
        font-size: 20px;
        animation: spin 10s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        25% { transform: rotate(90deg); }
        50% { transform: rotate(180deg); }
        75% { transform: rotate(270deg); }
        100% { transform: rotate(360deg); }
    }
    
    .batch-modal-header .close-modal {
        font-size: 28px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }
    
    .batch-modal-header .close-modal:hover {
        background-color: rgba(255, 255, 255, 0.3);
        transform: rotate(90deg);
    }
    
    .batch-modal-body {
        padding: 30px;
        background-color: #fff;
    }
    
    /* 卡通插图 */
    .kawaii-illustration {
        text-align: center;
        height: 80px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
    }
    
    .kawaii-cloud {
        position: absolute;
        width: 80px;
        height: 30px;
        background-color: #ffeff7;
        border-radius: 30px;
        top: 15px;
        left: 50%;
        margin-left: -100px;
        animation: floatCloud 8s ease-in-out infinite;
        box-shadow: 0 3px 5px rgba(255, 126, 185, 0.1);
    }
    
    .kawaii-cloud::before,
    .kawaii-cloud::after {
        content: '';
        position: absolute;
        background-color: #ffeff7;
        border-radius: 50%;
    }
    
    .kawaii-cloud::before {
        width: 35px;
        height: 35px;
        top: -20px;
        left: 10px;
    }
    
    .kawaii-cloud::after {
        width: 40px;
        height: 40px;
        top: -25px;
        right: 10px;
    }
    
    @keyframes floatCloud {
        0%, 100% { transform: translateX(0) translateY(0); }
        50% { transform: translateX(40px) translateY(-5px); }
    }
    
    .kawaii-robot {
        position: absolute;
        bottom: 0;
        right: 50%;
        margin-right: -20px;
        animation: floatRobot 3s ease-in-out infinite;
    }
    
    @keyframes floatRobot {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-8px); }
    }
    
    .robot-head {
        width: 40px;
        height: 35px;
        background-color: #ff9ed2;
        border-radius: 12px;
        position: relative;
        z-index: 2;
    }
    
    .robot-eyes {
        display: flex;
        justify-content: space-around;
        padding: 8px 5px 0;
    }
    
    .robot-eyes::before,
    .robot-eyes::after {
        content: '';
        width: 8px;
        height: 8px;
        background-color: white;
        border-radius: 50%;
    }
    
    .robot-mouth {
        width: 12px;
        height: 5px;
        background-color: white;
        border-radius: 2px;
        margin: 5px auto 0;
        position: relative;
    }
    
    .robot-body {
        width: 25px;
        height: 20px;
        background-color: #ff9ed2;
        border-radius: 5px;
        margin: -5px auto 0;
        position: relative;
        z-index: 1;
    }
    
    .robot-body::before,
    .robot-body::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 12px;
        background-color: #ff9ed2;
        border-radius: 4px;
        bottom: -8px;
    }
    
    .robot-body::before {
        left: 2px;
    }
    
    .robot-body::after {
        right: 2px;
    }
    
    /* 卡通标签 */
    .kawaii-label {
        display: block;
        font-size: 15px;
        font-weight: 500;
        color: #ff7eb9;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    
    .kawaii-label i {
        margin-right: 8px;
    }
    
    /* 卡通下拉框 */
    .kawaii-select-wrapper {
        position: relative;
        margin-bottom: 5px;
    }
    
    .kawaii-select {
        width: 100%;
        padding: 12px 15px;
        font-size: 14px;
        border: 2px solid #ffe4f1;
        border-radius: 16px;
        background-color: white;
        appearance: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .kawaii-select:hover {
        border-color: #ffcbe4;
        box-shadow: 0 3px 8px rgba(255, 126, 185, 0.1);
    }
    
    .kawaii-select:focus {
        outline: none;
        border-color: #ff9ed2;
        box-shadow: 0 0 0 3px rgba(255, 158, 210, 0.2);
    }
    
    .select-arrow {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #ff9ed2;
        pointer-events: none;
        transition: all 0.3s ease;
    }
    
    .kawaii-select:focus + .select-arrow {
        transform: translateY(-50%) rotate(180deg);
    }
    
    /* 卡通单选框 */
    .kawaii-radio-group {
        display: flex;
        gap: 25px;
        margin-top: 15px;
    }
    
    .kawaii-radio-item {
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 2px 0;
    }
    
    .kawaii-radio-input {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .radio-bubble {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        border: 2px solid #ffe4f1;
        margin-right: 10px;
        position: relative;
        display: inline-block;
        vertical-align: middle;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }
    
    .radio-bubble::after {
        content: '';
        width: 0;
        height: 0;
        border-radius: 50%;
        background-color: #ff9ed2;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        opacity: 0;
    }
    
    .radio-text {
        color: #666;
        font-size: 15px;
        vertical-align: middle;
        line-height: 22px;
        transition: all 0.3s ease;
    }
    
    .kawaii-radio-input:checked + .radio-bubble {
        border-color: #ff9ed2;
        animation: pulse 0.3s ease;
    }
    
    .kawaii-radio-input:checked + .radio-bubble::after {
        width: 12px;
        height: 12px;
        opacity: 1;
    }
    
    .kawaii-radio-input:checked ~ .radio-text {
        color: #ff7eb9;
        font-weight: 500;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    /* 卡通提示框 */
    .kawaii-notice {
        margin-top: 25px;
        padding: 15px;
        background-color: #fff9fc;
        border-radius: 16px;
        display: flex;
        align-items: center;
        box-shadow: 0 3px 10px rgba(255, 126, 185, 0.1);
        position: relative;
        overflow: hidden;
    }
    
    .kawaii-notice::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(to bottom, #ff9ed2, #ff7eb9);
        border-radius: 5px 0 0 5px;
    }
    
    .notice-icon {
        font-size: 20px;
        color: #ff7eb9;
        margin-right: 12px;
        animation: bounce 2s infinite;
    }
    
    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
    }
    
    .notice-text {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
    }
    
    /* 卡通按钮组 */
    .kawaii-form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin-top: 30px;
    }
    
    .kawaii-btn {
        padding: 12px 20px;
        border-radius: 50px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: none;
        position: relative;
        overflow: hidden;
    }
    
    .kawaii-btn i {
        margin-right: 8px;
        transition: all 0.3s ease;
    }
    
    .kawaii-btn-cancel {
        background-color: #f7f7f7;
        color: #999;
    }
    
    .kawaii-btn-cancel:hover {
        background-color: #efefef;
        transform: translateY(-2px);
    }
    
    .kawaii-btn-cancel:hover i {
        transform: rotate(-90deg);
    }
    
    .kawaii-btn-confirm {
        background-color: #ff9ed2;
        color: white;
        box-shadow: 0 4px 10px rgba(255, 158, 210, 0.3);
        position: relative;
    }
    
    .kawaii-btn-confirm::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, 
            rgba(255, 255, 255, 0) 0%, 
            rgba(255, 255, 255, 0.8) 50%, 
            rgba(255, 255, 255, 0) 100%);
        transform: translateX(-100%) skewX(-25deg);
        animation: shimmerBtn 3s infinite;
    }
    
    @keyframes shimmerBtn {
        0% { transform: translateX(-100%) skewX(-25deg); }
        20% { transform: translateX(100%) skewX(-25deg); }
        100% { transform: translateX(100%) skewX(-25deg); }
    }
    
    .kawaii-btn-confirm:hover {
        background-color: #ff7eb9;
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(255, 126, 185, 0.4);
    }
    
    .kawaii-btn-confirm:hover i {
        transform: scale(1.2);
    }
    
    .kawaii-btn:active {
        transform: translateY(1px);
        box-shadow: 0 2px 5px rgba(255, 126, 185, 0.2);
    }
    
    .btn-sparkles {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
    }
    
    .btn-sparkles::before,
    .btn-sparkles::after {
        content: '✦';
        position: absolute;
        opacity: 0;
        font-size: 20px;
        color: white;
        animation: sparkle 2s ease infinite;
    }
    
    .btn-sparkles::before {
        top: -15px;
        left: 10px;
    }
    
    .btn-sparkles::after {
        bottom: -15px;
        right: 10px;
        animation-delay: 0.3s;
    }
    
    @keyframes sparkle {
        0% { transform: translateY(0) scale(0); opacity: 0; }
        20% { transform: translateY(-5px) scale(1.2); opacity: 1; }
        40% { transform: translateY(-15px) scale(0.8); opacity: 0; }
        100% { transform: translateY(-15px) scale(0); opacity: 0; }
    }