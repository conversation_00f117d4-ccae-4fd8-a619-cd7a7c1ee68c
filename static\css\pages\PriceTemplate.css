:root {
        --primary-color: #ff7eb9;
        --primary-light: #ffa6d2;
        --primary-dark: #e55a9b;
        --accent-color: #9fe7ff;
        --background-color: #fff5f9;
        --card-bg: #ffffff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #ffdfed;
        --shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        --border-radius: 15px;
    }

    /* 页面容器 */
    #pricing-template-container {
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background-color: var(--background-color);
        color: var(--text-primary);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    /* 页面标题 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .page-title {
        font-size: 24px;
        color: #333;
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .page-title i {
        margin-right: 10px;
        color: var(--primary-color);
    }

    /* 模板列表卡片 */
    .templates-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 15px;
        flex-grow: 1;
        overflow: auto;
        animation: slideUp 0.5s ease;
        margin-top: 0;
    }

    .card-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .card-title div {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        display: flex;
        align-items: center;
    }

    .card-title i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    .add-template-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .add-template-btn i {
        margin-right: 8px;
        color: white !important;
    }

    .add-template-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    .templates-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .templates-table thead {
        background-color: #fff2f8;
    }

    .templates-table th {
        color: #ff7eb9;
        font-weight: 600;
        padding: 15px;
        text-align: left;
        border-bottom: 2px solid #ffdfed;
    }

    .templates-table th i {
        margin-right: 8px;
        font-size: 0.9em;
        opacity: 0.8;
    }

    .templates-table td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #555;
    }

    .templates-table tbody tr:hover {
        background-color: #fff9fc;
    }

    .templates-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* 徽章/标签 */
    .badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-right: 8px;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .badge-fixed {
        background-color: #9fe7ff;
        color: #0088cc;
    }

    .badge-percentage {
        background-color: #ffe08a;
        color: #d19c00;
    }

    /* 价格样例样式 */
    .price-sample {
        background-color: #f9f9fa;
        border-radius: 8px;
        padding: 10px;
        border-left: 3px solid var(--primary-light);
    }

    .price-result {
        font-weight: 600;
        color: var(--primary-color);
    }

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .edit-btn {
        background-color: #ffe08a;
        color: #d19c00;
    }

    .delete-btn {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 空状态 */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: var(--text-secondary);
        animation: fadeIn 0.8s ease;
    }

    .empty-state i {
        font-size: 60px;
        color: #ffdfed;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: var(--text-primary);
        font-size: 18px;
    }

    .empty-state p {
        color: #999;
        font-size: 14px;
    }

    /* 模态框样式 */
    #templateModal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
    }

    #templateModal.active {
        display: flex !important;
    }

    .modal-content {
        background: white;
        width: 550px;
        max-width: 95%;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(255, 182, 219, 0.3);
        border: 2px solid #ffe6f2;
        overflow: hidden;
        animation: modalPop 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    @keyframes modalPop {
        0% {
            transform: scale(0.9);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .modal-header {
        background-color: #ff9ed2;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px dashed #fff;
    }

    .modal-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        color: white;
    }

    .modal-title i {
        margin-right: 10px;
    }

    .modal-header button {
        font-size: 28px;
        cursor: pointer;
        color: white;
        transition: all 0.3s;
        opacity: 0.8;
        line-height: 0.8;
        height: 28px;
        width: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: none;
        border: none;
    }

    .modal-header button:hover {
        transform: rotate(90deg);
        opacity: 1;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-body::-webkit-scrollbar {
        width: 8px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f8f8f8;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background-color: #ff9ed2;
        border-radius: 10px;
        border: 2px solid #f8f8f8;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 15px 20px;
        border-top: 2px dashed #ffe6f2;
    }
    
    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 2px solid #ffe6f2;
        border-radius: 10px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #ff7eb9;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    /* 单选按钮样式 */
    .radio-group {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 15px;
    }

    .radio-option {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        position: relative;
    }

    .radio-input {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .radio-control {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid var(--primary-light);
        position: relative;
        background-color: white;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .radio-input:checked + .radio-control {
        border-color: var(--primary-color);
    }

    .radio-input:checked + .radio-control:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: var(--primary-color);
        animation: pulse 0.3s ease;
    }

    .radio-label {
        font-size: 14px;
        color: var(--text-primary);
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(0);
        }
        90% {
            transform: translate(-50%, -50%) scale(1.2);
        }
        100% {
            transform: translate(-50%, -50%) scale(1);
        }
    }

    /* 会员等级输入项样式 */
    .member-level-item {
        border: 1px solid #ffe6f2;
        border-radius: 12px;
        padding: 15px;
        background: #fff9fd;
        margin-bottom: 15px;
        border-left: 3px solid var(--primary-color);
        transition: all 0.3s ease;
    }

    .member-level-item:hover {
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.15);
        transform: translateY(-2px);
    }

    /* 输入组样式 */
    .input-group {
        display: flex;
        align-items: center;
    }

    .input-group .form-control {
        flex: 1;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .input-group-text {
        background-color: #fff2f8;
        border: 2px solid #ffe6f2;
        border-left: none;
        padding: 10px 15px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        color: #ff7eb9;
        font-weight: 500;
        min-width: 40px;
        text-align: center;
    }

    /* 示例计算区域 */
    .sample-calculation {
        background: #fff9fd;
        border: 2px dashed #ffe6f2;
        border-radius: 12px;
        padding: 15px;
        margin-top: 20px;
    }

    .sample-calculation h4 {
        margin: 0 0 15px;
        font-size: 16px;
        color: #ff7eb9;
        display: flex;
        align-items: center;
    }

    .sample-calculation h4:before {
        content: '✨';
        margin-right: 8px;
    }

    #calculationResults {
        background: white;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 2px 8px rgba(255, 126, 185, 0.1);
    }

    #calculationResults div {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 5px;
    }

    #calculationResults div:last-child {
        margin-bottom: 0;
    }

    #calculationResults div:nth-child(odd) {
        background-color: #fff5f9;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
    }

    .btn i {
        margin-right: 8px;
    }

    .btn-primary {
        background-color: #ff7eb9;
        color: white;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .btn-primary:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    .btn-secondary {
        background-color: #f0f0f0;
        color: #666;
    }

    .btn-secondary:hover {
        background-color: #e0e0e0;
    }

    .btn-danger {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .btn-danger:hover {
        background-color: #ff94a4;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 184, 194, 0.4);
    }

    /* 加载中指示器 */
    #loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 1500;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 126, 185, 0.3);
        border-radius: 50%;
        border-top: 4px solid #ff7eb9;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 确认删除对话框 */
    .confirm-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1100;
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .confirm-dialog.show {
        opacity: 1;
        visibility: visible;
    }

    .confirm-box {
        background-color: white;
        border-radius: 15px;
        width: 400px;
        max-width: 90%;
        padding: 20px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-20px);
        transition: transform 0.3s;
        border: 2px solid #ffe6f2;
    }

    .confirm-dialog.show .confirm-box {
        transform: translateY(0);
    }

    .confirm-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #ff3e5f;
        display: flex;
        align-items: center;
    }

    .confirm-title i {
        margin-right: 10px;
    }

    .confirm-message {
        margin-bottom: 20px;
        color: var(--text-primary);
        line-height: 1.5;
    }

    .confirm-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* 通知提示 */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        max-width: 300px;
        transform: translateX(120%);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 1200;
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .notification.success {
        border-left: 4px solid #42b983;
    }

    .notification.error {
        border-left: 4px solid #ff6b6b;
    }

    .notification-icon {
        margin-right: 12px;
        font-size: 18px;
    }

    .notification.success .notification-icon {
        color: #42b983;
    }

    .notification.error .notification-icon {
        color: #ff6b6b;
    }

    #notificationMessage {
        font-size: 14px;
        color: var(--text-primary);
    }

    /* 动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .templates-table {
            font-size: 0.9rem;
        }

        .action-btn {
            width: 35px;
            height: 35px;
        }
    }