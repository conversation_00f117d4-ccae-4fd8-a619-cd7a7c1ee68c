:root {
        --primary-color: #ff7eb9;
        --primary-light: #ffa6d2;
        --primary-dark: #e55a9b;
        --accent-color: #9fe7ff;
        --background-color: #fff5f9;
        --card-bg: #ffffff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #ffdfed;
        --shadow: 0 4px 8px rgba(255, 126, 185, 0.1);
        --border-radius: 15px;
    }

    /* 确保按钮图标为白色的全局规则 */
    .btn-primary i {
        color: white !important;
    }

    body {
        background-color: var(--background-color);
        color: var(--text-primary);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    /* 页面容器 */
    #stock-container {
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background-color: var(--background-color);
        color: var(--text-primary);
    }

    /* 页面标题 */
    .page-header {
        display: none; /* 隐藏原页面标题区域 */
    }

    /* 卡密仓库列表卡片 */
    .stock-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 20px;
        flex-grow: 1;
        overflow: auto;
        animation: slideUp 0.5s ease;
        margin-top: 0;
    }

    /* 卡片标题和按钮区域 */
    .card-title {
        font-size: 1.2rem;
        font-weight: 500;
        margin-bottom: 20px;
        color: var(--primary-dark);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-actions {
        display: flex;
        gap: 10px;
    }

    .card-title i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    /* 表格样式 */
    .stock-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .stock-table th,
    .stock-table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
    }

    /* 特定列的对齐方式 */
    .stock-table td:nth-child(3), /* 库存列 */
    .stock-table td:nth-child(4) { /* 操作列 */
        text-align: center !important;
        vertical-align: middle !important;
    }

    .stock-table th {
        background-color: #fff2f8;
        font-weight: 600;
        color: var(--primary-color);
        position: sticky;
        top: 0;
        z-index: 10;
        border-bottom: 2px solid #ffdfed;
    }

    .stock-table th i {
        margin-right: 8px;
        font-size: 0.9em;
        opacity: 0.8;
    }

    .stock-table tbody tr:last-child td {
        border-bottom: none;
    }

    .stock-table tr {
        position: relative;
    }

    .stock-table tbody tr:hover {
        background-color: #fff9fc;
    }

    /* 操作按钮 */
    .repo-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        width: fit-content;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .edit-btn {
        background-color: #ffe08a;
        color: #d19c00;
    }

    .edit-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(255, 224, 138, 0.5);
    }

    .delete-btn {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .delete-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(255, 184, 194, 0.5);
    }

    .export-btn {
        background-color: #d1f7c4;
        color: #5cb85c;
    }

    .export-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(209, 247, 196, 0.5);
    }

    .action-btn i {
        font-size: 16px;
    }

    /* 徽章/标签 */
    .badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        margin-right: 8px;
        color: white;
    }

    .badge-success {
        background-color: #d1f7c4;
        color: #5cb85c;
    }

    .badge-warning {
        background-color: #f8d7da;
        color: #dc3545;
    }

    /* 空状态 */
    .empty-state {
        text-align: center;
        padding: 50px 20px;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 60px;
        color: #ffdfed;
        margin-bottom: 20px;
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: var(--primary-color);
        font-weight: 600;
    }

    /* 模态框 */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        overflow: hidden; /* 防止整体页面滚动 */
    }

    .modal.show {
        display: flex;
    }

    .modal-dialog {
        position: relative;
        width: 100%;
        max-width: 600px;
        margin: 1.75rem auto;
        pointer-events: auto;
        overflow: hidden; /* 防止模态框内容溢出 */
    }

    .modal-content {
        position: relative;
        background: linear-gradient(to bottom, #fff, #fff5f9 120%);
        width: 100%;
        max-width: 600px;
        max-height: 90vh;
        border-radius: var(--border-radius);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 126, 185, 0.1);
        overflow: hidden;
        animation: modalFadeIn 0.3s ease;
        border: none;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
        color: white;
        padding: 18px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
        box-shadow: 0 3px 15px rgba(255, 126, 185, 0.2);
    }

    .modal-title {
        margin: 0;
        font-size: 20px;
        color: white;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .modal-title i {
        font-size: 22px;
    }

    .btn-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 22px;
        cursor: pointer;
        line-height: 1;
        transition: all 0.3s;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: rotate(90deg);
    }

    .modal-body {
        padding: 25px 30px;
        max-height: calc(90vh - 180px);
        overflow-y: auto;
        overflow-x: hidden;
        background-color: transparent;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        scrollbar-width: thin;
        scrollbar-color: var(--primary-light) transparent;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        background: rgba(255, 255, 255, 0.9);
        border-top: 1px solid rgba(255, 126, 185, 0.1);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        padding: 16px 30px 20px;
    }

    /* 美化表单样式 */
    .form-group {
        margin-bottom: 24px;
        position: relative;
    }

    .form-label {
        display: inline-block;
        margin-bottom: 10px;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.95rem;
        position: relative;
        padding-left: 18px;
        padding-bottom: 4px;
        border-bottom: 1px solid rgba(255, 126, 185, 0.1);
    }

    .form-label::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        background-color: var(--primary-color);
        border-radius: 50%;
        transition: all 0.3s;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        line-height: 1.5;
        color: var(--text-primary);
        position: relative;
        background-image: linear-gradient(to bottom, rgba(255, 245, 249, 0.5), rgba(255, 255, 255, 0.8));
    }

    .form-control::placeholder {
        color: #aaa;
        opacity: 0.7;
        transition: opacity 0.3s;
    }

    .form-control:focus::placeholder {
        opacity: 0.4;
    }

    .form-control:hover {
        border-color: var(--primary-light);
        box-shadow: 0 3px 8px rgba(255, 126, 185, 0.1);
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.15), inset 0 1px 2px rgba(0, 0, 0, 0.05);
        outline: none;
        background-image: linear-gradient(to bottom, rgba(255, 245, 249, 0.2), rgba(255, 255, 255, 1));
    }

    /* 美化文本域 */
    textarea.form-control {
        min-height: 130px;
        resize: vertical;
        line-height: 1.5;
        padding: 14px 16px;
        background-image: linear-gradient(to bottom, rgba(255, 245, 249, 0.3), rgba(255, 255, 255, 0.9) 20%);
        border-left: 3px solid var(--primary-light);
    }

    textarea.form-control:focus {
        border-left: 3px solid var(--primary-color);
    }

    /* 美化单选按钮样式 - 增加间距和改进视觉效果 */
    .radio-options {
        display: flex;
        gap: 40px;
        margin-top: 12px;
        margin-bottom: 10px;
        padding: 5px 0;
    }

    .form-check {
        position: relative;
        padding-left: 36px;
        margin-bottom: 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        min-height: 34px;
    }

    .form-check-input {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        cursor: pointer;
        margin-right: 12px;
        appearance: none;
        -webkit-appearance: none;
        border: 2px solid var(--border-color);
        border-radius: 50%;
        outline: none;
        transition: all 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .form-check-input:hover {
        border-color: var(--primary-light);
        box-shadow: 0 2px 6px rgba(255, 126, 185, 0.2);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(255, 126, 185, 0.2);
    }

    .form-check-input:checked::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 12px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: white;
        transform: translate(-50%, -50%);
    }

    .form-check-label {
        color: var(--text-primary);
        font-weight: 500;
        font-size: 0.95rem;
        cursor: pointer;
        transition: color 0.2s;
        margin-left: 4px;
        padding: 4px 0;
        position: relative;
    }

    .form-check-input:checked ~ .form-check-label {
        color: var(--primary-dark);
        font-weight: 600;
    }

    .form-check-input:checked ~ .form-check-label::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-light), transparent);
        opacity: 0.5;
    }

    /* 确认对话框美化 */
    .confirm-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 100001;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
        overflow: hidden;
    }

    .confirm-dialog.show {
        opacity: 1;
        visibility: visible;
    }

    .confirm-box {
        background-color: white;
        border-radius: 15px;
        width: 400px;
        max-width: 90%;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-20px);
        transition: transform 0.3s;
        border: 2px solid #ffd6eb;
        overflow: hidden;
    }

    .confirm-dialog.show .confirm-box {
        transform: translateY(0);
    }

    .confirm-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #ff6b6b;
        display: flex;
        align-items: center;
    }

    .confirm-title i {
        margin-right: 10px;
        font-size: 1.3rem;
        color: #ff6b6b;
    }

    .confirm-message {
        margin-bottom: 25px;
        color: var(--text-primary);
        font-size: 16px;
        line-height: 1.5;
        background-color: rgba(255, 107, 107, 0.05);
        padding: 15px;
        border-radius: 10px;
        border-left: 3px solid #ff6b6b;
    }

    .confirm-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
    }

    /* 通知提示 */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(255, 126, 185, 0.2);
        display: flex;
        align-items: center;
        max-width: 300px;
        width: auto;
        height: auto;
        transform: translateX(120%);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 200000;
        pointer-events: none;
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
        pointer-events: auto;
    }

    .notification.success {
        background: #d4f7e5;
        color: #155724;
        border-left: 4px solid #42b983;
    }

    .notification.error {
        background: #ffd6e0;
        color: #721c24;
        border-left: 4px solid #ff6b6b;
    }

    .notification-icon {
        margin-right: 10px;
        font-size: 18px;
    }

    .notification.success .notification-icon {
        color: #42b983;
    }

    .notification.error .notification-icon {
        color: #ff6b6b;
    }

    .notification-message {
        font-size: 14px;
        font-weight: 500;
    }

    /* 加载中指示器 */
    #loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 100000;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    #loading-overlay.show {
        display: flex;
    }

    .spinner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 126, 185, 0.3);
        border-radius: 50%;
        border-top: 2px solid var(--primary-color);
        animation: spin 1s linear infinite;
        margin-right: 6px;
        display: none; /* 默认隐藏 */
    }
    
    .loading-text {
        font-size: 16px;
        color: var(--primary-color);
        font-weight: 500;
    }

    /* 动画效果 */
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes rowFadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .stock-table tbody tr {
        animation: rowFadeIn 0.5s ease forwards;
    }
    .stock-table tbody tr:nth-child(1) { animation-delay: 0.05s; }
    .stock-table tbody tr:nth-child(2) { animation-delay: 0.1s; }
    .stock-table tbody tr:nth-child(3) { animation-delay: 0.15s; }
    .stock-table tbody tr:nth-child(4) { animation-delay: 0.2s; }
    .stock-table tbody tr:nth-child(5) { animation-delay: 0.25s; }
    .stock-table tbody tr:nth-child(6) { animation-delay: 0.3s; }
    .stock-table tbody tr:nth-child(7) { animation-delay: 0.35s; }
    .stock-table tbody tr:nth-child(8) { animation-delay: 0.4s; }
    .stock-table tbody tr:nth-child(9) { animation-delay: 0.45s; }
    .stock-table tbody tr:nth-child(10) { animation-delay: 0.5s; }

    /* 表单验证样式 */
    .invalid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.85em;
        color: #ff6b6b;
        padding-left: 18px;
        position: relative;
        animation: fadeIn 0.3s;
    }

    .invalid-feedback::before {
        content: "!";
        position: absolute;
        left: 0;
        top: 0;
        width: 14px;
        height: 14px;
        background-color: #ff6b6b;
        border-radius: 50%;
        color: white;
        font-size: 10px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }

    .form-control.is-invalid {
        border-color: #ff6b6b;
        background-image: none;
        padding-right: inherit;
    }

    .form-control.is-invalid ~ .invalid-feedback {
        display: block;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .stock-table {
            font-size: 0.9rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
        }

        .form-control,
        .btn {
            padding: 8px 15px;
            font-size: 0.9rem;
        }

        .modal-content {
            width: 95%;
        }
    }

    /* 库存状态颜色 */
    .stock-low {
        color: #ff6b6b;
        font-weight: 600;
        padding: 4px 10px;
        background-color: rgba(255, 107, 107, 0.1);
        border-radius: 20px;
        display: inline-block;
    }

    .stock-medium {
        color: #ffce56;
        font-weight: 600;
        padding: 4px 10px;
        background-color: rgba(255, 206, 86, 0.1);
        border-radius: 20px;
        display: inline-block;
    }

    .stock-high {
        color: #4cd964;
        font-weight: 600;
        padding: 4px 10px;
        background-color: rgba(76, 217, 100, 0.1);
        border-radius: 20px;
        display: inline-block;
    }

    /* 按钮波纹效果 */
    .btn::after {
        content: '';
        position: absolute;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 100px;
        height: 100px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.5s ease-out, opacity 0.5s ease-out;
        pointer-events: none;
        opacity: 0.7;
    }

    .btn:active::after {
        transform: translate(-50%, -50%) scale(2.5);
        opacity: 0;
    }

    /* 抖动动画 */
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    /* 清除Bootstrap的默认样式 */
    .btn-primary:focus,
    .btn-primary.focus,
    .btn-primary:not(:disabled):not(.disabled):active,
    .btn-primary:not(:disabled):not(.disabled).active,
    .show > .btn-primary.dropdown-toggle {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        box-shadow: none !important;
    }

    .btn-primary:not(:disabled):not(.disabled):active,
    .btn-primary:not(:disabled):not(.disabled).active {
        background-color: var(--primary-dark) !important;
        transform: translateY(1px) !important;
    }

    /* 覆盖Bootstrap的按钮激活状态样式 */
    .btn-check:checked + .btn-primary, 
    .btn-check:active + .btn-primary, 
    .btn-primary:active, 
    .btn-primary.active, 
    .show > .btn-primary.dropdown-toggle {
        color: #fff !important;
        background-color: var(--primary-dark) !important;
        border-color: var(--primary-dark) !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 126, 185, 0.25) !important;
    }

    /* 按钮呼吸灯效果 */
    @keyframes pulse {
        0% {
            box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
        }
        50% {
            box-shadow: 0 4px 12px rgba(255, 158, 210, 0.4);
        }
        100% {
            box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
        }
    }

    .btn-primary {
        animation: pulse 2s infinite;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .btn i {
        margin-right: 8px;
    }

    /* 主要按钮样式 */
    .btn-primary {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    .btn-light {
        background-color: #f0f0f0;
        color: #666;
        border: none;
    }

    .btn-light:hover {
        background-color: #e0e0e0;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-danger {
        background-color: #ff6b6b;
        color: white;
        border-color: #ff6b6b;
    }

    .btn-danger:hover {
        background-color: #e84c4c;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(232, 76, 76, 0.3);
    }

    /* 其他UI优化 */
    /* 确保模态框完全响应式 */
    @media (max-width: 640px) {
        .modal-content,
        .modal-dialog {
            width: 95%;
            max-width: 95%;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-header {
            padding: 15px 20px;
        }
        
        .modal-footer {
            padding: 15px 20px 20px;
        }
        
        .confirm-box {
            width: 320px;
        }
        
        .form-label {
            font-size: 14px;
        }
    }

    /* 表单焦点动画 */
    .form-control:focus + .form-label {
        color: var(--primary-color);
        transition: color 0.3s;
    }

    .form-control:focus ~ .form-label::before {
        transform: translateY(-50%) scale(1.2);
        box-shadow: 0 0 6px rgba(255, 126, 185, 0.5);
    }

    /* 模态框中输入项的过渡动画 */
    .modal-body .form-group {
        opacity: 0;
        transform: translateY(10px);
        animation: fadeInUp 0.4s forwards;
    }

    .modal-body .form-group:nth-child(1) { animation-delay: 0.1s; }
    .modal-body .form-group:nth-child(2) { animation-delay: 0.2s; }
    .modal-body .form-group:nth-child(3) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 修复水平滚动问题 */
    html, body {
        overflow-x: hidden;
        max-width: 100%;
    }

    #stock-container {
        overflow-x: hidden;
        max-width: 100vw;
    }

    /* 提高模态框弹出时的可点击性 */
    .modal, .confirm-dialog {
        pointer-events: auto;
    }

    .modal-dialog, .confirm-box {
        z-index: 10002;
    }

    /* 确保表单元素内容不溢出 */
    .form-control {
        box-sizing: border-box;
        word-break: break-word;
        max-width: 100%;
    }

    /* 美化滚动条 */
    .modal-body::-webkit-scrollbar {
        width: 6px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: rgba(255, 245, 249, 0.5);
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background-color: var(--primary-light);
        border-radius: 10px;
    }