:root {
        --primary-color: #ff7eb9;
        --primary-light: #ffa6d2;
        --primary-dark: #e55a9b;
        --accent-color: #9fe7ff;
        --background-color: #fff5f9;
        --card-bg: #ffffff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #ffdfed;
        --success-color: #4cd964;
        --warning-color: #ffce56;
        --danger-color: #ff6b6b;
        --shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        --border-radius: 15px;
    }

    #category-container {
        width: 100%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: auto;
        background-color: var(--background-color);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        background-color: var(--card-bg);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        box-shadow: var(--shadow);
        margin-bottom: 0;
    }

    .page-title {
        font-size: 24px;
        color: var(--text-primary);
        margin: 0;
        font-weight: 600;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border-radius: 25px;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .btn i {
        margin-right: 6px;
    }

    .btn-primary {
        background-color: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    .btn-outline {
        background-color: white;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

    .btn-outline:hover {
        background-color: var(--primary-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    /* 分类列表区域 */
    .category-list-container {
        background-color: var(--card-bg);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        box-shadow: var(--shadow);
        overflow: auto;
        max-height: calc(100vh - 150px);
        flex-grow: 1;
        animation: slideUp 0.5s ease;
        padding: 20px;
        margin-top: 0;
    }

    .category-list {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .category-list thead {
        background-color: #fff2f8;
    }

    .category-list th {
        color: #ff7eb9;
        font-weight: 600;
        padding: 15px;
        text-align: left;
        border-bottom: 2px solid #ffdfed;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .category-list td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #555;
        vertical-align: middle;
    }

    .category-list th:nth-child(1),
    .category-list td:nth-child(1) {
        width: 15%; 
        padding-left: 15px;
        white-space: nowrap;
    }

    .category-list th:nth-child(2),
    .category-list td:nth-child(2) {
        width: 15%;
        text-align: center;
    }

    .category-list th:nth-child(3),
    .category-list td:nth-child(3) {
        width: 20%;
    }

    .category-list th:nth-child(4),
    .category-list td:nth-child(4) {
        width: 15%; 
        text-align: center;
    }

    .category-list th:nth-child(5),
    .category-list td:nth-child(5) {
        width: 15%;
        text-align: center;
    }

    .category-list th:nth-child(6),
    .category-list td:nth-child(6) {
        width: 20%;
        text-align: center;
    }

    .category-list tr:last-child td {
        border-bottom: none;
    }

    .category-list tr:hover {
        background-color: #fff9fc;
    }

    /* 分类操作按钮 */
    .category-actions {
        display: flex;
        justify-content: center;
        gap: 8px;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .edit-btn {
        background-color: #ffe08a;
        color: #d19c00;
    }

    .edit-btn:hover {
        background-color: #ffd257;
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(255, 208, 87, 0.3);
    }

    .delete-btn {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .delete-btn:hover {
        background-color: #ff94a4;
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(255, 148, 164, 0.3);
    }

    .add-subcategory-btn {
        background-color: #a5edb7;
        color: #3bb853;
    }

    .add-subcategory-btn:hover {
        background-color: #83e79b;
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(131, 231, 155, 0.3);
    }

    /* 分类图片 */
    .category-image {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid var(--border-color);
        transition: transform 0.3s ease;
        box-shadow: 0 3px 6px rgba(255, 126, 185, 0.1);
    }

    .category-image:hover {
        transform: scale(1.15);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.2);
    }

    /* 层级关系图标 */
    .toggle-icon {
        cursor: pointer;
        margin-right: 8px;
        transition: transform 0.3s;
    }

    .parent-category {
        font-weight: 600;
    }

    /* 二级分类样式 */
    .subcategory {
        background-color: rgba(255, 246, 249, 0.5);
    }

    /* 删除旧的连接线样式 */
    .subcategory:before,
    .subcategory:after {
        content: none;
    }

    /* 二级分类箭头样式 - 最终调整 */
    .subcategory-arrow {
        position: relative;
        display: inline-flex;
        align-items: center;
        margin-left: 24px;
    }

    /* 二级分类箭头图标 */
    .subcategory-arrow i {
        color: #ff7eb9;
        font-size: 12px;
        margin-right: 4px;
    }

    /* 垂直连接线 */
    .subcategory-arrow::before {
        content: '';
        position: absolute;
        left: -14px;
        top: -25px;
        height: 50px;
        width: 2px;
        background-color: #ffd6eb;
        z-index: 1;
    }

    /* 水平连接线 */
    .subcategory-arrow::after {
        content: '';
        position: absolute;
        left: -14px;
        top: 50%;
        width: 14px;
        height: 2px;
        background-color: #ffd6eb;
        z-index: 1;
    }

    .subcategory .category-name {
        font-style: italic;
        color: var(--text-primary);
    }

    .collapsed .toggle-icon {
        transform: rotate(-90deg);
    }

    .hidden {
        display: none;
    }

    .category-level-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        color: white;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }

    .level-1 {
        background-color: var(--primary-color);
    }

    .level-2 {
        background-color: var(--accent-color);
    }

    /* 加载状态 */
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        flex-direction: column;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid rgba(255, 126, 185, 0.1);
        border-top-color: var(--primary-color);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* 模态框样式 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .modal {
        background-color: white;
        border-radius: var(--border-radius);
        width: 450px;
        max-width: 90%;
        box-shadow: 0 10px 30px rgba(255, 182, 219, 0.3);
        border: 2px solid #ffe6f2;
        overflow: hidden;
        transform: translateY(-20px);
        transition: all 0.3s;
        animation: modalPop 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    @keyframes modalPop {
        0% {
            transform: scale(0.9);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .modal-overlay.active .modal {
        transform: translateY(0);
    }

    .modal-header {
        background-color: #ff9ed2;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px dashed #fff;
    }

    .modal-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: white;
        display: flex;
        align-items: center;
    }

    .modal-title i {
        margin-right: 10px;
    }

    .modal-close {
        font-size: 24px;
        cursor: pointer;
        color: white;
        transition: all 0.3s;
        opacity: 0.8;
        line-height: 0.8;
        height: 28px;
        width: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: none;
        border: none;
    }

    .modal-close:hover {
        transform: rotate(90deg);
        opacity: 1;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-body::-webkit-scrollbar {
        width: 8px;
    }

    .modal-body::-webkit-scrollbar-track {
        background: #f8f8f8;
        border-radius: 10px;
    }

    .modal-body::-webkit-scrollbar-thumb {
        background-color: #ff9ed2;
        border-radius: 10px;
        border: 2px solid #f8f8f8;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 15px 20px;
        border-top: 2px dashed #ffe6f2;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 2px solid #ffe6f2;
        border-radius: 10px;
        font-size: 15px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        border-color: #ff7eb9;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    select.form-control {
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ff7eb9' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        padding-right: 30px;
    }

    /* 空状态 */
    .empty-state {
        padding: 40px;
        text-align: center;
        color: var(--text-secondary);
        animation: fadeIn 0.8s ease;
    }

    .empty-state i {
        font-size: 60px;
        color: #ffdfed;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: var(--text-primary);
        font-size: 18px;
    }

    .empty-state p {
        color: #999;
        font-size: 14px;
    }

    /* 通知样式 */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        max-width: 300px;
        transform: translateX(120%);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 1200;
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .notification.success {
        border-left: 4px solid #42b983;
    }

    .notification.error {
        border-left: 4px solid #ff6b6b;
    }

    .notification.warning {
        border-left: 4px solid #ffce56;
    }

    .notification.info {
        border-left: 4px solid #64b5f6;
    }

    .notification i {
        margin-right: 12px;
        font-size: 18px;
    }

    .notification.success i {
        color: #42b983;
    }

    .notification.error i {
        color: #ff6b6b;
    }

    .notification.warning i {
        color: #ffce56;
    }

    .notification.info i {
        color: #64b5f6;
    }

    .notification span {
        flex-grow: 1;
        font-size: 14px;
        color: var(--text-primary);
    }

    .notification .close-notification {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        opacity: 0.5;
        transition: opacity 0.2s;
        padding: 0;
        line-height: 1;
    }

    .notification .close-notification:hover {
        opacity: 1;
    }

    /* 确认删除对话框 */
    .confirm-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1100;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .confirm-dialog.active {
        opacity: 1;
        visibility: visible;
    }

    .confirm-box {
        background-color: white;
        border-radius: 15px;
        width: 400px;
        max-width: 90%;
        padding: 20px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-20px);
        transition: transform 0.3s;
        border: 2px solid #ffe6f2;
    }

    .confirm-dialog.active .confirm-box {
        transform: translateY(0);
    }

    .confirm-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #ff3e5f;
        display: flex;
        align-items: center;
    }

    .confirm-title i {
        margin-right: 10px;
    }

    .confirm-message {
        margin-bottom: 20px;
        color: var(--text-primary);
        line-height: 1.5;
    }

    .confirm-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .btn-danger {
        background-color: #ff6b6b;
        color: white;
    }

    .btn-danger:hover {
        background-color: #ff5252;
    }

    .btn-cancel {
        background-color: #f0f0f0;
        color: #666;
    }

    .btn-cancel:hover {
        background-color: #e0e0e0;
    }

    /* 图片上传预览 */
    .image-preview {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        border: 2px solid var(--border-color);
        margin-top: 10px;
        display: none;
        box-shadow: 0 3px 6px rgba(255, 126, 185, 0.2);
    }

    .image-preview.active {
        display: block;
    }

    .upload-btn {
        display: inline-block;
        padding: 8px 16px;
        background-color: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        margin-top: 10px;
        transition: all 0.3s ease;
    }

    .upload-btn:hover {
        background-color: var(--primary-light);
        color: white;
    }

    /* 子分类树形结构连接线样式 */
    .subcategory-row .id-column {
        position: relative;
    }

    /* 竖线 - 连接所有子分类 */
    .subcategory-row .id-column::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        height: 100%;
        width: 2px;
        background-color: #ffd6eb;
        z-index: 1;
    }

    /* 横线 - 连接到每个子分类 */
    .subcategory-row .id-column::after {
        content: '';
        position: absolute;
        left: 10px;
        top: 50%;
        width: 12px;
        height: 2px;
        background-color: #ffd6eb;
        z-index: 1;
    }

    /* 单个子分类显示完整竖线 */
    .tree-single .id-column::before {
        top: 0;
        height: 50%; /* 竖线到中间 */
    }

    /* 为第一个子分类添加特殊样式 */
    .tree-start .id-column::before {
        top: 50%;
        height: 50%;
    }

    /* 为最后一个子分类添加特殊样式 */
    .tree-end .id-column::before {
        height: 50%;
        bottom: 50%;
        top: auto;
    }

    /* 子分类行的内边距 */
    .subcategory-row .id-column > div {
        padding-left: 25px;
        position: relative;
        min-height: 22px;
        display: flex;
        align-items: center;
    }

    /* 添加动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 子分类悬停效果 */
    .subcategory-row:hover {
        background-color: #fff9fc;
    }

    /* 强制显示切换按钮 */
    .toggle-subcategory-container {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        margin-right: 2px;
    }

    /* 调整子分类样式 */
    tr.subcategory {
        background-color: rgba(255, 246, 249, 0.5);
    }

    /* 添加层级显示相关样式 */
    .subcategory-row {
        display: none; /* 默认隐藏二级分类 */
    }
    
    .subcategory-row.visible {
        display: table-row; /* 显示展开的二级分类 */
        animation: fadeIn 0.3s ease;
    }
    
    /* 切换按钮样式 */
    .toggle-subcategory {
        cursor: pointer;
        margin-right: 5px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        color: #aaa;
        transition: all 0.3s ease;
        background: none;
        border: none;
    }
    
    .toggle-subcategory i {
        font-size: 12px;
        transition: transform 0.3s ease;
    }
    
    .toggle-subcategory:hover {
        color: var(--primary-color);
    }
    
    .category-row {
        background-color: #fff;
    }
    
    .category-row.expanded {
        background-color: rgba(255, 245, 249, 0.8);
    }
    
    .subcategory-count {
        display: inline-block;
        background-color: var(--primary-light);
        color: white;
        font-size: 0.75rem;
        border-radius: 10px;
        padding: 2px 6px;
        margin-left: 5px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 166, 210, 0.4);
        }
        70% {
            box-shadow: 0 0 0 5px rgba(255, 166, 210, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 166, 210, 0);
        }
    }

    /* ========== 移动端布局层次结构优化 ========== */
    @media (max-width: 768px) {
        /* 分类页面容器 - 简洁布局 */
        #category-container {
            background-color: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        /* 页面头部 - 一级层次：重要功能区域 */
        .page-header {
            background-color: #ffffff !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
            padding: 32px 24px !important;
            margin-bottom: 32px !important;
            border: 1px solid #e5e5e5 !important;
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 24px !important;
        }

        /* 页面标题 - 最高层次 */
        .page-title {
            font-size: 1.875rem !important; /* 30px - 最大标题 */
            font-weight: 800 !important;
            color: #1a1a1a !important;
            margin-bottom: 0 !important;
        }

        .action-buttons {
            width: 100% !important;
            display: flex !important;
            justify-content: flex-end !important;
            gap: 8px !important;
            flex-wrap: wrap !important;
        }

        /* 分类列表卡片 - 主要内容区域 */
        .categories-card {
            background-color: #ffffff !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
            padding: 32px 24px !important;
            margin: 0 !important;
            border: 1px solid #e5e5e5 !important;
        }

        /* 表格 - 保持完整可读性，通过滑动解决空间问题 */
        .categories-table {
            font-size: 1rem !important; /* 16px - 保持最佳可读性 */
            min-width: 800px !important; /* 足够的宽度确保内容不被压缩 */
            background-color: #ffffff !important;
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e5e5e5 !important;
        }

        /* 表格头部 - 突出层次 */
        .categories-table th {
            background-color: #ff6b9d !important;
            color: white !important;
            font-weight: 600 !important;
            font-size: 1rem !important; /* 16px - 与内容同等重要 */
            padding: 16px 8px !important;
            border: none !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 10 !important;
            text-align: left !important;
        }

        /* 表格单元格 - 保持舒适的阅读体验 */
        .categories-table td {
            padding: 16px 8px !important;
            font-size: 1rem !important; /* 16px - 保持可读性 */
            border-bottom: 1px solid #f0f0f0 !important;
            color: #1a1a1a !important;
            line-height: 1.5 !important;
        }

        /* 表格行交互反馈 */
        .categories-table tbody tr:active {
            background-color: #fafafa !important;
        }

        /* 操作按钮 - 符合移动端触摸标准 */
        .action-btn {
            width: 44px !important; /* 符合44px最小触摸面积标准 */
            height: 44px !important;
            border-radius: 50% !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.2s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .action-btn:active {
            transform: scale(0.9) !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
        }

        .action-buttons {
            gap: 8px !important;
            justify-content: center !important;
            align-items: center !important;
        }

        /* 主要操作按钮 - 合理美观的设计 */
        .btn-primary {
            font-size: 14px !important; /* 合理的字体大小 */
            font-weight: 500 !important;
            padding: 10px 20px !important;
            border-radius: 8px !important;
            min-height: 40px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            border: none !important;
            transition: all 0.2s ease !important;
        }

        .btn-primary:active {
            transform: scale(0.98) !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
        }

        /* 次要操作按钮 */
        .btn-light {
            font-size: 13px !important; /* 稍小的字体 */
            font-weight: 400 !important;
            padding: 8px 16px !important;
            border-radius: 6px !important;
            min-height: 36px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.2s ease !important;
        }

        .btn-light:active {
            transform: scale(0.98) !important;
            background-color: #fafafa !important;
        }

        /* 表单控件 - 保持良好的输入体验 */
        .form-control {
            font-size: 1rem !important; /* 16px - 防止iOS缩放 */
            padding: 16px !important;
            border-radius: 8px !important;
            border: 2px solid #e5e5e5 !important;
            background-color: #ffffff !important;
            color: #1a1a1a !important;
            min-height: 48px !important;
            transition: border-color 0.2s ease !important;
        }

        .form-control:focus {
            border-color: #ff6b9d !important;
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1) !important;
            outline: none !important;
        }

        /* 模态框 - 等比例缩小，左右留空 */
        .modal-content {
            width: calc(100% - 32px) !important;
            max-width: calc(100% - 32px) !important;
            height: calc(100vh - 80px) !important;
            max-height: calc(100vh - 80px) !important;
            margin: 40px 16px !important;
            border-radius: 12px !important;
            border: none !important;
            background-color: #ffffff !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
            overflow: hidden !important;
        }

        .modal-header {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
            color: white !important;
            padding: 20px !important;
            border-bottom: none !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 100 !important;
        }

        .modal-title {
            font-size: 18px !important; /* 合理的标题大小 */
            font-weight: 600 !important;
            color: white !important;
        }

        .modal-body {
            padding: 20px !important;
            max-height: calc(100vh - 200px) !important;
            overflow-y: auto !important;
            background-color: #ffffff !important;
        }

        /* 确认对话框 - 重要决策的突出显示 */
        .confirm-box {
            width: 90% !important;
            max-width: 400px !important;
            padding: 32px !important;
            border-radius: 12px !important;
            background-color: #ffffff !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
        }

        .confirm-title {
            font-size: 1.25rem !important; /* 20px */
            font-weight: 700 !important;
            margin-bottom: 24px !important;
            color: #ff6b6b !important;
        }

        .confirm-message {
            font-size: 1rem !important;
            line-height: 1.6 !important;
            color: #1a1a1a !important;
            margin-bottom: 32px !important;
        }

        /* 通知提示 - 适配移动端位置 */
        .notification {
            top: 24px !important;
            right: 16px !important;
            left: 16px !important;
            max-width: none !important;
            width: auto !important;
            padding: 24px !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
            font-size: 1rem !important;
        }

        /* 分类树形结构 - 层次化显示 */
        .category-tree {
            padding: 16px !important;
            background-color: #fafafa !important;
            border-radius: 8px !important;
            border: 1px solid #f0f0f0 !important;
        }

        .category-item {
            padding: 12px !important;
            margin-bottom: 8px !important;
            background-color: #ffffff !important;
            border-radius: 6px !important;
            border: 1px solid #f0f0f0 !important;
        }

        .category-name {
            font-size: 1rem !important; /* 16px */
            font-weight: 500 !important;
            color: #1a1a1a !important;
        }

        .category-actions {
            gap: 8px !important;
            margin-top: 8px !important;
        }

        .category-actions .btn {
            padding: 8px 16px !important;
            font-size: 0.875rem !important; /* 14px */
            min-height: 36px !important;
            border-radius: 6px !important;
        }
    }

    /* 超小屏幕设备层次优化 */
    @media (max-width: 480px) {
        /* 表格 - 保持可读性，通过滑动解决空间 */
        .categories-table {
            min-width: 700px !important; /* 保持足够宽度 */
            font-size: 0.875rem !important; /* 14px - 在超小屏幕上适当缩小 */
        }

        .categories-table th,
        .categories-table td {
            padding: 8px 4px !important;
            font-size: 0.875rem !important; /* 14px */
        }

        /* 按钮和表单 - 保持触摸友好 */
        .btn-primary {
            font-size: 1rem !important;
            padding: 12px 24px !important;
            min-height: 44px !important; /* 保持触摸标准 */
        }

        .btn-light {
            font-size: 0.875rem !important;
            padding: 8px 16px !important;
        }

        .form-control {
            font-size: 1rem !important; /* 16px - 防止缩放 */
            padding: 12px !important;
        }

        /* 标题适配超小屏幕 */
        .page-title {
            font-size: 1.5rem !important; /* 24px */
        }

        /* 模态框适配 */
        .modal-body {
            padding: 16px !important;
        }

        /* 分类项目适配 */
        .category-name {
            font-size: 0.875rem !important; /* 14px */
        }

        .category-actions .btn {
            padding: 6px 12px !important;
            font-size: 0.75rem !important; /* 12px */
            min-height: 32px !important;
        }
    }

    /* ========== 分类页面移动端布局专项优化 ========== */
    @media (max-width: 768px) {
        /* 分类页面容器 - 整体美化描边效果 */
        #category-container {
            background-color: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
            position: relative !important;
            overflow: hidden !important; /* 禁用上下滚动 */
            height: 100vh !important; /* 固定高度为视窗高度 */
            display: flex !important;
            flex-direction: column !important;
        }

        /* 分类页面整体装饰边框 */
        #category-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.15) 0%,
                rgba(255, 139, 171, 0.1) 25%,
                rgba(255, 166, 210, 0.08) 50%,
                rgba(255, 139, 171, 0.1) 75%,
                rgba(255, 107, 157, 0.15) 100%) !important;
            border-radius: 16px !important;
            z-index: -1 !important;
            pointer-events: none !important;
        }

        /* 页面头部优化 - 包含表格容器的白色背景 */
        .page-header {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
            border-radius: 14px 14px 0 0 !important; /* 上圆角，下方连接表格 */
            box-shadow:
                0 4px 16px rgba(255, 107, 157, 0.08),
                0 2px 8px rgba(0, 0, 0, 0.06),
                0 1px 4px rgba(0, 0, 0, 0.04) !important;
            padding: 24px 20px 20px 20px !important;
            margin-bottom: 0 !important; /* 移除底部间距 */
            border: 2px solid rgba(255, 107, 157, 0.1) !important;
            border-bottom: none !important; /* 移除底部边框，与表格容器连接 */
            flex-direction: row !important; /* 水平排列 */
            align-items: center !important; /* 垂直居中对齐 */
            justify-content: space-between !important; /* 两端对齐 */
            gap: 16px !important;
            position: relative !important;
            overflow: hidden !important;
        }

        /* 页面头部顶部装饰条 */
        .page-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                #ff6b9d 0%,
                #ff8fab 25%,
                #ffa6d2 50%,
                #ff8fab 75%,
                #ff6b9d 100%) !important;
            border-radius: 14px 14px 0 0 !important;
            z-index: 1 !important;
        }

        /* 页面标题优化 - 左侧对齐 */
        .page-title {
            font-size: 1.75rem !important; /* 28px - 适中的标题大小 */
            font-weight: 700 !important;
            color: #1a1a1a !important;
            margin: 0 !important; /* 移除所有外边距 */
            flex: 1 !important; /* 占据剩余空间 */
        }

        /* 操作按钮区域优化 - 右上角对齐 */
        .action-buttons {
            width: auto !important; /* 自动宽度 */
            display: flex !important;
            justify-content: flex-end !important; /* 右对齐 */
            gap: 8px !important;
            flex-wrap: nowrap !important; /* 不换行 */
            margin: 0 !important; /* 移除外边距 */
            flex-shrink: 0 !important; /* 不收缩 */
        }

        /* 分类列表容器优化 - 与页面头部融为一体 */
        .category-list-container {
            background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%) !important;
            border-radius: 0 0 14px 14px !important; /* 下圆角，上方连接头部 */
            box-shadow:
                0 6px 20px rgba(255, 107, 157, 0.08),
                0 3px 10px rgba(0, 0, 0, 0.06),
                0 1px 4px rgba(0, 0, 0, 0.04) !important;
            padding: 0 20px 24px 20px !important; /* 顶部无内边距，与头部连接 */
            margin: 0 !important;
            border: 2px solid rgba(255, 107, 157, 0.1) !important;
            border-top: none !important; /* 移除顶部边框，与头部连接 */
            position: relative !important;
            overflow-y: auto !important; /* 启用垂直滚动 */
            overflow-x: hidden !important; /* 禁用水平滚动，由table-responsive处理 */
            flex: 1 !important; /* 占据剩余空间 */
            max-height: calc(100vh - 120px) !important; /* 限制最大高度，为header留出空间 */
            -webkit-overflow-scrolling: touch !important; /* iOS平滑滚动 */
        }

        /* 表格容器滚动优化 */
        .table-responsive {
            overflow-x: auto !important;
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
            margin-top: 20px !important; /* 与头部保持间距 */
            border-radius: 12px !important;
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.08) !important;
            background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%) !important;
            position: relative !important;
        }

        /* 表格容器美化滚动条样式 */
        .table-responsive::-webkit-scrollbar {
            width: 6px !important;
            height: 6px !important;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: rgba(255, 107, 157, 0.05) !important;
            border-radius: 3px !important;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
            border-radius: 3px !important;
            transition: all 0.3s ease !important;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff5a8a 0%, #ff7ea0 100%) !important;
            box-shadow: 0 2px 6px rgba(255, 107, 157, 0.3) !important;
        }

        .table-responsive::-webkit-scrollbar-corner {
            background: rgba(255, 107, 157, 0.05) !important;
        }

        /* 分类列表容器美化滚动条样式 */
        .category-list-container::-webkit-scrollbar {
            width: 6px !important;
        }

        .category-list-container::-webkit-scrollbar-track {
            background: rgba(255, 107, 157, 0.05) !important;
            border-radius: 3px !important;
        }

        .category-list-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
            border-radius: 3px !important;
            transition: all 0.3s ease !important;
        }

        .category-list-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff5a8a 0%, #ff7ea0 100%) !important;
            box-shadow: 0 2px 6px rgba(255, 107, 157, 0.3) !important;
        }

        /* 表格样式优化 - 增加列宽和列距离 */
        .category-list.categories-table {
            min-width: 1000px !important; /* 增加最小宽度，确保水平显示 */
            font-size: 1rem !important; /* 16px - 保持可读性 */
            background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%) !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow:
                0 6px 20px rgba(255, 107, 157, 0.08),
                0 3px 10px rgba(0, 0, 0, 0.06),
                0 1px 4px rgba(0, 0, 0, 0.04) !important;
            border: 2px solid rgba(255, 107, 157, 0.1) !important;
            position: relative !important;
        }

        /* 表格装饰边框 */
        .category-list.categories-table::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.2) 0%,
                rgba(255, 139, 171, 0.15) 25%,
                rgba(255, 166, 210, 0.1) 50%,
                rgba(255, 139, 171, 0.15) 75%,
                rgba(255, 107, 157, 0.2) 100%) !important;
            border-radius: 14px !important;
            z-index: -1 !important;
        }

        /* 表格头部优化 */
        .category-list.categories-table th {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
            color: white !important;
            font-weight: 600 !important;
            font-size: 1rem !important; /* 16px */
            padding: 16px 12px !important; /* 增加内边距 */
            border: none !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 10 !important;
            text-align: left !important;
            white-space: nowrap !important; /* 防止文字换行 */
            box-shadow: 0 2px 8px rgba(255, 107, 157, 0.2) !important;
        }

        /* 表格单元格优化 - 增加列宽和间距 */
        .category-list.categories-table td {
            padding: 16px 12px !important; /* 增加内边距 */
            font-size: 1rem !important; /* 16px */
            border-bottom: 1px solid #f0f0f0 !important;
            color: #1a1a1a !important;
            line-height: 1.5 !important;
            white-space: nowrap !important; /* 防止内容换行 */
        }

        /* 具体列宽优化 - 确保内容水平显示 */
        .category-list th:nth-child(1),
        .category-list td:nth-child(1) {
            width: 120px !important; /* ID列 */
            min-width: 120px !important;
            padding-left: 16px !important;
        }

        .category-list th:nth-child(2),
        .category-list td:nth-child(2) {
            width: 100px !important; /* 图片列 */
            min-width: 100px !important;
            text-align: center !important;
        }

        .category-list th:nth-child(3),
        .category-list td:nth-child(3) {
            width: 200px !important; /* 名称列 */
            min-width: 200px !important;
        }

        .category-list th:nth-child(4),
        .category-list td:nth-child(4) {
            width: 120px !important; /* 层级列 */
            min-width: 120px !important;
            text-align: center !important;
        }

        .category-list th:nth-child(5),
        .category-list td:nth-child(5) {
            width: 140px !important; /* 时间列 */
            min-width: 140px !important;
            text-align: center !important;
        }

        .category-list th:nth-child(6),
        .category-list td:nth-child(6) {
            width: 180px !important; /* 操作列 */
            min-width: 180px !important;
            text-align: center !important;
        }

        /* 表格行交互优化 */
        .category-list tbody tr:active {
            background-color: #fafafa !important;
        }

        /* 操作按钮优化 - 符合移动端触摸标准 */
        .action-btn {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.2s ease !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 2px !important; /* 增加按钮间距 */
        }

        .action-btn:active {
            transform: scale(0.9) !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
        }

        .category-actions {
            gap: 4px !important;
            justify-content: center !important;
            align-items: center !important;
        }
    }