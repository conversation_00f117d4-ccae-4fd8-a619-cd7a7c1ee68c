/* 卡券管理页面样式 */
.coupon-management-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    min-height: calc(100vh - 100px);
    width: 100%;
    box-sizing: border-box;
    position: relative;
}

/* 确保在单页面应用中的兼容性 */
#page-content .coupon-management-container {
    margin: 0;
    width: 100%;
    max-width: none;
}

/* 修复单页面应用中的容器问题 */
#page-content {
    padding: 6px !important;
}

#page-content .coupon-management-container {
    padding: 15px !important;
}

/* 针对单页面应用的电脑端布局修复 */
#page-content .coupon-management-container .page-header {
    min-width: auto; /* 在单页面应用中取消最小宽度限制 */
    flex-wrap: wrap; /* 允许换行 */
}

#page-content .coupon-management-container .header-center {
    min-width: 350px; /* 减少最小宽度要求 */
    flex: 1 1 auto; /* 允许伸缩 */
}

#page-content .coupon-management-container .filter-group {
    min-width: 350px; /* 减少最小宽度要求 */
    max-width: 100%; /* 允许占满容器 */
    justify-content: flex-start; /* 左对齐 */
}

/* 当容器宽度不足时的紧凑布局 */
@media (max-width: 1300px) {
    .coupon-management-container .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
        min-width: auto; /* 移除最小宽度限制 */
    }

    .coupon-management-container .header-left,
    .coupon-management-container .header-center,
    .coupon-management-container .header-actions {
        width: 100%;
        margin: 0;
        min-width: auto;
    }

    .coupon-management-container .header-center {
        justify-content: flex-start;
        min-width: auto; /* 移除最小宽度限制 */
    }

    .coupon-management-container .filter-group {
        justify-content: flex-start;
        min-width: auto;
        gap: 12px;
        flex-wrap: wrap; /* 允许换行 */
    }

    .coupon-management-container .search-box {
        width: 200px;
        min-width: 200px;
    }

    .coupon-management-container .filter-select {
        width: 140px;
        min-width: 140px;
    }
}

/* 页面标题和筛选操作区域 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    min-width: 1000px; /* 增加最小宽度确保有足够空间 */
    flex-wrap: nowrap; /* 防止换行 */
    gap: 20px; /* 增加间距 */
}

.header-left {
    flex: 0 0 auto;
    min-width: 150px;
    width: 150px; /* 固定宽度 */
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: flex-start; /* 改为左对齐 */
    margin: 0 20px;
    min-width: 550px; /* 增加最小宽度确保筛选区域有足够空间 */
    max-width: 750px; /* 限制最大宽度 */
}

.header-actions {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
}

.page-title {
    font-size: 24px;
    color: #333;
    margin: 0;
    font-weight: 600;
}

/* 筛选组样式 */
.filter-group {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: nowrap; /* 防止换行导致重叠 */
    justify-content: flex-start; /* 左对齐避免居中导致的空间不足 */
    min-width: 500px; /* 增加最小宽度确保有足够空间 */
    width: 100%;
    max-width: 700px; /* 增加最大宽度 */
}

.search-box {
    position: relative;
    width: 200px;
    min-width: 200px; /* 固定最小宽度 */
    flex-shrink: 0; /* 防止收缩 */
    flex-grow: 0; /* 防止扩展 */
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff7eb9;
    font-size: 16px;
}

.search-input {
    width: 100%;
    padding: 10px 15px 10px 45px;
    border: 1px solid #ffdfed;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-input:focus {
    outline: none;
    border-color: #ff7eb9;
}

.filter-select {
    padding: 10px 15px;
    border: 1px solid #ffdfed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s;
    min-width: 140px; /* 增加最小宽度 */
    width: 140px; /* 增加固定宽度 */
    flex-shrink: 0; /* 防止收缩 */
    flex-grow: 0; /* 防止扩展 */
    box-sizing: border-box; /* 确保盒模型一致 */
    margin-right: 5px; /* 增加右边距 */
}

.filter-select:focus {
    outline: none;
    border-color: #ff7eb9;
}

.reset-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #ff7eb9;
    border-radius: 50%;
    background: #ff7eb9;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0; /* 防止收缩 */
    flex-grow: 0; /* 防止扩展 */
    margin-left: 10px; /* 增加左边距 */
}

.reset-btn:hover {
    background: #ff5ca8;
}

.fix-layout-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #ff6b6b;
    border-radius: 50%;
    background: #ff6b6b;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
}

.fix-layout-btn:hover {
    background: #ee5a52;
    transform: scale(1.1);
}

/* 操作按钮样式 */
.add-coupon-btn, .export-btn, .delete-selected-btn {
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
}

.add-coupon-btn {
    background-color: #ff7eb9;
    color: white;
}

.export-btn {
    background-color: #4ecdc4;
    color: white;
}

.delete-selected-btn {
    background-color: #ff6b6b;
    color: white;
}

.add-coupon-btn:hover {
    background-color: #ff5ca8;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
}

.export-btn:hover {
    background-color: #44a08d;
    transform: translateY(-2px);
}

.delete-selected-btn:hover {
    background-color: #ee5a52;
    transform: translateY(-2px);
}

.add-coupon-btn:disabled, .export-btn:disabled, .delete-selected-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.add-coupon-btn i, .export-btn i, .delete-selected-btn i {
    margin-right: 8px;
}

/* 统计信息卡片 */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 2px solid #ffe6f2;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff7eb9, #ff9ed2);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 126, 185, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    color: white;
    font-size: 20px;
}

.stat-icon.active {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.stat-icon.used {
    background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.stat-icon.expired {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* 卡券列表样式 */
.coupon-list-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #ffe6f2;
    margin-bottom: 20px;
}

.coupon-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
}

.coupon-table thead {
    background: #fff2f8;
}

.coupon-table th {
    color: #ff7eb9;
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid #ffdfed;
    font-size: 14px;
}

.coupon-table th i {
    margin-right: 8px;
    font-size: 13px;
    opacity: 0.8;
}

.coupon-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #f8f8f8;
    color: #555;
    font-size: 14px;
    vertical-align: middle;
}

.coupon-table tbody tr:hover {
    background-color: #fff9fc;
}

.coupon-table tbody tr:last-child td {
    border-bottom: none;
}

.checkbox-column {
    width: 50px;
    text-align: center;
}

/* 卡券代码样式 */
.coupon-code {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    color: #495057;
    border: 1px solid #e9ecef;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-active {
    background: linear-gradient(135deg, #d1f7c4 0%, #c8f7c5 100%);
    color: #28a745;
    border: 1px solid #c3e6cb;
}

.status-used {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-expired {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.view-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}



.delete-btn {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 卡通复选框增强样式 */
.kawaii-checkbox {
    position: relative;
    display: inline-block;
}

.kawaii-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.kawaii-checkbox-label {
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 24px;
    width: 24px;
    background: white;
    border: 2px solid #ffcce5;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    transform-origin: center;
}

.kawaii-checkbox-face {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.kawaii-checkbox-eyes {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 2px;
}

.kawaii-checkbox-eyes:before,
.kawaii-checkbox-eyes:after {
    content: '';
    width: 4px;
    height: 4px;
    background: #ff7eb9;
    border-radius: 50%;
}

.kawaii-checkbox-mouth {
    width: 6px;
    height: 3px;
    border-radius: 3px 3px 6px 6px;
    background: #ff7eb9;
}

/* 选中状态样式 */
.kawaii-checkbox-input:checked + .kawaii-checkbox-label {
    background: #ff7eb9;
    border-color: #ff7eb9;
    transform: scale(1.1);
}

.kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-face {
    opacity: 1;
}

.kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-eyes:before,
.kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-eyes:after {
    background: white;
}

.kawaii-checkbox-input:checked + .kawaii-checkbox-label .kawaii-checkbox-mouth {
    background: white;
    width: 8px;
    height: 4px;
    border-radius: 0 0 4px 4px;
}

/* 悬停效果 */
.kawaii-checkbox-label:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 6px rgba(255, 126, 185, 0.3);
    border-color: #ff9ec7;
}

.kawaii-checkbox-input:checked + .kawaii-checkbox-label:hover {
    transform: scale(1.15);
}

/* 空状态和加载状态 */
.empty-state, .loading-state {
    padding: 60px 20px;
    text-align: center;
    color: #999;
}

.empty-state-icon {
    font-size: 64px;
    color: #ffe6f2;
    margin-bottom: 20px;
}

.empty-state-text {
    font-size: 18px;
    color: #666;
    margin-bottom: 10px;
    font-weight: 500;
}

.empty-state-hint {
    font-size: 14px;
    color: #999;
}

.spinner {
    border: 4px solid #ffe6f2;
    border-radius: 50%;
    border-top: 4px solid #ff7eb9;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #ff7eb9;
    font-weight: 500;
}

/* 表格行动画效果 */
.coupon-table tbody tr {
    opacity: 0;
    transform: translateY(20px);
    animation: rowFadeIn 0.5s ease forwards;
}

.coupon-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.coupon-table tbody tr:nth-child(2) { animation-delay: 0.15s; }
.coupon-table tbody tr:nth-child(3) { animation-delay: 0.2s; }
.coupon-table tbody tr:nth-child(4) { animation-delay: 0.25s; }
.coupon-table tbody tr:nth-child(5) { animation-delay: 0.3s; }
.coupon-table tbody tr:nth-child(6) { animation-delay: 0.35s; }
.coupon-table tbody tr:nth-child(7) { animation-delay: 0.4s; }
.coupon-table tbody tr:nth-child(8) { animation-delay: 0.45s; }
.coupon-table tbody tr:nth-child(9) { animation-delay: 0.5s; }
.coupon-table tbody tr:nth-child(10) { animation-delay: 0.55s; }

@keyframes rowFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.coupon-table tbody tr.removing {
    animation: rowFadeOut 0.3s ease forwards;
}

@keyframes rowFadeOut {
    to {
        opacity: 0;
        transform: translateX(-100%);
        height: 0;
        padding: 0;
        margin: 0;
    }
}

/* 内容区域过渡动画 */
.table-container {
    position: relative;
    overflow: hidden;
}

.table-content {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-content.loading {
    opacity: 0.6;
    transform: translateY(10px);
    filter: blur(1px);
}

.table-content.page-transition {
    opacity: 0;
    transform: translateX(30px);
}

/* 骨架屏动画 */
.skeleton-loader {
    display: none;
    padding: 20px;
}

.skeleton-loader.active {
    display: block;
    animation: skeletonFadeIn 0.3s ease;
}

@keyframes skeletonFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.skeleton-row {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.skeleton-cell {
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonShimmer 1.5s infinite;
    border-radius: 4px;
    margin-right: 15px;
}

.skeleton-cell:nth-child(1) { width: 40px; }
.skeleton-cell:nth-child(2) { width: 120px; }
.skeleton-cell:nth-child(3) { width: 80px; }
.skeleton-cell:nth-child(4) { width: 100px; }
.skeleton-cell:nth-child(5) { width: 90px; }
.skeleton-cell:nth-child(6) { width: 90px; }
.skeleton-cell:nth-child(7) { width: 60px; }
.skeleton-cell:nth-child(8) { width: 100px; }

@keyframes skeletonShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 操作反馈动画 */
.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: buttonSpin 1s linear infinite;
}

@keyframes buttonSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-success {
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(40, 167, 69, 0.5); }
    100% { transform: scale(1); }
}

/* 页面切换动画 */
.page-transition-enter {
    opacity: 0;
    transform: translateX(30px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-exit {
    opacity: 1;
    transform: translateX(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 通知动画增强 */
.notification {
    animation: notificationSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

.notification.removing {
    animation: notificationSlideOut 0.3s ease forwards;
}

@keyframes notificationSlideOut {
    to {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #ffe6f2;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-btn {
    width: 36px;
    height: 36px;
    border: 2px solid #ffe6f2;
    border-radius: 50%;
    background: white;
    color: #ff7eb9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #ff7eb9;
    color: white;
    transform: scale(1.1);
}

.page-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 36px;
    height: 36px;
    border: 2px solid #ffe6f2;
    border-radius: 50%;
    background: white;
    color: #ff7eb9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.page-number:hover, .page-number.active {
    background: #ff7eb9;
    color: white;
    transform: scale(1.1);
}

.page-size-select {
    border: 2px solid #ffe6f2;
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 14px;
    color: #ff7eb9;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-size-select:focus {
    outline: none;
    border-color: #ff7eb9;
    box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.1);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    width: 90%;
    max-width: 600px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.3s ease;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    color: white;
    transition: all 0.2s;
    opacity: 0.8;
}

.close-modal:hover {
    opacity: 1;
    transform: rotate(90deg);
}

.modal-body {
    padding: 25px;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ffe6f2;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #ff7eb9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.1);
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #999;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.btn-cancel, .btn-confirm {
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-cancel {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn-confirm {
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 126, 185, 0.3);
}

.btn-cancel:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 126, 185, 0.4);
}

/* 响应式设计 */
/* 完全重写移动端布局 - 使用更强的优先级和隔离策略 */
@media (max-width: 768px) {
    /* 强制重置所有可能的冲突样式 */
    body .coupon-management-container .page-header {
        display: flex !important;
        flex-direction: column !important;
        gap: 0 !important;
        padding: 20px !important;
        align-items: stretch !important;
        justify-content: flex-start !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    body .coupon-management-container .header-left {
        order: 1 !important;
        margin-bottom: 15px !important;
        width: 100% !important;
    }

    body .coupon-management-container .header-center {
        order: 2 !important;
        margin: 0 !important;
        width: 100% !important;
        display: block !important;
        margin-bottom: 20px !important;
    }

    body .coupon-management-container .header-actions {
        order: 3 !important;
        margin-top: 0 !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
        width: 100% !important;
    }

    /* 完全重构筛选组布局 */
    body .coupon-management-container .filter-group {
        display: block !important;
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* 搜索框独立容器 */
    body .coupon-management-container .search-box {
        display: block !important;
        width: 100% !important;
        margin: 0 0 20px 0 !important;
        padding: 0 !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        position: relative !important;
        clear: both !important;
        float: none !important;
    }

    body .coupon-management-container .search-box .search-input {
        display: block !important;
        width: 100% !important;
        background: #fff5f8 !important;
        border: 2px solid #ffcce5 !important;
        padding: 16px 20px 16px 55px !important;
        font-size: 16px !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(255, 126, 185, 0.1) !important;
        box-sizing: border-box !important;
        margin: 0 !important;
        position: relative !important;
        z-index: 1 !important;
    }

    body .coupon-management-container .search-box i {
        position: absolute !important;
        left: 20px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #ff7eb9 !important;
        font-size: 18px !important;
        z-index: 2 !important;
        pointer-events: none !important;
    }

    /* 筛选器独立容器 */
    body .coupon-management-container .filter-select {
        display: block !important;
        width: 100% !important;
        margin: 0 0 20px 0 !important;
        padding: 16px 20px !important;
        background: #fff5f8 !important;
        border: 2px solid #ffcce5 !important;
        border-radius: 12px !important;
        font-size: 16px !important;
        box-shadow: 0 2px 8px rgba(255, 126, 185, 0.1) !important;
        color: #333 !important;
        box-sizing: border-box !important;
        clear: both !important;
        float: none !important;
        position: relative !important;
        z-index: 1 !important;
    }

    body .coupon-management-container .filter-select:focus {
        border-color: #ff7eb9 !important;
        background: white !important;
        box-shadow: 0 4px 12px rgba(255, 126, 185, 0.2) !important;
        outline: none !important;
    }

    /* 重置按钮独立容器 */
    body .coupon-management-container .reset-btn {
        display: block !important;
        margin: 10px auto 0 auto !important;
        width: 44px !important;
        height: 44px !important;
        font-size: 16px !important;
        flex-shrink: 0 !important;
        clear: both !important;
        float: none !important;
        position: relative !important;
        z-index: 1 !important;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .coupon-table {
        font-size: 12px;
    }

    .coupon-table th,
    .coupon-table td {
        padding: 12px 8px;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .add-coupon-btn, .export-btn, .delete-selected-btn {
        width: 100%;
        justify-content: center;
    }
}

/* 卡券详情模态框美化样式 - 粉白色搭配 */
.detail-modal-content {
    max-width: 600px;
    background: linear-gradient(135deg, #fff0f6 0%, #ffe6f2 50%, #fff5f8 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(255, 126, 185, 0.2);
    border: 2px solid rgba(255, 126, 185, 0.1);
}

.detail-modal-content .modal-header {
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    color: white;
    padding: 20px 25px;
    border-bottom: none;
}

.detail-modal-content .modal-header h2 {
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.detail-modal-content .modal-body {
    padding: 30px;
    background: transparent;
}

.coupon-detail-card {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.coupon-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.coupon-ticket {
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    border-radius: 15px;
    padding: 25px;
    position: relative;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 15px 35px rgba(255, 126, 185, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.coupon-ticket::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="0" cy="50" r="8" fill="white" opacity="0.1"/><circle cx="100" cy="50" r="8" fill="white" opacity="0.1"/></svg>');
    background-size: 100% 100%;
    pointer-events: none;
}

.coupon-ticket::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    right: -8px;
    height: 1px;
    background: repeating-linear-gradient(
        to right,
        transparent 0px,
        transparent 5px,
        rgba(255, 255, 255, 0.3) 5px,
        rgba(255, 255, 255, 0.3) 10px
    );
    transform: translateY(-50%);
}

.ticket-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.ticket-value {
    color: white;
    font-size: 36px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 5px;
}

.ticket-value #detailDiscountUnit {
    font-size: 18px;
    font-weight: 500;
}

.ticket-body {
    position: relative;
    z-index: 1;
}

.ticket-code {
    text-align: center;
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.ticket-code span {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ticket-code strong {
    color: white;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.ticket-info {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.info-row {
    flex: 1;
    text-align: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.info-row span:first-child {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    margin-bottom: 3px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-row span:last-child {
    color: white;
    font-size: 13px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.coupon-meta {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 126, 185, 0.1);
    box-shadow: 0 4px 15px rgba(255, 126, 185, 0.1);
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 126, 185, 0.1);
}

.meta-item:last-child {
    border-bottom: none;
}

.meta-item label {
    color: #ff7eb9;
    font-weight: 600;
    font-size: 14px;
}

.meta-item span {
    color: #333;
    font-size: 14px;
}

.meta-item .status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

/* 响应式设计 - 卡券详情 */
@media (max-width: 768px) {
    .detail-modal-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }

    .coupon-ticket {
        max-width: 100%;
        padding: 20px;
    }

    .ticket-value {
        font-size: 28px;
    }

    .ticket-info {
        flex-direction: column;
        gap: 10px;
    }

    .meta-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    /* 额外的移动端修复 */
    .coupon-management-container .stats-container {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }

    .coupon-management-container .header-actions {
        gap: 8px !important;
    }

    .coupon-management-container .add-coupon-btn,
    .coupon-management-container .export-btn,
    .coupon-management-container .delete-selected-btn {
        padding: 8px 16px !important;
        font-size: 14px !important;
    }

    /* 确保表格在移动端正确显示 */
    .coupon-management-container .coupon-table {
        font-size: 12px !important;
    }

    .coupon-management-container .coupon-table th,
    .coupon-management-container .coupon-table td {
        padding: 8px 4px !important;
    }
}

/* 针对单页面应用的特殊修复 */
@media (max-width: 768px) {
    /* 确保在#page-content容器中的正确显示 */
    #page-content .coupon-management-container .page-header {
        margin-bottom: 20px !important;
    }

    #page-content .coupon-management-container .filter-group {
        padding: 0 !important;
        margin: 0 !important;
    }

    #page-content .coupon-management-container .search-box,
    #page-content .coupon-management-container .filter-select {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

/* 删除确认模态框美化样式 - 粉白色搭配 */
.delete-modal-content {
    width: 500px;
    max-width: 90%;
    border-radius: 24px;
    background: linear-gradient(135deg, #fff0f6 0%, #ffe6f2 50%, #fff5f8 100%);
    box-shadow: 0 20px 40px rgba(255, 126, 185, 0.3);
    border: 3px solid rgba(255, 126, 185, 0.2);
    overflow: hidden;
    animation: deleteModalBounceIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

@keyframes deleteModalBounceIn {
    0% {
        transform: scale(0.3) rotate(-10deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) rotate(2deg);
    }
    70% {
        transform: scale(0.95) rotate(-1deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.delete-modal-header {
    background: linear-gradient(135deg, #ff7eb9 0%, #ff9ed2 100%);
    padding: 20px 25px;
    position: relative;
    overflow: hidden;
    color: white;
}

.delete-modal-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%); }
    50% { transform: translateX(0%) translateY(0%); }
}

.delete-modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.delete-modal-header h2 i {
    margin-right: 12px;
    font-size: 22px;
    animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.delete-modal-body {
    padding: 30px;
    text-align: center;
    background: transparent;
}

.delete-warning-icon {
    margin-bottom: 20px;
    position: relative;
}

.delete-warning-icon i {
    font-size: 64px;
    color: #ff6b6b;
    animation: trashShake 1s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(255, 107, 107, 0.3));
}

@keyframes trashShake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-3deg); }
    75% { transform: rotate(3deg); }
}

.delete-warning-text {
    margin-bottom: 25px;
}

.delete-warning-text h3 {
    color: #333;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.delete-warning-text p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin: 0;
    max-width: 400px;
    margin: 0 auto;
}

.delete-coupon-info {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    border: 2px solid rgba(255, 126, 185, 0.1);
    backdrop-filter: blur(10px);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
}

.info-label {
    color: #ff7eb9;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.info-label i {
    margin-right: 8px;
    font-size: 18px;
}

.info-value {
    color: #333;
    font-weight: 700;
    font-size: 18px;
    background: linear-gradient(135deg, #ff7eb9, #ff9ed2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.delete-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-cancel {
    padding: 14px 28px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e0e0e0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-cancel i {
    margin-right: 8px;
    font-size: 14px;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: #ced4da;
}

.btn-delete-confirm {
    padding: 14px 28px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
}

.btn-delete-confirm::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-delete-confirm:hover::before {
    left: 100%;
}

.btn-delete-confirm i {
    margin-right: 8px;
    font-size: 14px;
}

.btn-delete-confirm:hover {
    background: linear-gradient(135deg, #ff5252 0%, #f44336 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
}

.btn-delete-confirm:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

/* 响应式设计 - 删除模态框 */
@media (max-width: 768px) {
    .delete-modal-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
        width: auto;
    }

    .delete-modal-body {
        padding: 20px;
    }

    .delete-warning-icon i {
        font-size: 48px;
    }

    .delete-warning-text h3 {
        font-size: 18px;
    }

    .delete-warning-text p {
        font-size: 14px;
    }

    .delete-actions {
        flex-direction: column;
        gap: 12px;
    }

    .btn-cancel,
    .btn-delete-confirm {
        width: 100%;
        padding: 12px 20px;
    }
}

/* Toast 通知样式 - 仿照 DockingCenter.html */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    pointer-events: none;
}

.toast {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    padding: 12px 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    transform: translateX(120%);
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 300px;
    pointer-events: auto;
    position: relative;
    opacity: 0;
    border-left: 4px solid #ccc;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-icon {
    margin-right: 12px;
    font-size: 1.2rem;
}

.toast-success {
    border-left: 4px solid #4cd964;
    box-shadow: 0 4px 15px rgba(76, 217, 100, 0.2);
}

.toast-success .toast-icon {
    color: #4cd964;
}

.toast-error {
    border-left: 4px solid #ff6b6b;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
}

.toast-error .toast-icon {
    color: #ff6b6b;
}

.toast-info {
    border-left: 4px solid #5bc0de;
    box-shadow: 0 4px 15px rgba(91, 192, 222, 0.2);
}

.toast-info .toast-icon {
    color: #5bc0de;
}

.toast-warning {
    border-left: 4px solid #f0ad4e;
    box-shadow: 0 4px 15px rgba(240, 173, 78, 0.2);
}

.toast-warning .toast-icon {
    color: #f0ad4e;
}

.toast-message {
    flex-grow: 1;
    font-size: 0.95rem;
    color: #333;
    font-weight: 500;
}