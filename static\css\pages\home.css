:root {
        --primary-color: #ff7eb9;
        --primary-light: #ffa6d2;
        --primary-dark: #e55a9b;
        --accent-color: #7eb8ff;
        --background-color: #fff5f9;
        --card-bg: #ffffff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #eeeeee;
        --success-color: #4cd964;
        --warning-color: #ffce56;
        --danger-color: #ff6b6b;
        --shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        --border-radius: 10px;
    }

    /* 统计卡片 */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-bottom: 15px;
        width: 100%;
    }

    .stat-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 16px;
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    }

    .stat-card .card-icon {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 126, 185, 0.1);
        border-radius: 10px;
        margin-bottom: 14px;
    }

    .stat-card .card-icon i {
        font-size: 1.4rem;
        color: var(--primary-color);
    }

    .stat-card .card-title {
        font-size: 0.85rem;
        color: var(--text-secondary);
        margin-bottom: 6px;
    }

    .stat-card .card-value {
        font-size: 1.6rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stat-card .trend {
        display: flex;
        align-items: center;
        font-size: 0.8rem;
    }

    .trend.up {
        color: var(--success-color);
    }

    .trend.down {
        color: var(--danger-color);
    }

    .trend i {
        margin-right: 5px;
    }

    /* 导航卡片 */
    .nav-cards-container {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 15px;
        margin-bottom: 15px;
        width: 100%;
    }

    .quick-nav-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 16px;
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .quick-nav-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
        background-color: rgba(255, 126, 185, 0.05);
    }

    .quick-nav-card .nav-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 126, 185, 0.1);
        border-radius: 50%;
        margin-bottom: 12px;
    }

    .quick-nav-card .nav-icon i {
        font-size: 1.6rem;
        color: var(--primary-color);
    }

    .quick-nav-card .nav-title {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    /* 公告和图表布局 */
    .dashboard-row {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 15px;
        margin-bottom: 15px;
        width: 100%;
    }

    /* 公告卡片 */
    .notice-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 18px;
        box-shadow: var(--shadow);
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        height: 100%;
    }

    .notice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
    }

    .notice-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
    }

    .notice-title i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    .notice-list {
        flex-grow: 1;
        overflow-y: auto;
    }

    .notice-item {
        padding: 10px 0;
        border-bottom: 1px dashed var(--border-color);
    }

    .notice-item:last-child {
        border-bottom: none;
    }

    .notice-item-title {
        font-weight: 500;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }

    .notice-item-title .badge {
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        color: white;
        margin-right: 6px;
    }

    .badge-new {
        background-color: var(--primary-color);
    }

    .badge-important {
        background-color: var(--danger-color);
    }

    .notice-item-date {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .notice-item-content {
        font-size: 0.85rem;
        color: var(--text-secondary);
        margin-top: 5px;
        line-height: 1.5;
    }

    /* 图表容器 */
    .chart-card {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        padding: 18px;
        box-shadow: var(--shadow);
        margin-bottom: 15px;
        width: 100%;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .chart-period {
        display: flex;
        gap: 8px;
    }

    .period-btn {
        padding: 4px 10px;
        border: 1px solid var(--border-color);
        border-radius: 15px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .period-btn.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .chart-container {
        height: 100%;
        min-height: 300px;
        position: relative;
        flex-grow: 1;
        width: 100%;
    }

    /* 适配容器 */
    #dashboard-container {
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: auto;
    }

    /* ========== 移动端布局层次结构优化 ========== */
    @media (max-width: 768px) {
        /* 首页容器 - 简洁布局 */
        #dashboard-container {
            background-color: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        /* 统计卡片容器 - 一级层次：最重要的数据展示 */
        .stats-container {
            display: grid !important;
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 16px !important;
            margin-bottom: 48px !important; /* 大间距突出重要性 */
            padding: 0 !important;
        }

        /* 统计卡片 - 美化设计 */
        .stat-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.12), 0 1px 3px rgba(0, 0, 0, 0.05) !important;
            padding: 20px 16px !important;
            border: 1px solid rgba(255, 107, 157, 0.1) !important;
            text-align: center !important;
            position: relative !important;
            overflow: hidden !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .stat-card:active {
            transform: scale(0.98) translateY(1px) !important;
            box-shadow: 0 2px 8px rgba(255, 107, 157, 0.15), 0 1px 2px rgba(0, 0, 0, 0.08) !important;
        }

        /* 数据值 - 最高层次：最大最突出 */
        .stat-card .card-value {
            font-size: 1.875rem !important; /* 30px - 最大字体突出数据 */
            font-weight: 800 !important;
            color: #ff6b9d !important;
            margin: 8px 0 !important;
            line-height: 1.2 !important;
        }

        /* 数据标题 - 二级层次：说明性文字 */
        .stat-card .card-title {
            font-size: 0.875rem !important; /* 14px */
            font-weight: 500 !important;
            color: #666666 !important;
            margin-bottom: 4px !important;
        }

        /* 趋势信息 - 三级层次：辅助信息 */
        .stat-card .trend {
            font-size: 0.75rem !important; /* 12px */
            color: #8a8a8a !important;
            margin-top: 8px !important;
        }

        /* 图标 - 视觉辅助，不抢夺数据焦点 */
        .stat-card .card-icon {
            width: 40px !important;
            height: 40px !important;
            margin-bottom: 8px !important;
            background-color: rgba(255, 107, 157, 0.1) !important;
            border-radius: 6px !important;
        }

        .stat-card .card-icon i {
            font-size: 1.2rem !important;
            color: #ff6b9d !important;
        }

        /* 导航卡片容器 - 二级层次：功能导航 */
        .nav-cards-container {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 8px !important;
            margin-bottom: 32px !important;
            padding: 0 !important;
        }

        /* 导航卡片 - 精致美化设计 */
        .quick-nav-card {
            background: linear-gradient(145deg, #fafafa 0%, #ffffff 100%) !important;
            border-radius: 8px !important;
            box-shadow: 0 2px 6px rgba(255, 107, 157, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04) !important;
            padding: 14px 10px !important;
            border: 1px solid rgba(255, 107, 157, 0.06) !important;
            text-align: center !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .quick-nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 107, 157, 0.2) 50%, transparent 100%);
        }

        /* 导航卡片交互反馈 */
        .quick-nav-card:active {
            transform: scale(0.96) translateY(1px) !important;
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
            box-shadow: 0 4px 10px rgba(255, 107, 157, 0.12), 0 1px 3px rgba(0, 0, 0, 0.06) !important;
        }

        .quick-nav-card .nav-icon {
            width: 36px !important;
            height: 36px !important;
            margin-bottom: 8px !important;
            background-color: rgba(255, 107, 157, 0.08) !important;
            border-radius: 6px !important;
        }

        .quick-nav-card .nav-icon i {
            color: #ff6b9d !important;
            font-size: 1.125rem !important; /* 18px */
        }

        .quick-nav-card .nav-title {
            font-size: 0.75rem !important; /* 12px */
            font-weight: 500 !important;
            color: #4a4a4a !important;
        }

        /* 仪表板行 - 三级层次：详细信息区域 */
        .dashboard-row {
            display: flex !important;
            flex-direction: column !important;
            gap: 24px !important;
            margin-top: 32px !important;
        }

        /* 公告卡片 - 信息展示区域 */
        .notice-card {
            background-color: #fafafa !important;
            border-radius: 8px !important;
            padding: 16px !important;
            margin-bottom: 16px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #f0f0f0 !important;
        }

        .notice-title {
            font-size: 1rem !important; /* 16px */
            font-weight: 600 !important;
            color: #1a1a1a !important;
        }

        .notice-item-title {
            font-size: 0.875rem !important; /* 14px */
            font-weight: 500 !important;
            color: #1a1a1a !important;
        }

        .notice-item-content {
            font-size: 0.75rem !important; /* 12px */
            color: #8a8a8a !important;
            line-height: 1.5 !important;
        }

        /* 图表卡片 - 数据可视化区域 */
        .chart-card {
            background-color: #ffffff !important;
            border-radius: 12px !important;
            padding: 20px !important;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid #e5e5e5 !important;
        }

        .chart-container {
            min-height: 280px !important;
            height: 280px !important;
            width: 100% !important;
        }

        .chart-header {
            flex-direction: column !important;
            align-items: flex-start !important;
            gap: 16px !important;
            margin-bottom: 24px !important;
        }

        .chart-title {
            font-size: 1.125rem !important; /* 18px */
            font-weight: 600 !important;
            color: #1a1a1a !important;
        }

        .chart-period {
            width: 100% !important;
            display: flex !important;
            justify-content: space-between !important;
            gap: 4px !important;
        }

        .period-btn {
            flex: 1 !important;
            text-align: center !important;
            padding: 8px 4px !important;
            font-size: 0.875rem !important; /* 14px */
            border-radius: 8px !important;
            min-height: 36px !important;
            font-weight: 500 !important;
        }
    }

    /* 超小屏幕设备层次优化 */
    @media (max-width: 480px) {
        /* 统计卡片 - 单列显示突出重要性 */
        .stats-container {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
            margin-bottom: 32px !important;
        }

        .stat-card .card-value {
            font-size: 1.5rem !important; /* 24px - 在小屏幕上仍然突出 */
        }

        /* 导航卡片 - 2列布局保持功能可见性 */
        .nav-cards-container {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 8px !important;
        }

        .quick-nav-card .nav-title {
            font-size: 0.75rem !important; /* 12px */
        }

        /* 图表适配超小屏幕 */
        .chart-container {
            min-height: 220px !important;
            height: 220px !important;
        }

        .chart-title {
            font-size: 1rem !important; /* 16px */
        }
    }