:root {
            --primary-color: #ff6b9d;
            --primary-light: #ff8fab;
            --primary-lighter: #ffecf2;
            --primary-dark: #e55a8a;
            --bg-white: #ffffff;
            --bg-light: #fafafa;
            --bg-pink: #fff5f8;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-muted: #999999;
            --border-color: #e0e0e0;
            --border-light: #f0f0f0;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 页面容器 */
        .order-container {
            padding: 20px;
            background-color: var(--bg-pink);
            min-height: 100vh;
        }

        /* 订单列表卡片 */
        .orders-card {
            background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            padding: 24px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 107, 157, 0.08);
            position: relative;
            overflow: hidden;
        }

        .orders-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        /* 卡片标题 */
        .card-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--border-light);
        }

        .card-title h2 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-title h2 i {
            color: var(--primary-color);
            font-size: 28px;
        }

        .card-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        /* 批量操作区域样式 */
        .batch-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            padding: 8px 16px;
            background: linear-gradient(135deg, var(--primary-lighter), rgba(255, 107, 157, 0.1));
            border-radius: 8px;
            border: 1px solid var(--primary-color);
            animation: slideInFromTop 0.3s ease-out;
        }

        @keyframes slideInFromTop {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .selected-count {
            font-size: 13px;
            font-weight: 600;
            color: var(--primary-dark);
            background: white;
            padding: 4px 12px;
            border-radius: 20px;
            border: 1px solid var(--primary-color);
        }

        .regular-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
        }

        .btn-light {
            background-color: var(--bg-light);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-light:hover {
            background-color: var(--primary-lighter);
            color: var(--primary-dark);
            border-color: var(--primary-color);
        }

        /* 搜索筛选区域 */
        .search-filter-section {
            background-color: var(--bg-white);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto auto;
            gap: 16px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .form-control {
            padding: 12px 16px;
            border: 2px solid var(--border-light);
            border-radius: 8px;
            font-size: 14px;
            transition: var(--transition);
            background-color: var(--bg-white);
            color: var(--text-primary);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        select.form-control {
            cursor: pointer;
        }

        .search-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
        }

        /* 外层表格容器 */
        .table-container {
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            margin-bottom: 20px;
        }

        /* 表格样式 - 强制滚动容器 */
        .table-responsive {
            background-color: var(--bg-white);
            border-radius: var(--border-radius);
            overflow-x: auto !important;
            overflow-y: hidden !important;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
            position: relative;
            width: 100%;
            max-width: 100%;
            display: block;
        }

        /* 强制显示滚动条样式 */
        .table-responsive::-webkit-scrollbar {
            height: 12px !important;
            -webkit-appearance: none;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f8f9fa !important;
            border-radius: 6px !important;
            -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
            border-radius: 6px !important;
            -webkit-box-shadow: 0 0 6px rgba(0,0,0,0.2);
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
        }

        /* 强制显示滚动条 - 兼容性处理 */
        .table-responsive {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) #f8f9fa;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            min-width: 2090px; /* 调整宽度以适配更小的复选框列 */
            table-layout: fixed;
            display: table;
        }

        .orders-table th {
            background: #f8f9fa;
            color: var(--text-primary);
            font-weight: 600;
            padding: 16px 12px;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            font-size: 13px;
            white-space: nowrap;
        }

        .orders-table th i {
            color: var(--primary-color);
            margin-right: 6px;
            font-size: 12px;
        }

        .orders-table td {
            padding: 14px 12px;
            border-bottom: 1px solid var(--border-light);
            vertical-align: middle;
            font-size: 13px;
            text-align: center;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 0;
        }

        .orders-table tbody tr {
            transition: var(--transition);
        }

        .orders-table tbody tr:hover {
            background-color: var(--primary-lighter);
        }

        /* 选中行样式 */
        .orders-table tbody tr.selected {
            background-color: rgba(255, 107, 157, 0.15) !important;
            border-left: 4px solid var(--primary-color);
        }

        .orders-table tbody tr.selected:hover {
            background-color: rgba(255, 107, 157, 0.2) !important;
        }

        /* 自定义复选框样式 */
        .checkbox-container {
            position: relative;
            display: inline-block;
        }

        .custom-checkbox {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkbox-label {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            background-color: var(--bg-white);
            border: 2px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
        }

        .checkbox-label i {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            color: white;
            opacity: 0;
            transition: var(--transition);
        }

        .custom-checkbox:checked + .checkbox-label {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-color: var(--primary-color);
        }

        .custom-checkbox:checked + .checkbox-label i {
            opacity: 1;
        }

        .checkbox-label:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
        }

        .custom-checkbox:focus + .checkbox-label {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.2);
        }

        /* 气泡框样式 */
        .cell-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            pointer-events: none;
            max-width: 300px;
            word-wrap: break-word;
            white-space: normal;
            line-height: 1.4;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-bottom: 5px;
        }

        .cell-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
        }

        .orders-table td:hover .cell-tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* 确保操作按钮列不应用文本省略 */
        .orders-table td:last-child {
            overflow: visible;
            text-overflow: unset;
            white-space: unset;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
        }

        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-paid { background-color: #d4edda; color: #155724; }
        .status-processing { background-color: #fff8dc; color: #b8860b; }
        .status-shipped { background-color: #cce7ff; color: #004085; }
        .status-delivered { background-color: #d1ecf1; color: #0c5460; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .status-refunded { background-color: #e2e3e5; color: #383d41; }
        .status-failed { background-color: #f8d7da; color: #721c24; }

        /* 价格样式 */
        .price {
            font-weight: 600;
            color: var(--text-primary);
        }

        .profit {
            font-weight: 600;
        }

        .profit.positive {
            color: #28a745;
        }

        .profit.negative {
            color: #dc3545;
        }

        /* 操作按钮 - 圆形设计 */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
            align-items: center;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: scale(0);
            transition: transform 0.3s ease;
        }

        .action-btn:hover::before {
            transform: scale(1);
        }

        .action-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        }

        .action-btn:active {
            transform: translateY(0) scale(0.95);
        }

        .action-btn.view-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .action-btn.view-btn:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
        }

        .action-btn.edit-btn {
            background: linear-gradient(135deg, #28a745, #34ce57);
            color: white;
        }

        .action-btn.edit-btn:hover {
            background: linear-gradient(135deg, #218838, #28a745);
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        .action-btn.delete-btn {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
        }

        .action-btn.delete-btn:hover {
            background: linear-gradient(135deg, #c82333, #dc3545);
            box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
        }

        /* 按钮工具提示 */
        .action-btn[title] {
            position: relative;
        }

        .action-btn[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            animation: tooltipFadeIn 0.2s ease;
        }

        @keyframes tooltipFadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(-5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* 加载动画样式 */
        .loading-animation {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: var(--primary-color);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 107, 157, 0.2);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .loading-subtext {
            font-size: 14px;
            color: var(--text-muted);
        }

        /* 数据行加载动画 */
        .row-loading {
            animation: rowFadeIn 0.5s ease-out;
        }

        @keyframes rowFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 刷新按钮动画 */
        .refresh-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
        }

        .refresh-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(255, 107, 157, 0.4);
        }

        .refresh-btn.loading {
            animation: spin 1s linear infinite;
        }

        .refresh-btn.loading:hover {
            transform: scale(1.05);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .empty-state h3 {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            color: var(--text-muted);
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: var(--bg-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
        }

        .pagination-info {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-size-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 13px;
            background-color: var(--bg-white);
            color: var(--text-primary);
            cursor: pointer;
        }

        .pagination-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--bg-white);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition);
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: var(--primary-lighter);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--bg-white);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition);
            font-size: 13px;
            min-width: 36px;
            text-align: center;
        }

        .page-number:hover {
            background-color: var(--primary-lighter);
            border-color: var(--primary-color);
            color: var(--primary-dark);
        }

        .page-number.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-content {
            background-color: var(--bg-white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            max-width: 800px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 2px solid var(--border-light);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-header h3 i {
            color: var(--primary-color);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: var(--text-muted);
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: var(--transition);
        }

        .modal-close:hover {
            background-color: var(--primary-lighter);
            color: var(--primary-dark);
        }

        .modal-body {
            padding: 24px;
        }

        /* 加载指示器 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 245, 248, 0.9);
            z-index: 999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--primary-lighter);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            margin-top: 16px;
            font-size: 16px;
            color: var(--text-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            /* 强制重置搜索区域所有form-control样式 */
            .search-filter-section .form-control {
                padding: unset !important;
                font-size: unset !important;
                height: unset !important;
                border-radius: unset !important;
                line-height: unset !important;
                border: unset !important;
            }
            .order-container {
                padding: 10px;
            }

            .orders-card {
                padding: 12px;
                margin-bottom: 12px;
            }

            .card-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                margin-bottom: 12px;
            }

            .card-title h2 {
                font-size: 18px;
                margin: 0;
            }

            .card-actions {
                width: 100%;
                justify-content: flex-end;
                flex-direction: column;
                gap: 8px;
            }

            .batch-actions {
                width: 100%;
                justify-content: space-between;
                padding: 6px 12px;
            }

            .regular-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .selected-count {
                font-size: 12px;
                padding: 3px 8px;
            }

            .search-filter-section {
                padding: 10px;
                min-height: 120px;
                overflow: visible;
            }

            .filter-row {
                grid-template-columns: 1fr 1fr 1fr;
                grid-template-rows: auto auto;
                gap: 8px;
                align-items: end;
            }

            /* 移动端搜索框占满第一行 */
            .filter-row .filter-group:first-child {
                grid-column: 1 / -1;
            }

            /* 按钮组在最后一行右侧 */
            .filter-row .search-btn,
            .filter-row .reset-btn {
                grid-row: 2;
            }

            .search-btn {
                justify-content: center;
                padding: 8px 16px;
                font-size: 13px;
                height: 36px;
                min-width: 80px;
            }

            .reset-btn {
                padding: 5px 10px;
                font-size: 11px;
                height: 30px;
            }

            /* 移动端表单元素优化 */
            .filter-group {
                margin-bottom: 0;
            }

            /* 强制覆盖搜索输入框样式 */
            .search-filter-section #searchKeyword.form-control {
                font-size: 12px !important;
                padding: 4px 8px !important;
                height: 32px !important;
                border-radius: 4px !important;
                line-height: 1.3 !important;
                border: 1px solid var(--border-light) !important;
            }

            .search-filter-section #searchKeyword.form-control::placeholder {
                font-size: 11px !important;
                color: #999 !important;
            }

            /* 强制覆盖下拉选择框样式 */
            .search-filter-section .filter-group select.form-control {
                font-size: 11px !important;
                padding: 3px 6px !important;
                height: 30px !important;
                border-radius: 4px !important;
                line-height: 1.2 !important;
                border: 1px solid var(--border-light) !important;
            }

            .filter-group .filter-label {
                font-size: 10px;
                margin-bottom: 1px;
                font-weight: 500;
                display: block;
            }

            /* 移动端表格滚动 - 保持桌面端完整宽度 */
            .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            /* 移动端也使用完整的桌面端表格宽度，不缩小 */
            .orders-table {
                min-width: 2090px; /* 与桌面端保持一致，包含复选框列 */
            }

            .orders-table th,
            .orders-table td {
                padding: 10px 8px;
                font-size: 13px;
            }

            .orders-table th {
                font-size: 12px;
            }

            /* 移动端操作按钮水平排列 */
            .action-buttons {
                display: flex !important;
                flex-direction: row !important;
                gap: 4px !important;
                justify-content: center !important;
                align-items: center !important;
            }

            .action-btn {
                width: 28px !important;
                height: 28px !important;
                font-size: 12px !important;
                flex-shrink: 0;
            }

            /* 移动端分页 */
            .pagination-container {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .pagination-controls {
                flex-direction: column;
                gap: 12px;
                width: 100%;
            }

            .pagination-buttons {
                justify-content: center;
                flex-wrap: wrap;
            }

            .page-numbers {
                flex-wrap: wrap;
                justify-content: center;
            }

            /* 移动端模态框 */
            .modal {
                padding: 10px;
            }

            .modal-content {
                max-height: 95vh;
            }

            .modal-header {
                padding: 16px;
            }

            .modal-header h3 {
                font-size: 18px;
            }

            .modal-body {
                padding: 16px;
            }


        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            /* 强制重置搜索区域所有form-control样式 */
            .search-filter-section .form-control {
                padding: unset !important;
                font-size: unset !important;
                height: unset !important;
                border-radius: unset !important;
                line-height: unset !important;
                border: unset !important;
            }
            .order-container {
                padding: 8px;
            }

            .orders-card {
                padding: 8px;
            }

            .card-title {
                gap: 6px;
                margin-bottom: 8px;
            }

            .card-title h2 {
                font-size: 16px;
                margin: 0;
            }

            .search-filter-section {
                padding: 8px;
                min-height: 100px;
                overflow: visible;
            }

            /* 超小屏幕使用2列布局 */
            .filter-row {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto;
                gap: 6px;
            }

            /* 搜索框占满第一行 */
            .filter-row .filter-group:first-child {
                grid-column: 1 / -1;
            }

            /* 筛选器在第二行 */
            .filter-row .filter-group:nth-child(2),
            .filter-row .filter-group:nth-child(3) {
                grid-row: 2;
            }

            .filter-row .filter-group:nth-child(4) {
                grid-row: 3;
                grid-column: 1;
            }

            /* 按钮在第三行右侧 */
            .filter-row .search-btn {
                grid-row: 3;
                grid-column: 2;
                padding: 6px 12px;
                font-size: 11px;
                height: 32px;
                min-width: 70px;
            }

            .filter-row .reset-btn {
                grid-row: 3;
                grid-column: 2;
                padding: 3px 6px;
                font-size: 9px;
                height: 26px;
                margin-left: 4px;
            }

            /* 超小屏幕表单元素优化 */
            /* 强制覆盖搜索输入框样式 */
            .search-filter-section #searchKeyword.form-control {
                font-size: 10px !important;
                padding: 3px 6px !important;
                height: 28px !important;
                border-radius: 3px !important;
                line-height: 1.2 !important;
                border: 1px solid var(--border-light) !important;
            }

            .search-filter-section #searchKeyword.form-control::placeholder {
                font-size: 9px !important;
                color: #999 !important;
            }

            /* 强制覆盖下拉选择框样式 */
            .search-filter-section .filter-group select.form-control {
                font-size: 9px !important;
                padding: 2px 4px !important;
                height: 26px !important;
                border-radius: 3px !important;
                line-height: 1.1 !important;
                border: 1px solid var(--border-light) !important;
            }

            .filter-group .filter-label {
                font-size: 8px;
                margin-bottom: 0px;
            }

            /* 超小屏幕也保持完整桌面端宽度 */
            .orders-table {
                min-width: 2090px; /* 与桌面端保持一致，包含复选框列 */
            }

            .orders-table th,
            .orders-table td {
                padding: 8px 6px;
                font-size: 12px;
            }

            .pagination-container {
                padding: 12px;
            }

            .modal-header {
                padding: 12px;
            }

            .modal-body {
                padding: 12px;
            }
        }

        /* 删除确认模态框样式 */
        .delete-confirm-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .delete-confirm-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .delete-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(2px);
        }

        .delete-modal-content {
            position: relative;
            background: linear-gradient(135deg, #fff 0%, #fff5f8 100%);
            width: 480px;
            max-width: 90%;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(255, 69, 95, 0.25);
            border: 3px solid rgba(255, 228, 241, 0.8);
            overflow: hidden;
            animation: deleteModalBounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateY(0);
        }

        @keyframes deleteModalBounceIn {
            0% {
                transform: scale(0.3) translateY(-50px);
                opacity: 0;
            }
            50% {
                transform: scale(1.05) translateY(0);
            }
            70% {
                transform: scale(0.95) translateY(0);
            }
            100% {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        .delete-modal-header {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
            color: white;
            padding: 25px;
            text-align: center;
            position: relative;
        }

        .delete-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            animation: pulse 2s infinite;
        }

        .delete-icon i {
            font-size: 28px;
            color: #fff;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        .delete-modal-title {
            font-size: 22px;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .delete-modal-body {
            padding: 30px;
            text-align: center;
        }

        .delete-warning-text {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .delete-order-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            font-size: 14px;
            line-height: 1.6;
        }

        .delete-order-info strong {
            color: #495057;
            font-weight: 600;
        }

        .delete-warning-notice {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            color: #856404;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .delete-warning-notice i {
            color: #f39c12;
        }

        .delete-modal-footer {
            padding: 25px 30px;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            gap: 15px;
            border-top: 1px solid #e9ecef;
        }

        .btn-cancel, .btn-confirm-delete {
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-width: 120px;
        }

        .btn-cancel {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-cancel:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .btn-confirm-delete {
            background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }

        .btn-confirm-delete:hover {
            background: linear-gradient(135deg, #ff5a8c 0%, #ff7ea0 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .delete-modal-content {
                width: calc(100% - 32px);
                margin: 16px;
            }

            .delete-modal-header {
                padding: 20px;
            }

            .delete-modal-title {
                font-size: 20px;
            }

            .delete-modal-body {
                padding: 20px;
            }

            .delete-warning-text {
                font-size: 16px;
            }

            .delete-modal-footer {
                padding: 20px;
                flex-direction: column;
            }

            .btn-cancel, .btn-confirm-delete {
                width: 100%;
                justify-content: center;
            }
        }