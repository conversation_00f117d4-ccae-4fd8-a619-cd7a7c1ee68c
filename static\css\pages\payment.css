/* 支付渠道配置页面样式 */
    .payment-config-container {
        padding: 20px;
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    /* 页面标题和添加按钮 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
    }

    .page-title {
        font-size: 24px;
        color: #333;
        margin: 0;
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        gap: 15px;
    }

    .add-payment-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .add-payment-btn i {
        margin-right: 8px;
    }

    .add-payment-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    /* 数据列表样式 */
    .payment-list-container {
        overflow-x: auto;
    }

    .payment-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .payment-table thead {
        background-color: #fff2f8;
    }

    .payment-table th {
        color: #ff7eb9;
        font-weight: 600;
        padding: 15px;
        text-align: left;
        border-bottom: 2px solid #ffdfed;
    }

    .payment-table th i {
        margin-right: 8px;
        font-size: 0.9em;
        opacity: 0.8;
    }

    .payment-table td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #555;
    }

    .payment-table tbody tr:hover {
        background-color: #fff9fc;
    }

    .payment-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .edit-btn {
        background-color: #ffe08a;
        color: #d19c00;
    }

    .delete-btn {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* 状态标签 */
    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
    }

    .status-active {
        background-color: #d1f7c4;
        color: #5cb85c;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #dc3545;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    /* 当模态框显示时的样式 */
    .modal[style*="display: flex"] {
        display: flex !important;
    }

    .modal-content {
        background-color: white;
        width: 500px;
        max-width: 85%;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease;
        overflow: hidden;
    }

    @keyframes modalSlideIn {
        from {
            transform: translateY(-50px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .modal-header {
        background-color: #ff7eb9;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .modal-header h2 i {
        margin-right: 10px;
    }

    .close-modal {
        font-size: 24px;
        cursor: pointer;
        color: white;
        transition: all 0.2s;
    }

    .close-modal:hover {
        transform: scale(1.2);
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 20px;
        width: 90%;
        margin-left: auto;
        margin-right: auto;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }

    .form-group label i {
        color: #ff7eb9;
        margin-right: 6px;
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 15px;
        transition: border 0.3s ease;
    }

    .form-control:focus {
        border-color: #ff7eb9;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    textarea.form-control {
        resize: vertical;
        min-height: 80px;
    }

    .form-hint {
        font-size: 12px;
        color: #888;
        margin-top: 5px;
    }

    /* 开关按钮样式 */
    .switch-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 90%;
        margin-left: auto;
        margin-right: auto;
    }

    .switch-label {
        display: flex;
        align-items: center;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 30px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #ff7eb9;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #ff7eb9;
    }

    input:checked + .slider:before {
        transform: translateX(30px);
    }

    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    /* 按钮样式 */
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin-top: 25px;
    }

    .btn-cancel, .btn-confirm {
        padding: 10px 25px;
        border-radius: 25px;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .btn-cancel i, .btn-confirm i {
        margin-right: 6px;
        font-size: 14px;
    }

    .btn-cancel {
        background-color: #f0f0f0;
        color: #666;
        border: none;
    }

    .btn-confirm {
        background-color: #ff7eb9;
        color: white;
        border: none;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .btn-cancel:hover {
        background-color: #e0e0e0;
    }

    .btn-confirm:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }

    /* 分配部分标题 */
    .config-section-title {
        font-size: 16px;
        color: #ff7eb9;
        padding-bottom: 8px;
        margin: 20px 0 15px;
        border-bottom: 1px dashed #ffdfed;
    }

    /* 通知样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        pointer-events: none;
    }

    .toast {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        padding: 12px 16px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        transform: translateX(120%);
        transition: transform 0.3s ease, opacity 0.3s ease;
        max-width: 300px;
        pointer-events: auto;
        position: relative;
        opacity: 0;
        border-left: 4px solid #ccc;
    }

    .toast.show {
        transform: translateX(0);
        opacity: 1;
    }

    .toast-icon {
        margin-right: 12px;
        font-size: 1.2rem;
    }

    .toast-success {
        border-left: 4px solid #4cd964;
        box-shadow: 0 4px 15px rgba(76, 217, 100, 0.2);
    }
    
    .toast-success .toast-icon {
        color: #4cd964;
    }

    .toast-error {
        border-left: 4px solid #ff6b6b;
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
    }
    
    .toast-error .toast-icon {
        color: #ff6b6b;
    }

    .toast-message {
        flex-grow: 1;
        font-size: 0.95rem;
        color: #333;
        font-weight: 500;
    }

    /* 加载中指示器 */
    .loading-indicator {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        z-index: 1500;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .loading-indicator.show {
        display: flex;
    }

    .spinner {
        border: 4px solid rgba(255, 126, 185, 0.3);
        border-radius: 50%;
        border-top: 4px solid #ff7eb9;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    .loading-text {
        font-size: 16px;
        color: #ff7eb9;
        font-weight: 500;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 空状态样式 */
    .empty-state {
        display: none; /* 默认隐藏，没有数据时显示 */
        padding: 50px 0;
        text-align: center;
    }

    .empty-state-icon {
        font-size: 60px;
        color: #ffdfed;
        margin-bottom: 20px;
    }

    .empty-state-text {
        font-size: 18px;
        color: #999;
    }
    
    /* 接口类型单选框样式 */
    .api-type-group {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: nowrap;
    }
    
    .api-type-label {
        min-width: auto;
        padding: 8px 12px;
        font-size: 13px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.25s ease;
    }
    
    .api-type-label span {
        padding-left: 0 !important;
    }
    
    .api-type-label:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(255, 126, 185, 0.15);
    }
    
    /* 单选框组样式 */
    .radio-group {
        display: flex;
        gap: 15px;
        margin-top: 12px;
        flex-wrap: wrap;
    }

    .radio-label {
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: #fff;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .radio-label:hover {
        border-color: #ff7eb9;
        background-color: #fff9fc;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 126, 185, 0.15);
    }

    .radio-label input[type="radio"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .radio-label span {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .radio-label input[type="radio"]:checked + span {
        color: #ff7eb9;
        font-weight: 600;
    }

    .radio-label input[type="radio"]:checked ~ .radio-label {
        border-color: #ff7eb9;
        background-color: #fff9fc;
    }

    .radio-label::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 10px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .radio-label input[type="radio"]:checked + span::before {
        content: '';
        position: absolute;
        left: -2px;
        top: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid #ff7eb9;
        border-radius: 12px;
        animation: radioSelect 0.3s ease;
    }

    @keyframes radioSelect {
        0% {
            transform: scale(0.8);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .radio-label input[type="radio"]:checked + span {
        color: #ff7eb9;
    }

    .radio-label:hover span {
        color: #ff7eb9;
    }

    /* 文件上传样式 */
    .file-upload-container {
        position: relative;
        margin-top: 8px;
    }

    .file-input {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
        overflow: hidden;
    }

    .file-upload-label {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 12px 16px;
        border: 2px dashed #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #666;
        font-size: 14px;
        font-weight: 500;
    }

    .file-upload-label:hover {
        border-color: #ff7eb9;
        background-color: #fff9fc;
        color: #ff7eb9;
    }

    .file-upload-label i {
        margin-right: 8px;
        font-size: 16px;
    }

    .file-upload-info {
        margin-top: 8px;
        font-size: 12px;
        color: #888;
        min-height: 16px;
    }

    .file-upload-info.success {
        color: #4cd964;
    }

    .file-upload-info.error {
        color: #ff6b6b;
    }

    .file-input:focus + .file-upload-label {
        border-color: #ff7eb9;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .file-upload-label.has-file {
        border-color: #ff7eb9;
        background-color: #fff9fc;
        color: #ff7eb9;
    }

    .file-upload-label.has-file i {
        color: #4cd964;
    }

    /* 密钥输入方式切换标签页样式 */
    .key-input-tabs {
        display: flex;
        margin-bottom: 12px;
        border-bottom: 1px solid #e0e0e0;
    }

    .key-input-tab {
        flex: 1;
        padding: 8px 16px;
        background: none;
        border: none;
        border-bottom: 2px solid transparent;
        cursor: pointer;
        font-size: 14px;
        color: #666;
        transition: all 0.3s ease;
        text-align: center;
    }

    .key-input-tab:hover {
        color: #ff7eb9;
        background-color: #fff9fc;
    }

    .key-input-tab.active {
        color: #ff7eb9;
        border-bottom-color: #ff7eb9;
        font-weight: 500;
    }

    .key-input-content {
        display: none;
    }

    .key-input-content.active {
        display: block;
    }

    /* 密钥文本输入框样式 */
    .key-textarea {
        width: 100%;
        min-height: 120px;
        padding: 12px;
        border: 2px dashed #ddd;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: vertical;
        background-color: #fafafa;
        transition: border-color 0.3s ease;
    }

    .key-textarea:focus {
        outline: none;
        border-color: #ff7eb9;
        background-color: #fff;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .key-textarea::placeholder {
        color: #999;
        font-style: italic;
    }

    /* 密钥输入提示信息 */
    .key-input-hint {
        margin-top: 8px;
        font-size: 12px;
        color: #888;
        line-height: 1.4;
    }

    .key-input-hint.success {
        color: #4cd964;
    }

    .key-input-hint.error {
        color: #ff6b6b;
    }