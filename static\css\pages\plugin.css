/* 插件市场页面样式 - 更卡通更简洁版 */
    .plugin-market-container {
        padding: 25px;
        background-color: #fff;
        border-radius: 20px;
        box-shadow: 0 10px 25px rgba(255, 126, 185, 0.15);
        height: 100%;
        min-height: 500px;
        max-height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 3px solid #ffe9f5;
    }

    /* 页面标题和搜索栏 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        flex-wrap: wrap;
        flex-shrink: 0;
        position: relative;
    }
    
    /* 添加装饰元素 */
    .page-header::before {
        content: '';
        position: absolute;
        top: -15px;
        left: 50px;
        width: 25px;
        height: 25px;
        background-color: #ffe9f5;
        border-radius: 50%;
        opacity: 0.6;
        animation: float 4s ease-in-out infinite;
    }
    
    .page-header::after {
        content: '';
        position: absolute;
        bottom: -10px;
        right: 80px;
        width: 15px;
        height: 15px;
        background-color: #ff9ecb;
        border-radius: 50%;
        opacity: 0.4;
        animation: float 3s ease-in-out infinite 1s;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    .page-title {
        font-size: 28px;
        color: #ff7eb9;
        margin: 0;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(255, 126, 185, 0.2);
        position: relative;
        display: inline-block;
    }
    
    /* 标题下划线装饰 */
    .page-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -8px;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #ff7eb9, #ffb8d9, #ff7eb9);
        border-radius: 4px;
    }

    .search-container {
        display: flex;
        align-items: center;
        width: 300px;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: 12px 18px;
        border: 3px solid #ffe6f2;
        border-radius: 30px;
        font-size: 16px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        padding-right: 50px;
        background-color: #fff;
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.1);
    }

    .search-input:focus {
        border-color: #ff7eb9;
        outline: none;
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.2);
        transform: translateY(-2px);
    }

    .search-btn {
        position: absolute;
        right: 8px;
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
    }

    .search-btn:hover {
        background-color: #ff5ca8;
        transform: scale(1.15) rotate(5deg);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
    }
    
    .search-btn:active {
        transform: scale(0.95);
    }

    /* 筛选标签样式 */
    .filter-tags {
        display: flex;
        gap: 12px;
        margin-bottom: 25px;
        flex-wrap: wrap;
        flex-shrink: 0;
    }
    
    .filter-tag {
        background-color: #fff;
        border: 2px solid #ffe6f2;
        border-radius: 50px;
        padding: 10px 18px;
        font-size: 14px;
        font-weight: 500;
        color: #ff7eb9;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.1);
    }
    
    .filter-tag i {
        margin-right: 6px;
        font-size: 14px;
    }
    
    .filter-tag:hover {
        border-color: #ffb8d9;
        background-color: #fff9fc;
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(255, 126, 185, 0.15);
    }
    
    .filter-tag.active {
        background-color: #ff7eb9;
        color: white;
        border-color: #ff7eb9;
        box-shadow: 0 6px 15px rgba(255, 126, 185, 0.25);
    }
    
    .filter-tag.active:hover {
        background-color: #ff5ca8;
        border-color: #ff5ca8;
    }

    /* 插件列表样式 */
    .plugin-list-container {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        margin: 0 -10px;
        padding: 0 15px;
        scrollbar-width: thin;
        scrollbar-color: #ff9ecb #ffeff7;
    }
    
    /* 自定义滚动条 */
    .plugin-list-container::-webkit-scrollbar {
        width: 8px;
    }
    
    .plugin-list-container::-webkit-scrollbar-track {
        background: #ffeff7;
        border-radius: 10px;
    }
    
    .plugin-list-container::-webkit-scrollbar-thumb {
        background: #ff9ecb;
        border-radius: 10px;
        border: 2px solid #ffeff7;
    }


    .plugin-list-container:empty, 
    .plugin-list-container:has(.empty-state[style*="display: block"]) {
        overflow: hidden;
    }

    .plugin-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
        gap: 25px;
        padding: 15px 0 20px 0; /* 增加上内边距，为悬浮效果腾出空间 */
        min-height: 200px;
    }


    .plugin-card {
        background-color: #fff;
        border-radius: 16px;
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.12);
        padding: 20px;
        display: flex;
        flex-direction: row;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        border: 3px solid #f8f8f8;
        height: auto;
        min-height: 130px;
        transform-origin: center center; /* 设置变换原点在中心 */
    }

    .plugin-card:hover {
        transform: translateY(-6px); /* 减小上移距离 */
        box-shadow: 0 15px 30px rgba(255, 126, 185, 0.18);
        border-color: #ffd6eb;
    }

    .plugin-card::before {
        content: '';
        position: absolute;
        bottom: -40px;
        right: -40px;
        width: 80px;
        height: 80px;
        background-color: #ffeff7;
        border-radius: 50%;
        z-index: 0;
        opacity: 0.6;
        transition: all 0.3s ease;
    }
    
    .plugin-card:hover::before {
        transform: scale(1.2);
    }

    .plugin-left {
        display: flex;
        flex-direction: column;
        width: 80px;
        margin-right: 20px;
        flex-shrink: 0;
        z-index: 1;
    }

    .plugin-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
        overflow: hidden;
        z-index: 1;
    }

    .plugin-icon {
        width: 70px;
        height: 70px;
        border-radius: 15px;
        background-color: #ffe6f2;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        flex-shrink: 0;
        margin-bottom: 12px;
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.2);
        transition: all 0.3s ease;
        border: 2px solid #ffcce5;
    }
    
    .plugin-card:hover .plugin-icon {
        transform: scale(1.05) rotate(5deg);
    }

    .plugin-icon img {
        width: 75%;
        height: 75%;
        object-fit: contain;
    }

    .plugin-icon i {
        font-size: 30px;
        color: #ff7eb9;
    }

    .plugin-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .plugin-title {
        flex: 1;
        min-width: 0;
    }

    .plugin-name {
        font-size: 20px;
        color: #333;
        margin: 0 0 6px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .plugin-author {
        font-size: 14px;
        color: #777;
        display: flex;
        align-items: center;
    }
    
    .plugin-author::before {
        content: '\f007';
        font-family: 'Font Awesome 5 Free';
        margin-right: 5px;
        font-size: 12px;
        color: #ff9ecb;
    }

    .plugin-description {
        margin-bottom: 15px;
        color: #555;
        font-size: 14px;
        line-height: 1.6;
        display: -webkit-box;
        display: -moz-box;
        display: box;
        line-clamp: 2;
        -webkit-line-clamp: 2;
        -moz-line-clamp: 2;
        -webkit-box-orient: vertical;
        -moz-box-orient: vertical;
        box-orient: vertical;
        overflow: hidden;
        max-height: 2.8em;
        text-overflow: ellipsis;
    }

    .plugin-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: auto;
    }

    .plugin-btn {
        padding: 8px 16px;
        border-radius: 50px;
        border: none;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        white-space: nowrap;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        font-weight: 500;
    }

    .plugin-btn i {
        margin-right: 6px;
        font-size: 14px;
    }

    .btn-download {
        background-color: #ff7eb9;
        color: white;
    }

    .btn-download:hover {
        background-color: #ff5ca8;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(255, 126, 185, 0.3);
    }
    
    .btn-download:active {
        transform: translateY(1px) scale(0.98);
    }

    .btn-disable {
        background-color: #ffb8c2;
        color: #ff3e5f;
    }

    .btn-disable:hover {
        background-color: #ff9eab;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(255, 62, 95, 0.3);
    }

    .btn-update {
        background-color: #9fe7ff;
        color: #0088cc;
    }

    .btn-update:hover {
        background-color: #78d8ff;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(0, 136, 204, 0.3);
    }

    .btn-settings {
        background-color: #f0f0f0;
        color: #666;
    }

    .btn-settings:hover {
        background-color: #e0e0e0;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    
    /* 插件状态标签 */
    .plugin-status {
        font-size: 12px;
        padding: 4px 10px;
        border-radius: 50px;
        text-align: center;
        margin-top: 8px;
        display: inline-block;
        white-space: nowrap;
        font-weight: 500;
        box-shadow: 0 3px 8px rgba(255, 126, 185, 0.15);
    }
    
    .plugin-status.installed {
        background-color: #ffe6f2;
        color: #ff7eb9;
        position: relative;
        padding-left: 22px;
    }
    
    .plugin-status.installed::before {
        content: '✓';
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-weight: bold;
    }
    
    /* 更新标签 */
    .plugin-badge {
        background-color: #9fe7ff;
        color: #0088cc;
        font-size: 12px;
        padding: 4px 10px;
        border-radius: 50px;
        font-weight: 500;
        white-space: nowrap;
        box-shadow: 0 3px 8px rgba(0, 136, 204, 0.15);
        display: inline-flex;
        align-items: center;
    }
    
    .plugin-badge::before {
        content: '\f2f1';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        font-size: 10px;
        animation: spin 4s linear infinite;
    }
    
    /* 插件价格标签样式 */
    .plugin-badges {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .plugin-price {
        font-size: 12px;
        padding: 4px 10px;
        border-radius: 50px;
        font-weight: 500;
        white-space: nowrap;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        display: inline-flex;
        align-items: center;
    }
    
    .plugin-price.free {
        background-color: #e1f5e1;
        color: #28a745;
    }
    
    .plugin-price.free::before {
        content: '\f06b';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        font-size: 10px;
    }
    
    .plugin-price.paid {
        background-color: #fff3cd;
        color: #d39e00;
    }
    
    .plugin-price.paid::before {
        content: '\f51e';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        font-size: 10px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 空状态样式 */
    .empty-state {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
    }

    .empty-state-icon {
        font-size: 70px;
        color: #ffdfed;
        margin-bottom: 25px;
        animation: bounce 2s ease infinite;
    }
    
    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-15px); }
    }

    .empty-state-text {
        font-size: 20px;
        color: #999;
        font-weight: 500;
    }

    /* 加载中指示器 */
    .loading-indicator {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 10;
    }

    .spinner {
        width: 60px;
        height: 60px;
        border: 5px solid #ffe6f2;
        border-top: 5px solid #ff7eb9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    .loading-text {
        font-size: 18px;
        color: #ff7eb9;
        font-weight: 500;
    }

    /* 通知样式 */
    .toast-container {
        position: fixed !important;
        top: 20px !important;
        right: 20px !important;
        left: auto !important;
        bottom: auto !important;
        z-index: 99999 !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-end !important;
        gap: 10px !important;
        pointer-events: none !important;
    }

    .toast {
        padding: 15px 20px;
        border-radius: 10px;
        background-color: #fff;
        color: #333;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        max-width: 300px;
        width: 100%;
        pointer-events: auto;
        transform: translateX(120%);
        opacity: 0;
        transform-origin: right center;
        border-left: 4px solid #ccc;
        margin-left: auto;
    }

    .toast.toast-success {
        border-left: 4px solid #28a745;
    }

    .toast.toast-error {
        border-left: 4px solid #dc3545;
    }

    .toast.toast-warning {
        border-left: 4px solid #ffc107;
    }

    .toast.toast-info {
        border-left: 4px solid #17a2b8;
    }

    .toast-icon {
        margin-right: 10px;
        font-size: 16px;
    }

    .toast-success .toast-icon {
        color: #28a745;
    }

    .toast-error .toast-icon {
        color: #dc3545;
    }

    .toast-warning .toast-icon {
        color: #ffc107;
    }

    .toast-info .toast-icon {
        color: #17a2b8;
    }

    .toast-content {
        flex: 1;
    }

    .toast-close {
        margin-left: 10px;
        cursor: pointer;
        font-size: 14px;
        color: #999;
        transition: color 0.2s;
    }

    .toast-close:hover {
        color: #555;
    }

    @keyframes toast-in {
        0% {
            transform: translateX(120%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes toast-out {
        0% {
            transform: translateX(0);
            opacity: 1;
        }
        100% {
            transform: translateX(120%);
            opacity: 0;
        }
    }

    /* 确认对话框 */
    .confirm-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 30000; /* 提高z-index，确保显示在开发者后台模态框(z-index: 20000)之上 */
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
        backdrop-filter: blur(5px);
    }

    .confirm-dialog.show {
        opacity: 1;
        visibility: visible;
    }

    .confirm-dialog-content {
        background-color: #fff;
        border-radius: 15px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: dialog-in 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        overflow: hidden;
        transform: scale(0.8);
        transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .confirm-dialog.show .confirm-dialog-content {
        transform: scale(1);
    }

    .confirm-dialog-header {
        padding: 15px 20px;
        background-color: #ffe6f2;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .confirm-dialog-header h3 {
        color: #ff7eb9;
        margin: 0;
        font-size: 18px;
        display: flex;
        align-items: center;
    }

    .confirm-dialog-header h3 i {
        margin-right: 8px;
    }

    .confirm-close-btn {
        background: none;
        border: none;
        color: #ff7eb9;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .confirm-close-btn:hover {
        color: #ff5ca8;
    }

    .confirm-dialog-body {
        padding: 20px;
    }

    .confirm-dialog-body p {
        margin: 0;
        font-size: 16px;
        color: #555;
    }

    .confirm-dialog-footer {
        padding: 15px 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background-color: #f9f9f9;
    }

    .confirm-btn {
        padding: 10px 20px;
        border-radius: 50px;
        border: none;
        font-size: 14px;
        cursor: pointer;
        font-weight: 500;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }

    .confirm-btn i {
        margin-right: 6px;
    }

    .cancel-btn {
        background-color: #f5f5f5;
        color: #777;
    }

    .cancel-btn:hover {
        background-color: #e5e5e5;
    }

    .confirm-btn-primary {
        background-color: #ff7eb9;
        color: white;
    }

    .confirm-btn-primary:hover {
        background-color: #ff5ca8;
    }

    /* 确保登录按钮样式更紧凑 */
    .developer-btn-primary {
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 10px 20px; /* 略微减小内边距 */
        font-size: 15px; /* 稍微减小字体大小 */
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-weight: 500;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap; /* 防止文本换行 */
        min-width: 90px; /* 确保按钮不会太窄 */
    }
    
    .developer-btn-primary .btn-text {
        position: relative;
        z-index: 1;
        transition: all 0.3s;
    }

    .developer-btn-primary .btn-icon {
        position: absolute;
        right: -20px;
        opacity: 0;
        transition: all 0.3s;
    }

    .developer-btn-primary:hover {
        background: linear-gradient(135deg, #ff5ca8, #ff3e8a);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.3);
        padding-right: 35px;
    }

    .developer-btn-primary:hover .btn-icon {
        right: 15px;
        opacity: 1;
    }

    .developer-btn-primary:active {
        transform: translateY(1px);
        box-shadow: 0 2px 5px rgba(255, 126, 185, 0.2);
    }

    @keyframes dialog-in {
        0% {
            transform: scale(0.8);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* 适配小屏幕 */
    @media (max-width: 767px) {
        .plugin-market-container {
            padding: 15px;
            border-radius: 15px;
        }
        
        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }
        
        .search-container {
            width: 100%;
        }
        
        .plugin-grid {
            grid-template-columns: 1fr;
        }
        
        .plugin-icon {
            width: 60px;
            height: 60px;
        }
        
        .plugin-card {
            padding: 15px;
        }
        
        .plugin-actions {
            flex-direction: column;
            align-items: flex-end;
        }
        
        .plugin-btn {
            width: 100%;
            justify-content: center;
        }
    }

    /* 禁用状态样式 */
    .plugin-status.disabled {
        background-color: #f0f0f0;
        color: #777;
        position: relative;
        padding-left: 22px;
    }

    .plugin-status.disabled::before {
        content: '✕';
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-weight: bold;
    }

    /* 开发者后台按钮样式 */
    .developer-btn-container {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
    }

    .developer-btn {
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        color: white;
        border: none;
        border-radius: 30px;
        padding: 10px 22px;
        font-size: 15px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 4px 15px rgba(255, 126, 185, 0.3);
        position: relative;
        overflow: hidden;
    }

    .developer-btn i {
        margin-right: 8px;
        transition: transform 0.3s ease;
    }

    .developer-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.4);
    }

    .developer-btn:hover i {
        transform: rotate(5deg) scale(1.2);
    }

    .developer-btn:active {
        transform: translateY(2px);
        box-shadow: 0 2px 5px rgba(255, 126, 185, 0.3);
    }

    .btn-sparkle {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 30px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
        transform: skewX(-20deg) translateX(-100%);
        transition: none;
    }

    .developer-btn:hover .btn-sparkle {
        animation: sparkle 1.5s ease-in-out infinite;
    }

    @keyframes sparkle {
        0% {
            transform: skewX(-20deg) translateX(-100%);
        }
        100% {
            transform: skewX(-20deg) translateX(200%);
        }
    }

    /* 开发者登录/注册模态框样式 - 增强版 */
    .developer-modal {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        z-index: 20000 !important;
        display: none;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
    }

    .developer-modal.show {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .developer-modal-content {
        background-color: #fff;
        border-radius: 24px;
        width: 90%;
        max-width: 480px;
        box-shadow: 0 20px 60px rgba(255, 126, 185, 0.3), 0 10px 25px rgba(255, 126, 185, 0.2);
        animation: modal-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        overflow: hidden;
        position: relative;
        border: 3px solid #ffe6f2;
        transform-origin: center bottom;
    }

    @keyframes modal-in {
        0% {
            transform: scale(0.9) translateY(30px);
            opacity: 0;
        }
        60% {
            transform: scale(1.02) translateY(-5px);
        }
        100% {
            transform: scale(1) translateY(0);
            opacity: 1;
        }
    }
    
    /* 增强模态框装饰元素 */
    .modal-decorations {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
        overflow: hidden;
        pointer-events: none;
    }
    
    .decoration-bubble {
        position: absolute;
        border-radius: 50%;
        background-color: #ffe6f2;
        opacity: 0.6;
    }
    
    .bubble-1 {
        width: 100px;
        height: 100px;
        top: -40px;
        left: -40px;
        animation: float 8s ease-in-out infinite;
    }
    
    .bubble-2 {
        width: 60px;
        height: 60px;
        top: 75%;
        right: -25px;
        animation: float 7s ease-in-out infinite 1s;
    }
    
    .bubble-3 {
        width: 35px;
        height: 35px;
        bottom: 25%;
        left: 10%;
        animation: float 9s ease-in-out infinite 0.5s;
    }
    
    .decoration-star {
        position: absolute;
        background-color: #ff9ecb;
        clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    }
    
    .star-1 {
        width: 30px;
        height: 30px;
        top: 25%;
        right: 12%;
        animation: twinkle 5s ease-in-out infinite;
    }
    
    .star-2 {
        width: 20px;
        height: 20px;
        bottom: 35%;
        left: 16%;
        animation: twinkle 4s ease-in-out infinite 1.5s;
    }
    
    .decoration-dot {
        position: absolute;
        border-radius: 50%;
        background-color: #ff7eb9;
        opacity: 0.4;
    }
    
    .dot-1 {
        width: 10px;
        height: 10px;
        top: 20%;
        left: 25%;
        animation: pulse 3s infinite;
    }
    
    .dot-2 {
        width: 15px;
        height: 15px;
        bottom: 40%;
        right: 25%;
        animation: pulse 4s infinite 1s;
    }
    
    .dot-3 {
        width: 8px;
        height: 8px;
        bottom: 20%;
        left: 45%;
        animation: pulse 5s infinite 2s;
    }
    
    .decoration-cloud {
        position: absolute;
        border-radius: 50px;
        background-color: rgba(255, 230, 242, 0.8);
    }
    
    .cloud-1 {
        width: 100px;
        height: 40px;
        top: 15%;
        left: 50%;
        transform: translateX(-50%);
        animation: float 10s ease-in-out infinite;
    }
    
    .cloud-1:before, .cloud-1:after {
        content: '';
        position: absolute;
        background-color: rgba(255, 230, 242, 0.8);
        border-radius: 50%;
    }
    
    .cloud-1:before {
        width: 50px;
        height: 50px;
        top: -20px;
        left: 15px;
    }
    
    .cloud-1:after {
        width: 40px;
        height: 40px;
        top: -15px;
        right: 15px;
    }
    
    .cloud-2 {
        width: 70px;
        height: 30px;
        bottom: 30%;
        right: 15%;
        animation: float 12s ease-in-out infinite 3s;
    }
    
    .cloud-2:before, .cloud-2:after {
        content: '';
        position: absolute;
        background-color: rgba(255, 230, 242, 0.8);
        border-radius: 50%;
    }
    
    .cloud-2:before {
        width: 35px;
        height: 35px;
        top: -15px;
        left: 10px;
    }
    
    .cloud-2:after {
        width: 30px;
        height: 30px;
        top: -12px;
        right: 10px;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-15px); }
    }
    
    @keyframes twinkle {
        0%, 100% { opacity: 0.5; transform: scale(1) rotate(0deg); }
        50% { opacity: 1; transform: scale(1.2) rotate(10deg); }
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.4; }
        50% { transform: scale(1.3); opacity: 0.7; }
    }

    .developer-modal-header {
        padding: 25px 30px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 1;
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        text-align: center;
    }
    
    .header-icon {
        width: 50px;
        height: 50px;
        background-color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        animation: rotate-icon 20s linear infinite;
    }
    
    .header-icon i {
        font-size: 24px;
        color: #ff7eb9;
    }
    
    @keyframes rotate-icon {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .developer-modal-header h3 {
        color: white;
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .developer-modal-close-btn {
        position: absolute;
        right: 15px;
        top: 15px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .developer-modal-close-btn:hover {
        background-color: rgba(255, 255, 255, 0.3);
        transform: rotate(90deg) scale(1.1);
    }
    
    /* 增强标签页切换样式 */
    .developer-modal-tabs {
        display: flex;
        position: relative;
        background-color: #fff;
        padding: 0;
        z-index: 1;
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.1);
    }
    
    .developer-tab-btn {
        flex: 1;
        background: none;
        border: none;
        padding: 18px 0;
        font-size: 16px;
        font-weight: 600;
        color: #999;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        text-align: center;
        overflow: hidden;
    }
    
    .developer-tab-btn i {
        margin-right: 8px;
        font-size: 14px;
        transition: all 0.3s;
    }
    
    .developer-tab-btn:hover i {
        transform: translateY(-3px);
    }
    
    .developer-tab-btn.active {
        color: #ff7eb9;
    }
    
    .developer-tab-btn::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background-color: #ff7eb9;
        transition: all 0.3s ease;
        opacity: 0;
        transform: translateX(-50%);
    }
    
    .developer-tab-btn:hover::after {
        width: 40%;
        opacity: 0.5;
    }
    
    .developer-tab-btn.active::after {
        width: 80%;
        opacity: 1;
    }
    
    .tab-indicator {
        position: absolute;
        bottom: 0;
        height: 3px;
        background-color: #ff7eb9;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        border-radius: 3px 3px 0 0;
        width: 50%;
        left: 0;
    }
    
    /* 美化表单内容样式 */
    .developer-tab-content {
        padding: 35px 30px;
        display: none;
        position: relative;
        z-index: 1;
    }
    
    .developer-tab-content.active {
        display: flex;
        flex-direction: column;
        animation: fade-in 0.5s forwards;
    }
    
    .tab-illustration {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }
    
    .illustration-container {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: linear-gradient(45deg, #ff9ecb, #ff7eb9);
        box-shadow: 0 10px 20px rgba(255, 126, 185, 0.2);
        animation: bounce 3s ease infinite;
    }
    
    .illustration-icon {
        color: white;
        font-size: 32px;
    }
    
    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }
    
    @keyframes fade-in {
        from { opacity: 0; transform: translateY(15px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .developer-form {
        display: flex;
        flex-direction: column;
        gap: 12px; /* 从20px减少到12px */
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px; /* 从8px减少到5px */
        position: relative;
    }
    
    .form-group label {
        font-size: 14px;
        color: #555;
        display: flex;
        align-items: center;
        font-weight: 600;
        margin-left: 5px;
    }
    
    .form-group label i {
        margin-right: 8px;
        color: #ff7eb9;
        font-size: 16px;
        transition: all 0.3s;
    }
    
    .form-input {
        border: 2px solid #f0f0f0;
        border-radius: 12px;
        padding: 14px 15px;
        font-size: 15px;
        transition: all 0.3s;
        background-color: #f9f9f9;
        color: #444;
        position: relative;
        z-index: 1;
    }
    
    /* 密码输入框和眼睛图标样式 */
    .password-input-container {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .password-input-container .form-input {
        width: 100%;
        padding-right: 40px;
    }
    
    .toggle-password-btn {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        transition: all 0.3s;
        height: 40px;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
        padding: 0;
    }
    
    .toggle-password-btn:hover {
        color: #ff7eb9;
    }
    
    .toggle-password-btn i {
        font-size: 20px;
    }
    
    .input-highlight {
        position: absolute;
        bottom: 0;
        left: 10px;
        height: 2px;
        width: 0;
        background: linear-gradient(90deg, #ff7eb9, #ff9ecb);
        transition: all 0.3s ease;
        z-index: 0;
        border-radius: 10px;
        opacity: 0;
    }
    
    .form-input:focus {
        border-color: #ff9ecb;
        outline: none;
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.1);
        background-color: #fff;
    }
    
    .form-input:focus + .input-highlight,
    .form-input:focus ~ .input-highlight {
        width: 50%;
        opacity: 1;
    }
    
    .form-input:focus ~ label i {
        transform: scale(1.2);
        color: #ff5ca8;
    }
    
    /* 美化勾选框样式 */
    .form-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: #666;
        margin: 5px 0 15px;
        position: relative;
    }
    
    .form-checkbox input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }
    
    .form-checkbox label {
        position: relative;
        cursor: pointer;
        user-select: none;
        padding-left: 30px;
        line-height: 22px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        transition: color 0.3s;
    }
    
    .form-checkbox label:before {
        content: '';
        position: absolute;
        left: 0;
        top: 1px;
        width: 20px;
        height: 20px;
        border: 2px solid #ffcce5;
        background-color: #fff;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 2px 5px rgba(255, 126, 185, 0.1);
    }
    
    .form-checkbox label:after {
        content: '\f004'; /* 使用FontAwesome心形图标 */
        font-family: 'Font Awesome 5 Free';
        position: absolute;
        left: 0;
        top: 1px;
        width: 20px;
        height: 20px;
        color: #ff7eb9;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: scale(0) rotate(-30deg);
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    }
    
    .form-checkbox input[type="checkbox"]:checked + label:before {
        border-color: #ff7eb9;
        background-color: #fff2f8;
        transform: scale(1.05);
    }
    
    .form-checkbox input[type="checkbox"]:checked + label:after {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        font-weight: 900; /* 使用实心心形 */
        animation: checkbox-heart 0.6s cubic-bezier(0.17, 0.89, 0.32, 1.49);
    }
    
    @keyframes checkbox-heart {
        0% { transform: scale(0) rotate(-30deg); opacity: 0; }
        40% { transform: scale(1.3) rotate(0deg); opacity: 0.8; }
        100% { transform: scale(1) rotate(0deg); opacity: 1; }
    }
    
    .form-checkbox input[type="checkbox"]:focus + label:before {
        box-shadow: 0 0 0 4px rgba(255, 126, 185, 0.2);
    }
    
    .form-checkbox:hover label:before {
        border-color: #ff9ecb;
        transform: translateY(-2px) scale(1.03);
        box-shadow: 0 4px 10px rgba(255, 126, 185, 0.15);
    }
    
    /* 特别优化同意条款复选框 */
    .agreement-checkbox label {
        padding-top: 1px;
        padding-bottom: 1px;
    }
    
    .agreement-checkbox .checkbox-text {
        margin-right: 4px;
    }
    
    .agreement-checkbox .inline-link {
        color: #ff7eb9;
        font-weight: 600;
        text-decoration: none;
        margin: 0 4px;
        position: relative;
        z-index: 1;
        transition: all 0.3s;
    }
    
    .agreement-checkbox .inline-link:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 1.5px;
        background: linear-gradient(90deg, #ff7eb9, #ff9ecb);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
    }
    
    .agreement-checkbox .inline-link:hover {
        color: #ff5ca8;
    }
    
    .agreement-checkbox .inline-link:hover:after {
        transform: scaleX(1);
    }
    
    .agreement-checkbox .terms-link:hover ~ .privacy-link:after {
        transform: scaleX(0);
    }

    .form-submit-btn {
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 15px 20px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        margin-top: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 15px rgba(255, 126, 185, 0.2);
    }
    
    .form-submit-btn:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 25px rgba(255, 126, 185, 0.3);
    }
    
    .form-submit-btn:active {
        transform: translateY(2px);
        box-shadow: 0 5px 10px rgba(255, 126, 185, 0.2);
    }
    
    .btn-text {
        position: relative;
        z-index: 1;
        letter-spacing: 1px;
    }
    
    .btn-icon {
        margin-left: 10px;
        position: relative;
        z-index: 1;
        transition: all 0.3s;
    }
    
    .form-submit-btn:hover .btn-icon {
        transform: translateX(5px);
    }
    
    .btn-shine {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0) 100%
        );
        transform: rotate(45deg);
        animation: btn-shine 3s infinite;
    }
    
    @keyframes btn-shine {
        0% {
            left: -50%;
            top: -50%;
        }
        100% {
            left: 150%;
            top: 150%;
        }
    }
    
    .form-footer {
        text-align: center;
        margin-top: 15px;
        font-size: 14px;
    }
    
    .form-link {
        color: #ff7eb9;
        text-decoration: none;
        transition: all 0.3s;
        font-weight: 500;
    }
    
    .form-link:hover {
        color: #ff5ca8;
        text-decoration: underline;
    }
    
    .inline-link {
        color: #ff7eb9;
        text-decoration: none;
        transition: all 0.3s;
        font-weight: 500;
    }
    
    .inline-link:hover {
        color: #ff5ca8;
        text-decoration: underline;
    }
    
    /* 优化底部装饰 */
    .modal-bottom-decoration {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 80px;
        z-index: 0;
        opacity: 0.8;
    }
    
    .modal-bottom-decoration svg {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 100%;
    }
    
    /* 响应式调整 */
    @media (max-width: 576px) {
        .developer-modal-content {
            max-width: 95%;
            margin: 0 10px;
        }
        
        .developer-tab-content {
            padding: 25px 20px;
        }
        
        .form-submit-btn {
            padding: 12px 15px;
            font-size: 15px;
        }
        
        .developer-modal-header {
            padding: 20px 15px;
        }
        
        .header-icon {
            width: 40px;
            height: 40px;
        }
        
        .developer-modal-header h3 {
            font-size: 20px;
        }
    }

    /* 账号后台模态框样式 */
    .account-dashboard-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
    }

    .account-dashboard-modal.show {
        display: flex;
    }

    .account-dashboard-content {
        background-color: #fff;
        border-radius: 20px;
        width: 90%;
        max-width: 800px;
        box-shadow: 0 15px 50px rgba(255, 126, 185, 0.3);
        animation: modal-slide-in 0.4s ease-out forwards;
        overflow: hidden;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    @keyframes modal-slide-in {
        0% {
            transform: translateY(30px);
            opacity: 0;
        }
        100% {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .account-modal-header {
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        color: white;
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px solid #ffe6f2;
    }

    .account-modal-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    }

    .account-modal-close-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .account-modal-close-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: rotate(90deg);
    }

    .account-modal-tabs {
        display: flex;
        background-color: #f9f9f9;
        position: relative;
        border-bottom: 1px solid #f0f0f0;
    }

    .account-tab-item {
        padding: 16px 20px;
        font-size: 16px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        text-align: center;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .account-tab-item i {
        margin-right: 8px;
        font-size: 16px;
    }

    .account-tab-item:hover {
        color: #ff7eb9;
        background-color: #fff5fa;
    }

    .account-tab-item.active {
        color: #ff7eb9;
        background-color: #fff;
    }

    .account-modal-tabs .tab-indicator {
        position: absolute;
        bottom: 0;
        height: 3px;
        background-color: #ff7eb9;
        transition: all 0.3s ease;
        border-radius: 3px 3px 0 0;
    }

    .account-tab-content {
        padding: 25px;
        overflow-y: auto;
        max-height: calc(90vh - 130px);
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
        animation: fade-in 0.3s ease forwards;
    }

    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 首页样式 */
    .account-welcome {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #fff5fa, #fff9fc);
        border-radius: 15px;
    }

    .welcome-icon {
        width: 80px;
        height: 80px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.2);
    }

    .welcome-icon i {
        font-size: 40px;
        color: #ff7eb9;
    }

    .welcome-title {
        font-size: 24px;
        color: #333;
        margin-bottom: 10px;
    }

    .welcome-message {
        color: #777;
        font-size: 16px;
    }

    .account-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 20px;
        display: flex;
        align-items: center;
        border: 1px solid #f0f0f0;
        transition: all 0.3s;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(255, 126, 185, 0.1);
        border-color: #ffcce5;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background-color: #fff5fa;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: #ff7eb9;
    }

    .stat-icon i {
        font-size: 22px;
    }

    .stat-info {
        flex: 1;
    }

    .stat-value {
        display: block;
        font-size: 22px;
        font-weight: 700;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 14px;
        color: #777;
    }

    /* 插件列表样式 */
    .plugins-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .plugins-header h3 {
        margin: 0;
        font-size: 20px;
        color: #333;
    }

    .new-plugin-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 15px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
    }

    .new-plugin-btn i {
        margin-right: 8px;
    }

    .new-plugin-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.2);
    }

    .empty-plugins {
        text-align: center;
        padding: 50px 20px;
        background-color: #f9f9f9;
        border-radius: 15px;
    }

    .empty-icon {
        font-size: 50px;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-text {
        color: #999;
        margin-bottom: 20px;
        font-size: 16px;
    }

    .create-plugin-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
    }

    .create-plugin-btn i {
        margin-right: 8px;
    }

    .create-plugin-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.2);
    }

    /* 账号设置样式 */
    .settings-form {
        max-width: 600px;
        margin: 0 auto;
    }

    .settings-form h3 {
        margin: 0 0 20px;
        font-size: 20px;
        color: #333;
    }

    .settings-form h4 {
        margin: 25px 0 15px;
        font-size: 18px;
        color: #555;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }

    .form-group label i {
        margin-right: 8px;
        color: #ff7eb9;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s;
        background-color: #f9f9f9;
    }

    .form-control:focus {
        border-color: #ff9ecb;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.1);
        background-color: #fff;
    }

    .form-control[readonly] {
        background-color: #f5f5f5;
        color: #888;
        cursor: not-allowed;
    }

    .form-divider {
        height: 1px;
        background-color: #f0f0f0;
        margin: 30px 0;
    }

    .password-input-container {
        position: relative;
    }

    .password-input-container .form-control {
        padding-right: 40px;
    }

    .toggle-password-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        transition: all 0.2s;
    }

    .toggle-password-btn:hover {
        color: #ff7eb9;
    }

    .save-password-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s;
        margin-top: 10px;
        font-weight: 500;
    }

    .save-password-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.2);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .account-stats {
            grid-template-columns: 1fr;
        }
        
        .account-tab-item {
            padding: 12px 10px;
            font-size: 14px;
        }
        
        .account-tab-item i {
            margin-right: 5px;
        }
        
        .account-dashboard-content {
            width: 95%;
        }
        
        .account-modal-title {
            font-size: 20px;
        }
    }

    /* 资金管理页面样式 */
    .finance-container {
        padding: 25px;
    }

    .balance-card {
        background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
        border-radius: 20px;
        padding: 25px;
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        box-shadow: 0 10px 25px rgba(255, 126, 185, 0.2);
        position: relative;
        overflow: hidden;
    }

    .balance-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        animation: shine 15s linear infinite;
    }

    @keyframes shine {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .balance-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }

    .balance-icon i {
        font-size: 28px;
        color: white;
    }

    .balance-info {
        flex: 1;
    }

    .balance-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        margin-bottom: 5px;
    }

    .balance-amount {
        color: white;
        font-size: 32px;
        font-weight: bold;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .withdraw-btn {
        background-color: white;
        color: #ff7eb9;
        border: none;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        margin-left: 20px;
    }

    .withdraw-btn i {
        margin-right: 8px;
    }

    .withdraw-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .qr-code-section {
        background-color: white;
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 2px solid #f8f8f8;
    }

    .section-title {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .section-title::before {
        content: '';
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #ff7eb9, #ff5ca8);
        margin-right: 10px;
        border-radius: 2px;
    }

    .qr-code-container {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }

    .qr-code-preview {
        width: 200px;
        height: 200px;
        border: 2px dashed #e0e0e0;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #f9f9f9;
        transition: all 0.3s;
    }

    .qr-code-preview:hover {
        border-color: #ff9ecb;
        background-color: #fff5fa;
    }

    .qr-code-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .qr-code-placeholder {
        text-align: center;
        color: #999;
    }

    .qr-code-placeholder i {
        font-size: 48px;
        margin-bottom: 10px;
    }

    .qr-code-placeholder p {
        margin: 0;
        font-size: 14px;
    }

    .qr-code-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        justify-content: center;
    }

    .upload-btn, .delete-btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
    }

    .upload-btn {
        background-color: #ff7eb9;
        color: white;
        border: none;
    }

    .upload-btn:hover {
        background-color: #ff5ca8;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 126, 185, 0.2);
    }

    .delete-btn {
        background-color: #fff5f5;
        color: #dc3545;
        border: none;
    }

    .delete-btn:hover {
        background-color: #ffe5e5;
        transform: translateY(-2px);
    }

    .upload-btn i, .delete-btn i {
        margin-right: 8px;
    }

    .qr-code-tips {
        background-color: #fff9fc;
        border-radius: 12px;
        padding: 15px;
        margin-top: 20px;
    }

    .qr-code-tips p {
        margin: 5px 0;
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .qr-code-tips p::before {
        content: '•';
        color: #ff7eb9;
        margin-right: 8px;
        font-size: 18px;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .balance-card {
            flex-direction: column;
            text-align: center;
            padding: 20px;
        }
        
        .balance-icon {
            margin: 0 0 15px 0;
        }
        
        .withdraw-btn {
            margin: 15px 0 0 0;
            width: 100%;
            justify-content: center;
        }
        
        .qr-code-container {
            flex-direction: column;
            align-items: center;
        }
        
        .qr-code-actions {
            width: 100%;
        }
    }