:root {
            --primary-color: #ff7eb9;
            --primary-light: #ffa6d2;
            --primary-dark: #e55a9b;
            --accent-color: #9fe7ff;
            --background-color: #fff5f9;
            --card-bg: #ffffff;
            --text-primary: #333333;
            --text-secondary: #666666;
            --border-color: #ffdfed;
            --shadow: 0 4px 8px rgba(255, 126, 185, 0.1);
            --border-radius: 15px;
        }

        .btn-primary i {
            color: white !important;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        #product-container {
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background-color: var(--background-color);
            color: var(--text-primary);
        }

        /* 页面标题 */
        .page-header {
            display: none;
            /* 隐藏原页面标题区域 */
        }

        /* 商品列表卡片 */
        .products-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 20px;
            flex-grow: 1;
            overflow: auto;
            animation: slideUp 0.5s ease;
            margin-top: 0;
        }

        /* 卡片标题和按钮区域 */
        .card-title {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 20px;
            color: var(--primary-dark);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-actions {
            display: flex;
            gap: 10px;
        }

        .card-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        /* 表格样式 */
        .products-table {
            width: 100%;
            min-width: 1400px;
            /* 确保10列都有足够空间显示 */
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        }

        .products-table th,
        .products-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }

        /* 所有列都已设置为居中对齐，移除特定列的样式以保持一致性 */
        /* 确保所有表头和单元格都居中对齐 */
        .products-table th,
        .products-table td {
            text-align: center !important;
            vertical-align: middle !important;
        }

        .products-table th {
            background-color: #fff2f8;
            font-weight: 600;
            color: var(--primary-color);
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid #ffdfed;
        }

        .products-table th i {
            margin-right: 8px;
            font-size: 0.9em;
            opacity: 0.8;
        }

        .products-table tbody tr:last-child td {
            border-bottom: none;
        }

        .products-table tr {
            position: relative;
        }

        .products-table tbody tr:hover {
            background-color: #fff9fc;
        }

        /* 桌面端水平滚动条样式 */
        .table-responsive {
            overflow-x: auto;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table-responsive::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: rgba(255, 126, 185, 0.1);
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 2px 6px rgba(255, 126, 185, 0.3);
        }

        .table-responsive::-webkit-scrollbar-corner {
            background: rgba(255, 126, 185, 0.1);
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 126, 185, 0.1);
            margin-top: 20px;
        }

        .pagination-info {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-size-select {
            padding: 8px 12px;
            border: 1px solid rgba(255, 126, 185, 0.2);
            border-radius: 6px;
            font-size: 13px;
            background-color: white;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-size-select:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 6px rgba(255, 126, 185, 0.2);
        }

        .pagination-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 1px solid rgba(255, 126, 185, 0.2);
            border-radius: 6px;
            background-color: white;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        .pagination-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 126, 185, 0.3);
            transform: translateY(-1px);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid rgba(255, 126, 185, 0.2);
            border-radius: 6px;
            background-color: white;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            min-width: 36px;
            text-align: center;
            font-weight: 500;
        }

        .page-number:hover {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 126, 185, 0.3);
            transform: translateY(-1px);
        }

        .page-number.active {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 126, 185, 0.4);
        }

        /* 商品图片 */
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
            border: 2px solid var(--border-color);
            display: block;
            margin: 0 auto;
            box-shadow: 0 3px 6px rgba(255, 158, 210, 0.2);
            transition: all 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 10px rgba(255, 158, 210, 0.3);
        }

        /* 价格样式 */
        .price {
            font-weight: 600;
            color: var(--primary-dark);
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            width: fit-content;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .edit-btn {
            background-color: #ffe08a;
            color: #d19c00;
        }

        .edit-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(255, 224, 138, 0.5);
        }

        .delete-btn {
            background-color: #ffb8c2;
            color: #ff3e5f;
        }

        .delete-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(255, 184, 194, 0.5);
        }

        .action-btn i {
            font-size: 16px;
        }

        /* 徽章/标签 */
        .badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
            color: white;
        }

        .badge-success {
            background-color: #d1f7c4;
            color: #5cb85c;
        }

        .badge-warning {
            background-color: #f8d7da;
            color: #dc3545;
        }

        /* 价格提示卡片 */
        .tooltip-card {
            position: absolute;
            top: calc(100% + 10px);
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(255, 158, 210, 0.2);
            width: 300px;
            z-index: 1000;
            padding: 15px;
            display: none;
            border-left: 3px solid var(--primary-color);
            animation: popIn 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        }

        @keyframes popIn {
            from {
                opacity: 0;
                transform: translateX(-50%) scale(0.9);
            }

            to {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }

        tr:hover .tooltip-card {
            display: block;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 60px;
            color: #ffdfed;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }

            100% {
                transform: translateY(0px);
            }
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 模态框 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .modal-overlay.active {
            display: flex;
        }

        #productModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 100000;
        }

        #productModal.active {
            display: flex !important;
        }

        .modal-content {
            position: relative;
            background: white;
            width: 800px;
            max-width: 95%;
            max-height: 90vh;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: none;
        }

        .modal-title {
            margin: 0;
            font-size: 20px;
            color: white;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            line-height: 1;
            transition: all 0.2s;
        }

        .modal-close:hover {
            transform: scale(1.2);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
        }

        /* 导航选项卡 */
        .nav-tabs {
            display: flex;
            padding: 0;
            margin: 0 0 20px 0;
            list-style: none;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }

        .nav-tabs .nav-link {
            border: none;
            padding: 10px 15px;
            display: block;
            text-decoration: none;
            color: var(--text-secondary);
            margin-right: 5px;
            transition: all 0.3s;
            border-radius: 5px 5px 0 0;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: white;
            border-bottom: 2px solid var(--primary-color);
        }

        .nav-tabs .nav-link:hover:not(.active) {
            color: var(--primary-dark);
            background-color: rgba(255, 126, 185, 0.05);
        }

        .tab-content>.tab-pane {
            display: none;
        }

        .tab-content>.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.95rem;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
            outline: none;
        }

        .form-control.error {
            border-color: #ff6b6b;
        }

        /* 无限库存复选框自定义样式 */
        #unlimitedStock {
            accent-color: var(--primary-color);
        }

        #unlimitedStock:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* 兼容旧版浏览器的复选框样式 */
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-check-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(255, 126, 185, 0.25);
        }

        /* 添加请求头按钮悬停样式 */
        #addHeaderBtn:hover {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 126, 185, 0.3);
        }

        /* 变量标签样式 */
        .variable-tag {
            display: inline-block;
            padding: 4px 8px;
            background-color: rgba(255, 126, 185, 0.1);
            border: 1px solid rgba(255, 126, 185, 0.3);
            border-radius: 4px;
            font-size: 12px;
            color: var(--primary-dark);
            font-weight: 500;
        }

        /* 变量按钮样式 */
        .variable-btn {
            padding: 6px 12px;
            margin: 2px;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 12px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .variable-btn:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        /* 变量快捷区域样式 */
        .variable-shortcuts {
            background-color: #f8f9fa;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
        }

        /* 变量高亮样式 - 仅修改字体颜色 */
        .variable-highlight {
            color: #ff6b9d !important;
        }

        /* Contenteditable 样式 - 优化placeholder和变量高亮 */
        .editable-content {
            min-height: 38px;
            height: auto;
            background-color: #ffffff;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: var(--text-primary);
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            position: relative;
        }

        .editable-content:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
        }

        .editable-content.multi-line {
            height: 200px;
        }

        /* 优化的placeholder样式 - 使用CSS伪元素 */
        .editable-content:empty::before {
            content: attr(data-placeholder);
            color: #999;
            pointer-events: none;
            position: absolute;
            top: 0.375rem;
            left: 0.75rem;
            font-style: italic;
        }

        .editable-content:focus:empty::before {
            opacity: 0.5;
        }

        /* 类型卡片 */
        .type-card-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .type-card {
            flex: 1;
            cursor: pointer;
            padding: 20px;
            text-align: center;
            border-radius: 15px;
            border: 2px solid transparent;
            background-color: #fff9fc;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(255, 158, 210, 0.1);
        }

        .type-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(255, 158, 210, 0.2);
        }

        .type-card.selected {
            background-color: rgba(255, 126, 185, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-3px);
        }

        .type-card i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .type-card h5 {
            font-size: 1rem;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-weight: 600;
        }

        .type-card p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin: 0;
        }

        /* 参数行 */
        .parameter-row {
            padding: 15px;
            padding-right: 48px;
            /* 为右上角删除按钮留出适当空间 */
            background-color: #fff9fc;
            border-radius: 12px;
            margin-bottom: 15px;
            border-left: 3px solid var(--primary-light);
            transition: all 0.3s ease;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 3px 8px rgba(255, 158, 210, 0.1);
            position: relative;
            /* 为绝对定位的删除按钮提供定位上下文 */
            overflow: visible;
            /* 确保删除按钮不被裁剪 */
        }

        .parameter-row:hover {
            background-color: #fff5f9;
            border-left-color: var(--primary-color);
            box-shadow: 0 5px 12px rgba(255, 158, 210, 0.15);
            transform: translateY(-2px);
        }

        .parameter-row .btn.remove-param {
            position: absolute;
            /* 绝对定位 */
            top: 8px;
            /* 右上角定位 */
            right: 12px;
            /* 稍微左移一点点 */
            width: 30px;
            /* 确保正圆形 */
            height: 30px;
            /* 确保正圆形 */
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            /* 完美圆形 */
            margin: 0;
            /* 移除margin-top */
            transition: all 0.2s;
            border: 1px solid #ffd6eb;
            background-color: white;
            color: var(--primary-color);
            z-index: 10;
            /* 确保在顶层 */
            box-shadow: 0 2px 4px rgba(255, 158, 210, 0.1);
            /* 添加阴影 */
            line-height: 1;
            /* 确保图标垂直居中 */
        }

        /* 桌面端删除按钮图标居中 */
        .parameter-row .btn.remove-param i {
            font-size: 12px;
            /* 适配30px按钮的图标大小 */
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            line-height: 1;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            /* 完美居中 */
        }

        .parameter-row .btn.remove-param:hover {
            background-color: #fff5f9;
            border-color: #ff6b6b;
            color: #ff6b6b;
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(255, 126, 185, 0.2);
        }

        .parameter-row .btn.remove-param:hover i {
            transform: translate(-50%, -50%) scale(1.05);
            /* 悬浮时图标轻微放大 */
        }

        .parameter-row .form-group {
            margin-bottom: 0;
        }

        /* 价格计算区域 */
        .price-calculation {
            background-color: #fff9fc;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            box-shadow: 0 3px 10px rgba(255, 158, 210, 0.1);
        }

        .price-calculation-header {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--primary-dark);
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
            -webkit-tap-highlight-color: transparent;
            /* 移除移动设备上的点击高亮 */
        }

        /* 特殊按钮尺寸 */
        .modal-footer .btn {
            padding: 8px 15px;
            font-size: 14px;
        }

        .modal-footer .btn-primary {
            min-width: 80px;
            max-width: 100px;
        }

        .btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: inherit;
            opacity: 0;
            transition: all 0.2s;
            pointer-events: none;
        }

        .btn:active {
            transform: translateY(1px);
        }

        .btn:active::after {
            opacity: 1;
        }

        .btn i {
            margin-right: 8px;
        }

        /* 主要按钮样式 */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 10px rgba(255, 126, 185, 0.3);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 126, 185, 0.4);
        }

        .btn-primary:active,
        .btn-primary:focus,
        .btn-primary:active:focus {
            background-color: var(--primary-dark) !important;
            border-color: var(--primary-dark) !important;
            color: white !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 126, 185, 0.25) !important;
        }

        .btn-primary:disabled {
            background-color: var(--primary-light) !important;
            border-color: var(--primary-light) !important;
            opacity: 0.8;
        }

        .btn-light {
            background-color: #f0f0f0;
            color: #666;
            border: none;
        }

        .btn-light:hover {
            background-color: #e0e0e0;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-danger {
            background-color: #ff6b6b;
            color: white;
            border-color: #ff6b6b;
        }

        .btn-danger:hover {
            background-color: #e84c4c;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(232, 76, 76, 0.3);
        }

        /* 确认删除对话框 */
        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .confirm-dialog.show {
            opacity: 1;
            visibility: visible;
        }

        .confirm-box {
            background-color: white;
            border-radius: 15px;
            width: 400px;
            max-width: 90%;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-20px);
            transition: transform 0.3s;
            border: 2px solid #ffd6eb;
        }

        .confirm-dialog.show .confirm-box {
            transform: translateY(0);
        }

        .confirm-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #ff6b6b;
            display: flex;
            align-items: center;
        }

        .confirm-title i {
            margin-right: 10px;
        }

        .confirm-message {
            margin-bottom: 25px;
            color: var(--text-primary);
            font-size: 16px;
            line-height: 1.5;
        }

        .confirm-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        /* 通知提示 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(255, 126, 185, 0.2);
            display: flex;
            align-items: center;
            max-width: 300px;
            width: auto;
            height: auto;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 200000;
            pointer-events: none;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
            pointer-events: auto;
        }

        .notification.success {
            background: #d4f7e5;
            color: #155724;
            border-left: 4px solid #42b983;
        }

        .notification.error {
            background: #ffd6e0;
            color: #721c24;
            border-left: 4px solid #ff6b6b;
        }

        .notification-icon {
            margin-right: 10px;
            font-size: 18px;
        }

        .notification.success .notification-icon {
            color: #42b983;
        }

        .notification.error .notification-icon {
            color: #ff6b6b;
        }

        .notification-message {
            font-size: 14px;
            font-weight: 500;
        }

        /* 加载中指示器 */
        #loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 100000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        #loading-overlay.show {
            display: flex;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 126, 185, 0.3);
            border-radius: 50%;
            border-top: 2px solid var(--primary-color);
            animation: spin 1s linear infinite;
            margin-right: 6px;
            display: none;
            /* 默认隐藏 */
        }

        .loading-text {
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 500;
        }

        /* 动画效果 */
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 按钮呼吸灯效果 */
        @keyframes pulse {
            0% {
                box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
            }

            50% {
                box-shadow: 0 4px 12px rgba(255, 158, 210, 0.4);
            }

            100% {
                box-shadow: 0 4px 8px rgba(255, 158, 210, 0.2);
            }
        }

        .btn-primary {
            animation: pulse 2s infinite;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .products-table {
                font-size: 0.9rem;
            }

            .action-btn {
                width: 36px;
                height: 36px;
            }

            .form-control,
            .btn {
                padding: 8px 15px;
                font-size: 0.9rem;
            }

            .modal-content {
                width: 95%;
            }
        }

        /* 确保模态框弹层层级正确 */
        #productModal.active {
            display: flex !important;
        }

        #productModal .modal-dialog {
            position: relative;
            z-index: 100001;
            margin: 1.75rem auto;
            pointer-events: auto;
        }

        #productModal .modal-content {
            pointer-events: auto;
        }

        /* 关闭按钮样式增强 */
        .modal-header .btn-close {
            background: none !important;
            color: white !important;
            opacity: 0.8;
            font-size: 24px;
            padding: 0;
            margin: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            border: none;
            position: relative;
            box-shadow: none !important;
            outline: none !important;
        }

        .modal-header .btn-close:hover {
            opacity: 1;
            background-color: rgba(255, 255, 255, 0.1) !important;
            transform: rotate(90deg);
        }

        .modal-header .btn-close:active,
        .modal-header .btn-close:focus {
            opacity: 1;
            background-color: rgba(255, 255, 255, 0.2) !important;
            box-shadow: none !important;
            outline: none !important;
        }

        /* 全局按钮样式优化 - 增强选择器优先级 */
        button:focus,
        .btn:focus,
        button:active,
        .btn:active,
        button:active:focus,
        .btn:active:focus,
        button.active:focus,
        .btn.active:focus,
        .btn-close:focus,
        button:focus-visible,
        .btn:focus-visible,
        .btn-close:focus-visible {
            outline: none !important;
            box-shadow: none !important;
            border-color: inherit !important;
        }

        /* 清除Bootstrap的默认样式 */
        .btn-primary:focus,
        .btn-primary.focus,
        .btn-primary:not(:disabled):not(.disabled):active,
        .btn-primary:not(:disabled):not(.disabled).active,
        .show>.btn-primary.dropdown-toggle {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            box-shadow: none !important;
        }

        .btn-primary:not(:disabled):not(.disabled):active,
        .btn-primary:not(:disabled):not(.disabled).active {
            background-color: var(--primary-dark) !important;
            transform: translateY(1px) !important;
        }

        /* 覆盖Bootstrap的按钮激活状态样式 */
        .btn-check:checked+.btn-primary,
        .btn-check:active+.btn-primary,
        .btn-primary:active,
        .btn-primary.active,
        .show>.btn-primary.dropdown-toggle {
            color: #fff !important;
            background-color: var(--primary-dark) !important;
            border-color: var(--primary-dark) !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 126, 185, 0.25) !important;
        }

        /* 添加按钮点击波纹效果 */
        .btn::after {
            content: '';
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 100px;
            height: 100px;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%) scale(0);
            transition: transform 0.5s ease-out, opacity 0.5s ease-out;
            pointer-events: none;
            opacity: 0.7;
        }

        .btn:active::after {
            transform: translate(-50%, -50%) scale(2.5);
            opacity: 0;
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            10%,
            30%,
            50%,
            70%,
            90% {
                transform: translateX(-5px);
            }

            20%,
            40%,
            60%,
            80% {
                transform: translateX(5px);
            }
        }

        @keyframes rowFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .products-table tbody tr {
            animation: rowFadeIn 0.5s ease forwards;
        }

        .products-table tbody tr:nth-child(1) {
            animation-delay: 0.05s;
        }

        .products-table tbody tr:nth-child(2) {
            animation-delay: 0.1s;
        }

        .products-table tbody tr:nth-child(3) {
            animation-delay: 0.15s;
        }

        .products-table tbody tr:nth-child(4) {
            animation-delay: 0.2s;
        }

        .products-table tbody tr:nth-child(5) {
            animation-delay: 0.25s;
        }

        .products-table tbody tr:nth-child(6) {
            animation-delay: 0.3s;
        }

        .products-table tbody tr:nth-child(7) {
            animation-delay: 0.35s;
        }

        .products-table tbody tr:nth-child(8) {
            animation-delay: 0.4s;
        }

        .products-table tbody tr:nth-child(9) {
            animation-delay: 0.45s;
        }

        .products-table tbody tr:nth-child(10) {
            animation-delay: 0.5s;
        }

        /* 表单验证样式 */
        .form-control.is-invalid {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.15);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23ff6b6b' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ff6b6b' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            padding-right: calc(1.5em + 0.75rem);
        }

        .invalid-feedback {
            display: none;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #ff6b6b;
        }

        .invalid-feedback.show {
            display: block;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-5px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 修复Bootstrap按钮激活状态的颜色 */
        .btn-check:checked+.btn-primary,
        .btn-check:active+.btn-primary,
        .btn-primary.active,
        .btn-primary:active,
        .show>.btn-primary.dropdown-toggle {
            background-color: var(--primary-dark) !important;
            border-color: var(--primary-dark) !important;
            color: white !important;
        }

        .show>.btn-primary.dropdown-toggle:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 126, 185, 0.25) !important;
        }

        /* 修复模态框中的按钮样式 */
        .modal-footer .btn-primary:not(:disabled):not(.disabled):active,
        .modal-footer .btn-primary:not(:disabled):not(.disabled).active {
            background-color: var(--primary-dark) !important;
            border-color: var(--primary-dark) !important;
        }

        /* 旋转加载图标样式 */
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
            border-width: 0.2em;
            color: white;
        }

        /* 对接商品类型的特殊样式 */
        .docking-product-type {
            display: inline-flex;
            align-items: center;
            background-color: #e1f7ff;
            color: #0095db;
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.85rem;
            box-shadow: 0 2px 5px rgba(0, 149, 219, 0.2);
            border: 1px solid #b8e8ff;
            animation: pulse-blue 2s infinite;
        }

        @keyframes pulse-blue {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 149, 219, 0.4);
            }

            70% {
                box-shadow: 0 0 0 6px rgba(0, 149, 219, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(0, 149, 219, 0);
            }
        }

        /* 搜索筛选区域样式 */
        .search-filter-section {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(255, 126, 185, 0.1);
            border: 1px solid rgba(255, 126, 185, 0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto auto;
            gap: 16px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .filter-label {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .search-filter-section .form-control {
            padding: 12px 16px;
            border: 2px solid rgba(255, 126, 185, 0.2);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: white;
        }

        .search-filter-section .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
            outline: none;
        }

        .search-filter-section .form-control::placeholder {
            color: #999;
        }

        .search-filter-section select.form-control {
            cursor: pointer;
        }

        .search-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4);
        }

        .reset-btn {
            padding: 12px;
            background-color: #f0f0f0;
            color: #666;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
        }

        .reset-btn:hover {
            background-color: #e0e0e0;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        /* 移动端搜索筛选区域响应式样式 */
        @media (max-width: 768px) {
            .search-filter-section {
                padding: 15px;
                margin-bottom: 15px;
            }

            .filter-row {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto auto;
                gap: 12px;
            }

            /* 搜索框占满第一行 */
            .filter-row .filter-group:first-child {
                grid-column: 1 / -1;
            }

            /* 按钮组在最后一行 */
            .filter-row .search-btn,
            .filter-row .reset-btn {
                grid-row: 4;
            }

            .search-btn {
                justify-content: center;
                padding: 10px 20px;
                font-size: 13px;
                height: 40px;
            }

            .reset-btn {
                width: 40px;
                height: 40px;
                padding: 8px;
            }

            .filter-label {
                font-size: 12px;
                margin-bottom: 2px;
            }

            .search-filter-section .form-control {
                font-size: 13px;
                padding: 10px 12px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .search-filter-section {
                padding: 12px;
            }

            .filter-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .filter-row .filter-group:first-child {
                grid-column: 1;
            }

            .search-btn {
                padding: 8px 16px;
                font-size: 12px;
                height: 36px;
            }

            .reset-btn {
                width: 36px;
                height: 36px;
                padding: 6px;
            }

            .search-filter-section .form-control {
                font-size: 12px;
                padding: 8px 10px;
                height: 36px;
            }
        }

        /* ========== 移动端布局层次结构优化 ========== */
        @media (max-width: 768px) {

            /* 商品页面容器 - 简洁布局 */
            #product-container {
                background-color: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            /* 商品列表卡片 - 一级层次：主要功能区域 */
            .products-card {
                background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
                border-radius: 16px !important;
                box-shadow:
                    0 8px 32px rgba(255, 107, 157, 0.12),
                    0 4px 16px rgba(0, 0, 0, 0.08),
                    0 2px 8px rgba(0, 0, 0, 0.04) !important;
                padding: 32px 24px !important;
                margin: 0 0 24px 0 !important;
                border: 2px solid transparent !important;
                background-clip: padding-box !important;
                position: relative !important;
                overflow: hidden !important;
            }

            /* 卡片渐变边框效果 */
            .products-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg,
                        rgba(255, 107, 157, 0.15) 0%,
                        rgba(255, 139, 171, 0.1) 25%,
                        rgba(255, 166, 210, 0.08) 50%,
                        rgba(255, 107, 157, 0.12) 75%,
                        rgba(255, 107, 157, 0.15) 100%) !important;
                border-radius: 16px !important;
                padding: 2px !important;
                mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0) !important;
                mask-composite: exclude !important;
                -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0) !important;
                -webkit-mask-composite: xor !important;
                z-index: -1 !important;
            }

            /* 卡片顶部装饰条 */
            .products-card::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg,
                        #ff6b9d 0%,
                        #ff8fab 25%,
                        #ffa6d2 50%,
                        #ff8fab 75%,
                        #ff6b9d 100%) !important;
                border-radius: 16px 16px 0 0 !important;
                z-index: 1 !important;
            }

            /* 卡片标题 - 突出功能重要性 */
            .card-title {
                font-size: 1.5rem !important;
                /* 24px - 页面级标题 */
                font-weight: 700 !important;
                color: #1a1a1a !important;
                margin-bottom: 24px !important;
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 16px !important;
            }

            .card-actions {
                width: 100% !important;
                display: flex !important;
                justify-content: flex-end !important;
                gap: 8px !important;
                margin-top: 16px !important;
            }

            /* 表格容器 - 支持垂直和水平滚动 */
            .table-responsive {
                max-height: calc(100vh - 300px) !important;
                /* 限制最大高度，确保可以垂直滚动 */
                overflow-x: auto !important;
                /* 水平滚动 */
                overflow-y: auto !important;
                /* 垂直滚动 */
                -webkit-overflow-scrolling: touch !important;
                /* iOS 平滑滚动 */
                border-radius: 12px !important;
                box-shadow: 0 4px 12px rgba(255, 107, 157, 0.08) !important;
                background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%) !important;
                position: relative !important;
            }

            /* 表格容器滚动条样式 */
            .table-responsive::-webkit-scrollbar {
                width: 6px !important;
                height: 6px !important;
            }

            .table-responsive::-webkit-scrollbar-track {
                background: rgba(255, 107, 157, 0.05) !important;
                border-radius: 3px !important;
            }

            .table-responsive::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
                border-radius: 3px !important;
                transition: all 0.3s ease !important;
            }

            .table-responsive::-webkit-scrollbar-thumb:hover {
                background: linear-gradient(135deg, #ff5a8a 0%, #ff7ea0 100%) !important;
                box-shadow: 0 2px 6px rgba(255, 107, 157, 0.3) !important;
            }

            .table-responsive::-webkit-scrollbar-corner {
                background: rgba(255, 107, 157, 0.05) !important;
            }

            /* 表格 - 保持完整可读性，通过滑动解决空间问题 */
            .products-table {
                font-size: 1rem !important;
                /* 16px - 保持最佳可读性 */
                min-width: 1200px !important;
                /* 增大宽度确保所有内容水平显示不换行 */
                background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%) !important;
                border-radius: 12px !important;
                overflow: hidden !important;
                box-shadow:
                    0 6px 20px rgba(255, 107, 157, 0.08),
                    0 3px 10px rgba(0, 0, 0, 0.06),
                    0 1px 4px rgba(0, 0, 0, 0.04) !important;
                border: 2px solid rgba(255, 107, 157, 0.1) !important;
                position: relative !important;
            }

            /* 表格装饰边框 */
            .products-table::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(135deg,
                        rgba(255, 107, 157, 0.2) 0%,
                        rgba(255, 139, 171, 0.15) 25%,
                        rgba(255, 166, 210, 0.1) 50%,
                        rgba(255, 139, 171, 0.15) 75%,
                        rgba(255, 107, 157, 0.2) 100%) !important;
                border-radius: 14px !important;
                z-index: -1 !important;
            }

            /* 表格头部 - 突出层次 */
            .products-table th {
                background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
                color: white !important;
                font-weight: 600 !important;
                font-size: 1rem !important;
                /* 16px - 与内容同等重要 */
                padding: 16px 12px !important;
                /* 增加左右内边距，与td保持一致 */
                border: none !important;
                position: sticky !important;
                top: 0 !important;
                z-index: 10 !important;
                text-align: left !important;
                white-space: nowrap !important;
                /* 确保表头文字不换行 */
                min-width: 120px !important;
                /* 设置最小列宽 */
                box-shadow: 0 2px 8px rgba(255, 107, 157, 0.2) !important;
            }

            /* 表格单元格 - 保持舒适的阅读体验 */
            .products-table td {
                padding: 16px 12px !important;
                /* 增加左右内边距，改善列距 */
                font-size: 1rem !important;
                /* 16px - 保持可读性 */
                border-bottom: 1px solid rgba(255, 107, 157, 0.08) !important;
                color: #1a1a1a !important;
                line-height: 1.5 !important;
                white-space: nowrap !important;
                /* 确保文字不换行 */
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                min-width: 120px !important;
                /* 设置最小列宽 */
            }

            /* 表格行交互反馈 */
            .products-table tbody tr:active {
                background-color: #fafafa !important;
            }

            /* 商品图片 - 保持清晰可见 */
            .product-image {
                width: 60px !important;
                /* 足够大以保持清晰 */
                height: 60px !important;
                border-radius: 6px !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                object-fit: cover !important;
            }

            /* 操作按钮 - 符合移动端触摸标准 */
            .action-btn {
                width: 44px !important;
                /* 符合44px最小触摸面积标准 */
                height: 44px !important;
                border-radius: 50% !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.2s ease !important;
                display: inline-flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .action-btn:active {
                transform: scale(0.9) !important;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
            }

            .action-buttons {
                gap: 8px !important;
                justify-content: center !important;
                align-items: center !important;
            }

            /* 主要操作按钮 - 合理美观的设计 */
            .btn-primary {
                font-size: 14px !important;
                /* 合理的字体大小 */
                font-weight: 500 !important;
                padding: 10px 20px !important;
                border-radius: 8px !important;
                min-height: 40px !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                border: none !important;
                transition: all 0.2s ease !important;
            }

            .btn-primary:active {
                transform: scale(0.98) !important;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
            }

            /* 次要操作按钮 */
            .btn-light {
                font-size: 13px !important;
                /* 稍小的字体 */
                font-weight: 400 !important;
                padding: 8px 16px !important;
                border-radius: 6px !important;
                min-height: 36px !important;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                transition: all 0.2s ease !important;
            }

            .btn-light:active {
                transform: scale(0.98) !important;
                background-color: #fafafa !important;
            }

            /* 表单控件 - 保持良好的输入体验 */
            .form-control {
                font-size: 1rem !important;
                /* 16px - 防止iOS缩放 */
                padding: 16px !important;
                border-radius: 8px !important;
                border: 2px solid #e5e5e5 !important;
                background-color: #ffffff !important;
                color: #1a1a1a !important;
                min-height: 48px !important;
                transition: border-color 0.2s ease !important;
            }

            .form-control:focus {
                border-color: #ff6b9d !important;
                box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1) !important;
                outline: none !important;
            }

            /* 模态框 - 等比例缩小，左右留空 */
            .modal-content {
                width: calc(100% - 32px) !important;
                max-width: calc(100% - 32px) !important;
                height: calc(100vh - 80px) !important;
                max-height: calc(100vh - 80px) !important;
                margin: 40px 16px !important;
                border-radius: 12px !important;
                border: none !important;
                background-color: #ffffff !important;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
                overflow: hidden !important;
            }

            .modal-header {
                background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
                color: white !important;
                padding: 20px !important;
                border-bottom: none !important;
                position: sticky !important;
                top: 0 !important;
                z-index: 100 !important;
            }

            .modal-title {
                font-size: 18px !important;
                /* 合理的标题大小 */
                font-weight: 600 !important;
                color: white !important;
            }

            .modal-body {
                padding: 20px !important;
                max-height: calc(100vh - 200px) !important;
                overflow-y: auto !important;
                background-color: #ffffff !important;
            }

            .modal-footer {
                padding: 16px 20px !important;
                border-top: 1px solid #e5e5e5 !important;
                background-color: #fafafa !important;
                position: sticky !important;
                bottom: 0 !important;
                gap: 12px !important;
            }

            /* 导航选项卡 - 移动端简洁样式 */
            .nav-tabs {
                flex-wrap: wrap !important;
                border-bottom: 2px solid #e5e5e5 !important;
                margin-bottom: 24px !important;
            }

            .nav-tabs .nav-link {
                padding: 12px 16px !important;
                /* 减小内边距 */
                font-size: 1rem !important;
                font-weight: 500 !important;
                margin-right: 2px !important;
                /* 减小右边距 */
                margin-bottom: 4px !important;
                border-radius: 8px 8px 0 0 !important;
                min-height: 44px !important;
                /* 减小最小高度 */
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .nav-tabs .nav-link.active {
                background-color: #ff6b9d !important;
                color: white !important;
                border-color: #ff6b9d !important;
            }

            /* 商品类型选择卡片 - 长方形垂直排列样式 */
            .type-card-container {
                display: flex !important;
                flex-direction: column !important;
                gap: 10px !important;
                margin: 0 16px 20px 16px !important;
                /* 左右留出空余 */
                padding: 0 !important;
            }

            .type-card {
                width: 100% !important;
                min-height: 60px !important;
                /* 设置最小高度形成长方形 */
                padding: 16px 20px !important;
                /* 增加内边距 */
                text-align: left !important;
                display: flex !important;
                align-items: center !important;
                gap: 16px !important;
                /* 增加图标与文字间距 */
                border-radius: 10px !important;
                /* 稍大的圆角 */
                background: #ffffff !important;
                border: 1px solid #e9ecef !important;
                transition: all 0.2s ease !important;
                position: relative !important;
                cursor: pointer !important;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;
                /* 添加轻微阴影 */
            }

            /* 移动端专用隐藏类 - 优先级高于上面的display规则 */
            .type-card.mobile-hidden {
                display: none !important;
            }

            .type-card i {
                font-size: 1.4rem !important;
                /* 稍大的图标适配长方形 */
                color: #6c757d !important;
                flex-shrink: 0 !important;
                width: 40px !important;
                /* 增大图标容器 */
                height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                background: #f8f9fa !important;
                border-radius: 8px !important;
                /* 稍大的圆角 */
                transition: all 0.2s ease !important;
            }

            .type-card-content {
                flex: 1 !important;
                display: flex !important;
                flex-direction: column !important;
                gap: 4px !important;
                /* 适当增加间距 */
                min-width: 0 !important;
                /* 确保文字可以正确截断 */
            }

            .type-card h5 {
                font-size: 1rem !important;
                /* 适中的标题大小 */
                margin-bottom: 0 !important;
                color: #212529 !important;
                font-weight: 600 !important;
                /* 稍重的字重 */
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                line-height: 1.2 !important;
            }

            .type-card p {
                font-size: 0.85rem !important;
                /* 适中的描述文字 */
                color: #6c757d !important;
                margin: 0 !important;
                line-height: 1.3 !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
            }

            /* 悬浮状态 */
            .type-card:hover {
                border-color: #ff6b9d !important;
                box-shadow: 0 4px 12px rgba(255, 107, 157, 0.12) !important;
                transform: translateY(-1px) !important;
                /* 轻微上移 */
            }

            .type-card:hover i {
                background: rgba(255, 107, 157, 0.1) !important;
                color: #ff6b9d !important;
                transform: scale(1.05) !important;
                /* 图标轻微放大 */
            }

            /* 选中状态 */
            .type-card.selected {
                background: linear-gradient(135deg, #fff5f9 0%, #ffffff 100%) !important;
                border-color: #ff6b9d !important;
                box-shadow: 0 4px 16px rgba(255, 107, 157, 0.2) !important;
                transform: translateY(-1px) !important;
            }

            .type-card.selected i {
                background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
                color: white !important;
                transform: scale(1.05) !important;
            }

            .type-card.selected h5 {
                color: #ff6b9d !important;
                font-weight: 700 !important;
                /* 选中时字重加重 */
            }

            /* 附带参数卡片 - 垂直布局优化，适应右上角按钮 */
            .parameter-row {
                padding: 20px !important;
                /* 增加内边距 */
                background: #ffffff !important;
                border-radius: 12px !important;
                /* 更大的圆角 */
                margin-bottom: 16px !important;
                /* 增加底部间距 */
                margin-top: 8px !important;
                /* 增加顶部间距为按钮留空间 */
                margin-left: 4px !important;
                /* 左侧留空间 */
                margin-right: 4px !important;
                /* 右侧留空间 */
                border: 1px solid #e9ecef !important;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08) !important;
                /* 增强阴影 */
                position: relative !important;
                transition: all 0.2s ease !important;
                overflow: visible !important;
                /* 确保删除按钮不被裁剪 */
            }

            /* 删除参数按钮 - 移动端右上角定位，粉红色系 */
            .parameter-row .btn.btn-light.remove-param {
                position: absolute !important;
                top: 10px !important;
                /* 进一步向下移动 */
                right: 10px !important;
                /* 进一步向左移动 */
                width: 30px !important;
                /* 适中的点击区域 */
                height: 30px !important;
                /* 确保完美圆形 */
                padding: 0 !important;
                /* 强制覆盖.btn-light的padding */
                margin: 0 !important;
                /* 强制覆盖任何默认margin */
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 50% !important;
                /* 完美圆形 */
                background: #ffffff !important;
                border: 2px solid #ff6b9d !important;
                /* 粉红色边框 */
                color: #ff6b9d !important;
                /* 粉红色图标 */
                transition: all 0.2s ease !important;
                z-index: 20 !important;
                /* 最高层级 */
                box-shadow: 0 3px 6px rgba(255, 107, 157, 0.15) !important;
                /* 粉红色阴影 */
                line-height: 1 !important;
                transform: translate(0, 0) !important;
                min-height: auto !important;
                /* 覆盖.btn-light的min-height */
                font-size: inherit !important;
                /* 覆盖.btn-light的font-size */
            }

            /* 删除按钮图标 - 移动端强制居中，覆盖所有默认样式 */
            .parameter-row .btn.btn-light.remove-param i {
                font-size: 13px !important;
                /* 适配30px按钮的图标大小 */
                transition: all 0.2s ease !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 100% !important;
                height: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                line-height: 1 !important;
                text-align: center !important;
                position: absolute !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                color: inherit !important;
                /* 继承按钮颜色 */
            }

            /* 删除按钮悬浮和点击效果 - 移动端粉红色系 */
            .parameter-row .btn.btn-light.remove-param:hover {
                background: #ff6b9d !important;
                /* 粉红色背景 */
                border-color: #ff6b9d !important;
                color: white !important;
                transform: scale(1.15) !important;
                /* 适中的放大效果 */
                box-shadow: 0 4px 10px rgba(255, 107, 157, 0.35) !important;
                /* 粉红色阴影 */
            }

            .parameter-row .btn.btn-light.remove-param:hover i {
                color: white !important;
                /* 悬浮时图标变白 */
                transform: translate(-50%, -50%) scale(1.05) !important;
                /* 图标轻微放大 */
            }

            .parameter-row .btn.btn-light.remove-param:active {
                transform: scale(1.0) !important;
                box-shadow: 0 2px 5px rgba(255, 107, 157, 0.25) !important;
                /* 粉红色点击阴影 */
            }

            /* 参数行布局 - 垂直排列，适应右上角按钮 */
            .parameter-row .row {
                display: flex !important;
                flex-direction: column !important;
                gap: 12px !important;
                padding-right: 40px !important;
                /* 为右上角按钮留出空间 */
                padding-top: 8px !important;
                /* 适中的顶部空间 */
                margin-top: 0 !important;
                /* 移除额外边距 */
            }

            .parameter-row .form-group {
                margin-bottom: 0 !important;
                width: 100% !important;
            }

            .parameter-row .form-control {
                height: 36px !important;
                /* 减小输入框高度 */
                font-size: 0.875rem !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                border: 1px solid #dee2e6 !important;
                transition: all 0.2s ease !important;
            }

            .parameter-row .form-control:focus {
                border-color: #ff6b9d !important;
                box-shadow: 0 0 0 0.2rem rgba(255, 107, 157, 0.1) !important;
            }

            .parameter-row .form-label {
                font-size: 0.8rem !important;
                font-weight: 500 !important;
                color: #495057 !important;
                margin-bottom: 4px !important;
            }

            /* 参数行悬浮效果 */
            .parameter-row:hover {
                border-color: rgba(255, 107, 157, 0.2) !important;
                box-shadow: 0 4px 12px rgba(255, 107, 157, 0.08) !important;
            }

            /* 价格设置页面 - 输入组件优化 */
            .input-group {
                border-radius: 8px !important;
                overflow: hidden !important;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
                transition: all 0.2s ease !important;
            }

            .input-group:focus-within {
                box-shadow: 0 4px 12px rgba(255, 107, 157, 0.1) !important;
                transform: translateY(-1px) !important;
            }

            .input-group-text {
                background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%) !important;
                color: white !important;
                border: none !important;
                font-weight: 500 !important;
                font-size: 1rem !important;
                padding: 12px 16px !important;
                border-radius: 8px 0 0 8px !important;
                transition: all 0.2s ease !important;
                position: relative !important;
            }

            .input-group-text::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                width: 1px;
                background: rgba(255, 255, 255, 0.2) !important;
            }

            .input-group .form-control {
                border: none !important;
                border-radius: 0 8px 8px 0 !important;
                padding: 12px 16px !important;
                font-size: 1rem !important;
                background: #ffffff !important;
                transition: all 0.2s ease !important;
                box-shadow: none !important;
            }

            .input-group .form-control:focus {
                outline: none !important;
                box-shadow: none !important;
                background: #fafbfc !important;
            }

            /* 输入组件悬浮效果 */
            .input-group:hover .input-group-text {
                background: linear-gradient(135deg, #ff5a8a 0%, #ff7ea0 100%) !important;
            }

            .input-group:hover .form-control {
                background: #fafbfc !important;
            }
        }

        /* 超小屏幕设备层次优化 */
        @media (max-width: 480px) {

            /* 表格 - 保持可读性，通过滑动解决空间 */
            .products-table {
                min-width: 1000px !important;
                /* 保持足够宽度确保不换行 */
                font-size: 0.875rem !important;
                /* 14px - 在超小屏幕上适当缩小 */
            }

            .products-table th,
            .products-table td {
                padding: 12px 10px !important;
                /* 保持合适的列距 */
                font-size: 0.875rem !important;
                /* 14px */
                min-width: 100px !important;
                /* 超小屏幕的最小列宽 */
            }

            /* 按钮和表单 - 保持触摸友好 */
            .btn-primary {
                font-size: 1rem !important;
                padding: 12px 24px !important;
                min-height: 44px !important;
                /* 保持触摸标准 */
            }

            .btn-light {
                font-size: 0.875rem !important;
                padding: 8px 16px !important;
            }

            .form-control {
                font-size: 1rem !important;
                /* 16px - 防止缩放 */
                padding: 12px !important;
            }

            /* 卡片标题适配超小屏幕 */
            .card-title {
                font-size: 1.25rem !important;
                /* 20px */
            }

            /* 模态框适配 */
            .modal-body {
                padding: 16px !important;
            }

            /* 超小屏幕导航选项卡优化 */
            .nav-tabs .nav-link {
                padding: 10px 12px !important;
                /* 进一步减小内边距 */
                font-size: 0.875rem !important;
                margin-right: 1px !important;
                /* 进一步减小间距 */
            }

            /* 超小屏幕卡片边距优化 */
            .products-card {
                padding: 24px 16px !important;
                margin: 0 0 16px 0 !important;
            }

            /* 超小屏幕商品类型卡片 - 保持长方形布局 */
            .type-card-container {
                margin: 0 12px 20px 12px !important;
                /* 减小左右边距 */
            }

            .type-card {
                min-height: 56px !important;
                /* 稍小的最小高度 */
                padding: 14px 16px !important;
                /* 减小内边距 */
                gap: 12px !important;
            }

            .type-card i {
                width: 36px !important;
                /* 稍小的图标容器 */
                height: 36px !important;
                font-size: 1.2rem !important;
            }

            .type-card h5 {
                font-size: 0.9rem !important;
                /* 稍小的标题 */
            }

            .type-card p {
                font-size: 0.8rem !important;
                /* 稍小的描述 */
            }

            /* 移动端分页 */
            .pagination-container {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .pagination-controls {
                flex-direction: column;
                gap: 12px;
                width: 100%;
            }

            .pagination-buttons {
                justify-content: center;
                flex-wrap: wrap;
            }

            .page-numbers {
                flex-wrap: wrap;
                justify-content: center;
            }

            .page-size-select {
                width: 100%;
                max-width: 200px;
                margin: 0 auto;
            }
        }