:root {
        --primary-color: #ff7eb9;
        --primary-light: #ffa6d2;
        --primary-dark: #e55a9b;
        --accent-color: #7eb8ff;
        --background-color: #fff5f9;
        --card-bg: #ffffff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --border-color: #eeeeee;
        --success-color: #4cd964;
        --warning-color: #ffce56;
        --danger-color: #ff6b6b;
        --shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
        --border-radius: 10px;
    }

    #membership-container {
        width: 100%;
        height: 100%;
        padding: 15px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        overflow: auto;
        background-color: var(--background-color);
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }

    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
    }

    .page-title i {
        margin-right: 10px;
        color: var(--primary-color);
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border-radius: 20px;
        border: none;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn i {
        margin-right: 6px;
    }

    .btn-primary {
        background-color: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 126, 185, 0.3);
    }

    .warning-message {
        color: var(--danger-color);
        font-size: 0.9rem;
        margin-bottom: 15px;
        padding: 8px 12px;
        background-color: rgba(255, 107, 107, 0.1);
        border-radius: 6px;
        border-left: 3px solid var(--danger-color);
    }

    /* 会员等级列表区域 */
    .membership-list-container {
        background-color: var(--card-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        overflow: auto;
        max-height: calc(100vh - 180px);
        flex-grow: 1;
    }

    .membership-list {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
    }

    .membership-list thead {
        background-color: #f9f9f9;
    }

    .membership-list th {
        background-color: #f9f9f9;
        padding: 12px 8px;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .membership-list td {
        padding: 12px 8px;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
    }

    .membership-list th:nth-child(1),
    .membership-list td:nth-child(1) {
        width: 50%;
        padding-left: 15px;
    }

    .membership-list th:nth-child(2),
    .membership-list td:nth-child(2) {
        width: 30%;
    }

    .membership-list th:nth-child(3),
    .membership-list td:nth-child(3) {
        width: 20%;
        text-align: center;
    }

    .membership-list tr:last-child td {
        border-bottom: none;
    }

    .membership-list tr:hover {
        background-color: rgba(255, 126, 185, 0.05);
    }

    /* 会员等级操作按钮 */
    .level-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .delete-btn {
        background-color: var(--danger-color);
        color: white;
    }

    .delete-btn:hover {
        background-color: #ff5252;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(255, 107, 107, 0.3);
    }

    /* 空状态 */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: var(--text-secondary);
    }

    .empty-icon {
        font-size: 3rem;
        color: var(--border-color);
        margin-bottom: 15px;
    }

    .empty-text {
        font-size: 1.1rem;
        margin-bottom: 20px;
    }

    /* 模态框样式 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-overlay.show {
        display: flex;
    }

    .modal-container {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 500px;
        padding: 20px;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    }

    .modal-overlay.show .modal-container {
        transform: translateY(0);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }

    .modal-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .modal-body {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: border-color 0.2s;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 126, 185, 0.2);
    }

    .form-error {
        color: var(--danger-color);
        font-size: 0.85rem;
        margin-top: 4px;
        display: none;
    }

    .form-input.error {
        border-color: var(--danger-color);
    }

    .form-input.error+.form-error {
        display: block;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .modal-btn {
        padding: 8px 16px;
        border-radius: 20px;
        border: none;
        cursor: pointer;
        font-weight: 500;
    }

    .modal-btn-cancel {
        background-color: #f0f0f0;
        color: var(--text-secondary);
    }

    .modal-btn-submit {
        background-color: var(--primary-color);
        color: white;
    }

    .modal-btn-submit:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    /* 确认删除模态框 */
    .confirm-text {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 15px;
        color: var(--text-primary);
    }

    .level-name-highlight {
        font-weight: 600;
        color: var(--primary-dark);
    }

    /* 加载中动画 */
    .spinner {
        width: 18px;
        height: 18px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 0.8s linear infinite;
        display: inline-block;
        margin-right: 8px;
        vertical-align: middle;
        display: none;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* 通知样式 */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 2000;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .toast {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 12px 16px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        transform: translateX(120%);
        transition: transform 0.3s ease;
        max-width: 300px;
    }

    .toast.show {
        transform: translateX(0);
    }

    .toast-icon {
        margin-right: 12px;
        font-size: 1.2rem;
    }

    .toast-success .toast-icon {
        color: var(--success-color);
    }

    .toast-error .toast-icon {
        color: var(--danger-color);
    }

    .toast-message {
        flex-grow: 1;
        font-size: 0.9rem;
        color: var(--text-primary);
    }

    /* 缓动动画 */
    .fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 响应式适配 */
    @media screen and (max-width: 768px) {

        .membership-list th:nth-child(1),
        .membership-list td:nth-child(1) {
            width: 35%;
        }

        .membership-list th:nth-child(2),
        .membership-list td:nth-child(2) {
            width: 35%;
        }

        .membership-list th:nth-child(3),
        .membership-list td:nth-child(3) {
            width: 30%;
        }
    }