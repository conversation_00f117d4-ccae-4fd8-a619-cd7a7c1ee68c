/* 变量化改造 */
:root {
    --sidebar-width: 200px;
    --mobile-breakpoint: 768px;
    --primary-color: #ff6b9d;
    --primary-light: #ffecf2;
    --transition-curve: cubic-bezier(0.4, 0, 0.2, 1);
    --content-max-width: none;
    --content-padding: 6px;
    --top-bar-height: 60px;
    
    /* 基础颜色变量 */
    --bg-color: #fff5f8;
    --text-color: #333;
    --border-color: #ddd;
    --secondary-color: #f0f0f0;
    --sidebar-bg: white;
    --card-bg: white;
    --top-bar-bg: white;
    --dropdown-bg: white;
    --dropdown-hover: #ffecf2;
    --dropdown-text: #666;
    --shadow-color: rgba(0, 0, 0, 0.05);
}

/* 基础样式 */
body {
    margin: 0;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
}

/* 顶部白色区块 */
.top-bar {
    background-color: var(--top-bar-bg);
    padding: 0 20px;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 1px 6px var(--shadow-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: var(--top-bar-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 顶部左侧区域样式 */
.left-section {
    display: flex;
    align-items: center;
}

/* 用户信息和通知样式 */
.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification {
    position: relative;
    cursor: pointer;
}

.notification i {
    font-size: 1.4rem;
    color: var(--text-color);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
}

/* Logo样式 */
.logo {
    color: var(--primary-color);
    font-size: 20px;
    font-weight: bold;
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.logo i {
    margin-right: 10px;
    font-size: 22px;
}

/* 三条杠按钮 */
.menu-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 3px;
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-left: 6px;
}

.menu-toggle span {
    width: 22px;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 1px;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

/* 侧边导航栏 */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--text-color);
    padding: 20px 0;
    box-shadow: 1px 0 6px var(--shadow-color);
    position: fixed;
    top: calc(var(--top-bar-height));
    left: 0;
    height: calc(100vh - var(--top-bar-height));
    transition: transform 0.3s var(--transition-curve);
    z-index: 999;
    overflow-y: auto;
}

.sidebar.active {
    transform: translateX(-100%);
}

.sidebar .nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar .nav-item {
    margin: 8px 0;
}

.sidebar .nav-item a {
    color: var(--text-color);
    text-decoration: none;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 14px;
}

.sidebar .nav-item a i {
    margin-right: 10px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.sidebar .nav-item a:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.sidebar .nav-item.active a {
    background-color: var(--primary-color);
    color: #fff;
}

/* 下拉菜单 */
.sidebar .has-dropdown {
    position: relative;
}

.sidebar .has-dropdown>a::after {
    content: "▾";
    float: right;
    transition: transform 0.3s ease;
    margin-left: auto;
}

.sidebar .has-dropdown.active>a::after {
    transform: rotate(180deg);
}

.sidebar .dropdown-menu {
    list-style: none;
    padding: 0;
    margin: 0 0 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.sidebar .has-dropdown.active .dropdown-menu {
    max-height: 300px;
    /* 根据内容调整 */
}

.sidebar .dropdown-menu .nav-item a {
    padding: 8px 15px;
    background-color: var(--dropdown-bg);
    color: var(--dropdown-text);
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 13px;
    border-left: 1px solid var(--border-color);
}

.sidebar .dropdown-menu .nav-item a:hover {
    background-color: var(--dropdown-hover);
    color: var(--primary-color);
}

.sidebar .dropdown-menu .nav-item.active a {
    background-color: var(--primary-color);
    color: #fff;
}

/* 内容区域 */
.content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    padding: 0;
    overflow: hidden;
    position: relative;
    z-index: 50;
    transition: margin-left 0.3s var(--transition-curve), width 0.3s var(--transition-curve);
}

/* 当侧边栏隐藏时的状态 */
.sidebar.active+.content {
    margin-left: 0;
    width: 100%;
    padding: 0;
}

/* 确保内容可见性和宽度限制 */
.content>* {
    max-width: none;
    width: 100%;
    box-sizing: border-box;
}

/* 页面内容样式 */
#page-content {
    width: 100%;
    height: 100%;
    padding: var(--content-padding);
    box-sizing: border-box;
    opacity: 1;
    transition: opacity 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
    background-color: #fff5f8;
}

/* 容器修正 */
.container,
.dashboard-container,
.main-content,
.page-container {
    width: 100%;
    min-height: 100%;
    margin: 0;
    padding: 8px;
    background-color: #fff5f8;
    border-radius: 0;
    box-shadow: none;
    box-sizing: border-box;
}

/* 确保里面的内容项也填充满 */
.dashboard-item,
.card,
.panel,
.content-box {
    width: 100%;
    margin-bottom: 10px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
}

/* 移动设备样式 */
@media (max-width: 768px) {

    .container,
    .dashboard-container,
    .main-content,
    .page-container {
        padding: 6px;
    }

    .top-bar {
        height: var(--top-bar-height);
        padding: 6px 12px;
    }

    .sidebar {
        top: calc(var(--top-bar-height) + 12px);
        height: calc(100vh - var(--top-bar-height) - 12px);
        width: min(220px, 75vw);
        padding: 12px 10px;
    }

    .content {
        margin-top: var(--top-bar-height);
        padding: 0;
    }

    .menu-toggle {
        display: flex;
        /* 显示三条杠按钮 */
    }
}

/* 缓动动画 */
.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (min-width: 769px) {

    /* 电脑端：显示三条杠按钮 */
    .menu-toggle {
        display: flex !important;
        margin-left: 8px;
    }

    /* 侧边栏动画修正 */
    .sidebar {
        transform: translateX(0);
        transition: transform 0.3s ease;
    }

    /* 统一使用.active类控制隐藏 */
    .sidebar.active {
        transform: translateX(-100%);
    }

    /* 内容区域基础样式 */
    .content {
        padding: 0;
        transition: margin-left 0.3s ease, width 0.3s ease;
    }

    /* 当侧边栏隐藏时的状态 */
    .sidebar.active+.content {
        margin-left: 0;
        width: 100%;
        padding: 0;
    }

    /* 确保内容可见性 */
    .content>* {
        max-width: none;
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }
}

/* 统一动画设置 */
.sidebar,
.content,
.sidebar-mask {
    transition: all 0.3s var(--transition-curve);
    will-change: transform, opacity;
}

/* 优化移动端遮罩层 */
.sidebar-mask {
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
}

/* 添加错误样式 */
.error-container {
    padding: 2rem;
    text-align: center;
    animation: shake 0.5s;
}

@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-10px);
    }

    75% {
        transform: translateX(10px);
    }
}

.loading-state {
    text-align: center;
    padding: 50px;
}

.loader {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6b9d;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    margin: 0 auto 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}