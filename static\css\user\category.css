:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemF<PERSON>, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }
        
        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .logo:hover {
            transform: scale(1.05);
        }
        
        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
        }
        
        .nav-item {
            margin: 0 15px;
            position: relative;
        }
        
        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        .nav-link.active {
            font-weight: 700;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .nav-icons {
            display: flex;
            align-items: center;
        }
        
        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }
        
        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }
        
        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 70px;
            padding: 0;
            height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background-color: var(--bg-light);
        }
        
        /* 页面标题 */
        .page-title {
            font-size: 1.5rem;
            padding: 20px 24px;
            font-weight: 700;
            color: var(--text-color);
            background-color: var(--bg-color);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            margin: 0;
            position: relative;
            z-index: 10;
            box-shadow: 0 2px 10px rgba(0,0,0,0.03);
            display: flex;
            align-items: center;
        }
        
        /* 页面标题 */
        .page-title::before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 24px;
            background: var(--bg-gradient);
            border-radius: 3px;
            margin-right: 12px;
            transform: translateY(1px);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        /* 分类页面样式 */
        .mobile-category-container {
            display: flex;
            flex: 1;
            height: calc(100vh - 120px);
            overflow: hidden;
            box-shadow: none;
            border-radius: 0;
            margin: 0;
            position: relative;
            background-color: var(--bg-light);
        }
        
        /* 左侧一级分类列表容器 */
        .primary-category-wrapper {
            position: relative;
            background-color: white; /* 添加纯白色背景 */
            box-shadow: 2px 0 15px rgba(64, 169, 255, 0.1);
            z-index: 5;
        }
        
        /* 一级分类下拉按钮，默认在非移动端隐藏 */
        .primary-category-dropdown-btn {
            display: none;
        }
        
        /* 左侧一级分类列表 */
        .primary-category-list {
            width: 175px;
            background: var(--bg-color);
            height: 100%;
            border-right: 1px solid rgba(64, 169, 255, 0.1);
            box-shadow: 2px 0 10px rgba(64, 169, 255, 0.05);
            position: relative;
            z-index: 5;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .primary-category-list::-webkit-scrollbar {
            width: 4px;
        }

        .primary-category-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .primary-category-list::-webkit-scrollbar-thumb {
            background: rgba(64, 169, 255, 0.3);
            border-radius: 4px;
        }

        .primary-category-list::-webkit-scrollbar-thumb:hover {
            background: rgba(64, 169, 255, 0.5);
        }
        
        .primary-category-item {
            padding: 16px 12px;
            text-align: center;
            font-size: 14px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.05);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            color: var(--text-light);
            font-weight: 500;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .primary-category-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(to right, rgba(64, 169, 255, 0.1), transparent);
            transition: width 0.3s ease;
            z-index: -1;
        }

        .primary-category-item:hover {
            color: var(--primary-dark);
            background-color: rgba(145, 213, 255, 0.7);
        }

        .primary-category-item:hover::after {
            width: 100%;
        }

        .primary-category-item.active {
            background: linear-gradient(to right, rgba(145, 213, 255, 0.8), rgba(255, 255, 255, 0.9));
            color: var(--primary-dark);
            font-weight: 600;
            padding-left: 15px;
            transform: translateX(0);
            box-shadow: 0 3px 10px rgba(24, 144, 255, 0.12);
        }

        .primary-category-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
            border-radius: 0 3px 3px 0;
            box-shadow: 2px 0 8px rgba(24, 144, 255, 0.25);
        }
        

        
        /* 右侧二级分类区域 */
        .secondary-category-container {
            flex: 1;
            padding: 24px 20px;
            overflow-y: auto;
            height: 100%;
            background: var(--bg-light);
            position: relative;
        }
        
        .secondary-category-container::-webkit-scrollbar {
            width: 5px;
        }

        .secondary-category-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .secondary-category-container::-webkit-scrollbar-thumb {
            background: rgba(64, 169, 255, 0.3);
            border-radius: 5px;
        }

        .secondary-category-container::-webkit-scrollbar-thumb:hover {
            background: rgba(64, 169, 255, 0.5);
        }
        
        .secondary-category-title {
            font-size: 16px;
            color: var(--text-color);
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.1);
            font-weight: 600;
            position: relative;
            padding-left: 14px;
            display: flex;
            align-items: center;
        }

        .secondary-category-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 18px;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.25);
        }
        
        .secondary-category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 16px;
        }
        
        .secondary-category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 10px;
            cursor: pointer;
            transition: var(--transition);
            border-radius: var(--border-radius);
            background: var(--bg-color);
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.08);
            animation: fadeInUp 0.5s ease forwards;
            opacity: 0;
            transform: translateY(10px);
            border: 1px solid rgba(64, 169, 255, 0.05);
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .secondary-category-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(64, 169, 255, 0.15), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .secondary-category-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.15);
            background: var(--bg-color);
        }

        .secondary-category-item:hover::before {
            opacity: 1;
        }
        
        .secondary-category-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 12px;
            object-fit: cover;
            background: white;
            padding: 6px;
            border: 1px solid rgba(64, 169, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 12px rgba(64, 169, 255, 0.1);
        }

        .secondary-category-item:hover .secondary-category-icon {
            transform: scale(1.08);
            border-color: var(--primary-light);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.3);
        }
        
        .secondary-category-name {
            font-size: 13px;
            color: var(--text-light);
            text-align: center;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            transition: color 0.3s ease;
            margin-top: 4px;
        }
        
        .secondary-category-item:hover .secondary-category-name {
            color: var(--primary-dark);
            font-weight: 500;
        }
        
        .secondary-category-section {
            margin-bottom: 36px;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .secondary-category-section:last-child {
            margin-bottom: 10px;
        }
        
        /* 页脚 */
        footer {
            background-color: var(--bg-color);
            padding: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 -4px 12px rgba(64, 169, 255, 0.08);
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
            opacity: 0.8;
        }
        
        .footer-content {
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.6rem;
            color: var(--primary-dark);
            margin-bottom: 16px;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .footer-content .logo:hover {
            transform: scale(1.05);
        }
        
        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        
        .copyright {
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--text-light);
            letter-spacing: 0.5px;
        }
        
        /* 响应式设计 */
        @media (min-width: 1400px) {
            .secondary-category-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                gap: 20px;
            }
            
            .secondary-category-container {
                padding: 30px 40px;
            }
        }
        
        @media (min-width: 1200px) and (max-width: 1399px) {
            .secondary-category-grid {
                grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
                gap: 18px;
            }
        }
        
        @media (min-width: 992px) and (max-width: 1199px) {
            .secondary-category-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }
        }
        
        @media (min-width: 768px) and (max-width: 991px) {
            .secondary-category-grid {
                grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
                gap: 15px;
            }
            
            .primary-category-list {
                width: 100px;
            }
        }
        
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: var(--transition);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                padding: 20px 0;
                border-radius: 0 0 var(--border-radius) var(--border-radius);
            }
            
            .nav-menu.active {
                left: 0;
                animation: slideInMenu 0.3s ease forwards;
            }
            
            @keyframes slideInMenu {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .nav-item {
                margin: 15px 0;
            }
            
            .nav-link {
                color: var(--text-color);
                display: inline-block;
                padding: 8px 20px;
                border-radius: 20px;
                transition: var(--transition);
            }
            
            .nav-link:hover, .nav-link.active {
                background: var(--bg-gradient);
                color: white;
                box-shadow: 0 4px 12px rgba(255, 79, 123, 0.15);
            }
            
            .nav-link:hover::after, .nav-link.active::after {
                width: 0;
            }
            
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .secondary-category-grid {
                grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
                gap: 12px;
            }
            
            .primary-category-list {
                width: 100px;
            }
            
            .page-title {
                font-size: 1.3rem;
                padding: 16px 20px;
            }
            
            .page-title::before {
                height: 20px;
            }
        }
        
        @media (max-width: 576px) {
            .main-content {
                margin-top: 80px;
                height: calc(100vh - 80px);
            }
            

            
            .mobile-category-container {
                flex-direction: column;
                height: auto;
            }

            /* 响应式网格列数设置 */
            .primary-category-inline-grid {
                grid-template-columns: repeat(2, 1fr); /* 小屏幕2列 */
            }

            .primary-category-wrapper {
                position: relative;
                margin-top: 5px;
                border-bottom: 1px solid rgba(64, 169, 255, 0.08);
                padding-right: 40px; /* 为固定按钮留出空间 */
                overflow: hidden; /* 确保子元素不会溢出 */
                background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
                transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                height: auto; /* 默认高度 */
                transform-origin: top center;
                border-radius: 0 0 8px 8px;
            }

            /* 展开状态的样式 */
            .primary-category-wrapper.expanded {
                height: 300px; /* 展开时的固定高度 */
                border-bottom: 1px solid rgba(64, 169, 255, 0.25);
                box-shadow:
                    0 8px 32px rgba(64, 169, 255, 0.15),
                    0 4px 16px rgba(64, 169, 255, 0.1),
                    0 2px 8px rgba(64, 169, 255, 0.05);
                transform: scaleY(1.02) scaleX(1.01);
                background: linear-gradient(135deg,
                    rgba(145, 213, 255, 0.05) 0%,
                    rgba(255, 255, 255, 0.98) 30%,
                    rgba(255, 255, 255, 1) 70%,
                    rgba(145, 213, 255, 0.03) 100%);
                border-radius: 0 0 12px 12px;
            }

            /* 添加展开动画的关键帧 */
            @keyframes expandContainer {
                0% {
                    transform: scaleY(0.95) scaleX(0.98);
                    box-shadow: 0 2px 8px rgba(64, 169, 255, 0.05);
                }
                50% {
                    transform: scaleY(1.05) scaleX(1.02);
                    box-shadow: 0 12px 40px rgba(64, 169, 255, 0.2);
                }
                100% {
                    transform: scaleY(1.02) scaleX(1.01);
                    box-shadow:
                        0 8px 32px rgba(64, 169, 255, 0.15),
                        0 4px 16px rgba(64, 169, 255, 0.1),
                        0 2px 8px rgba(64, 169, 255, 0.05);
                }
            }

            /* 收起动画的关键帧 */
            @keyframes collapseContainer {
                0% {
                    transform: scaleY(1.02) scaleX(1.01);
                    box-shadow:
                        0 8px 32px rgba(64, 169, 255, 0.15),
                        0 4px 16px rgba(64, 169, 255, 0.1);
                }
                50% {
                    transform: scaleY(0.98) scaleX(0.99);
                    box-shadow: 0 4px 12px rgba(64, 169, 255, 0.08);
                }
                100% {
                    transform: scaleY(1) scaleX(1);
                    box-shadow: 0 2px 8px rgba(64, 169, 255, 0.05);
                }
            }

            /* 内嵌网格容器样式 */
            .primary-category-inline-grid {
                display: none;
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); /* 响应式列数 */
                grid-auto-rows: 60px; /* 设置网格行的固定高度 */
                gap: 10px;
                padding: 15px;
                max-height: 250px;
                overflow-y: auto;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background:
                    radial-gradient(circle at 20% 20%, rgba(145, 213, 255, 0.08) 0%, transparent 50%),
                    radial-gradient(circle at 80% 80%, rgba(64, 169, 255, 0.06) 0%, transparent 50%),
                    linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(250, 253, 255, 1) 100%);
                z-index: 5;
                align-content: start; /* 网格内容从顶部开始排列 */
                animation: backgroundShimmer 4s ease-in-out infinite alternate;
            }

            /* 背景闪烁动画 */
            @keyframes backgroundShimmer {
                0% {
                    background:
                        radial-gradient(circle at 20% 20%, rgba(145, 213, 255, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, rgba(64, 169, 255, 0.06) 0%, transparent 50%),
                        linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(250, 253, 255, 1) 100%);
                }
                50% {
                    background:
                        radial-gradient(circle at 30% 70%, rgba(145, 213, 255, 0.1) 0%, transparent 60%),
                        radial-gradient(circle at 70% 30%, rgba(64, 169, 255, 0.08) 0%, transparent 60%),
                        linear-gradient(135deg, rgba(252, 254, 255, 1) 0%, rgba(255, 255, 255, 0.99) 100%);
                }
                100% {
                    background:
                        radial-gradient(circle at 80% 20%, rgba(145, 213, 255, 0.06) 0%, transparent 50%),
                        radial-gradient(circle at 20% 80%, rgba(64, 169, 255, 0.04) 0%, transparent 50%),
                        linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 252, 255, 0.98) 100%);
                }
            }

            .primary-category-wrapper.expanded .primary-category-inline-grid {
                display: grid;
            }

            .primary-category-wrapper.expanded .primary-category-list {
                display: none;
            }

            .primary-category-inline-grid-item {
                display: flex;
                flex-direction: row; /* 改为水平布局 */
                align-items: center; /* 垂直居中对齐 */
                justify-content: flex-start; /* 从左开始排列 */
                padding: 8px 10px;
                background: var(--bg-color);
                border-radius: 8px;
                border: 1px solid rgba(64, 169, 255, 0.08);
                cursor: pointer;
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                font-size: 13px;
                color: var(--text-light);
                animation: elasticSlideIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
                opacity: 0;
                transform: translateY(30px) translateX(-20px) scale(0.8);
                height: 60px; /* 设置固定高度 */
                min-height: 60px; /* 确保最小高度 */
                max-height: 60px; /* 限制最大高度 */
                overflow: hidden; /* 防止内容溢出 */
                line-height: 1.2; /* 优化行高 */
                gap: 8px; /* 图片和文本之间的间距 */
                box-shadow: 0 2px 8px rgba(64, 169, 255, 0.05);
            }

            /* 弹性滑入动画 */
            @keyframes elasticSlideIn {
                0% {
                    opacity: 0;
                    transform: translateY(30px) translateX(-20px) scale(0.8) rotateX(15deg);
                }
                60% {
                    opacity: 0.8;
                    transform: translateY(-5px) translateX(2px) scale(1.05) rotateX(-2deg);
                }
                80% {
                    opacity: 0.95;
                    transform: translateY(2px) translateX(-1px) scale(0.98) rotateX(1deg);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) translateX(0) scale(1) rotateX(0deg);
                }
            }

            /* 波浪式进入动画（备用） */
            @keyframes waveSlideIn {
                0% {
                    opacity: 0;
                    transform: translateY(40px) scale(0.7);
                    filter: blur(2px);
                }
                50% {
                    opacity: 0.7;
                    transform: translateY(-8px) scale(1.1);
                    filter: blur(0px);
                }
                70% {
                    transform: translateY(3px) scale(0.95);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                    filter: blur(0px);
                }
            }

            .primary-category-inline-grid-item:hover {
                background: linear-gradient(135deg, rgba(145, 213, 255, 0.6), rgba(255, 255, 255, 0.95));
                transform: translateY(-4px) scale(1.02);
                box-shadow:
                    0 8px 20px rgba(64, 169, 255, 0.15),
                    0 4px 12px rgba(64, 169, 255, 0.1),
                    0 0 0 1px rgba(64, 169, 255, 0.1);
                color: var(--primary-dark);
                height: 60px; /* 保持固定高度 */
                border-color: rgba(64, 169, 255, 0.2);
            }

            .primary-category-inline-grid-item.active {
                background: linear-gradient(135deg,
                    rgba(64, 169, 255, 0.95) 0%,
                    rgba(145, 213, 255, 0.9) 30%,
                    rgba(255, 255, 255, 0.95) 70%,
                    rgba(145, 213, 255, 0.85) 100%);
                color: var(--primary-dark);
                font-weight: 700;
                box-shadow:
                    0 8px 20px rgba(64, 169, 255, 0.25),
                    0 4px 12px rgba(64, 169, 255, 0.2),
                    0 0 0 3px rgba(64, 169, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                height: 60px; /* 保持固定高度 */
                transform: translateY(-3px) scale(1.02);
                border-color: var(--primary-color);
                animation: activeGlow 2s ease-in-out infinite alternate;
                position: relative;
                z-index: 2;
            }

            /* 为激活状态添加更明显的标识 */
            .primary-category-inline-grid-item.active::before {
                content: '✓';
                position: absolute;
                top: -2px;
                right: -2px;
                width: 18px;
                height: 18px;
                background: var(--primary-color);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
                box-shadow: 0 2px 6px rgba(64, 169, 255, 0.3);
                animation: checkmarkPulse 1.5s ease-in-out infinite;
            }

            /* 选中标记的脉冲动画 */
            @keyframes checkmarkPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 0.8;
                }
            }

            /* 激活状态的光晕动画 */
            @keyframes activeGlow {
                0% {
                    box-shadow:
                        0 6px 16px rgba(64, 169, 255, 0.2),
                        0 3px 8px rgba(64, 169, 255, 0.15),
                        0 0 0 2px rgba(64, 169, 255, 0.15);
                }
                100% {
                    box-shadow:
                        0 8px 20px rgba(64, 169, 255, 0.25),
                        0 4px 12px rgba(64, 169, 255, 0.2),
                        0 0 0 2px rgba(64, 169, 255, 0.2);
                }
            }

            /* 分类图片容器样式 */
            .primary-category-inline-image {
                width: 40px;
                height: 40px;
                border-radius: 12px; /* 增大圆角 */
                overflow: hidden;
                flex-shrink: 0; /* 防止图片容器被压缩 */
                background: rgba(64, 169, 255, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgba(64, 169, 255, 0.15);
                transition: var(--transition);
            }

            .primary-category-inline-image img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            /* 图片加载失败时的占位符 */
            .primary-category-inline-image.no-image {
                background: linear-gradient(135deg, rgba(64, 169, 255, 0.15), rgba(145, 213, 255, 0.1));
                color: rgba(64, 169, 255, 0.6);
                font-size: 16px;
            }

            /* 分类文本容器样式 */
            .primary-category-inline-text {
                flex: 1; /* 占据剩余空间 */
                text-align: left;
                font-weight: 500;
                word-break: break-all;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2; /* 最多显示2行 */
                line-clamp: 2; /* 标准属性 */
                -webkit-box-orient: vertical;
                line-height: 1.3;
            }

            /* 悬停状态下的图片效果 */
            .primary-category-inline-grid-item:hover .primary-category-inline-image {
                border-color: rgba(64, 169, 255, 0.3);
                box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
            }

            .primary-category-inline-grid-item:hover .primary-category-inline-image img {
                transform: scale(1.05);
            }

            /* 激活状态下的图片效果 */
            .primary-category-inline-grid-item.active .primary-category-inline-image {
                border-color: var(--primary-color);
                box-shadow: 0 3px 10px rgba(64, 169, 255, 0.3);
            }

            .primary-category-inline-grid-item.active .primary-category-inline-text {
                font-weight: 600;
            }
            
            /* 添加右侧渐变遮罩，防止内容与按钮重叠 */
            .primary-category-wrapper::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 40px;
                height: 100%;
                background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 60%);
                pointer-events: none;
                z-index: 10;
            }
            
            .primary-category-list {
                width: 100%;
                height: auto;
                flex-direction: row;
                overflow-x: auto;
                overflow-y: hidden;
                border-right: none;
                border-bottom: none;
                padding: 10px 0 10px 10px;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }
            
            .primary-category-list::-webkit-scrollbar {
                display: none;
            }
            
            /* 固定下拉按钮在右侧 */
            .primary-category-dropdown-btn {
                display: flex;
                position: absolute;
                top: 50%;
                right: 5px;
                transform: translateY(-50%);
                z-index: 15;
                background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
                width: 30px;
                height: 30px;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 50%;
                box-shadow:
                    0 4px 12px rgba(64, 169, 255, 0.15),
                    0 2px 6px rgba(64, 169, 255, 0.1);
                border: 1px solid rgba(64, 169, 255, 0.1);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                overflow: hidden;
            }

            .primary-category-dropdown-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: radial-gradient(circle at center, rgba(64, 169, 255, 0.1) 0%, transparent 70%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .primary-category-dropdown-btn:hover {
                transform: translateY(-50%) scale(1.1);
                box-shadow:
                    0 6px 16px rgba(64, 169, 255, 0.2),
                    0 3px 8px rgba(64, 169, 255, 0.15);
                background: linear-gradient(135deg, rgba(145, 213, 255, 0.1) 0%, #ffffff 100%);
                border-color: rgba(64, 169, 255, 0.2);
            }

            .primary-category-dropdown-btn:hover::before {
                opacity: 1;
            }

            .primary-category-dropdown-btn .fa-solid {
                font-size: 20px;
                color: var(--primary-dark);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                position: relative;
                z-index: 1;
            }

            /* 按钮展开状态 */
            .primary-category-dropdown-btn.expanded {
                animation: buttonPulse 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                background: linear-gradient(135deg, rgba(64, 169, 255, 0.15) 0%, #ffffff 100%);
                border-color: rgba(64, 169, 255, 0.25);
            }

            .primary-category-dropdown-btn.expanded .fa-solid {
                transform: rotate(180deg) scale(1.1);
                color: var(--primary-color);
                filter: drop-shadow(0 2px 4px rgba(64, 169, 255, 0.3));
            }

            /* 按钮脉冲动画 */
            @keyframes buttonPulse {
                0% {
                    transform: translateY(-50%) scale(1);
                    box-shadow:
                        0 4px 12px rgba(64, 169, 255, 0.15),
                        0 2px 6px rgba(64, 169, 255, 0.1);
                }
                50% {
                    transform: translateY(-50%) scale(1.2);
                    box-shadow:
                        0 8px 20px rgba(64, 169, 255, 0.25),
                        0 4px 12px rgba(64, 169, 255, 0.2);
                }
                100% {
                    transform: translateY(-50%) scale(1.05);
                    box-shadow:
                        0 6px 16px rgba(64, 169, 255, 0.2),
                        0 3px 8px rgba(64, 169, 255, 0.15);
                }
            }
            

            
            .primary-category-item {
                padding: 10px 15px;
                margin-right: 8px;
                border-bottom: none;
                border-radius: 20px;
                white-space: nowrap;
                font-size: 13px;
                flex-shrink: 0;
            }
            
            .primary-category-item.active {
                padding: 10px 15px;
                transform: translateY(-2px);
                background: var(--primary-color);
                color: white;
                box-shadow: 0 4px 10px rgba(64, 169, 255, 0.2);
            }
            
            .primary-category-item.active::before {
                display: none;
            }
            
            .secondary-category-container {
                height: calc(100vh - 180px);
                padding: 20px 15px;
            }
            
            .secondary-category-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
            }
            
            .secondary-category-icon {
                width: 50px;
                height: 50px;
            }
            
            .secondary-category-item {
                padding: 12px 8px;
            }
            
            .loading-state, .empty-state, .error-state {
                height: 200px;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
            }
        }
        
        /* 中等屏幕响应式设置 */
        @media (min-width: 577px) and (max-width: 768px) {
            .primary-category-inline-grid {
                grid-template-columns: repeat(3, 1fr); /* 中等屏幕3列 */
            }
        }

        /* 大屏幕响应式设置 */
        @media (min-width: 769px) {
            .primary-category-inline-grid {
                grid-template-columns: repeat(4, 1fr); /* 大屏幕4列 */
            }
        }

        @media (max-width: 375px) {
            .secondary-category-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            /* 超小屏幕网格设置 */
            .primary-category-inline-grid {
                grid-template-columns: repeat(2, 1fr); /* 超小屏幕保持2列 */
                gap: 8px; /* 减小间距 */
                padding: 12px; /* 减小内边距 */
            }

            /* 超小屏幕网格项目优化 */
            .primary-category-inline-grid-item {
                padding: 6px 8px; /* 减小内边距 */
                font-size: 12px; /* 减小字体 */
                height: 55px; /* 稍微减小高度 */
                min-height: 55px;
                max-height: 55px;
            }

            /* 超小屏幕图片优化 */
            .primary-category-inline-image {
                width: 35px; /* 减小图片尺寸 */
                height: 35px;
            }
            
            .navbar {
                padding: 12px 16px;
            }
            
            .logo {
                font-size: 1.3rem;
            }
            
            .logo .fa-solid {
                font-size: 1.5rem;
            }
            
            .page-title {
                font-size: 1.2rem;
                padding: 14px 16px;
            }
        }
        
        /* 加载状态和空状态样式 */
        .loading-state, .empty-state, .error-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 240px;
            text-align: center;
            color: var(--text-light);
            padding: 20px;
            width: 100%;
            animation: fadeIn 0.5s ease;
        }
        
        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(64, 169, 255, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
            margin-bottom: 16px;
            box-shadow: 0 4px 10px rgba(64, 169, 255, 0.1);
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-text, .empty-state-text, .error-text {
            font-size: 15px;
            color: var(--text-light);
            margin-top: 12px;
            font-weight: 500;
        }

        .empty-state-subtext {
            font-size: 13px;
            color: var(--text-lighter);
            margin-top: 6px;
            opacity: 0.8;
        }
        
        .empty-state-icon, .error-icon {
            font-size: 52px;
            margin-bottom: 16px;
            color: rgba(64, 169, 255, 0.6);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.7;
            }
        }

        .error-icon {
            color: rgba(24, 144, 255, 0.6);
        }
        
        .retry-button {
            margin-top: 16px;
            padding: 10px 24px;
            background: var(--bg-gradient);
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        .retry-button:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.25);
        }
        

        
        /* 确保Font Awesome图标正确显示 */
        .fa-solid {
            font-size: 18px;
            display: inline-block;
            vertical-align: middle;
        }
        
        .nav-icon .fa-solid {
            font-size: 18px;
        }
        
        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
        }
        
        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }