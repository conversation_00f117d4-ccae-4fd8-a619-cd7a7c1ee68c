:root {
            --primary-color: #1890ff;
            --primary-light: #e6f7ff;
            --primary-dark: #0050b3;
            --error-color: #ff4d4f;
            --text-color: #262626;
            --text-light: #8c8c8c;
            --text-secondary: #595959;
            --bg-color: #ffffff;
            --bg-light: #fafafa;
            --border-color: #e8e8e8;
            --border-light: #f0f0f0;
            --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.08);
            --shadow-strong: 0 8px 24px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
            --border-radius: 8px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        

        
        /* 主内容区 */
        .main-content {
            padding: 40px 24px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 简洁失败容器 */
        .failure-container {
            background: var(--bg-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            padding: 48px 40px;
            text-align: center;
            border: 1px solid var(--border-color);
            width: 100%;
            transition: var(--transition);
        }

        .failure-container:hover {
            box-shadow: var(--shadow-strong);
        }
        

        
        .failure-icon {
            font-size: 4rem;
            color: var(--error-color);
            margin-bottom: 24px;
        }
        
        .failure-title {
            font-size: 1.8rem;
            color: var(--text-color);
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        .failure-message {
            font-size: 1rem;
            color: var(--text-light);
            margin-bottom: 32px;
            line-height: 1.6;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }


        
        .failure-actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 500;
            font-size: 0.95rem;
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid transparent;
            min-width: 160px;
            justify-content: center;
            text-decoration: none;
        }

        .action-btn:active {
            transform: translateY(1px);
        }
        
        .action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .action-btn.secondary {
            background: var(--bg-color);
            color: var(--text-secondary);
            border-color: var(--border-color);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn.primary:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow-medium);
        }

        .action-btn.secondary:hover {
            background: var(--primary-light);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .action-btn .fa-solid {
            font-size: 1rem;
        }


        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 24px 16px;
            }

            .failure-container {
                padding: 32px 24px;
            }

            .failure-icon {
                font-size: 3rem;
            }

            .failure-title {
                font-size: 1.5rem;
            }

            .failure-message {
                font-size: 0.9rem;
            }

            .action-btn {
                min-width: 140px;
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }