:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --success-color: #4CAF50;
            --warning-color: #FFC107;
            --error-color: #F44336;
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: #f8f8f8;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }
        
        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .logo:hover {
            transform: scale(1.05);
        }
        
        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
        }
        
        .nav-item {
            margin: 0 15px;
            position: relative;
        }
        
        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        .nav-link.active {
            font-weight: 700;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .nav-icons {
            display: flex;
            align-items: center;
        }
        
        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }
        
        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }
        
        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 30px 25px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* 页面标题 */
        .page-title {
            font-size: 1.8rem;
            margin-bottom: 30px;
            font-weight: 700;
            color: #444;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--primary-light);
            position: relative;
            display: inline-block;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 80px;
            height: 2px;
            background-color: var(--primary-dark);
        }
        
        .page-title .material-icons,
        .page-title .fa-solid {
            vertical-align: middle;
            margin-right: 8px;
            color: var(--primary-color);
            font-size: 1.7rem;
        }
        
        /* 订单状态进度条 */
        .order-progress {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            padding: 30px;
            margin-bottom: 30px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .order-progress:hover {
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            transform: translateY(-3px);
        }
        
        .order-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 3px 0 0 3px;
        }
        
        .progress-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .progress-title .material-icons {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            margin-top: 50px;
        }
        
        .progress-steps::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: #f0f0f0;
            z-index: 1;
        }
        
        .progress-steps::after {
            content: '';
            position: absolute;
            top: 25px;
            left: 0;
            width: var(--progress-width, 75%);
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            z-index: 2;
            transition: width 1s ease;
            border-radius: 0 2px 2px 0;
        }
        
        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 3;
            width: 60px;
        }
        
        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .step-icon .material-icons {
            color: #aaa;
            font-size: 1.3rem;
            transition: all 0.3s ease;
        }
        
        .progress-step.active .step-icon,
        .progress-step.completed .step-icon {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.4);
        }
        
        .progress-step.active .step-icon .material-icons,
        .progress-step.completed .step-icon .material-icons {
            color: white;
        }
        
        .progress-step.active .step-icon {
            transform: scale(1.1);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(64, 169, 255, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(64, 169, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(64, 169, 255, 0);
            }
        }
        
        .step-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: #777;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .progress-step.active .step-label,
        .progress-step.completed .step-label {
            color: var(--primary-dark);
        }
        
        .step-time {
            font-size: 0.75rem;
            color: #999;
            margin-top: 5px;
            text-align: center;
        }
        
        /* 订单状态卡片 */
        .order-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .order-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .order-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle at top right, rgba(145, 213, 255, 0.15), transparent 70%);
            border-radius: 0 15px 0 100%;
            z-index: 0;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }
        
        .order-id {
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .order-id:before {
            content: '';
            display: inline-block;
            width: 8px;
            height: 20px;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            margin-right: 10px;
            border-radius: 3px;
        }
        
        .order-date {
            color: #666;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }
        
        .order-date .material-icons {
            font-size: 16px;
            margin-right: 5px;
            color: #999;
        }
        
        .order-status {
            display: flex;
            align-items: center;
            padding: 6px 15px;
            font-size: 0.85rem;
            border-radius: 30px;
            font-weight: 600;
            color: white;
            background-color: var(--success-color);
            box-shadow: 0 3px 10px rgba(76, 175, 80, 0.2);
            transition: all 0.3s ease;
        }
        
        .order-status .material-icons {
            font-size: 16px;
            margin-right: 5px;
        }
        
        .order-status:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        
        .order-status.pending {
            background-color: var(--warning-color);
            color: #333;
            box-shadow: 0 3px 10px rgba(255, 193, 7, 0.2);
        }
        
        .order-status.pending:hover {
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        
        .order-status.failed {
            background-color: var(--error-color);
            box-shadow: 0 3px 10px rgba(244, 67, 54, 0.2);
        }
        
        .order-status.failed:hover {
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
        }
        
        .order-item-info {
            background-color: #f9f9f9;
            padding: 15px 20px;
            border-radius: 12px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
            line-height: 1.4;
            border-left: 3px solid var(--primary-light);
        }
        
        .order-item-info strong {
            color: #555;
            margin-right: 8px;
        }
        
        .order-item-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 8px;
        }
        
        .order-item-row:last-child {
            margin-bottom: 0;
        }
        
        .order-item-label {
            font-weight: 600;
            color: #555;
            width: 100px;
            flex-shrink: 0;
        }
        
        .order-item-value {
            flex: 1;
        }
        
        .order-price {
            color: #ff5252;
            font-weight: 600;
        }
        
        .product-id {
            font-family: 'Courier New', monospace;
            color: var(--primary-dark);
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }
        
        /* 卡密信息卡片 */
        .cdkey-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .cdkey-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .cdkey-card::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle at bottom left, rgba(145, 213, 255, 0.15), transparent 70%);
            border-radius: 0 100% 0 15px;
            z-index: 0;
        }
        
        .cdkey-title {
            font-size: 1.2rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .cdkey-title .material-icons {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .cdkey-container {
            position: relative;
            border: 1px dashed #e0e0e0;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            transition: all 0.3s ease;
            z-index: 1;
        }
        
        .cdkey-container:hover {
            border-color: var(--primary-light);
            background-color: #fafafa;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .cdkey-warning {
            font-size: 0.9rem;
            color: var(--warning-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            background-color: rgba(255, 193, 7, 0.1);
            padding: 12px 15px;
            border-radius: 8px;
            border-left: 3px solid var(--warning-color);
        }
        
        .cdkey-warning .material-icons {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .cdkey-value {
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            letter-spacing: 3px;
            word-break: break-all;
            user-select: none;
            filter: blur(4px);
            transition: all 0.5s ease;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #f0f0f0;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        
        .cdkey-value.revealed {
            filter: blur(0);
            box-shadow: 0 5px 20px rgba(64, 169, 255, 0.2);
            border-color: var(--primary-light);
            animation: reveal 0.5s ease;
        }
        
        @keyframes reveal {
            0% {
                opacity: 0.5;
                transform: scale(0.98);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .cdkey-actions {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }
        
        .cdkey-btn {
            padding: 10px 18px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
        }
        
        .cdkey-btn .material-icons {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .reveal-btn {
            background-color: #f2f2f2;
            color: var(--text-color);
        }
        
        .reveal-btn:hover {
            background-color: #e6e6e6;
            transform: translateY(-2px);
        }
        
        .copy-btn {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            position: relative;
            overflow: hidden;
        }
        
        .copy-btn:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.3);
        }
        
        .copy-btn:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 10px;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            transition: transform 0.5s ease-out;
            z-index: 0;
        }
        
        .copy-btn:active:after {
            transform: scale(20);
            opacity: 0;
        }
        
        .cdkey-note {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background-color: rgba(64, 169, 255, 0.08);
            font-size: 0.9rem;
            line-height: 1.5;
            color: #666;
            border-left: 3px solid var(--primary-light);
        }
        
        .cdkey-note strong {
            color: var(--primary-dark);
        }
        
        /* 使用说明卡片 */
        .instructions-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            padding: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }
        
        .instructions-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .instructions-card::before {
            content: '';
            position: absolute;
            top: -100px;
            right: -100px;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle at top right, rgba(145, 213, 255, 0.1), transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }
        
        .instructions-title {
            font-size: 1.2rem;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .instructions-title .material-icons,
        .instructions-title .fa-solid {
            margin-right: 10px;
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        /* 商品说明样式 */
        .product-description {
            position: relative;
            z-index: 1;
        }
        
        .description-content {
            line-height: 1.8;
            color: var(--text-color);
            font-size: 0.95rem;
        }
        
        .description-content h1,
        .description-content h2,
        .description-content h3,
        .description-content h4,
        .description-content h5,
        .description-content h6 {
            color: var(--primary-dark);
            margin: 15px 0 10px 0;
        }
        
        .description-content p {
            margin-bottom: 12px;
        }
        
        .description-content ul,
        .description-content ol {
            margin: 10px 0 10px 20px;
        }
        
        .description-content li {
            margin-bottom: 5px;
        }
        
        .no-description {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-light);
            background-color: rgba(255, 193, 7, 0.05);
            border-radius: 10px;
            border: 1px dashed rgba(255, 193, 7, 0.3);
        }
        
        .no-description .fa-solid {
            font-size: 2rem;
            color: var(--warning-color);
            margin-bottom: 15px;
        }
        
        .no-description p {
            margin-bottom: 8px;
        }
        
        .description-hint {
            font-size: 0.85rem;
            color: #999;
        }
        
        .instructions-list {
            list-style: none;
            position: relative;
            z-index: 1;
        }
        
        .instruction-item {
            display: flex;
            margin-bottom: 20px;
            line-height: 1.6;
            transition: all 0.3s ease;
            padding: 5px;
            border-radius: 8px;
        }
        
        .instruction-item:hover {
            background-color: rgba(145, 213, 255, 0.08);
            transform: translateX(5px);
        }
        
        .instruction-item:last-child {
            margin-bottom: 0;
        }
        
        .instruction-number {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.85rem;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 3px 8px rgba(64, 169, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .instruction-item:hover .instruction-number {
            transform: scale(1.1);
        }
        
        .instruction-text {
            flex: 1;
            padding-top: 5px;
        }
        

        
        /* 防截图措施 */
        .anti-screenshot {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(64, 169, 255, 0.05) 10px,
                rgba(64, 169, 255, 0.05) 20px
            );
            pointer-events: none;
            z-index: 1;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        /* 页脚 */
        footer {
            background-color: var(--bg-color);
            padding: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 -4px 12px rgba(64, 169, 255, 0.08);
            margin-top: 30px;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
            opacity: 0.8;
        }
        
        .footer-content {
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.6rem;
            color: var(--primary-dark);
            margin-bottom: 16px;
        }
        
        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        
        .copyright {
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--text-light);
            letter-spacing: 0.5px;
        }
        

        


        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                padding: 12px 15px;
            }
            
            .logo {
                font-size: 1.3rem;
            }
            
            .main-content {
                margin-top: 70px;
                padding: 20px 15px;
            }
            
            .page-title {
                font-size: 1.5rem;
            }
            
            .progress-steps {
                flex-wrap: wrap;
                justify-content: center;
                gap: 15px;
            }
            
            .progress-step {
                width: calc(50% - 30px);
                margin-bottom: 20px;
            }
            
            .progress-steps::before,
            .progress-steps::after {
                display: none;
            }
            
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .order-status {
                align-self: flex-start;
            }
            
            .cdkey-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .cdkey-btn {
                width: 100%;
            }
            

            
            .bottom-btn {
                width: 100%;
                justify-content: center;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 10px;
                align-items: center;
            }
        }
        
        @media (max-width: 480px) {
            .navbar {
                padding: 10px;
            }
            
            .logo .material-icons {
                font-size: 1.5rem;
            }
            
            .main-content {
                padding: 15px 10px;
            }
            
            .page-title {
                font-size: 1.3rem;
                margin-bottom: 20px;
            }
            
            .order-card,
            .cdkey-card,
            .instructions-card {
                padding: 15px;
                border-radius: 10px;
            }
            
            .order-progress {
                padding: 20px 15px;
            }
            
            .progress-step {
                width: 100%;
            }
            
            .step-icon {
                width: 40px;
                height: 40px;
            }
            
            .step-icon .material-icons {
                font-size: 1.1rem;
            }
            
            .cdkey-value {
                font-size: 1.2rem;
                letter-spacing: 2px;
                padding: 15px 10px;
            }
            
            .instruction-item {
                align-items: flex-start;
            }
            
            .instruction-number {
                width: 25px;
                height: 25px;
                font-size: 0.8rem;
                margin-top: 2px;
            }
            
            .contact-methods {
                gap: 10px;
            }
            
            .modal-content {
                padding: 20px 15px;
            }
        }
        
        /* 浅色动画效果 */
        @keyframes softPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(64, 169, 255, 0.3);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(64, 169, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(64, 169, 255, 0);
            }
        }
        
        .soft-pulse {
            animation: softPulse 2s infinite;
        }
        
        .support-info {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 10px;
            border-left: 3px solid var(--primary-light);
            transition: all 0.3s ease;
        }
        
        .support-info:hover {
            background-color: #f5f5f5;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .support-title {
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            color: var(--primary-dark);
        }
        
        .support-title .material-icons {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .support-text {
            font-size: 0.95rem;
            line-height: 1.6;
            color: #666;
        }
        
        .support-link, .faq-link {
            color: var(--primary-dark);
            text-decoration: none;
            border-bottom: 1px dashed var(--primary-light);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .support-link:hover, .faq-link:hover {
            color: var(--primary-color);
            border-bottom: 1px solid var(--primary-color);
        }