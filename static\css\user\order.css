:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemF<PERSON>, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }

        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1;
        }

        .nav-icons {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .nav-item {
            margin: 0 15px;
            position: relative;
        }

        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            font-weight: 700;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 30px 25px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 0.95rem;
            color: #666;
            background-color: #f9f9f9;
            padding: 12px 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
        }
        
        .breadcrumb-item {
            display: flex;
            align-items: center;
            transition: color 0.3s ease;
        }
        
        .breadcrumb-item:not(:last-child) {
            position: relative;
            margin-right: 25px;
        }
        
        .breadcrumb-item:not(:last-child)::after {
            content: '›';
            position: absolute;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            font-size: 1.2rem;
            font-weight: 300;
        }
        
        .breadcrumb-item a {
            color: #666;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .breadcrumb-item a:hover {
            color: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .breadcrumb-item:last-child {
            color: var(--primary-dark);
            font-weight: 600;
        }

        /* 禁用商品列表链接的悬停效果 */
        .breadcrumb-item a#productListLink {
            color: #666 !important;
            cursor: default !important;
            text-decoration: none !important;
        }

        .breadcrumb-item a#productListLink:hover {
            color: #666 !important;
            transform: none !important;
        }



        /* 下单容器 */
        .order-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 40px;
        }

        /* 左侧表单区域 */
        .order-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* 商品信息区域 */
        .product-info-section {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 25px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
        }

        .product-info-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .product-info-section:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title .fa-solid {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .product-display {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .product-image {
            width: 120px;
            height: 120px;
            border-radius: var(--border-radius);
            object-fit: cover;
            box-shadow: var(--kawaii-shadow);
            border: 2px solid rgba(64, 169, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .product-image:hover {
            transform: scale(1.05) translateY(-3px);
            box-shadow: 0 20px 40px rgba(240, 77, 158, 0.25);
            border-color: var(--primary-light);
        }

        .product-details {
            flex: 1;
        }

        .product-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .product-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            color: var(--text-light);
            font-size: 0.95rem;
        }

        .product-meta span {
            display: flex;
            align-items: center;
        }

        .product-meta span::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--primary-color);
            margin-right: 8px;
        }

        .product-price {
            font-size: 1.8rem;
            color: var(--primary-dark);
            font-weight: 700;
            background: linear-gradient(to right, var(--primary-dark), #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }

        /* 商品信息操作按钮 */
        .product-actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }

        /* 区域标题头部布局 */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header .section-title {
            margin-bottom: 0;
        }

        .section-header .action-btn {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        /* 通用操作按钮样式 */
        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-color), #1976d2);
            color: white;
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, #1976d2, var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .action-btn.secondary:hover {
            background: linear-gradient(135deg, #e9ecef, #f8f9fa);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            color: var(--primary-dark);
        }



        /* 参数输入区域 */
        .params-section {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 25px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
        }

        .params-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .params-section:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        .param-group {
            margin-bottom: 20px;
        }

        .param-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.95rem;
            transition: color 0.3s ease;
        }

        .param-group:focus-within .param-label {
            color: var(--primary-dark);
        }

        .param-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #eaeaea;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .param-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 169, 255, 0.2);
            background-color: white;
        }

        .param-input:hover {
            border-color: #ddd;
        }

        /* 优惠券区域 */
        .coupon-section {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 25px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
        }

        .coupon-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .coupon-section:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        .coupon-group {
            display: flex;
            gap: 10px;
        }

        .coupon-input {
            flex: 1;
            padding: 14px 16px;
            border: 2px solid #eaeaea;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        .coupon-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 169, 255, 0.2);
            background-color: white;
        }

        .coupon-btn {
            padding: 0 25px;
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(24, 144, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .coupon-btn:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(24, 144, 255, 0.3);
        }

        /* 支付方式区域 */
        .payment-section {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 25px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
        }

        .payment-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .payment-section:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        /* 支付通道导航栏 */
        .payment-nav {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.2);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .payment-nav::-webkit-scrollbar {
            display: none;
        }

        .payment-nav-item {
            flex-shrink: 0;
            padding: 12px 20px;
            margin-right: 5px;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: var(--text-light);
            background-color: transparent;
            border: none;
            position: relative;
        }

        .payment-nav-item:hover {
            color: var(--primary-color);
            background-color: rgba(64, 169, 255, 0.05);
        }

        .payment-nav-item.active {
            color: var(--primary-color);
            background-color: rgba(64, 169, 255, 0.1);
            font-weight: 600;
        }

        .payment-nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--primary-color);
        }

        /* 支付方式内容区域 */
        .payment-content {
            min-height: 120px;
        }

        .payment-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .payment-option {
            position: relative;
            cursor: pointer;
        }

        .payment-input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .payment-label {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
            gap: 12px;
        }

        .payment-label:hover {
            border-color: #d0d0d0;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
        }

        .payment-input:checked + .payment-label {
            border-color: var(--primary-color);
            background-color: #f0f8ff;
            box-shadow: 0 6px 15px rgba(64, 169, 255, 0.2);
        }

        .payment-icon {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .payment-input:checked + .payment-label .payment-icon {
            transform: scale(1.1);
        }

        .payment-name {
            font-weight: 600;
            color: var(--text-color);
        }

        /* 支付方式图标颜色 */
        .payment-icon.wxpay {
            background: linear-gradient(135deg, #1aad19, #00d100);
        }

        .payment-icon.alipay {
            background: linear-gradient(135deg, #1677ff, #00a6fb);
        }

        .payment-icon.qqpay {
            background: linear-gradient(135deg, #12b7f5, #0099e5);
        }

        .payment-icon.default {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        /* 订单摘要 */
        .order-summary {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 25px;
            position: sticky;
            top: 100px;
            height: fit-content;
            border: var(--kawaii-border);
            transition: box-shadow 0.3s ease;
        }

        .order-summary:hover {
            box-shadow: 0 10px 30px rgba(240, 77, 158, 0.12);
        }

        .summary-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px dashed rgba(64, 169, 255, 0.3);
            color: var(--text-color);
            text-align: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .summary-title .fa-solid {
            color: var(--primary-color);
        }

        .summary-title::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: var(--kawaii-gradient);
            border-radius: 2px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.1);
        }

        .item-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .item-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: var(--kawaii-shadow);
            border: 2px solid white;
            transition: all 0.3s ease;
        }

        .order-item:hover .item-img {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(64, 169, 255, 0.3);
            border-color: var(--primary-light);
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .item-quantity {
            color: var(--text-light);
            font-size: 0.85rem;
        }

        .item-price {
            font-weight: 700;
            color: var(--primary-dark);
            font-size: 1.1rem;
        }

        .summary-rows {
            margin-bottom: 25px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 0.95rem;
        }

        .summary-row:last-child {
            margin-top: 20px;
            padding-top: 12px;
            border-top: 1px dashed rgba(64, 169, 255, 0.3);
            font-size: 1.1rem;
        }

        .summary-label {
            color: var(--text-light);
        }

        .summary-value {
            font-weight: 600;
            color: var(--text-color);
        }

        .total-amount {
            font-size: 1.4rem;
            color: var(--primary-dark);
            font-weight: 700;
            background: linear-gradient(to right, var(--primary-dark), #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .order-btn {
            width: 100%;
            padding: 16px 0;
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .order-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: all 0.6s ease;
        }

        .order-btn:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(24, 144, 255, 0.4);
        }

        .order-btn:hover::before {
            left: 100%;
        }

        .order-btn .fa-solid {
            font-size: 1.2rem;
        }

        .security-info {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-top: 20px;
            gap: 10px;
        }

        .security-badge {
            display: flex;
            align-items: center;
            color: var(--text-light);
            font-size: 0.85rem;
            transition: all 0.3s ease;
            gap: 5px;
        }

        .security-badge:hover {
            color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .security-badge .fa-solid {
            color: var(--primary-color);
        }

        /* 移动端固定底部操作栏 */
        .mobile-order-bar {
            display: none;
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: white;
            padding: 15px 20px;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
            z-index: 100;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            transition: transform 0.4s ease;
            border-top: var(--kawaii-border);
        }

        .mobile-order-bar::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
        }

        .mobile-order-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-total {
            color: var(--primary-dark);
            font-weight: 700;
            font-size: 1.4rem;
            background: linear-gradient(to right, var(--primary-dark), #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .mobile-order-btn {
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mobile-order-btn:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
        }

        .mobile-order-btn .fa-solid {
            font-size: 20px;
        }

        /* 页脚 */
        footer {
            background-color: #f8f8f8;
            padding: 40px 20px;
            margin-top: 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--primary-dark);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .footer-content .logo:hover {
            transform: scale(1.05);
        }

        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.8rem;
        }

        .copyright {
            margin-top: 20px;
            font-size: 0.95rem;
            color: #777;
            letter-spacing: 0.5px;
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .order-container {
                grid-template-columns: 1fr;
            }

            .order-summary {
                position: relative;
                top: 0;
                margin-top: 0;
                order: -1;
            }
        }

        @media (max-width: 768px) {
            .mobile-order-bar {
                display: block;
            }

            .order-summary {
                display: none;
            }

            .main-content {
                padding-bottom: 80px;
            }

            .payment-options {
                grid-template-columns: 1fr;
            }

            /* 移动端支付导航优化 */
            .payment-nav {
                margin-bottom: 15px;
            }

            .payment-nav-item {
                padding: 10px 15px;
                font-size: 0.9rem;
            }

            .payment-label {
                padding: 12px;
                gap: 10px;
            }

            .payment-icon {
                width: 35px;
                height: 35px;
                font-size: 18px;
            }

            /* 移动端面包屑导航优化 */
            .breadcrumb {
                font-size: 0.85rem;
                padding: 10px 15px;
                flex-wrap: nowrap;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .breadcrumb::-webkit-scrollbar {
                display: none;
            }

            .breadcrumb-item:not(:last-child) {
                margin-right: 15px;
                flex-shrink: 0;
            }

            .breadcrumb-item:not(:last-child)::after {
                right: -10px;
                font-size: 1rem;
            }

            .breadcrumb-item:last-child {
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex-shrink: 1;
            }

            /* 移动端导航菜单 */
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: all 0.4s ease;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
                padding: 15px 0;
                z-index: 999;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-item {
                margin: 12px 0;
            }

            .nav-link {
                color: var(--text-color);
                font-size: 1.1rem;
                padding: 10px;
                display: inline-block;
                width: 80%;
                border-radius: 30px;
                transition: all 0.3s ease;
            }

            .nav-link:hover {
                background-color: var(--primary-light);
                color: var(--primary-dark);
            }

            .nav-link::after {
                display: none;
            }

            .mobile-menu-btn {
                display: flex;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 20px 15px 80px;
            }

            .section-title {
                font-size: 1.1rem;
            }

            .product-display {
                flex-direction: column;
                text-align: center;
            }

            /* 移动端按钮样式调整 */
            .product-actions {
                justify-content: center;
                margin-top: 15px;
            }

            .action-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            /* 移动端section-header调整 */
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .section-header .action-btn {
                align-self: flex-end;
                padding: 8px 14px;
                font-size: 0.85rem;
            }

            .product-image {
                width: 100px;
                height: 100px;
            }

            .coupon-group {
                flex-direction: column;
            }

            .coupon-btn {
                margin-top: 10px;
            }
        }