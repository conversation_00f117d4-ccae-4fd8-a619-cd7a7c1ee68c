:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }

        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1;
        }

        .nav-icons {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .nav-item {
            margin: 0 15px;
            position: relative;
        }

        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            font-weight: 700;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 30px 25px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: calc(100vh - 160px);
        }
        
        /* 支付容器 */
        .payment-container {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 40px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .payment-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .payment-container:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        /* 支付标题 */
        .payment-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            animation: fadeInDown 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .payment-title .fa-solid {
            color: var(--primary-color);
            font-size: 1.8rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* 订单信息区域 */
        .order-info {
            margin-bottom: 30px;
            padding: 25px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
            border-radius: 15px;
            border: 1px solid rgba(64, 169, 255, 0.15);
            box-shadow: 0 6px 20px rgba(64, 169, 255, 0.08);
            animation: fadeIn 1s cubic-bezier(0.25, 0.8, 0.25, 1) 0.2s both;
            position: relative;
            overflow: hidden;
        }

        .order-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--kawaii-gradient);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 主要信息区域 */
        .primary-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px dashed rgba(64, 169, 255, 0.2);
            position: relative;
        }

        .primary-info::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: var(--kawaii-gradient);
            border-radius: 2px;
        }

        .product-info {
            flex: 1;
        }

        .product-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
            line-height: 1.3;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInLeft 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.3s both;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .product-name .fa-solid {
            color: var(--primary-color);
            font-size: 1.2rem;
            animation: rotateIn 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.5s both;
        }

        @keyframes rotateIn {
            from {
                opacity: 0;
                transform: rotate(-180deg) scale(0.5);
            }
            to {
                opacity: 1;
                transform: rotate(0deg) scale(1);
            }
        }

        .product-id-subtitle {
            font-size: 0.8rem;
            color: var(--text-light);
            font-weight: 500;
            font-family: 'Courier New', monospace;
            margin-top: 4px;
            padding: 4px 8px;
            background: rgba(76, 175, 80, 0.08);
            border-radius: 6px;
            border: 1px solid rgba(76, 175, 80, 0.15);
            color: #2e7d32;
            word-break: break-all;
            line-height: 1.3;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: all;
            display: inline-block;
            max-width: 100%;
            animation: fadeInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0.8s both;
        }

        .product-id-subtitle:hover {
            background: rgba(76, 175, 80, 0.12);
            border-color: rgba(76, 175, 80, 0.25);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        .price-display {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 5px;
            animation: slideInRight 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.4s both;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .price-label {
            font-size: 0.85rem;
            color: var(--text-light);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            animation: fadeInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0.6s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .price-value {
            font-size: 2.2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-dark), #1976d2, var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(64, 169, 255, 0.1);
            position: relative;
            animation: bounceIn 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.7s both;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .price-value::before {
            content: '¥';
            font-size: 1.4rem;
            position: absolute;
            left: -20px;
            top: 2px;
            color: var(--primary-dark);
            animation: slideInLeft 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0.8s both;
        }

        /* 次要信息区域 */
        .secondary-info {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .order-item-full {
            grid-column: 1 / -1;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 15px;
            background: rgba(64, 169, 255, 0.03);
            border-radius: 10px;
            border: 1px solid rgba(64, 169, 255, 0.08);
            transition: all 0.3s ease;
            position: relative;
            animation: slideInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) both;
        }

        .info-item:nth-child(1) {
            animation-delay: 0.5s;
        }

        .info-item:nth-child(2) {
            animation-delay: 0.7s;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .info-item:hover {
            background: rgba(64, 169, 255, 0.06);
            border-color: rgba(64, 169, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 169, 255, 0.1);
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--kawaii-gradient);
            border-radius: 0 3px 3px 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .info-item:hover::before {
            opacity: 1;
        }

        .info-label {
            font-size: 0.85rem;
            color: var(--text-light);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .info-label .fa-solid {
            color: var(--primary-color);
            font-size: 0.8rem;
        }

        .info-value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            word-break: break-all;
        }

        .info-value.order-id {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background: rgba(64, 169, 255, 0.08);
            padding: 8px 12px;
            border-radius: 8px;
            color: var(--primary-dark);
            border: 1px solid rgba(64, 169, 255, 0.2);
            word-break: break-all;
            line-height: 1.4;
            max-width: 100%;
            overflow-wrap: break-word;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: all;
        }

        .info-value.order-id:hover {
            background: rgba(64, 169, 255, 0.15);
            border-color: rgba(64, 169, 255, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
        }

        .info-value.order-id::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--kawaii-gradient);
            border-radius: 8px 0 0 8px;
        }



        /* 二维码区域 */
        .qrcode-section {
            text-align: center;
            margin-bottom: 30px;
            animation: zoomIn 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.4s both;
        }

        @keyframes zoomIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .qrcode-container {
            display: inline-block;
            padding: 20px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(64, 169, 255, 0.15);
            border: 2px solid rgba(64, 169, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .qrcode-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--kawaii-gradient);
            border-radius: 15px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .qrcode-container:hover::before {
            opacity: 1;
        }

        .qrcode-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(64, 169, 255, 0.25);
        }

        .qrcode-image {
            width: 250px;
            height: 250px;
            border-radius: 10px;
            display: block;
        }

        .qrcode-tip {
            margin-top: 15px;
            font-size: 0.95rem;
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .qrcode-tip .fa-solid {
            color: var(--primary-color);
        }

        /* 操作按钮区域 */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            animation: slideInUp 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.6s both;
        }

        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: all 0.6s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--primary-color), #1976d2);
            color: white;
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, #1976d2, var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .action-btn.secondary:hover {
            background: linear-gradient(135deg, #e9ecef, #f8f9fa);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            color: var(--primary-dark);
        }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 50%;
            box-shadow: var(--kawaii-shadow);
            border: var(--kawaii-border);
            z-index: 100;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(-50%);
            }
            40% {
                transform: translateY(-60%);
            }
            60% {
                transform: translateY(-55%);
            }
        }

        /* 复制提示动画 */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        .status-indicator .fa-solid {
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 20px 15px;
                margin-top: 70px;
            }

            .payment-container {
                padding: 25px 20px;
            }

            .payment-title {
                font-size: 1.5rem;
                margin-bottom: 25px;
            }

            .order-info {
                padding: 20px;
            }

            .primary-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
                text-align: left;
            }

            .price-display {
                align-items: flex-start;
                text-align: left;
            }

            .price-value {
                font-size: 1.8rem;
            }

            .price-value::before {
                left: -16px;
            }

            .secondary-info {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .product-name {
                font-size: 1.2rem;
            }

            .product-id-subtitle {
                font-size: 0.75rem;
                padding: 3px 6px;
            }

            .qrcode-image {
                width: 200px;
                height: 200px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }

            .status-indicator {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .payment-container {
                padding: 20px 15px;
            }

            .order-info {
                padding: 15px;
            }

            .primary-info {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }

            .product-name {
                font-size: 1.1rem;
            }

            .price-value {
                font-size: 1.6rem;
            }

            .price-value::before {
                left: -14px;
                font-size: 1.2rem;
            }

            .info-item {
                padding: 12px;
            }

            .info-label {
                font-size: 0.8rem;
            }

            .info-value {
                font-size: 0.95rem;
            }

            .info-value.order-id {
                font-size: 0.8rem;
                padding: 10px 12px;
                line-height: 1.3;
            }

            .product-id-subtitle {
                font-size: 0.7rem;
                padding: 2px 5px;
            }

            .qrcode-image {
                width: 180px;
                height: 180px;
            }

            .payment-title {
                font-size: 1.3rem;
            }
        }