:root {
            --success-color: #52c41a;
            --success-light: #95de64;
            --success-dark: #389e0d;
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }

        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1;
        }

        .nav-icons {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .nav-item {
            margin: 0 15px;
            position: relative;
        }

        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            font-weight: 700;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 30px 25px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: calc(100vh - 160px);
        }
        
        /* 成功容器 */
        .success-container {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 40px;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-align: center;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--success-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .success-container:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }

        /* 成功图标 */
        .success-icon {
            width: 120px;
            height: 120px;
            background: var(--kawaii-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: bounceIn 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.2s both;
            box-shadow: 0 10px 30px rgba(64, 169, 255, 0.3);
            position: relative;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .success-icon::before {
            content: '';
            position: absolute;
            width: 140px;
            height: 140px;
            border: 2px solid rgba(64, 169, 255, 0.2);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.1);
                opacity: 0;
            }
        }

        .success-icon .fa-solid {
            font-size: 3rem;
            color: white;
            animation: checkmark 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 0.8s both;
        }

        @keyframes checkmark {
            from {
                opacity: 0;
                transform: scale(0.5) rotate(-45deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        /* 成功标题 */
        .success-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: 15px;
            animation: fadeInDown 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.4s both;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-subtitle {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 40px;
            animation: fadeIn 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.6s both;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 订单信息卡片 */
        .order-summary {
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(64, 169, 255, 0.15);
            box-shadow: 0 6px 20px rgba(64, 169, 255, 0.08);
            animation: slideInLeft 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.8s both;
            position: relative;
            overflow: hidden;
        }

        .order-summary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--kawaii-gradient);
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .summary-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 25px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-title .fa-solid {
            color: var(--primary-color);
        }

        /* 主要信息区域 */
        .primary-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px dashed rgba(64, 169, 255, 0.2);
            position: relative;
            gap: 30px;
        }

        .primary-summary::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: var(--kawaii-gradient);
            border-radius: 2px;
        }

        .product-summary {
            flex: 1;
            max-width: 60%;
            min-width: 300px;
        }

        .product-name-summary {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 8px;
            line-height: 1.3;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideInLeft 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 0.9s both;
        }

        .product-name-summary .fa-solid {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .product-order-id-summary {
            font-size: 0.8rem;
            color: var(--text-light);
            font-weight: 500;
            font-family: 'Courier New', monospace;
            margin-top: 4px;
            padding: 4px 8px;
            background: rgba(64, 169, 255, 0.08);
            border-radius: 6px;
            border: 1px solid rgba(64, 169, 255, 0.15);
            color: var(--primary-dark);
            word-break: break-all;
            line-height: 1.3;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: all;
            display: inline-block;
            max-width: 100%;
            animation: fadeInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) 1.1s both;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        .product-order-id-summary:hover {
            background: rgba(64, 169, 255, 0.12);
            border-color: rgba(64, 169, 255, 0.25);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
        }

        .price-summary {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 5px;
            animation: slideInRight 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 1s both;
            flex-shrink: 0;
            min-width: 150px;
        }

        .price-label-summary {
            font-size: 0.85rem;
            color: var(--text-light);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .price-value-summary {
            font-size: 2.2rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-dark), #1976d2, var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .price-value-summary::before {
            content: '¥';
            font-size: 1.4rem;
            position: absolute;
            left: -20px;
            top: 2px;
            color: var(--primary-dark);
        }

        /* 次要信息区域 */
        .secondary-summary {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .time-item-full {
            grid-column: 1 / -1;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 15px;
            background: rgba(64, 169, 255, 0.03);
            border-radius: 10px;
            border: 1px solid rgba(64, 169, 255, 0.08);
            transition: all 0.3s ease;
            position: relative;
            animation: slideInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1) both;
        }

        .summary-item:nth-child(1) {
            animation-delay: 1.1s;
        }

        .summary-item:hover {
            background: rgba(64, 169, 255, 0.06);
            border-color: rgba(64, 169, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 169, 255, 0.1);
        }

        .summary-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--kawaii-gradient);
            border-radius: 0 3px 3px 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .summary-item:hover::before {
            opacity: 1;
        }

        .summary-label {
            font-size: 0.85rem;
            color: var(--text-light);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .summary-label .fa-solid {
            color: var(--primary-color);
            font-size: 0.8rem;
        }

        .summary-value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            word-break: break-all;
        }



        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            animation: slideInUp 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) 1s both;
        }

        .action-btn {
            padding: 14px 28px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: all 0.6s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn.primary {
            background: var(--kawaii-gradient);
            color: white;
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, #1976d2, var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .action-btn.secondary:hover {
            background: linear-gradient(135deg, #e9ecef, #f8f9fa);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            color: var(--primary-dark);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-content {
                max-width: 900px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px 15px;
                margin-top: 70px;
                max-width: 100%;
            }

            .success-container {
                padding: 25px 20px;
            }

            .success-title {
                font-size: 2rem;
            }

            .success-icon {
                width: 100px;
                height: 100px;
            }

            .success-icon .fa-solid {
                font-size: 2.5rem;
            }

            .primary-summary {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
                text-align: left;
            }

            .product-summary {
                max-width: 100%;
                min-width: auto;
            }

            .price-summary {
                align-items: flex-start;
                text-align: left;
                min-width: auto;
            }

            .price-value-summary {
                font-size: 1.8rem;
            }

            .price-value-summary::before {
                left: -16px;
            }

            .secondary-summary {
                gap: 15px;
            }

            .product-order-id-summary {
                font-size: 0.75rem;
                padding: 3px 6px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .success-container {
                padding: 20px 15px;
            }

            .success-title {
                font-size: 1.8rem;
            }

            .success-icon {
                width: 80px;
                height: 80px;
            }

            .success-icon .fa-solid {
                font-size: 2rem;
            }

            .order-summary {
                padding: 20px;
            }

            .product-order-id-summary {
                font-size: 0.7rem;
                padding: 2px 5px;
            }
        }