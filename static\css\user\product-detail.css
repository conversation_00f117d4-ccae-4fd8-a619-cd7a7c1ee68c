:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemF<PERSON>, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }

        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1;
        }

        .nav-icons {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .nav-item {
            margin: 0 15px;
            position: relative;
        }

        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            font-weight: 700;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .nav-icons {
            display: flex;
            align-items: center;
        }

        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 30px 25px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            font-size: 0.95rem;
            color: #666;
            background-color: #f9f9f9;
            padding: 12px 20px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
        }
        
        .breadcrumb-item {
            display: flex;
            align-items: center;
            transition: color 0.3s ease;
        }
        
        .breadcrumb-item:not(:last-child) {
            position: relative;
            margin-right: 25px;
        }
        
        .breadcrumb-item:not(:last-child)::after {
            content: '›';
            position: absolute;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            font-size: 1.2rem;
            font-weight: 300;
        }
        
        .breadcrumb-item a {
            color: #666;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .breadcrumb-item a:hover {
            color: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .breadcrumb-item:last-child {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        /* 商品详情区 */
        .product-detail {
            display: flex;
            flex-direction: row-reverse; /* 反转布局，图片移到右侧 */
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            overflow: hidden;
            margin-bottom: 40px;
            position: relative;
            border: var(--kawaii-border);
        }
        
        .product-detail::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .product-detail:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }
        
        .product-gallery {
            flex: 0 0 40%;
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, rgba(145, 213, 255, 0.05), rgba(255, 255, 255, 0.98));
            border-left: 1px solid rgba(64, 169, 255, 0.1);
            min-height: 400px;
        }

        .product-main-image {
            width: 90%;
            aspect-ratio: 1;
            max-width: 90%;
            max-height: 90%;
            object-fit: cover;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(64, 169, 255, 0.1);
        }

        .product-main-image:hover {
            transform: scale(1.02) translateY(-3px);
            box-shadow: 0 20px 40px rgba(24, 144, 255, 0.25);
            border-color: var(--primary-light);
        }
        

        
        .product-info {
            flex: 1;
            padding: 35px;
            display: flex;
            flex-direction: column;
        }

        .product-title {
            font-size: 2rem;
            margin-bottom: 15px;
            line-height: 1.3;
            color: #333;
            font-weight: 700;
        }

        .product-meta {
            margin-bottom: 25px;
            color: #666;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .product-meta span {
            display: flex;
            align-items: center;
        }

        .product-meta span::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--primary-color);
            margin-right: 8px;
        }

        .product-price-box {
            background: linear-gradient(to right, #f0f8ff, #f8fcff);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .product-price-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 5px 0 0 5px;
        }

        .product-price {
            font-size: 2.2rem;
            color: var(--primary-dark);
            font-weight: 700;
            background: linear-gradient(to right, var(--primary-dark), #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        
        .price-note {
            font-size: 0.95rem;
            color: #666;
            margin-top: 8px;
            display: flex;
            align-items: center;
        }
        
        .price-note::before {
            content: '';
            display: inline-block;
            width: 15px;
            height: 15px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="%23FF9FB5"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 6px;
        }
        
        .product-stock {
            margin-bottom: 25px;
            color: #4CAF50;
            display: flex;
            align-items: center;
            font-weight: 500;
            padding: 10px 15px;
            background-color: rgba(76, 175, 80, 0.08);
            border-radius: 8px;
            width: fit-content;
        }
        
        .product-stock .fa-solid {
            margin-right: 8px;
            font-size: 20px;
            filter: drop-shadow(0 2px 4px rgba(76, 175, 80, 0.3));
        }
        
        .product-stock.low {
            color: #FFC107;
            background-color: rgba(255, 193, 7, 0.08);
        }
        
        .product-stock.out {
            color: #F44336;
            background-color: rgba(244, 67, 54, 0.08);
        }
        
        .product-quantity {
            margin-bottom: 30px;
        }
        
        .quantity-label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: #444;
            font-size: 1rem;
        }
        
        .quantity-control {
            display: flex;
            align-items: center;
            max-width: 180px;
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 5px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        
        .quantity-btn {
            width: 45px;
            height: 45px;
            background-color: white;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            color: #555;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .quantity-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(64, 169, 255, 0.2);
        }
        
        .quantity-input {
            width: 70px;
            height: 45px;
            text-align: center;
            border: none;
            background-color: transparent;
            margin: 0 10px;
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        
        .quantity-input:focus {
            outline: none;
        }

        /* 商品参数样式 */
        .product-params {
            margin-bottom: 30px;
        }

        .params-title {
            font-weight: 600;
            color: #444;
            font-size: 1rem;
            margin-bottom: 12px;
        }

        .params-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .param-tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background-color: var(--primary-light);
            color: var(--primary-dark);
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            border: 1px solid rgba(64, 169, 255, 0.3);
            transition: all 0.3s ease;
        }

        .param-tag:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.3);
        }

        .buy-actions {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }
        
        .buy-btn {
            flex: 1;
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .buy-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.4);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .buy-btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: scale(25, 25);
                opacity: 0.3;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        
        .buy-now-btn {
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
        }
        
        .buy-now-btn:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(24, 144, 255, 0.4);
        }
        

        
        .buy-btn .fa-solid {
            margin-right: 10px;
            font-size: 22px;
            transition: transform 0.3s ease;
        }

        .buy-btn:hover .fa-solid {
            transform: scale(1.2);
        }
        
        /* 商品详情标签 */
        .product-tabs {
            margin-bottom: 60px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .tabs-header {
            display: flex;
            border-bottom: 1px solid #eee;
            background: linear-gradient(to right, #f0f8ff, #fff);
        }
        
        .tab-item {
            padding: 18px 25px;
            cursor: pointer;
            position: relative;
            font-weight: 500;
            color: #555;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 150px;
        }
        
        .tab-item::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            transition: width 0.3s ease;
        }
        
        .tab-item.active {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        .tab-item.active::after,
        .tab-item:hover::after {
            width: 100%;
        }
        
        .tab-item:hover {
            color: var(--primary-color);
            background-color: rgba(145, 213, 255, 0.1);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.4s;
            padding: 30px;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tab-content.active {
            display: block;
        }
        
        .tab-content h3 {
            margin-bottom: 20px;
            font-size: 1.4rem;
            color: #333;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
            position: relative;
        }
        
        .tab-content h3::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -1px;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--primary-light));
            border-radius: 3px;
        }
        
        .tab-content p {
            margin-bottom: 15px;
            line-height: 1.7;
            color: #555;
            font-size: 1.05rem;
        }
        
        .tab-content ul {
            padding-left: 25px;
            margin-bottom: 20px;
        }
        
        .tab-content li {
            margin-bottom: 10px;
            line-height: 1.7;
            color: #555;
            position: relative;
            padding-left: 5px;
        }
        
        .tab-content li::marker {
            color: var(--primary-color);
            font-size: 1.1em;
        }
        
        .tab-content strong {
            color: #333;
            font-weight: 600;
        }

        /* 商品详情内容样式 */
        .product-info-content {
            line-height: 1.8;
            color: #555;
        }

        .product-info-content h1,
        .product-info-content h2,
        .product-info-content h3,
        .product-info-content h4,
        .product-info-content h5,
        .product-info-content h6 {
            color: #333;
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        .product-info-content p {
            margin-bottom: 15px;
            line-height: 1.7;
        }

        .product-info-content ul,
        .product-info-content ol {
            margin: 15px 0;
            padding-left: 25px;
        }

        .product-info-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .product-info-content img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 15px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .product-info-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .product-info-content th,
        .product-info-content td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .product-info-content th {
            background-color: var(--bg-light);
            font-weight: 600;
            color: #333;
        }

        .product-info-content blockquote {
            background-color: #f8f9fa;
            border-left: 4px solid var(--primary-color);
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }

        .product-info-content code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .product-info-content pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .product-info-content a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .product-info-content a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        /* 固定底部购买栏（移动端） */
        .mobile-buy-bar {
            display: none;
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: white;
            padding: 15px 20px;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
            z-index: 100;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            transition: transform 0.4s ease;
        }
        
        .mobile-buy-bar::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
        }
        
        .mobile-buy-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .mobile-price {
            color: var(--primary-dark);
            font-weight: 700;
            font-size: 1.6rem;
            background: linear-gradient(to right, var(--primary-dark), #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .mobile-buy-btn {
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(24, 144, 255, 0.3);
            display: flex;
            align-items: center;
        }
        
        .mobile-buy-btn:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
        }
        
        .mobile-buy-btn .fa-solid {
            margin-right: 8px;
            font-size: 20px;
        }
        
        /* 在移动设备上显示底部购买栏 */
        @media (max-width: 768px) {
            .mobile-buy-bar {
                display: block;
            }

            .buy-actions {
                display: none;
            }

            /* 隐藏移动端上方的价格框，避免与底部购买栏重复显示 */
            .product-price-box {
                display: none;
            }

            /* 页面内容添加底部边距 */
            .main-content {
                padding-bottom: 80px;
            }

            /* 移动端面包屑导航优化 */
            .breadcrumb {
                font-size: 0.85rem;
                padding: 10px 15px;
                flex-wrap: nowrap;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .breadcrumb::-webkit-scrollbar {
                display: none;
            }

            .breadcrumb-item:not(:last-child) {
                margin-right: 15px;
                flex-shrink: 0;
            }

            .breadcrumb-item:not(:last-child)::after {
                right: -10px;
                font-size: 1rem;
            }

            .breadcrumb-item:last-child {
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex-shrink: 1;
            }

            /* 移动端导航菜单 */
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: all 0.4s ease;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
                padding: 15px 0;
                z-index: 999;
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-item {
                margin: 12px 0;
            }
            
            .nav-link {
                color: var(--text-color);
                font-size: 1.1rem;
                padding: 10px;
                display: inline-block;
                width: 80%;
                border-radius: 30px;
                transition: all 0.3s ease;
            }
            
            .nav-link:hover {
                background-color: var(--primary-light);
                color: var(--primary-dark);
            }
            
            .nav-link::after {
                display: none;
            }
            
            .mobile-menu-btn {
                display: flex;
            }
            
            /* 标签栏滚动 */
            .tabs-header {
                overflow-x: auto;
                white-space: nowrap;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }
            
            .tabs-header::-webkit-scrollbar {
                display: none;
            }
            
            .tab-item {
                padding: 15px 20px;
            }
        }
        
        /* 页脚 */
        footer {
            background-color: #f8f8f8;
            padding: 40px 20px;
            margin-top: 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--primary-dark);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .footer-content .logo:hover {
            transform: scale(1.05);
        }
        
        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        
        .copyright {
            margin-top: 20px;
            font-size: 0.95rem;
            color: #777;
            letter-spacing: 0.5px;
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .product-detail {
                flex-direction: column;
            }

            .product-gallery {
                flex: none;
                width: 100%;
                min-height: 300px;
                padding: 15px;
                border-left: none;
                border-bottom: 1px solid rgba(64, 169, 255, 0.1);
            }

            .product-main-image {
                width: 85%;
                max-width: 85%;
                max-height: 85%;
            }

            .product-info {
                border-left: none;
            }
        }

        @media (max-width: 480px) {
            .product-gallery {
                min-height: 280px;
                padding: 12px;
            }

            .product-main-image {
                width: 88%;
                max-width: 88%;
                max-height: 88%;
            }

            .product-title {
                font-size: 1.5rem;
            }

            .product-price {
                font-size: 1.8rem;
            }

            .tab-item {
                padding: 12px 15px;
                font-size: 0.9rem;
            }
        }
        
        /* 购物车动画 */
        @keyframes flyToCart {
            0% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            20% {
                opacity: 0.8;
                transform: scale(0.8) translateY(-20px);
            }
            100% {
                opacity: 0;
                transform: scale(0.2) translateY(-100px) translateX(100px);
            }
        }
        
        .flying-item {
            position: absolute;
            width: 50px;
            height: 50px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: flyToCart 0.8s ease-out forwards;
        }
        
        .flying-item .fa-solid {
            color: var(--primary-color);
            font-size: 24px;
        }

        /* 确保Font Awesome图标正确显示 */
        .fa-solid {
            font-size: 18px;
            display: inline-block;
            vertical-align: middle;
        }

        .nav-icon .fa-solid {
            font-size: 18px;
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
        }

        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        /* 公告弹窗美化样式 */
        .notice-modal-content {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(64, 169, 255, 0.15);
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8fcff 100%);
        }

        .notice-modal-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            padding: 25px 30px;
            position: relative;
            overflow: hidden;
        }

        .notice-modal-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .notice-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin-right: 15px;
            position: relative;
            z-index: 2;
        }

        .notice-icon i {
            font-size: 24px;
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .notice-modal-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .notice-btn-close {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .notice-btn-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .notice-btn-close::before {
            content: '×';
            color: white;
            font-size: 20px;
            font-weight: bold;
            line-height: 1;
        }

        .notice-modal-body {
            padding: 30px;
            background: white;
            position: relative;
        }

        .notice-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #444;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }

        .notice-content::-webkit-scrollbar {
            width: 6px;
        }

        .notice-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .notice-content::-webkit-scrollbar-thumb {
            background: var(--primary-light);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .notice-content::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        .notice-content h1, .notice-content h2, .notice-content h3,
        .notice-content h4, .notice-content h5, .notice-content h6 {
            color: var(--primary-dark);
            margin: 20px 0 15px 0;
            font-weight: 600;
        }

        .notice-content p {
            margin-bottom: 15px;
        }

        .notice-content ul, .notice-content ol {
            margin: 15px 0;
            padding-left: 25px;
        }

        .notice-content li {
            margin-bottom: 8px;
        }

        .notice-content strong {
            color: var(--primary-dark);
            font-weight: 600;
        }

        .notice-content a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .notice-content a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .notice-modal-footer {
            background: linear-gradient(to right, #f8fcff, #ffffff);
            border: none;
            padding: 20px 30px;
            display: flex;
            justify-content: center;
        }

        .notice-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .notice-btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .notice-btn-primary:hover::before {
            left: 100%;
        }

        .notice-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 169, 255, 0.4);
        }

        .notice-btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
        }

        .notice-btn-primary i {
            font-size: 16px;
        }

        /* 弹窗动画效果 */
        #noticeModal.fade .modal-dialog {
            transform: scale(0.8) translateY(-50px);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        #noticeModal.show .modal-dialog {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .notice-modal-content {
                margin: 20px;
                border-radius: 15px;
            }

            .notice-modal-header {
                padding: 20px 25px;
            }

            .notice-icon {
                width: 40px;
                height: 40px;
                margin-right: 12px;
            }

            .notice-icon i {
                font-size: 20px;
            }

            .notice-modal-title {
                font-size: 1.3rem;
            }

            .notice-modal-body {
                padding: 25px;
            }

            .notice-content {
                font-size: 1rem;
                max-height: 300px;
            }

            .notice-modal-footer {
                padding: 15px 25px;
            }

            .notice-btn-primary {
                padding: 10px 25px;
                font-size: 0.95rem;
            }
        }