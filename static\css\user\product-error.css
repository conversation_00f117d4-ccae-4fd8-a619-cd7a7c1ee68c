:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }

        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            flex: 1;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1;
        }

        .nav-icons {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        .nav-item {
            margin: 0 15px;
            position: relative;
        }

        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link.active {
            font-weight: 700;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 50px 25px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 错误容器 */
        .error-container {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--kawaii-shadow);
            padding: 50px 40px;
            text-align: center;
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .error-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--kawaii-gradient);
            transform: scaleX(0.8);
            opacity: 0.8;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .error-container:hover::after {
            transform: scaleX(1);
            opacity: 1;
        }
        
        .error-icon {
            font-size: 5rem;
            color: var(--primary-color);
            margin-bottom: 30px;
            filter: drop-shadow(0 4px 8px rgba(24, 144, 255, 0.3));
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .error-title {
            font-size: 2.2rem;
            color: var(--text-color);
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }
        
        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 14px 30px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            min-width: 200px;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .action-btn.primary {
            background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
            color: white;
            box-shadow: 0 6px 15px rgba(24, 144, 255, 0.3);
        }
        
        .action-btn.secondary {
            background: white;
            color: var(--primary-dark);
            border: 2px solid var(--primary-color);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .action-btn::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: all 0.6s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
        }
        
        .action-btn.primary:hover {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            box-shadow: 0 10px 20px rgba(24, 144, 255, 0.4);
        }
        
        .action-btn.secondary:hover {
            background: var(--primary-light);
            color: var(--primary-dark);
            border-color: var(--primary-dark);
            box-shadow: 0 6px 15px rgba(64, 169, 255, 0.2);
        }
        
        .action-btn:hover::before {
            left: 100%;
        }
        
        .action-btn .fa-solid {
            font-size: 1.1rem;
        }
        
        /* 页脚 */
        footer {
            background-color: #f8f8f8;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            margin-top: auto;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-dark);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }
        
        .footer-content .logo:hover {
            transform: scale(1.05);
        }
        
        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        
        .copyright {
            margin-top: 15px;
            font-size: 0.9rem;
            color: #777;
            letter-spacing: 0.5px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                padding: 30px 15px;
            }
            
            .error-container {
                padding: 40px 25px;
            }
            
            .error-icon {
                font-size: 4rem;
            }
            
            .error-title {
                font-size: 1.8rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .action-btn {
                min-width: 180px;
                padding: 12px 25px;
            }
            
            /* 移动端导航菜单 */
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: all 0.4s ease;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                border-bottom-left-radius: 15px;
                border-bottom-right-radius: 15px;
                padding: 15px 0;
                z-index: 999;
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-item {
                margin: 12px 0;
            }
            
            .nav-link {
                color: var(--text-color);
                font-size: 1.1rem;
                padding: 10px;
                display: inline-block;
                width: 80%;
                border-radius: 30px;
                transition: all 0.3s ease;
            }
            
            .nav-link:hover {
                background-color: var(--primary-light);
                color: var(--primary-dark);
            }
            
            .nav-link::after {
                display: none;
            }
            
            .mobile-menu-btn {
                display: flex;
            }
        }
        
        @media (max-width: 480px) {
            .error-container {
                padding: 30px 20px;
            }
            
            .error-icon {
                font-size: 3.5rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .action-btn {
                min-width: 160px;
                padding: 10px 20px;
                font-size: 0.95rem;
            }
        }