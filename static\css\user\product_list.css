:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
        }

        /* 基础样式重置 */
        .filter-backdrop {
            box-sizing: border-box;
        }

        /* 强制隐藏筛选面板的初始状态 */
        #advancedFilterPanel:not(.open) {
            right: -480px !important; /* 根据新宽度调整隐藏位置 */
            transform: translateX(0) !important;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            flex: 1; /* 左侧占据1/3空间 */
            justify-content: flex-start; /* 左对齐 */
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo .material-icons {
            margin-right: 8px;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
            flex: 1; /* 中间占据1/3空间 */
            margin: 0; /* 移除默认边距 */
            padding: 0; /* 移除默认内边距 */
        }
        
        .nav-item {
            margin: 0 15px;
            position: relative;
        }
        
        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: all 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        .nav-link.active {
            font-weight: 700;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .nav-icons {
            display: flex;
            align-items: center;
            flex: 1; /* 右侧占据1/3空间 */
            justify-content: flex-end; /* 右对齐 */
        }

        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .mobile-menu-btn {
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 70px;
            padding: 0;
            min-height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
            background-color: var(--bg-light);
        }
        
        .page-title {
            font-size: 1.5rem;
            padding: 0;
            font-weight: 700;
            color: #444;
            background-color: var(--bg-color);
            margin: 0;
            position: relative;
            z-index: 10;
            box-shadow: 0 1px 3px rgba(0,0,0,0.03);
        }
        
        /* 页脚 */
        footer {
            background-color: var(--bg-color);
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
        }
        
        .footer-content {
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--primary-dark);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .footer-content .logo:hover {
            transform: scale(1.05);
        }
        
        .footer-content .logo .material-icons {
            margin-right: 10px;
            font-size: 2rem;
        }
        
        .copyright {
            margin-top: 10px;
            font-size: 0.95rem;
            color: #777;
            letter-spacing: 0.5px;
        }
        
        /* 商品列表样式 */
        .product-list-container {
            padding: 10px 15px 20px;
            background-color: var(--bg-light);
            max-width: 100%;
            margin: 0 auto;
            width: calc(100% - 20px); /* 移动端两侧留出10px间隙 */
        }
        
        /* 高级筛选面板 */
        .filter-toggle {
            cursor: pointer;
            padding: 10px; /* 从8px增加到10px */
            border-radius: 50%;
            background: var(--primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-dark);
            font-size: 24px; /* 增加图标大小 */
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        }
        
        .filter-toggle:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px; /* 从15px增加到18px */
            background: var(--bg-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 18px; /* 从15px增加到18px */
        }
        
        .filter-bar.no-shadow {
            box-shadow: none;
            border-bottom: 1px solid #f0f0f0;
            border-radius: 0;
            margin-bottom: 0;
        }
        
        .filter-title {
            font-weight: 600;
            color: #555;
            display: flex;
            align-items: center;
        }
        
        .filter-title .material-icons {
            font-size: 24px; /* 从20px增加到24px */
            margin-right: 8px; /* 从6px增加到8px */
            color: var(--primary-color);
        }

        .filter-title {
            font-size: 18px; /* 从16px增加到18px */
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .sort-options {
            display: flex;
            gap: 15px;
        }
        
        .sort-option {
            cursor: pointer;
            padding: 12px 20px; /* 继续增加内边距 */
            border-radius: 22px; /* 继续增加圆角 */
            font-size: 17px; /* 从16px增加到17px */
            color: #777;
            transition: all 0.3s ease;
            font-weight: 600; /* 进一步增加字体粗细 */
        }
        
        .sort-option:hover {
            color: var(--primary-dark);
        }
        
        .sort-option.active {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            font-weight: 500;
        }
        
        .products-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .product-card {
            display: flex;
            background: var(--bg-color);
            border-radius: 12px; /* 从8px增加到12px */
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(0,0,0,0.12); /* 增强阴影效果 */
            transition: all 0.3s ease;
            position: relative;
            margin-bottom: 20px; /* 从12px增加到20px */
            border: 1px solid #e8e8e8;
            padding: 24px; /* 从16px增加到24px */
            align-items: center;
            gap: 24px; /* 从16px增加到24px */
        }

        .product-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .product-image {
            width: 100px; /* 从80px增加到100px */
            height: 100px; /* 从80px增加到100px */
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
            background: #f5f5f5;
            border-radius: 10px; /* 从8px增加到10px */
        }

        .product-image::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-top: 20px solid #ff6b35;
            z-index: 2;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        .product-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 0;
        }

        .product-name {
            font-size: 20px; /* 从16px增加到20px */
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin: 0 0 8px 0; /* 增加底部间距 */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .product-id {
            font-size: 15px; /* 从12px增加到15px */
            color: #999;
            margin: 0 0 12px 0; /* 增加底部间距 */
        }

        .product-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            margin-top: 4px;
        }

        .product-tag {
            padding: 4px 12px; /* 增加内边距 */
            border-radius: 14px; /* 稍微增加圆角 */
            font-size: 13px; /* 从11px增加到13px */
            font-weight: 500;
            border: 1px solid;
            cursor: default;
        }

        .tag-type {
            color: #1890ff;
            border-color: #1890ff;
            background: rgba(24, 144, 255, 0.1);
        }

        .tag-status {
            color: #52c41a;
            border-color: #52c41a;
            background: rgba(82, 196, 26, 0.1);
        }

        .tag-hot {
            color: #ff4d4f;
            border-color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
        }

        .tag-sales {
            color: #722ed1;
            border-color: #722ed1;
            background: rgba(114, 46, 209, 0.1);
        }
        
        .product-price-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
            flex-shrink: 0;
        }

        .product-price {
            font-size: 30px; /* 从26px增加到30px */
            font-weight: 700;
            color: #52c41a;
            margin: 0 0 8px 0; /* 增加底部间距 */
        }

        .price-currency {
            font-size: 18px; /* 从14px增加到18px */
            margin-right: 3px;
        }

        .original-price {
            font-size: 14px; /* 从12px增加到14px */
            color: #999;
            text-decoration: line-through;
            margin: 0;
        }

        .view-detail-btn {
            padding: 10px 20px; /* 从6px 16px增加到10px 20px */
            background: var(--primary-color); /* 使用网站主色调 */
            color: white;
            border: none;
            border-radius: 6px; /* 从4px增加到6px */
            font-size: 15px; /* 从12px增加到15px */
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .view-detail-btn:hover {
            background: var(--primary-dark); /* 悬停时使用深色主色调 */
            transform: translateY(-1px);
        }
        
        /* 加载状态和空状态样式 */
        .loading-state, .empty-state, .error-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            text-align: center;
            color: #888;
            padding: 20px;
            width: 100%;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(64, 169, 255, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .loading-text, .empty-state-text, .error-text {
            font-size: 14px;
            color: #999;
            margin-top: 10px;
        }
        
        .empty-state-icon, .error-icon {
            font-size: 48px;
            margin-bottom: 10px;
            color: #ddd;
        }
        
        .error-icon {
            color: #ffcccb;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
            margin-bottom: 20px;
            padding: 5px;
            background-color: var(--bg-color);
            border-radius: 30px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.06);
            display: inline-flex;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .page-item {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
            font-weight: 500;
            font-size: 15px;
        }
        
        .page-item.active {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: 0 4px 10px rgba(64, 169, 255, 0.3);
            transform: scale(1.1);
        }
        
        .page-nav {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }
        
        .page-nav:hover {
            background-color: #f0f0f0;
            color: var(--primary-dark);
        }
        
        .page-nav.disabled {
            color: #ccc;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (min-width: 768px) and (max-width: 991px) {
            .product-list-container {
                width: 95%;
                padding: 18px; /* 从15px增加到18px */
            }

            .product-image {
                width: 110px; /* 从90px增加到110px */
                height: 110px; /* 从90px增加到110px */
            }

            .product-card {
                padding: 20px; /* 增加内边距 */
            }

            .product-name {
                font-size: 18px; /* 中等屏幕适中的字体 */
            }

            .product-price {
                font-size: 26px; /* 从24px增加到26px */
            }
        }

        @media (min-width: 992px) {
            .products-grid {
                gap: 25px; /* 从20px增加到25px */
            }

            .product-card {
                margin-bottom: 0;
                padding: 22px; /* 适度调整，从28px降到22px */
            }

            .product-image {
                width: 105px; /* 适度调整，从120px降到105px */
                height: 105px; /* 适度调整，从120px降到105px */
            }

            .product-list-container {
                padding: 25px; /* 从20px增加到25px */
                max-width: 1200px;
                margin: 0 auto;
                width: 90%;
            }

            .filter-bar.no-shadow {
                padding-left: 25px; /* 从20px增加到25px */
                padding-right: 25px; /* 从20px增加到25px */
            }

            /* 大屏幕上适度增大字体 */
            .product-name {
                font-size: 19px; /* 适度调整，从22px降到19px */
            }

            .product-id {
                font-size: 14px; /* 适度调整，从16px降到14px */
            }

            .product-price {
                font-size: 28px; /* 从24px增加到28px */
            }

            .view-detail-btn {
                padding: 10px 20px; /* 适度调整，从12px 24px降到10px 20px */
                font-size: 14px; /* 适度调整，从16px降到14px */
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: var(--transition);
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                flex: none; /* 移动端取消flex布局 */
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-item {
                margin: 15px 0;
            }

            .nav-link {
                color: var(--text-color);
            }

            .mobile-menu-btn {
                display: flex;
            }

            .nav-icons {
                flex: none; /* 移动端取消flex布局 */
            }

            .sort-options {
                flex-wrap: wrap;
            }

            .product-card {
                padding: 12px;
                gap: 12px;
            }

            .product-image {
                width: 90px; /* 从70px增加到90px */
                height: 90px; /* 从70px增加到90px */
            }

            .product-name {
                font-size: 14px;
            }

            .product-id {
                font-size: 11px;
            }

            .product-tag {
                font-size: 10px;
                padding: 1px 6px;
            }

            .product-price {
                font-size: 16px;
            }

            .view-detail-btn {
                padding: 6px 14px; /* 从4px 12px增加到6px 14px */
                font-size: 12px; /* 从11px增加到12px */
            }
        }

        @media (max-width: 576px) {
            .product-card {
                padding: 10px;
                gap: 10px;
            }

            .product-image {
                width: 80px; /* 从60px增加到80px */
                height: 80px; /* 从60px增加到80px */
            }

            .product-name {
                font-size: 13px;
            }

            .product-id {
                font-size: 10px;
            }

            .product-tags {
                gap: 4px;
            }

            .product-tag {
                font-size: 9px;
                padding: 1px 4px;
            }

            .product-price {
                font-size: 14px;
            }

            .original-price {
                font-size: 10px;
            }

            .view-detail-btn {
                padding: 6px 12px; /* 从3px 8px增加到6px 12px */
                font-size: 12px; /* 从10px增加到12px */
            }

            .stat-item {
                font-size: 10px;
                padding: 3px 6px;
            }

            .stat-icon {
                font-size: 12px;
            }

            .price-currency {
                font-size: 14px;
            }

            .price-decimal {
                font-size: 14px;
            }

            .buy-button {
                padding: 8px 12px;
                font-size: 12px;
                border-radius: 16px;
            }

            .buy-button .material-icons {
                font-size: 16px;
                margin-right: 4px;
            }

            .product-rating {
                font-size: 11px;
            }

            .sort-options {
                gap: 8px;
            }

            .sort-option {
                padding: 4px 8px;
                font-size: 12px;
            }

            .filter-bar {
                padding: 10px;
            }

            .page-item, .page-nav {
                width: 35px;
                height: 35px;
                margin: 0 3px;
            }
        }

        /* 高级筛选面板 - 确保在正确层级且完全适应屏幕 */
        .advanced-filter-panel {
            position: fixed !important;
            top: 0 !important;
            right: -480px !important; /* 面板完全隐藏在屏幕右侧外部，根据新宽度调整 */
            width: 460px !important; /* 加大面板宽度 */
            height: 100vh !important; /* 完全适应视口高度 */
            max-height: 100vh !important; /* 确保不超过视口高度 */
            min-height: 100vh !important; /* 确保至少占满视口高度 */
            background-color: #f8f9fa;
            box-shadow: -5px 0 20px rgba(0,0,0,0.15);
            z-index: 1300 !important; /* 提高层级，确保在遮罩层之上 */
            transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex !important;
            flex-direction: column;
            border-radius: 0;
            overflow: hidden;
            /* 确保面板不受任何父容器影响 */
            margin: 0 !important;
            padding: 0 !important;
            /* 强制隐藏，防止任何样式冲突 */
            visibility: visible !important;
            opacity: 1 !important;
            /* 强制确保面板在屏幕外 */
            transform: translateX(0) !important;
            left: auto !important;
            bottom: 0 !important; /* 确保面板底部对齐屏幕底部 */
            /* 确保面板不受遮罩层影响 */
            pointer-events: auto !important;
        }

        .advanced-filter-panel.open {
            right: 0 !important; /* 面板完全显示 */
        }
        
        .filter-panel-header {
            padding: 22px 20px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
        }
        
        .filter-panel-header h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .filter-panel-header h3::before {
            content: 'category';
            font-family: 'Material Icons';
            margin-right: 12px;
            font-size: 24px;
        }
        
        .close-filter {
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
        }
        
        .close-filter:hover {
            transform: rotate(90deg);
            background: rgba(255,255,255,0.3);
        }
        
        .filter-panel-body {
            padding: 0;
            overflow-y: auto;
            flex: 1; /* 占据剩余空间 */
            background-color: #f8f9fa;
            height: calc(100vh - 80px); /* 减去头部高度，确保内容区域正确适应 */
            max-height: calc(100vh - 80px);
        }
        
        /* 分类树样式 */
        .category-tree {
            width: 100%;
            padding: 15px 12px;
            background-color: #f8f9fa;
        }
        
        .category-item {
            border-bottom: none;
            margin-bottom: 12px;
        }
        
        .category-item:last-child {
            margin-bottom: 0;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.04);
            margin: 0 4px;
            border: 1px solid rgba(0,0,0,0.03);
        }
        
        .category-header:hover {
            background-color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.07);
        }
        
        .category-header.active {
            background-color: #fff;
            border-left: 3px solid var(--primary-color);
        }
        
        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 10px;
            margin-right: 16px;
            object-fit: cover;
            background-color: #f5f5f5;
            border: 1px solid #eee;
            padding: 0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .category-header:hover .category-icon {
            transform: scale(1.05);
            border-color: var(--primary-light);
        }
        
        .category-name {
            flex: 1;
            font-size: 17px;
            color: #444;
            font-weight: 500;
        }
        
        .category-toggle {
            margin-left: 10px;
            color: #888;
            transition: transform 0.3s ease;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border-radius: 50%;
            font-size: 20px;
        }
        
        .category-header.expanded .category-toggle {
            transform: rotate(180deg);
            background-color: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .subcategory-list {
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background-color: transparent;
            padding: 0 6px;
        }
        
        .category-item.expanded .subcategory-list {
            max-height: 600px;
            margin-top: 12px;
        }
        
        .subcategory-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: none;
            background-color: #fff;
            border-radius: 10px;
            margin-bottom: 8px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.03);
            border: 1px solid rgba(0,0,0,0.02);
        }
        
        .subcategory-item:last-child {
            margin-bottom: 0;
        }
        
        .subcategory-item:hover {
            background-color: #fff;
            transform: translateX(3px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }
        
        .subcategory-item.active {
            background-color: #fff;
            border-left: 3px solid var(--primary-dark);
        }
        
        .subcategory-icon {
            width: 38px;
            height: 38px;
            border-radius: 8px;
            margin-right: 14px;
            object-fit: cover;
            background-color: white;
            border: 1px solid #eee;
            transition: all 0.3s ease;
            padding: 0;
        }
        
        .subcategory-item:hover .subcategory-icon {
            transform: scale(1.05);
            border-color: var(--primary-color);
            box-shadow: 0 2px 5px rgba(0,0,0,0.08);
        }
        
        .subcategory-name {
            font-size: 16px;
            color: #555;
            font-weight: 400;
        }
        
        .subcategory-arrow {
            margin-left: auto;
            opacity: 0.5;
            color: var(--primary-color);
            transition: opacity 0.2s ease, transform 0.2s ease;
            font-size: 16px;
        }
        
        .subcategory-item:hover .subcategory-arrow {
            opacity: 1;
            transform: translateX(3px);
        }
        
        /* 波纹动画效果 */
        .subcategory-item {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(64, 169, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(2.5);
                opacity: 0;
            }
        }
        
        /* 点击动画 */
        .subcategory-item.clicked {
            animation: item-clicked 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background-color: var(--primary-light);
        }
        
        @keyframes item-clicked {
            0% {
                transform: scale(0.95);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        /* 添加遮罩层 - 减少透明度，避免色差过于明显 */
        .filter-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.15); /* 大幅降低透明度，减少色差 */
            z-index: 1050; /* 确保在面板下方 */
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            margin: 0;
            padding: 0;
            pointer-events: auto;
        }

        .filter-backdrop.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 15px;
        }
        
        /* 加载状态优化 */
        .category-tree .loading-state {
            padding: 30px;
            background: #fff;
            margin: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        /* 媒体查询优化 */
        @media (max-width: 768px) {
            .advanced-filter-panel {
                width: 320px !important; /* 在小屏幕上也加大宽度 */
                right: -340px !important; /* 确保在小屏幕上也完全隐藏 */
                height: 100vh !important; /* 确保在小屏幕上也完全适应高度 */
                max-height: 100vh !important;
                min-height: 100vh !important;
            }
            
            .category-tree {
                padding: 10px 8px;
            }
            
            .category-header {
                padding: 15px 16px;
            }
            
            .subcategory-item {
                padding: 12px 16px;
            }
            
            .category-icon {
                width: 42px;
                height: 42px;
            }
            
            .subcategory-icon {
                width: 34px;
                height: 34px;
            }
        }
        
        @media (max-width: 576px) {
            .advanced-filter-panel {
                width: 350px !important; /* 在超小屏幕上也适当加大宽度 */
                right: -370px !important; /* 确保在超小屏幕上也完全隐藏 */
                height: 100vh !important; /* 确保在超小屏幕上也完全适应高度 */
                max-height: 100vh !important;
                min-height: 100vh !important;
            }
            
            .category-icon {
                width: 40px;
                height: 40px;
            }
            
            .subcategory-icon {
                width: 32px;
                height: 32px;
            }
            
            .category-name {
                font-size: 16px;
            }
            
            .subcategory-name {
                font-size: 14px;
            }
        }