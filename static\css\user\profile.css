:root {
            --primary-color: #40a9ff;
            --primary-light: #91d5ff;
            --primary-dark: #1890ff;
            --accent-color: #096dd9;
            --text-color: #333;
            --text-light: #666;
            --bg-color: #fff;
            --bg-light: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, var(--primary-light), var(--primary-color));
            --card-shadow: 0 8px 16px rgba(24, 144, 255, 0.08);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            --border-radius: 12px;
            --kawaii-shadow: 0 4px 15px rgba(24, 144, 255, 0.15);
            --kawaii-gradient: linear-gradient(135deg, #91d5ff, #40a9ff);
            --kawaii-border: 1px solid rgba(64, 169, 255, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemF<PERSON>, "Se<PERSON>e UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-light);
            line-height: 1.6;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            font-weight: 600;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 24px;
            background: var(--bg-gradient);
            box-shadow: 0 4px 20px rgba(64, 169, 255, 0.25);
            z-index: 1000;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            border-bottom: var(--kawaii-border);
        }
        
        .navbar:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.5rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .logo:hover {
            transform: scale(1.05);
        }
        
        .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.5rem;
            color: currentColor;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
        
        .nav-menu {
            display: flex;
            justify-content: center;
            list-style: none;
        }
        
        .nav-item {
            margin: 0 15px;
            position: relative;
        }
        
        .nav-link {
            color: white;
            font-weight: 600;
            position: relative;
            padding: 5px 0;
            transition: var(--transition);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: white;
            transition: width 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        
        .nav-link.active {
            font-weight: 700;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .nav-icons {
            display: flex;
            align-items: center;
        }
        
        .nav-icon {
            color: white;
            margin-left: 15px;
            cursor: pointer;
            transition: var(--transition);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .nav-icon:hover {
            transform: scale(1.15);
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        .mobile-menu-btn {
            position: relative;
            display: none;
            color: white;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }
        
        .mobile-menu-btn .fa-solid {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
        }
        
        .mobile-menu-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }
        
        /* 主内容区 */
        .main-content {
            margin-top: 80px;
            padding: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 20px;
        }
        
        /* 侧边栏 */
        .sidebar {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px 20px;
            position: sticky;
            top: 100px;
            height: fit-content;
            transition: box-shadow 0.3s ease;
            border: var(--kawaii-border);
        }
        
        .sidebar:hover {
            box-shadow: 0 12px 24px rgba(24, 144, 255, 0.12);
            transform: translateY(-5px);
        }
        
        .user-profile {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 25px 15px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.1);
            margin-bottom: 20px;
            background: linear-gradient(135deg, #f0f8ff, #e6f4ff);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .user-profile::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle at top right, rgba(64, 169, 255, 0.3), transparent 70%);
            z-index: 0;
        }
        
        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 15px;
            border: 3px solid var(--primary-light);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.3);
            position: relative;
            z-index: 1;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.3s ease;
        }
        
        .avatar:hover {
            transform: scale(1.08);
            box-shadow: 0 8px 20px rgba(64, 169, 255, 0.4);
        }
        
        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: filter 0.3s ease;
        }
        
        .avatar:hover img {
            filter: brightness(1.05);
        }
        
        .user-name {
            font-weight: 700;
            font-size: 1.3rem;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
            color: var(--primary-dark);
        }
        
        .user-email {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .user-level {
            background: var(--bg-gradient);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 3px 8px rgba(64, 169, 255, 0.2);
            position: relative;
            z-index: 1;
            transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.3s ease;
        }
        
        .user-level:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 5px 12px rgba(64, 169, 255, 0.3);
        }
        
        .sidebar-menu {
            list-style: none;
            margin-top: 10px;
        }
        
        .sidebar-item {
            margin-bottom: 8px;
        }
        
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 10px;
            color: var(--text-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0;
            background: linear-gradient(to right, rgba(64, 169, 255, 0.15), transparent);
            transition: width 0.3s ease;
            opacity: 0.5;
            z-index: 0;
        }
        
        .sidebar-link:hover {
            background-color: rgba(145, 213, 255, 0.1);
            transform: translateX(5px);
            color: var(--primary-dark);
        }
        
        .sidebar-link:hover::before {
            width: 100%;
        }
        
        .sidebar-link.active {
            background: linear-gradient(to right, rgba(145, 213, 255, 0.2), rgba(255, 255, 255, 0.9));
            color: var(--primary-dark);
            font-weight: 600;
            box-shadow: 0 3px 8px rgba(64, 169, 255, 0.15);
        }
        
        .sidebar-link i {
            margin-right: 12px;
            font-size: 20px;
            transition: transform 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .sidebar-link:hover i {
            transform: scale(1.15);
            color: var(--primary-dark);
        }
        
        .sidebar-link span:not(i) {
            position: relative;
            z-index: 1;
        }
        
        /* 主要内容容器 */
        .content-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .section-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 25px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            border: var(--kawaii-border);
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.5s ease forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section-card:hover {
            box-shadow: 0 12px 24px rgba(24, 144, 255, 0.12);
            transform: translateY(-5px);
        }
        
        .section-title {
            font-size: 1.3rem;
            margin-bottom: 25px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.1);
            display: flex;
            align-items: center;
            position: relative;
            color: var(--primary-dark);
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--bg-gradient);
            border-radius: 3px;
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
        }
        
        .section-title i {
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 24px;
        }
        
        /* 账户概览 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: linear-gradient(145deg, #ffffff, #f0f8ff);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 15px rgba(24, 144, 255, 0.08);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: var(--kawaii-border);
            animation: fadeInUp 0.5s ease forwards;
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 20px rgba(24, 144, 255, 0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--bg-gradient);
        }
        
        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 70px;
            height: 70px;
            background: radial-gradient(circle at top right, rgba(64, 169, 255, 0.15), transparent 70%);
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: var(--text-light);
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-dark);
            position: relative;
            z-index: 1;
            letter-spacing: 0.5px;
        }
        
        .balance-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .balance-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .balance-btn i {
            font-size: 18px;
            margin-right: 8px;
            transition: transform 0.3s ease;
        }
        
        .balance-btn:hover i {
            transform: scale(1.2);
        }
        
        .recharge-btn {
            background: var(--bg-gradient);
            color: white;
            flex: 1.5;
            box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
        }
        
        .recharge-btn:hover {
            background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(64, 169, 255, 0.4);
        }
        
        .withdraw-btn {
            background: white;
            color: var(--text-color);
            border: 1px solid #e0e0e0;
            flex: 1;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .withdraw-btn:hover {
            background-color: #f9f9f9;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.08);
        }
        
        /* 订单记录 */
        #orders {
            padding-bottom: 0 !important;
            overflow: visible;
        }

        .order-table-wrapper {
            margin: 0 -25px 0 -25px;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            overflow: hidden;
            box-shadow: none;
            border: var(--kawaii-border);
            border-top: 1px solid rgba(64, 169, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .order-table-container {
            background: white;
            overflow: hidden;
            max-height: none;
        }

        .order-table-container::-webkit-scrollbar {
            display: none;
        }

        .order-table-container {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .order-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            table-layout: fixed;
        }

        /* 设置各列的宽度 */
        .order-table th:nth-child(1),
        .order-table td:nth-child(1) {
            width: 20%; /* 订单号 */
        }

        .order-table th:nth-child(2),
        .order-table td:nth-child(2) {
            width: 35%; /* 商品名称 */
        }

        .order-table th:nth-child(3),
        .order-table td:nth-child(3) {
            width: 12%; /* 金额 */
        }

        .order-table th:nth-child(4),
        .order-table td:nth-child(4) {
            width: 12%; /* 日期 */
        }

        .order-table th:nth-child(5),
        .order-table td:nth-child(5) {
            width: 12%; /* 状态 */
        }

        .order-table th:nth-child(6),
        .order-table td:nth-child(6) {
            width: 9%; /* 操作 */
        }

        /* 商品名称列特殊处理 */
        .order-table td:nth-child(2) {
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: normal;
            line-height: 1.4;
            padding: 12px 8px;
            text-overflow: unset;
            overflow: visible;
            max-width: 0;
            min-width: 0;
        }
        
        .order-table th,
        .order-table td {
            padding: 15px 8px;
            text-align: center !important;
            border-bottom: 1px solid rgba(64, 169, 255, 0.08);
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .order-table th {
            font-weight: 600;
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background-color: rgba(64, 169, 255, 0.05);
        }
        
        .order-table th:first-child {
            border-top-left-radius: 10px;
        }
        
        .order-table th:last-child {
            border-top-right-radius: 10px;
        }
        
        .order-table tr:last-child td {
            border-bottom: none;
        }
        
        .order-table tr {
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            min-height: 60px;
        }

        .order-table tbody tr {
            height: auto;
        }
        
        .order-table tr:hover {
            background-color: rgba(64, 169, 255, 0.05);
            transform: translateY(-2px) scale(1.005);
            box-shadow: 0 5px 15px rgba(24, 144, 255, 0.08);
            position: relative;
            z-index: 1;
        }
        
        .order-id {
            color: var(--primary-dark);
            font-weight: 600;
            font-family: 'Montserrat', 'Noto Sans SC', sans-serif;
            transition: color 0.2s ease;
            text-align: center !important;
        }

        /* 强制所有表格内容居中 */
        .order-table td * {
            text-align: center !important;
        }

        .order-table .status-badge {
            display: inline-block;
            text-align: center !important;
        }

        .order-table .action-btn {
            display: inline-flex !important;
            margin: 0 auto;
        }
        
        .order-table tr:hover .order-id {
            color: var(--primary-color);
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 16px;
            font-size: 0.75rem;
            text-align: center;
            min-width: 60px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            white-space: nowrap;
        }
        
        .status-badge:hover {
            transform: scale(1.05);
        }
        
        .status-completed {
            background-color: #E8F5E9;
            color: #2E7D32;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.15);
        }
        
        .status-pending {
            background-color: #FFF8E1;
            color: #F57F17;
            box-shadow: 0 2px 8px rgba(245, 127, 23, 0.15);
        }
        
        .status-failed {
            background-color: #FFEBEE;
            color: #C62828;
            box-shadow: 0 2px 8px rgba(198, 40, 40, 0.15);
        }
        
        /* 强化操作按钮样式 */
        .action-btn {
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(64, 169, 255, 0.1);
            text-decoration: none;
            border: 2px solid var(--primary-light);
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.15);
        }
        
        .action-btn i {
            font-size: 18px;
            color: var(--primary-dark);
            transition: transform 0.3s ease;
        }
        
        .action-btn:hover {
            color: white;
            background-color: var(--primary-color);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.25);
            border-color: var(--primary-color);
        }
        
        .action-btn:hover i {
            color: white;
            transform: scale(1.2);
        }
        
        .no-orders {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-light);
            font-style: italic;
            background-color: rgba(64, 169, 255, 0.03);
            border-radius: 10px;
            margin: 20px 0;
            border: 1px dashed rgba(64, 169, 255, 0.2);
        }
        
        .view-all {
            display: block;
            text-align: center;
            margin-top: 25px;
            color: var(--primary-dark);
            font-weight: 600;
            padding: 12px;
            background-color: rgba(64, 169, 255, 0.08);
            border-radius: 10px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.1);
            border: var(--kawaii-border);
        }
        
        .view-all:hover {
            background-color: var(--primary-light);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.2);
        }

        /* 订单分页样式 */
        .order-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 20px;
            border-top: 1px solid rgba(64, 169, 255, 0.1);
        }

        .pagination-info {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-btn {
            width: 32px;
            height: 32px;
            border: 1px solid rgba(64, 169, 255, 0.2);
            background: white;
            color: var(--text-color);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-pages {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid rgba(64, 169, 255, 0.2);
            background: white;
            color: var(--text-color);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .page-btn:hover {
            background: var(--primary-light);
            color: white;
            border-color: var(--primary-light);
        }

        .page-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 查看更多按钮样式 */
        .order-actions {
            margin: 0;
            padding: 0;
        }

        .view-more-btn {
            width: 100%;
            padding: 12px 20px;
            background: rgba(64, 169, 255, 0.08);
            color: var(--primary-dark);
            border: none;
            border-top: 1px solid rgba(64, 169, 255, 0.1);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            margin: 0;
        }

        .view-more-btn:hover {
            background: rgba(64, 169, 255, 0.12);
            color: var(--primary-color);
            border-color: rgba(64, 169, 255, 0.3);
        }

        .view-more-btn i {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .view-more-btn.expanded i {
            transform: rotate(180deg);
        }
        
        /* 个人设置 */
        .form-group {
            margin-bottom: 18px;
            position: relative;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
            transition: color 0.3s ease;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid rgba(64, 169, 255, 0.1);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: rgba(64, 169, 255, 0.02);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 169, 255, 0.15);
            background-color: white;
        }
        
        .form-input:hover {
            border-color: rgba(64, 169, 255, 0.3);
        }
        
        .form-group:hover .form-label {
            color: var(--primary-dark);
        }
        
        .form-input::placeholder {
            color: #aaa;
        }
        
        .form-submit {
            padding: 10px 24px;
            background: var(--bg-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            margin-top: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
            float: right;
        }
        
        .form-submit:hover {
            background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(64, 169, 255, 0.4);
        }
        
        .form-submit:active {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.3);
        }
        
        .form-submit i {
            margin-right: 8px;
            transition: transform 0.3s ease;
        }
        
        .form-submit:hover i {
            transform: scale(1.2);
        }

        /* 对接中心特定样式 */
        #integration .form-group {
            margin-bottom: 15px;
        }

        /* 表单清除浮动 */
        #settings form::after {
            content: "";
            display: table;
            clear: both;
        }
        
        /* API 密钥操作按钮 */
        .reset-key-btn,
        .copy-key-btn,
        .copy-account-btn {
            position: relative;
            right: auto;
            top: auto;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            margin-left: 10px;
            min-width: 120px;
            justify-content: center;
            white-space: nowrap;
        }
        
        .reset-key-btn {
            background: var(--bg-gradient);
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.2);
        }

        .copy-key-btn,
        .copy-account-btn {
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            box-shadow: 0 3px 10px rgba(64, 169, 255, 0.2);
        }
        
        .reset-key-btn:hover,
        .copy-key-btn:hover,
        .copy-account-btn:hover {
            transform: translateY(-3px) scale(1.02);
        }
        
        .reset-key-btn:hover {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.3);
        }
        
        .copy-key-btn:hover,
        .copy-account-btn:hover {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.3);
        }
        
        .reset-key-btn i {
            font-size: 16px;
            margin-right: 5px;
            animation: spin 2s linear infinite paused;
        }
        
        .copy-key-btn i,
        .copy-account-btn i {
            font-size: 16px;
            margin-right: 5px;
            transition: transform 0.3s ease;
        }
        
        .reset-key-btn:hover i {
            animation-play-state: running;
        }
        
        .copy-key-btn:hover i,
        .copy-account-btn:hover i {
            transform: scale(1.5);
        }
        
        /* 主题切换 */
        .theme-toggle {
            display: none; /* 隐藏深色模式切换按钮 */
        }
        
        .toggle-label {
            display: none;
        }
        
        .toggle-switch {
            display: none;
        }
        
        /* 响应式设计优化 */
        @media (max-width: 992px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .sidebar {
                position: static;
                margin-bottom: 0;
                padding: 25px;
            }
            
            .user-profile {
                flex-direction: row;
                align-items: center;
                padding: 20px;
                border-bottom: none;
                margin-bottom: 5px;
            }
            
            .avatar {
                width: 80px;
                height: 80px;
                margin-bottom: 0;
                margin-right: 20px;
            }
            
            .user-info {
                flex: 1;
            }
            
            .sidebar-menu {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                margin-top: 15px;
            }
            
            .sidebar-item {
                margin-bottom: 0;
                flex: 1;
                min-width: 180px;
            }
        }
        
        @media (max-width: 768px) {
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                flex-direction: column;
                background-color: white;
                width: 100%;
                text-align: center;
                transition: var(--transition);
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-item {
                margin: 15px 0;
            }
            
            .nav-link {
                color: var(--text-color);
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .order-table-container {
                overflow-x: auto;
            }
            
            .sidebar-menu {
                flex-direction: column;
            }
            
            .sidebar-item {
                min-width: auto;
            }
        }
        
        @media (max-width: 480px) {
            .main-content {
                padding: 15px 10px;
            }
            
            .section-card {
                padding: 15px;
            }
            
            .balance-actions {
                flex-direction: column;
            }
            
            .balance-btn {
                width: 100%;
            }
            
            .user-profile {
                flex-direction: column;
                text-align: center;
            }
            
            .avatar {
                margin-right: 0;
                margin-bottom: 15px;
            }
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow: auto;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            box-shadow: 0 8px 30px rgba(24, 144, 255, 0.25);
            animation: modalFadeIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            border: var(--kawaii-border);
            transform: translateY(0);
        }
        
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .modal-header {
            padding: 20px 25px;
            background: var(--bg-gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-header h2 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .close:hover {
            transform: rotate(90deg);
        }
        
        .modal-body {
            padding: 20px 25px 15px 25px;
        }
        
        .modal-footer {
            padding: 15px 25px;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            border-top: 1px solid rgba(64, 169, 255, 0.1);
        }
        
        .cancel-btn {
            padding: 12px 20px;
            border: 1px solid rgba(64, 169, 255, 0.1);
            border-radius: 10px;
            background: white;
            color: var(--text-light);
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .cancel-btn:hover {
            background: #f9f5f8;
            transform: translateY(-2px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            border-color: rgba(64, 169, 255, 0.2);
        }
        
        .confirm-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            background: var(--bg-gradient);
            color: white;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
        }
        
        .confirm-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(64, 169, 255, 0.4);
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
        }
        
        /* 充值模态框支付方式样式 */
        .payment-nav {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(64, 169, 255, 0.2);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .payment-nav::-webkit-scrollbar {
            display: none;
        }

        .payment-nav-item {
            flex-shrink: 0;
            padding: 12px 20px;
            margin-right: 5px;
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: var(--text-light);
            background-color: transparent;
            border: none;
            position: relative;
        }

        .payment-nav-item:hover {
            color: var(--primary-color);
            background-color: rgba(64, 169, 255, 0.05);
        }

        .payment-nav-item.active {
            color: var(--primary-color);
            background-color: rgba(64, 169, 255, 0.1);
            font-weight: 600;
        }

        .payment-nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--primary-color);
        }

        .payment-content {
            min-height: 120px;
        }

        .payment-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .payment-option {
            position: relative;
            cursor: pointer;
        }

        .payment-input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .payment-label {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background-color: white;
            gap: 15px;
        }

        .payment-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(64, 169, 255, 0.15);
        }

        .payment-input:checked + .payment-label {
            border-color: var(--primary-color);
            background-color: rgba(64, 169, 255, 0.05);
            box-shadow: 0 0 0 3px rgba(64, 169, 255, 0.2);
        }

        .payment-icon {
            width: 45px;
            height: 45px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .payment-details {
            flex: 1;
        }

        .payment-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }
        
        /* 动画效果 */
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.05);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.7;
            }
        }
        
        /* 页脚 */
        footer {
            background-color: var(--bg-color);
            padding: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 -4px 12px rgba(64, 169, 255, 0.08);
            margin-top: 30px;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
            opacity: 0.8;
        }
        
        .footer-content {
            position: relative;
            z-index: 1;
        }
        
        .footer-content .logo {
            display: inline-flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.6rem;
            color: var(--primary-dark);
            margin-bottom: 16px;
        }
        
        .footer-content .logo .fa-solid {
            margin-right: 10px;
            font-size: 1.8rem;
        }
        
        .copyright {
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--text-light);
            letter-spacing: 0.5px;
        }