:root {
            --primary-color: #40a9ff;
            --primary-dark: #1890ff;
            --primary-light: #91d5ff;
            --secondary-color: #f0f8ff;
            --text-color: #333;
            --shadow-color: rgba(64, 169, 255, 0.3);
            --success-color: #42b983;
            --error-color: #ff6b6b;
            --border-radius: 12px;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: var(--secondary-color);
            background-image: linear-gradient(135deg, #f8fcff 25%, #f0f8ff 25%, #f0f8ff 50%, #f8fcff 50%, #f8fcff 75%, #f0f8ff 75%, #f0f8ff 100%);
            background-size: 40px 40px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: background-color 0.5s ease;
        }
        
        .login-container {
            background-color: #ffffff;
            border-radius: var(--border-radius);
            box-shadow: 0 15px 35px var(--shadow-color);
            overflow: hidden;
            width: 100%;
            max-width: 950px;
            display: flex;
            min-height: 600px;
            position: relative;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: box-shadow 0.3s ease;
        }
        
        .login-container:hover {
            box-shadow: 0 20px 40px var(--shadow-color);
        }
        
        .form-container {
            width: 50%;
            padding: 40px;
            transition: transform 0.6s ease-in-out, opacity 0.5s ease;
            position: relative;
            z-index: 1;
        }
        
        .image-container {
            background: linear-gradient(135deg, var(--primary-color), #91d5ff);
            width: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: inset -5px 0 15px rgba(0,0,0,0.05);
        }
        
        .image-container::before {
            content: '';
            position: absolute;
            width: 200%;
            height: 200%;
            background: url('{% static "images/pattern.svg" %}') repeat;
            opacity: 0.1;
            animation: rotate 120s linear infinite;
        }
        
        /* 添加浮动图形元素 */
        .image-container::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            transform: translate(-50%, -50%);
            border-radius: 50%;
            animation: pulse 8s infinite alternate ease-in-out;
            z-index: 0;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
            100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
        }
        
        .image-container h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-weight: 700;
        }
        
        .image-container p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
            max-width: 80%;
            line-height: 1.6;
            text-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: var(--text-color);
            font-weight: 700;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        
        .form-control {
            height: 54px;
            border-radius: var(--border-radius);
            border: 2px solid #eaeaea;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.02);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
            transform: translateY(-2px);
        }
        
        .form-label {
            position: absolute;
            top: 15px;
            left: 15px;
            color: #999;
            transition: all 0.3s ease;
            pointer-events: none;
            background-color: #fff;
            padding: 0 5px;
            z-index: 1;
            font-size: 1rem;
        }
        
        .form-control:focus ~ .form-label,
        .form-control:not(:placeholder-shown) ~ .form-label {
            top: -12px;
            left: 10px;
            font-size: 0.8rem;
            color: var(--primary-color);
            background-color: #fff;
            padding: 0 8px;
            z-index: 2;
            font-weight: 600;
        }
        
        /* 特别处理验证码输入框 */
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 15px;
            width: 100%;
        }
        
        .captcha-container .position-relative {
            flex: 1;
            width: 100%;
            min-width: 0;
        }
        
        .captcha-image {
            background-color: #f8f8f8;
            border-radius: var(--border-radius);
            height: 54px;
            width: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #eaeaea;
        }
        
        .captcha-image:hover {
            box-shadow: 0 5px 12px rgba(0,0,0,0.12);
            transform: translateY(-3px);
        }
        
        .btn {
            height: 54px;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn:active::after {
            animation: ripple 0.6s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(30, 30);
                opacity: 0;
            }
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 4px 10px var(--shadow-color);
        }
        
        .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            box-shadow: 0 6px 15px var(--shadow-color);
            transform: translateY(-2px);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active, .btn-outline-primary.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 5px 15px var(--shadow-color);
            transform: translateY(-2px);
        }
        
        /* 覆盖Bootstrap默认蓝色焦点效果 */
        .btn-outline-primary:focus, .btn-outline-primary:active:focus {
            box-shadow: 0 0 0 0.25rem var(--shadow-color);
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-outline-primary:active {
            background-color: #1890ff !important;
            border-color: #1890ff !important;
            transform: translateY(1px) !important;
        }
        
        /* 覆盖表单控件的蓝色焦点 */
        .form-control:focus, .form-check-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem var(--shadow-color);
        }
        
        /* 覆盖复选框的颜色 */
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
            border-color: #f0f0f0;
        }
        
        .btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-secondary.active {
            background-color: #e0e0e0;
            border-color: #e0e0e0;
            color: #666;
        }
        
        #login-form, #register-form {
            display: flex;
            flex-direction: column;
            height: 100%;
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        #login-form.inactive, #register-form.inactive {
            opacity: 0;
            transform: translateY(10px);
        }
        
        .password-strength {
            height: 6px;
            margin-top: 8px;
            border-radius: 10px;
            transition: all 0.3s ease;
            background-color: #f0f0f0;
            overflow: hidden;
        }
        
        .strength-text {
            font-size: 0.8rem;
            margin-top: 6px;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .fade-in {
            animation: fadeIn 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 4px 10px var(--shadow-color); }
            50% { transform: scale(1.03); box-shadow: 0 8px 18px var(--shadow-color); }
            100% { transform: scale(1); box-shadow: 0 4px 10px var(--shadow-color); }
        }
        
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                max-width: 500px;
                min-height: auto;
            }
            
            .form-container, .image-container {
                width: 100%;
            }
            
            .image-container {
                order: -1;
                padding: 30px 20px;
                min-height: 180px;
                border-radius: var(--border-radius) var(--border-radius) 0 0;
            }
            
            .form-container {
                padding: 30px 20px;
            }
            
            .image-container h2 {
                font-size: 1.8rem;
                margin-bottom: 10px;
            }
            
            .image-container p {
                font-size: 1rem;
                margin-bottom: 15px;
            }
            
            .captcha-container {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }
            
            .captcha-image {
                width: 100%;
                max-width: 200px;
                margin: 0 auto;
            }
            
            .form-group {
                margin-bottom: 20px;
            }
            
            h1 {
                font-size: 1.8rem;
                margin-bottom: 20px;
            }
            
            .notification {
                max-width: calc(100% - 40px);
                left: 20px;
                right: 20px;
                transform: translateY(-100%);
            }
            
            .notification.show {
                transform: translateY(0);
            }
        }
        
        /* 小屏幕手机优化 */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .login-container {
                box-shadow: 0 10px 25px var(--shadow-color);
            }
            
            .form-container {
                padding: 25px 15px;
            }
            
            .image-container {
                padding: 25px 15px;
                min-height: 150px;
            }
            
            .image-container h2 {
                font-size: 1.5rem;
            }
            
            .image-container p {
                font-size: 0.9rem;
                margin-bottom: 10px;
            }
            
            .btn {
                height: 48px;
                font-size: 0.9rem;
            }
            
            .form-control {
                height: 48px;
                font-size: 0.95rem;
            }
            
            .captcha-image {
                height: 48px;
            }
            
            .input-group button {
                min-width: 100px;
                font-size: 0.8rem;
            }
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            max-width: 320px;
            transform: translateX(120%);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 1200;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success {
            border-left: 4px solid var(--success-color);
            background: linear-gradient(to right, rgba(66, 185, 131, 0.05), transparent);
        }

        .notification.error {
            border-left: 4px solid var(--error-color);
            background: linear-gradient(to right, rgba(255, 107, 107, 0.05), transparent);
        }

        .notification-icon {
            margin-right: 15px;
            font-size: 20px;
        }

        .notification.success .notification-icon {
            color: var(--success-color);
        }

        .notification.error .notification-icon {
            color: var(--error-color);
        }

        #notificationMessage {
            font-size: 14px;
            color: var(--text-color);
            font-weight: 500;
        }

        /* 忘记密码表单样式 */
        #forgot-form {
            display: none;
            flex-direction: column;
            height: 100%;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        #forgot-form.active {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }

        /* 输入框图标样式 */
        .input-icon {
            position: relative;
            width: 100%;
        }
        
        .input-icon i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            transition: all 0.3s ease;
            z-index: 2;
        }
        
        .input-icon .form-control {
            padding-left: 45px;
        }
        
        /* 解决图标与提示文本重叠问题 */
        .input-icon .form-label {
            left: 45px; /* 将标签向右移动，避开图标 */
        }
        
        .input-icon .form-control:focus ~ .form-label,
        .input-icon .form-control:not(:placeholder-shown) ~ .form-label {
            left: 10px; /* 当输入框获得焦点或有输入时，标签移回左侧 */
        }
        
        .input-icon .form-control:focus ~ i,
        .input-icon .form-control:not(:placeholder-shown) ~ i {
            color: var(--primary-color);
        }
        
        /* 修复邮箱验证码输入框边框问题 */
        .input-group {
            position: relative;
            display: flex;
            flex-wrap: nowrap;
            align-items: stretch;
            width: 100%;
            border-radius: var(--border-radius);
            background-color: #fff;
        }
        
        .input-group .input-icon {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
        }
        
        .input-group .input-icon .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            width: 100%;
        }
        
        .input-group .form-label {
            background-color: #fff;
            z-index: 3;
        }
        
        .input-group button {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            white-space: nowrap;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
            margin-left: -1px;
        }
        
        /* 确保表单聚焦时标签正确显示 */
        .input-group .form-control:focus ~ .form-label,
        .input-group .form-control:not(:placeholder-shown) ~ .form-label {
            background-color: #fff;
            padding: 0 8px;
            top: -12px;
            left: 10px;
        }
        
        /* 链接效果 */
        .link-effect {
            position: relative;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .link-effect::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: var(--primary-color);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }
        
        .link-effect:hover {
            color: var(--primary-dark) !important;
        }
        
        .link-effect:hover::after {
            transform: scaleX(1);
            transform-origin: left;
        }

        /* 添加初始隐藏类 */
        #register-form {
            display: none;
        }