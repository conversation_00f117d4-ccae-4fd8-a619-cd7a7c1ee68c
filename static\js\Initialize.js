// 配置信息
        const config = {
            apiBaseUrl: 'http://localhost:8000',
            debug: true
        };

        const customModal = document.getElementById('customModal');
        const confirmBtn = document.getElementById('modalConfirmBtn');
        const cancelBtn = document.getElementById('modalCancelBtn');
        let initializationSuccessful = false;
        let currentUserId = null;

        confirmBtn.addEventListener('click', function () {
            customModal.classList.remove('show');
            setTimeout(function () {
                window.handleReConfig();
            }, 100);
        });

        cancelBtn.addEventListener('click', function () {
            customModal.classList.remove('show');
            setTimeout(function () {
                window.handleCancel();
            }, 100);
        });

        document.addEventListener('DOMContentLoaded', async () => {
            let shouldAllowRedirect = true;

            const steps = document.querySelectorAll('.step');
            const formSteps = document.querySelectorAll('.form-step');
            const prevBtn = document.querySelector('.btn-secondary');
            const nextBtn = document.querySelector('.btn-primary');
            const loadingOverlay = document.querySelector('.loading-overlay');
            let currentStep = 0;

            // 生成用户ID，用于加密通信
            currentUserId = CommunicationModule.generateUserId();

            window.handleReConfig = function () {
                clearDatabaseConfiguration();
            };

            window.handleCancel = function () {
                shouldAllowRedirect = true;
                showSuccessModal(true);
            };

            await checkSystemInitialization();

            const dataBindings = {
                dbHost: 'previewHost',
                dbName: 'previewName',
                adminAccount: 'previewAccount'
            };

            Object.entries(dataBindings).forEach(([inputId, previewId]) => {
                const input = document.getElementById(inputId);
                const preview = document.getElementById(previewId);
                input.addEventListener('input', () => {
                    preview.textContent = input.value.trim() || '-';
                });
            });

            // 添加单选按钮组更改事件监听
            document.querySelectorAll('input[name="dbEncode"]').forEach(radio => {
                radio.addEventListener('change', () => {
                    if (radio.checked) {
                        document.getElementById('previewEncode').textContent = radio.value;
                    }
                });
            });
            // 初始化选中的编码值
            document.getElementById('previewEncode').textContent = document.querySelector('input[name="dbEncode"]:checked').value;

            const validationRules = {
                dbHost: value => !!value.trim(),
                dbName: value => !!value.trim(),
                dbUser: value => !!value.trim(),
                dbPass: value => !!value.trim(),
                adminAccount: value => {
                    const account = value.trim();
                    return !!account && !['admin', 'Admin'].includes(account);
                },
                adminPass: value => value.length >= 8,
                confirmPass: value => {
                    const pass = document.getElementById('adminPass').value;
                    return value === pass && value.length >= 8;
                }
            };

            function validateField(input) {
                // 跳过单选按钮的验证
                if (input.type === 'radio') {
                    return true;
                }

                // 确保input.id存在于validationRules对象中
                if (!validationRules[input.id]) {
                    return true; // 如果没有规则，默认视为有效
                }

                const isValid = validationRules[input.id](input.value);
                const errorSpan = input.nextElementSibling;

                input.classList.toggle('error', !isValid);
                errorSpan.classList.toggle('show', !isValid);
                errorSpan.textContent = getErrorMessage(input.id, isValid);

                return isValid;
            }

            function getErrorMessage(id) {
                const messages = {
                    confirmPass: '两次密码不一致',
                    adminPass: '至少8位字符',
                    adminAccount: document.getElementById('adminAccount').value.trim().toLowerCase() === 'admin'
                        ? 'Admin不允许作为账号'
                        : '必填字段',
                    default: '必填字段'
                };
                return messages[id] || messages.default;
            }

            function validateCurrentStep() {
                const inputs = [...formSteps[currentStep].querySelectorAll('input:not([type="radio"])')];
                return inputs.every(input => {
                    const isValid = validateField(input);
                    if (!isValid) input.focus();
                    return isValid;
                });
            }

            function updateStep(step) {
                currentStep = Math.max(0, Math.min(step, steps.length - 1));

                steps.forEach((s, i) => s.classList.toggle('active', i === currentStep));

                formSteps.forEach((form, i) => {
                    form.style.display = i === currentStep ? 'block' : 'none';
                });

                prevBtn.disabled = currentStep === 0;
                nextBtn.textContent = currentStep === steps.length - 1 ? '完成' : '下一步';
            }

            nextBtn.addEventListener('click', async () => {
                if (!validateCurrentStep()) {
                    return;
                }


                if (currentStep < steps.length - 1) {
                    updateStep(currentStep + 1);
                } else {
                    const formData = {
                        operation: "initialize_database",
                        data: {
                            database_address: document.getElementById('dbHost').value,
                            database_name: document.getElementById('dbName').value,
                            database_user: document.getElementById('dbUser').value,
                            database_password: document.getElementById('dbPass').value,
                            database_encode: document.querySelector('input[name="dbEncode"]:checked').value,
                            administrator_account: document.getElementById('adminAccount').value,
                            administrator_password: document.getElementById('adminPass').value
                        }
                    };

                    loadingOverlay.style.display = 'flex';

                    try {
                        // 初始化加密通信
                        const communicationInitialized = await CommunicationModule.initCommunication(currentUserId, {
                            logCallback: (message, type) => {
                            }
                        });

                        if (!communicationInitialized) {
                            throw new Error("通信初始化失败，无法安全提交数据");
                        }

                        // 使用RC4加密数据
                        const encryptedData = CommunicationModule.encryptData(formData, currentUserId);

                        // 发送加密数据
                        const response = await fetch(config.apiBaseUrl + '/api/admin', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-User-ID': currentUserId
                            },
                            body: JSON.stringify({
                                data: encryptedData
                            })
                        });

                        if (response.ok) {
                            const encryptedResult = await response.json();

                            // 解密响应数据
                            const result = encryptedResult.data ?
                                CommunicationModule.decryptData(encryptedResult.data, currentUserId) :
                                encryptedResult;

                            loadingOverlay.style.display = 'none';
                            initializationSuccessful = true;
                            shouldAllowRedirect = true;
                            showSuccessModal(true);
                        } else {
                            const errorResponse = await response.json();
                            const errorData = errorResponse.data ?
                                CommunicationModule.decryptData(errorResponse.data, currentUserId) :
                                errorResponse;

                            loadingOverlay.style.display = 'none';

                            if (response.status === 400) {
                                showCustomModal(
                                    '系统已初始化',
                                    '系统已经完成初始化配置。是否要清空现有配置并重新设置？',
                                    '是',
                                    '否',
                                    async function () {
                                        try {
                                            await clearDatabaseConfiguration();
                                        } catch (err) {
                                            showAlert("清空配置出错: " + err.message);
                                        }
                                    },
                                    null
                                );
                            } else {
                                showAlert('初始化失败: ' + (errorData.message || '服务器错误'));
                            }
                        }
                    } catch (error) {
                        loadingOverlay.style.display = 'none';
                        showAlert('连接服务器失败: ' + error.message);
                    }
                }
            });

            prevBtn.addEventListener('click', () => updateStep(currentStep - 1));

            document.querySelectorAll('input').forEach(input => {
                input.addEventListener('blur', () => validateField(input));
                input.addEventListener('input', () => validateField(input));
            });

            function showSuccessModal(autoRedirect = true) {
                const modal = document.querySelector('.success-modal');
                modal.classList.add('show');
                if (autoRedirect && shouldAllowRedirect) {
                    setTimeout(() => {
                        window.location.href = '/Admin/logon.html';
                    }, 3500);
                }
            }

            window.closeSuccessModal = function () {
                document.querySelector('.success-modal').classList.remove('show');
                if (shouldAllowRedirect &&
                    document.querySelector('.success-modal').getAttribute('data-auto-redirect') !== 'false') {
                    window.location.href = '/Admin/logon.html';
                }
            }

            function showCustomModal(title, message, confirmText, cancelText, onConfirm, onCancel) {
                const modal = document.getElementById('customModal');
                const modalTitle = document.getElementById('modalTitle');
                const modalMessage = document.getElementById('modalMessage');
                const confirmBtn = document.getElementById('modalConfirmBtn');
                const cancelBtn = document.getElementById('modalCancelBtn');

                modalTitle.textContent = title;
                modalMessage.textContent = message;

                if (cancelText) {
                    cancelBtn.textContent = cancelText;
                    cancelBtn.style.display = 'block';
                    cancelBtn.setAttribute('data-callback', onCancel ? 'true' : 'false');
                } else {
                    cancelBtn.style.display = 'none';
                }

                if (confirmText) {
                    confirmBtn.textContent = confirmText;
                    confirmBtn.style.display = 'block';
                    confirmBtn.setAttribute('data-callback', onConfirm ? 'true' : 'false');
                } else {
                    confirmBtn.style.display = 'none';
                }

                modal.classList.add('show');
            }

            async function clearDatabaseConfiguration() {
                if (clearDatabaseConfiguration.isRunning) {
                    return;
                }
                clearDatabaseConfiguration.isRunning = true;

                try {
                    loadingOverlay.style.display = 'flex';

                    // 确保通信初始化
                    const communicationInitialized = await CommunicationModule.initCommunication(currentUserId, {
                        logCallback: (message, type) => {
                        }
                    });

                    if (!communicationInitialized) {
                        throw new Error("通信初始化失败，无法安全提交数据");
                    }

                    // 使用RC4加密数据
                    const encryptedData = CommunicationModule.encryptData({
                        operation: "clear_database",
                        data: {}
                    }, currentUserId);

                    const response = await fetch(config.apiBaseUrl + '/api/admin', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-User-ID': currentUserId
                        },
                        body: JSON.stringify({
                            data: encryptedData
                        })
                    });

                    const encryptedResult = await response.json();
                    const result = encryptedResult.data ?
                        CommunicationModule.decryptData(encryptedResult.data, currentUserId) :
                        encryptedResult;

                    loadingOverlay.style.display = 'none';
                    clearDatabaseConfiguration.isRunning = false;

                    if (response.ok) {
                        showAlert("配置已清空，您可以重新进行初始化设置。");

                        // 重置初始化状态
                        initializationSuccessful = false;

                        document.querySelectorAll('input[type="text"], input[type="password"]').forEach(input => {
                            input.value = '';
                        });

                        updateStep(0);
                    } else {
                        throw new Error("服务器返回错误: " + (result.message || "未知错误"));
                    }
                } catch (error) {
                    loadingOverlay.style.display = 'none';
                    clearDatabaseConfiguration.isRunning = false;
                    showAlert('清空配置失败: ' + error.message);
                }
            }

            async function checkSystemInitialization() {
                if (initializationSuccessful) {
                    return;
                }

                try {
                    loadingOverlay.style.display = 'flex';

                    // 尝试初始化通信 - 不允许降级为非加密通信
                    try {
                        const communicationInitialized = await CommunicationModule.initCommunication(currentUserId, {
                            logCallback: (message, type) => {
                            }
                        });

                        if (!communicationInitialized) {
                            throw new Error("通信初始化失败");
                        }

                        // 使用加密通信
                        const encryptedData = CommunicationModule.encryptData({
                            operation: "get_initialization_status",
                            data: {}
                        }, currentUserId);
                        const response = await fetch(config.apiBaseUrl + '/api/admin', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-User-ID': currentUserId
                            },
                            body: JSON.stringify({
                                data: encryptedData
                            })
                        });

                        const encryptedResult = await response.json();
                        const result = encryptedResult.data ?
                            CommunicationModule.decryptData(encryptedResult.data, currentUserId) :
                            encryptedResult;

                        loadingOverlay.style.display = 'none';

                        if (result.success && result.initialized) {
                            shouldAllowRedirect = false;

                            showCustomModal(
                                '系统已初始化',
                                '系统已经完成初始化配置。是否要清空现有配置并重新设置？',
                                '重新配置',
                                '取消',
                                true,
                                true
                            );
                        }
                    } catch (commError) {
                        loadingOverlay.style.display = 'none';
                        showAlert('无法建立安全通信连接，请检查网络或联系管理员。');
                        return;
                    }
                } catch (error) {
                    loadingOverlay.style.display = 'none';
                    showAlert('系统初始化检查失败: ' + error.message);
                }
            }

            function showAlert(message) {
                showCustomModal('提示', message, '确定', null, null, null);
            }

            updateStep(0);
        });