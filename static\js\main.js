document.addEventListener("DOMContentLoaded", function () {
    // 页面缓存与状态
    const pageCache = new Map();
    let currentPage = '';
    const pageContent = document.getElementById("page-content");
    const menuToggle = document.getElementById("menu-toggle");
    const sidebar = document.querySelector(".sidebar");
    const content = document.querySelector(".content");
    const navList = document.querySelector('.nav-list');
    const CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟缓存过期时间
    const MAX_CACHE_SIZE = 20; // 最大缓存条目数
    let progressInterval;
    
    // 性能监控对象
    const performance = {
        metrics: {},
        startTime: null,
        mark: function(name) {
            this.metrics[name] = Date.now();
        },
        measure: function(name, startMark, endMark) {
            if (this.metrics[startMark] && this.metrics[endMark]) {
                return this.metrics[endMark] - this.metrics[startMark];
            }
            return null;
        },
        startPageLoad: function() {
            this.startTime = Date.now();
            this.mark('loadStart');
        },
        endPageLoad: function() {
            this.mark('loadEnd');
            return this.measure('pageLoad', 'loadStart', 'loadEnd');
        },
        reset: function() {
            this.metrics = {};
            this.startTime = null;
        }
    };

    // 用户设置管理
    const userPreferences = {
        storageKey: 'sakura_mall_preferences',
        defaults: {
            sidebarCollapsed: false,
            cacheEnabled: true
        },
        settings: {},
        
        init: function() {
            try {
                const stored = localStorage.getItem(this.storageKey);
                this.settings = stored ? JSON.parse(stored) : {};
                // 合并默认设置
                this.settings = {...this.defaults, ...this.settings};
            } catch (e) {
                this.settings = {...this.defaults};
            }
            this.applySettings();
            return this.settings;
        },
        
        save: function() {
            try {
                localStorage.setItem(this.storageKey, JSON.stringify(this.settings));
            } catch (e) {
            }
        },
        
        set: function(key, value) {
            this.settings[key] = value;
            this.save();
            this.applySettings();
        },
        
        get: function(key) {
            return this.settings[key];
        },
        
        applySettings: function() {
            // 应用侧边栏状态
            if (this.settings.sidebarCollapsed) {
                document.body.classList.add('sidebar-collapsed');
            } else {
                document.body.classList.remove('sidebar-collapsed');
            }
        }
    };

    // 路由历史管理
    function handleRouteChange(page) {
        window.history.pushState({ page }, '', `#${page}`);
        loadPage(page);
        setActiveMenu(page);
        currentPage = page;
    }

    // 初始化
    function initialize() {
        // 初始化用户设置
        userPreferences.init();
        
        // 设置全局错误处理
        setupErrorHandling();
        
        // 检查URL哈希值
        const hash = window.location.hash.replace('#', '');
        const defaultPage = hash || 'pages/home.html';
        
        // 添加popstate事件监听
        window.addEventListener('popstate', (event) => {
            if (event.state?.page) {
                loadPage(event.state.page);
                setActiveMenu(event.state.page);
                currentPage = event.state.page;
            } else {
                loadPage(defaultPage);
                setActiveMenu(defaultPage);
                currentPage = defaultPage;
            }
        });
        
        loadPage(defaultPage);
        currentPage = defaultPage;
        setActiveMenu(defaultPage);
        adaptSidebar();
        
        // 定期清理缓存
        setInterval(cleanupCache, 15 * 60 * 1000); // 每15分钟清理一次
    }
    
    // 设置全局错误处理
    function setupErrorHandling() {
        window.addEventListener('error', function(event) {
            // 可以在这里添加错误上报逻辑
            const errorInfo = {
                message: event.message,
                source: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.stack : null,
                url: window.location.href,
                timestamp: new Date().toISOString()
            };
            
            // 在生产环境中可以将错误信息发送到服务器
            if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
                // 示例: 将错误信息发送到服务器
                // sendErrorToServer(errorInfo);
            }
            
            return false;
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            // 可以在这里添加Promise错误上报逻辑
            return false;
        });
    }
    
    // 清理过期或超量的缓存
    function cleanupCache() {
        // 清理过期缓存
        const now = Date.now();
        for (const [key, value] of pageCache.entries()) {
            if (now - value.timestamp > CACHE_EXPIRY) {
                pageCache.delete(key);
            }
        }
        
        // 如果缓存条目数超过限制，删除最旧的条目
        if (pageCache.size > MAX_CACHE_SIZE) {
            const entries = Array.from(pageCache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const entriesToDelete = entries.slice(0, entries.length - MAX_CACHE_SIZE);
            entriesToDelete.forEach(entry => {
                pageCache.delete(entry[0]);
            });
        }
    }

    // 动态加载页面内容
    async function loadPage(url, retryCount = 0) {
        if (!url) return;
        
        // 开始性能计时
        performance.reset();
        performance.startPageLoad();
        
        // 显示加载动画
        showLoading();
        
        // 添加页面退出动画
        pageContent.classList.add('page-exit');
        
        try {
            let html;
            // 只有当用户设置启用缓存时才使用缓存
            if (userPreferences.get('cacheEnabled') && pageCache.has(url)) {
                const cachedData = pageCache.get(url);
                // 检查缓存是否过期
                if (Date.now() - cachedData.timestamp < CACHE_EXPIRY) {
                    html = cachedData.html;
                    performance.mark('cacheHit');
                } else {
                    // 缓存已过期，重新请求
                    performance.mark('cacheMiss');
                    const resp = await fetch(url);
                    if (!resp.ok) throw new Error('页面加载失败');
                    html = await resp.text();
                    pageCache.set(url, { html, timestamp: Date.now() });
                }
            } else {
                performance.mark('fetchStart');
                const resp = await fetch(url);
                if (!resp.ok) throw new Error('页面加载失败');
                html = await resp.text();
                performance.mark('fetchEnd');
                
                // 只有当用户设置启用缓存时才缓存页面
                if (userPreferences.get('cacheEnabled')) {
                    pageCache.set(url, { html, timestamp: Date.now() });
                }
            }
            
            // 延迟一小段时间以确保过渡动画完成
            setTimeout(() => {
                pageContent.innerHTML = html;

                // 添加页面进入动画
                pageContent.classList.remove('page-exit');
                pageContent.classList.add('page-enter');

                setTimeout(() => {
                    pageContent.classList.remove('page-enter');
                }, 300);

                pageContent.scrollTop = 0;
                window.scrollTo(0, 0);

                // 确保DOM完全渲染后再执行脚本
                setTimeout(() => {
                    executeInlineScripts(pageContent);
                }, 100);

                // 记录性能指标
                const loadTime = performance.endPageLoad();
                if (loadTime) {
                }
            }, 150);
            
        } catch (e) {
            
            if (retryCount < 2) {
                // 自动重试
                setTimeout(() => loadPage(url, retryCount + 1), 1000);
            } else {
                // 显示错误界面与重试按钮
                pageContent.innerHTML = `
                    <div style="padding:2em;text-align:center;">
                        <div style="color:#f00;margin-bottom:1em;">页面加载失败</div>
                        <p style="color:#666;margin-bottom:1em;">错误信息: ${e.message}</p>
                        <button class="retry-button" style="padding:8px 16px;background:#ff7eb9;color:white;border:none;border-radius:4px;cursor:pointer;">
                            重新加载
                        </button>
                    </div>
                `;
                // 添加重试按钮事件
                document.querySelector('.retry-button').addEventListener('click', () => {
                    loadPage(url, 0);
                });
            }
        } finally {
            hideLoading();
        }
    }

    // 执行内容区内联JS
    function executeInlineScripts(container) {
        const scripts = container.querySelectorAll('script');
        scripts.forEach(oldScript => {
            const newScript = document.createElement('script');
            Array.from(oldScript.attributes).forEach(attr => {
                newScript.setAttribute(attr.name, attr.value);
            });
            newScript.textContent = oldScript.textContent;
            oldScript.parentNode.replaceChild(newScript, oldScript);
        });
    }

    // 设置菜单高亮
    function setActiveMenu(pageUrl) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        const target = document.querySelector(`.nav-item[data-page='${pageUrl}']`);
        if (target) {
            target.classList.add('active');
            const parent = target.closest('.has-dropdown');
            if (parent) parent.classList.add('active');
        }
    }

    // 侧边栏自适应
    function adaptSidebar() {
        if (window.innerWidth < 769) {
            sidebar.classList.add('active');
        } else {
            sidebar.classList.remove('active');
        }
        adjustContentLayout();
    }

    // 内容区自适应
    function adjustContentLayout() {
        const topBar = document.querySelector('.top-bar');
        const topBarHeight = topBar ? topBar.offsetHeight : 60;
        content.style.marginTop = `${topBarHeight}px`;
        content.style.height = `calc(100vh - ${topBarHeight}px)`;
        if (window.innerWidth >= 769 && !sidebar.classList.contains('active')) {
            content.style.marginLeft = '200px';
            content.style.width = 'calc(100% - 200px)';
        } else {
            content.style.marginLeft = '0';
            content.style.width = '100%';
        }
    }

    // 显示加载动画
    function showLoading() {
        // 添加进度条
        const nprogress = document.getElementById('nprogress');
        let width = 0;
        nprogress.style.width = '0%';
        
        clearInterval(progressInterval); // 清除可能存在的旧计时器
        progressInterval = setInterval(() => {
            width += (100 - width) * 0.1;
            if (width > 90) width = 90; // 限制最大宽度
            nprogress.style.width = `${width}%`;
        }, 200);
        
        // 添加骨架屏
        pageContent.innerHTML = `
            <div class="skeleton" style="height:60px;width:100%;"></div>
            <div class="skeleton" style="height:300px;width:100%;"></div>
            <div class="skeleton" style="height:150px;width:80%;"></div>
            <div class="skeleton" style="height:100px;width:60%;"></div>
        `;
    }

    function hideLoading() {
        clearInterval(progressInterval);
        const nprogress = document.getElementById('nprogress');
        nprogress.style.width = '100%';
        setTimeout(() => {
            nprogress.style.width = '0%';
        }, 300);
    }

    // 菜单点击事件
    navList.addEventListener('click', function (e) {
        const navItem = e.target.closest('.nav-item');
        if (!navItem) return;
        const page = navItem.getAttribute('data-page');
        if (navItem.classList.contains('has-dropdown')) {
            navItem.classList.toggle('active');
            document.querySelectorAll('.has-dropdown').forEach(item => {
                if (item !== navItem) item.classList.remove('active');
            });
        } else if (page) {
            // 如果是同一页面，不重新加载
            if (page === currentPage) return;
            
            handleRouteChange(page); // 使用handleRouteChange替代直接调用loadPage
            if (window.innerWidth < 769) sidebar.classList.add('active');
        }
        e.preventDefault();
    });

    // 三条杠按钮点击
    menuToggle.addEventListener('click', function (e) {
        sidebar.classList.toggle('active');
        
        // 保存用户偏好
        userPreferences.set('sidebarCollapsed', sidebar.classList.contains('active'));
        
        adjustContentLayout();
        e.preventDefault();
    });

    // 点击内容区空白收起侧边栏（移动端）
    content.addEventListener('click', function (e) {
        if (window.innerWidth < 769 && sidebar.classList.contains('active')) {
            if (!e.target.closest('.menu-toggle')) {
                sidebar.classList.remove('active');
                adjustContentLayout();
            }
        }
    });

    // 窗口大小变化自适应
    window.addEventListener('resize', adaptSidebar);

    // 初始化
    initialize();
});