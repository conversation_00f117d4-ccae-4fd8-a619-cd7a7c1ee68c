/**
 * 订单列表页面JavaScript
 * 功能：搜索、筛选、分页、详情查看
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalRecords = 0;
let totalPages = 0;
let currentFilters = {};
let allOrders = []; // 存储所有订单数据
let filteredOrders = []; // 存储筛选后的订单数据
let selectedOrders = new Set(); // 存储选中的订单ID

// 确保全局变量可访问
function ensureGlobalVariables() {
    if (typeof window !== 'undefined') {
        window.currentPage = currentPage;
        window.pageSize = pageSize;
        window.totalRecords = totalRecords;
        window.totalPages = totalPages;
        window.currentFilters = currentFilters;
        window.allOrders = allOrders;
        window.filteredOrders = filteredOrders;
        window.selectedOrders = selectedOrders;
    }
}

// 页面初始化函数（由HTML中的DOMContentLoaded事件调用）
function initializeOrderListPage() {
    try {
        // 确保全局变量可访问
        ensureGlobalVariables();

        initializePage();
        bindEvents();
        // loadOrderData内部已经有加载动画处理
        loadOrderData();
    } catch (error) {
        hideLoading();
        throw error;
    }
}

/**
 * 初始化页面
 */
function initializePage() {

    // 设置默认分页大小
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.value = pageSize;
    }

    // 初始化响应式滚动控制
    initializeResponsiveScrolling();

    // 阻止页面点击事件冒泡到父框架
    const orderContainer = document.querySelector('.order-container');
    if (orderContainer) {
        orderContainer.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    // 搜索框回车事件
    const searchKeyword = document.getElementById('searchKeyword');
    if (searchKeyword) {
        searchKeyword.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    // 筛选器变化事件
    const filters = ['statusFilter', 'sourceFilter', 'typeFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleSearch);
        }
    });

    // 分页大小变化
    const pageSizeSelect = document.getElementById('pageSizeSelect');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1;

            // 确保filteredOrders有数据
            if (filteredOrders.length === 0 && allOrders.length > 0) {
                filteredOrders = [...allOrders];
            }

            // 重新计算总记录数和分页信息
            totalRecords = filteredOrders.length;
            calculatePagination();

            renderOrderList();
            updatePagination();
        });
    }

    // 导出全部按钮
    const exportAllBtn = document.getElementById('exportAllBtn');
    if (exportAllBtn) {
        exportAllBtn.addEventListener('click', exportAllOrderData);
    }

    // 批量导出按钮
    const batchExportBtn = document.getElementById('batchExportBtn');
    if (batchExportBtn) {
        batchExportBtn.addEventListener('click', exportSelectedOrderData);
    }

    // 清除选择按钮
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', clearAllSelection);
    }

    // 全选复选框
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', handleSelectAll);
    }

    // 分页按钮
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                renderOrderList();
                updatePagination();
            }
        });
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                renderOrderList();
                updatePagination();
            }
        });
    }

    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshOrderList();
        });
    }

    // 导出按钮
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportAllOrderData();
        });
    }

    // 模态框关闭
    const closeDetailModal = document.getElementById('closeDetailModal');
    if (closeDetailModal) {
        closeDetailModal.addEventListener('click', function() {
            closeOrderDetailModal();
        });
    }

    // 点击模态框背景关闭
    const orderDetailModal = document.getElementById('orderDetailModal');
    if (orderDetailModal) {
        orderDetailModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeOrderDetailModal();
            }
        });
    }

    // 事件委托处理操作按钮点击
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer) {
        tableContainer.addEventListener('click', function(e) {
            // 阻止事件冒泡
            e.stopPropagation();

            const button = e.target.closest('.action-btn');
            if (button) {
                const action = button.getAttribute('data-action');
                const orderId = button.getAttribute('data-order-id');

                switch (action) {
                    case 'view':
                        viewOrderDetail(orderId);
                        break;
                    case 'delete':
                        deleteOrder(orderId);
                        break;
                }
            }
        });
    }
}

/**
 * 加载订单数据
 */
function loadOrderData() {
    try {
        showLoading();

        // 从API加载真实数据
        loadRealOrderData().catch(error => {
            // API失败时显示空状态
            allOrders = [];
            finishDataLoading();
        });

    } catch (error) {
        throw error;
    }
}

/**
 * 从API加载真实订单数据
 */
async function loadRealOrderData() {
    try {
        // 构建请求参数 - 严格按照API文档要求
        const params = {};

        // 只添加有值的参数
        if (currentFilters.keyword) {
            params.keyword = currentFilters.keyword;
        }
        if (currentFilters.status) {
            params.status = currentFilters.status;
        }
        if (currentFilters.order_source) {
            params.order_source = currentFilters.order_source;
        }
        if (currentFilters.product_type) {
            params.product_type = currentFilters.product_type;
        }
        if (currentFilters.source_channel) {
            params.source_channel = currentFilters.source_channel;
        }

        // 构建URL
        const url = '/api/GetOrdersList?' + new URLSearchParams(params).toString();

        // 发送请求 - 不需要特殊头部
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            allOrders = result.data.orders || [];
            finishDataLoading();
        } else {
            throw new Error(result.message || '获取订单列表失败');
        }

    } catch (error) {
        throw error;
    }
}


async function loadRealOrderDataForRefresh() {
    try {
        // 构建请求参数 - 严格按照API文档要求
        const params = {};

        // 只添加有值的参数
        if (currentFilters.keyword) {
            params.keyword = currentFilters.keyword;
        }
        if (currentFilters.status) {
            params.status = currentFilters.status;
        }
        if (currentFilters.order_source) {
            params.order_source = currentFilters.order_source;
        }
        if (currentFilters.product_type) {
            params.product_type = currentFilters.product_type;
        }
        if (currentFilters.source_channel) {
            params.source_channel = currentFilters.source_channel;
        }

        // 构建URL
        const url = '/api/GetOrdersList?' + new URLSearchParams(params).toString();

        // 发送请求 - 不需要特殊头部
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            allOrders = result.data.orders || [];
        } else {
            throw new Error(result.message || '获取订单列表失败');
        }

    } catch (error) {
        throw error;
    }
}


/**
 * 完成数据加载
 */
function finishDataLoading() {
    // 确保allOrders是数组
    if (!Array.isArray(allOrders)) {
        allOrders = [];
    }

    filteredOrders = [...allOrders];
    totalRecords = filteredOrders.length;

    calculatePagination();
    renderOrderList();
    updatePagination();
    hideLoading();
}

/**
 * 处理搜索
 */
function handleSearch() {
    const keyword = document.getElementById('searchKeyword').value.trim();
    const status = document.getElementById('statusFilter').value;
    const source = document.getElementById('sourceFilter').value;
    const type = document.getElementById('typeFilter').value;

    currentFilters = {
        keyword: keyword,
        status: status,
        source: source,
        type: type
    };

    // 应用筛选
    applyFilters();
    
    // 重置到第一页
    currentPage = 1;
    
    // 重新渲染
    renderOrderList();
    updatePagination();
}

/**
 * 应用筛选条件
 */
function applyFilters() {
    filteredOrders = allOrders.filter(order => {
        // 关键字搜索
        if (currentFilters.keyword) {
            const keyword = currentFilters.keyword.toLowerCase();
            const searchFields = [
                order.id,
                order.order_number,
                order.product_name,
                order.customer_name,
                order.payment_order
            ].join(' ').toLowerCase();
            
            if (!searchFields.includes(keyword)) {
                return false;
            }
        }

        // 状态筛选
        if (currentFilters.status && order.status !== currentFilters.status) {
            return false;
        }

        // 货源筛选
        if (currentFilters.source && order.source_channel !== currentFilters.source) {
            return false;
        }

        // 类型筛选
        if (currentFilters.type && order.product_type !== currentFilters.type) {
            return false;
        }

        return true;
    });

    totalRecords = filteredOrders.length;
    calculatePagination();
}

/**
 * 计算分页信息
 */
function calculatePagination() {
    totalPages = Math.ceil(totalRecords / pageSize);
    if (currentPage > totalPages) {
        currentPage = Math.max(1, totalPages);
    }
}

/**
 * 渲染订单列表
 */
function renderOrderList() {
    const tableBody = document.getElementById('orderListTable');

    if (!tableBody) {
        return;
    }

    // 确保分页信息是最新的
    totalRecords = filteredOrders.length;
    calculatePagination();

    // 清空现有内容
    tableBody.innerHTML = '';

    if (filteredOrders.length === 0) {
        // 显示友好的空状态
        const emptyRow = document.createElement('tr');
        emptyRow.id = 'emptyState';
        emptyRow.innerHTML = `
            <td colspan="15">
                <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                    <i class="fas fa-inbox" style="font-size: 64px; margin-bottom: 20px; color: #dee2e6;"></i>
                    <h3 style="margin-bottom: 12px; color: #495057;">暂无订单数据</h3>
                    <p style="margin-bottom: 20px; font-size: 14px;">
                        ${allOrders.length === 0 ? '系统中还没有任何订单记录' : '当前筛选条件下没有找到匹配的订单'}
                    </p>
                    ${allOrders.length === 0 ?
                        '<p style="font-size: 13px; color: #868e96;">订单数据将在有新订单时自动显示</p>' :
                        '<button onclick="clearFilters()" style="background: linear-gradient(135deg, #ff6b9d, #ff8fab); color: white; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; font-size: 13px; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);" onmouseover="this.style.transform=\'translateY(-2px)\'; this.style.boxShadow=\'0 4px 12px rgba(255, 107, 157, 0.4)\';" onmouseout="this.style.transform=\'translateY(0)\'; this.style.boxShadow=\'0 2px 8px rgba(255, 107, 157, 0.3)\';"><i class="fas fa-filter"></i> 清除筛选条件</button>'
                    }
                </div>
            </td>
        `;
        tableBody.appendChild(emptyRow);
        return;
    }

    // 计算当前页的数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredOrders.length);
    const pageOrders = filteredOrders.slice(startIndex, endIndex);

    // 渲染订单行
    pageOrders.forEach(order => {
        const row = createOrderRow(order);
        tableBody.appendChild(row);
    });

    // 检查是否需要滚动
    setTimeout(() => {
        checkTableScrollNeed();
    }, 100);

    // 更新选择状态UI
    updateSelectionUI();
    updateSelectAllCheckbox();
}

/**
 * 创建订单行
 */
function createOrderRow(order) {
    const row = document.createElement('tr');
    row.setAttribute('data-order-id', order.id);
    
    // 计算利润和利润率
    const profit = order.sale_price - order.purchase_price;
    const profitClass = profit >= 0 ? 'positive' : 'negative';
    
    row.innerHTML = `
        <td style="text-align: center;">
            <div class="checkbox-container">
                <input type="checkbox" id="checkbox_${order.id}" class="custom-checkbox order-checkbox" data-order-id="${order.id}">
                <label for="checkbox_${order.id}" class="checkbox-label">
                    <i class="fas fa-check"></i>
                </label>
            </div>
        </td>
        <td>
            ${formatDateTime(order.created_at)}
            <div class="cell-tooltip">${formatDateTime(order.created_at)}</div>
        </td>
        <td>
            <strong>${order.order_number}</strong>
            <div class="cell-tooltip">${order.order_number}</div>
        </td>
        <td>
            <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span>
            <div class="cell-tooltip">${getStatusText(order.status)}</div>
        </td>
        <td>
            ${order.product_id}
            <div class="cell-tooltip">${order.product_id}</div>
        </td>
        <td>
            ${order.product_name}
            <div class="cell-tooltip">${order.product_name}</div>
        </td>
        <td>
            ${getSourceText(order.order_source)}
            <div class="cell-tooltip">${getSourceText(order.order_source)}</div>
        </td>
        <td>
            ${getTypeText(order.product_type)}
            <div class="cell-tooltip">${getTypeText(order.product_type)}</div>
        </td>
        <td>
            ${getSourceText(order.source_channel)}
            <div class="cell-tooltip">${getSourceText(order.source_channel)}</div>
        </td>
        <td>
            ${order.customer_name}
            <div class="cell-tooltip">${order.customer_name}</div>
        </td>
        <td>
            ${order.payment_order}
            <div class="cell-tooltip">${order.payment_order}</div>
        </td>
        <td class="price">
            ¥${order.sale_price.toFixed(2)}
            <div class="cell-tooltip">销售价: ¥${order.sale_price.toFixed(2)}</div>
        </td>
        <td class="price">
            ¥${order.purchase_price.toFixed(2)}
            <div class="cell-tooltip">进货价: ¥${order.purchase_price.toFixed(2)}</div>
        </td>
        <td class="profit ${profitClass}">
            ¥${profit.toFixed(2)}
            <div class="cell-tooltip">利润: ¥${profit.toFixed(2)}</div>
        </td>
        <td>
            <div class="action-buttons">
                <button class="action-btn view-btn" data-action="view" data-order-id="${order.id}" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn delete-btn" data-action="delete" data-order-id="${order.id}" title="删除订单">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;

    // 添加复选框事件监听器
    const checkbox = row.querySelector('.order-checkbox');
    if (checkbox) {
        checkbox.addEventListener('change', function() {
            handleOrderSelection(order.id, this.checked);
        });
    }

    // 检查是否应该选中（保持选中状态）
    if (selectedOrders.has(order.id)) {
        checkbox.checked = true;
        row.classList.add('selected');
    }

    return row;
}

/**
 * 更新分页组件
 */
function updatePagination() {
    // 更新分页信息
    const paginationInfo = document.getElementById('paginationInfo');
    if (paginationInfo) {
        const startRecord = totalRecords === 0 ? 0 : (currentPage - 1) * pageSize + 1;
        const endRecord = Math.min(currentPage * pageSize, totalRecords);
        paginationInfo.textContent = `显示第 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录`;
    }

    // 更新分页按钮状态
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');

    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage <= 1;
    }

    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage >= totalPages;
    }

    // 更新页码按钮
    updatePageNumbers();
}

/**
 * 更新页码按钮
 */
function updatePageNumbers() {
    const pageNumbers = document.getElementById('pageNumbers');
    if (!pageNumbers) return;

    pageNumbers.innerHTML = '';

    if (totalPages <= 1) return;

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // 确保显示5个页码（如果可能）
    if (endPage - startPage < 4) {
        if (startPage === 1) {
            endPage = Math.min(totalPages, startPage + 4);
        } else if (endPage === totalPages) {
            startPage = Math.max(1, endPage - 4);
        }
    }

    // 添加第一页和省略号
    if (startPage > 1) {
        addPageNumber(1);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'page-ellipsis';
            pageNumbers.appendChild(ellipsis);
        }
    }

    // 添加页码按钮
    for (let i = startPage; i <= endPage; i++) {
        addPageNumber(i);
    }

    // 添加最后一页和省略号
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'page-ellipsis';
            pageNumbers.appendChild(ellipsis);
        }
        addPageNumber(totalPages);
    }
}

/**
 * 添加页码按钮
 */
function addPageNumber(pageNum) {
    const pageNumbers = document.getElementById('pageNumbers');
    const button = document.createElement('button');
    button.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
    button.textContent = pageNum;
    button.addEventListener('click', function() {
        if (pageNum !== currentPage) {
            currentPage = pageNum;
            renderOrderList();
            updatePagination();
        }
    });
    pageNumbers.appendChild(button);
}

/**
 * 查看订单详情
 */
async function viewOrderDetail(orderId) {
    try {
        // 先从本地数据查找基本信息
        const order = allOrders.find(o => o.id === orderId);
        if (!order) {
            alert('订单不存在');
            return;
        }

        const modal = document.getElementById('orderDetailModal');
        const content = document.getElementById('orderDetailContent');

        if (!modal || !content) return;

        // 显示加载状态
        content.innerHTML = '<div style="text-align: center; padding: 40px;"><i class="fas fa-spinner fa-spin"></i> 正在加载详细信息...</div>';
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // 从API获取详细信息
        const detailData = await getOrderDetailFromAPI(orderId);

        // 存储当前订单数据，供视图切换使用
        window.currentOrderData = order;
        window.currentOrderDetailData = detailData;

        // 生成详情HTML
        const detailHTML = generateOrderDetailHTML(order, detailData);
        content.innerHTML = detailHTML;

    } catch (error) {
        const content = document.getElementById('orderDetailContent');
        if (content) {
            content.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #dc3545;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h3>获取详情失败</h3>
                    <p>${error.message}</p>
                    <button onclick="closeOrderDetailModal()" style="background: #ff6b9d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;
        }
    }
}

/**
 * 从API获取订单详情
 */
async function getOrderDetailFromAPI(orderId) {
    try {
        // 构建URL - 严格按照API文档要求
        const url = `/api/orders?order_id=${encodeURIComponent(orderId)}`;

        // 发送请求 - 不需要特殊头部
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            return result.data;
        } else {
            throw new Error(result.message || '获取订单详情失败');
        }

    } catch (error) {
        throw error;
    }
}

/**
 * 生成订单详情HTML
 */
function generateOrderDetailHTML(order, detailData) {
    const profit = order.sale_price - order.purchase_price;
    const profitRate = ((profit / order.purchase_price) * 100).toFixed(2);

    // 从详细数据中获取商品和用户信息
    const productInfo = detailData?.product || {};
    const userInfo = detailData?.user || {};
    const productType = productInfo.type || order.product_type;
    const dockingInfo = productInfo.docking_info || {};

    // 生成货源渠道显示内容
    let sourceChannelContent = '';
    if (productType == '3' && dockingInfo.docking_site_name) {
        // 对接商品显示格式：docking_site_name(docking_site_id)
        sourceChannelContent = `${dockingInfo.docking_site_name}(${dockingInfo.docking_site_id})`;
    } else if (productType == '1' || productType == '2') {
        // 卡密或虚拟商品不显示货源渠道
        sourceChannelContent = null;
    } else {
        sourceChannelContent = '未知';
    }

    return `
        <div class="order-detail-grid">
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                <div class="detail-row">
                    <span class="detail-label">订单号：</span>
                    <span class="detail-value">${order.order_number}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">支付订单号：</span>
                    <span class="detail-value">${order.id}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">创建时间：</span>
                    <span class="detail-value">${formatDateTime(order.created_at)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">订单状态：</span>
                    <span class="detail-value">
                        <span class="status-badge status-${order.status}">${getStatusText(order.status)}</span>
                    </span>
                </div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-box"></i> 商品信息</h4>
                <div class="detail-row">
                    <span class="detail-label">商品编号：</span>
                    <span class="detail-value">${order.product_id}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">商品名称：</span>
                    <span class="detail-value">${order.product_name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">商品类型：</span>
                    <span class="detail-value">${getProductTypeText(productType)}</span>
                </div>
                ${sourceChannelContent ? `
                <div class="detail-row">
                    <span class="detail-label">货源渠道：</span>
                    <span class="detail-value">${sourceChannelContent}</span>
                </div>
                ` : ''}
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-user"></i> 客户信息</h4>
                <div class="detail-row">
                    <span class="detail-label">客户姓名：</span>
                    <span class="detail-value">${order.customer_name}</span>
                </div>
                ${userInfo.notice_mail ? `
                <div class="detail-row">
                    <span class="detail-label">客户邮箱：</span>
                    <span class="detail-value">${userInfo.notice_mail}</span>
                </div>
                ` : ''}
                ${!userInfo.is_guest && userInfo.id ? `
                <div class="detail-row">
                    <span class="detail-label">客户ID：</span>
                    <span class="detail-value">${userInfo.id}</span>
                </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">下单来源：</span>
                    <span class="detail-value">${getSourceText(order.order_source)}</span>
                </div>
            </div>

            <div class="detail-section">
                <h4><i class="fas fa-yen-sign"></i> 价格信息</h4>
                <div class="detail-row">
                    <span class="detail-label">销售价格：</span>
                    <span class="detail-value price">¥${order.sale_price.toFixed(2)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">进货价格：</span>
                    <span class="detail-value price">¥${order.purchase_price.toFixed(2)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">利润金额：</span>
                    <span class="detail-value profit ${profit >= 0 ? 'positive' : 'negative'}">¥${profit.toFixed(2)}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">利润率：</span>
                    <span class="detail-value profit ${profit >= 0 ? 'positive' : 'negative'}">${profitRate}%</span>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="order-actions-section">
            <button class="btn btn-primary" onclick="showOrderHistory()">
                <i class="fas fa-history"></i>
                查看订单历史
            </button>
            <button class="btn btn-light" onclick="handleOrderRefund()">
                <i class="fas fa-undo"></i>
                订单退款
            </button>
        </div>

        <style>
            .order-detail-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 24px;
            }

            .order-actions-section {
                margin-top: 24px;
                padding: 20px;
                background-color: var(--bg-light);
                border-radius: var(--border-radius);
                display: flex;
                gap: 16px;
                justify-content: center;
                flex-wrap: wrap;
                border: 1px solid var(--border-light);
            }

            .detail-section {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
            }

            .detail-section h4 {
                margin: 0 0 16px 0;
                color: var(--primary-color);
                font-size: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .detail-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .detail-row:last-child {
                border-bottom: none;
            }

            .detail-label {
                font-weight: 500;
                color: var(--text-secondary);
                min-width: 100px;
            }

            .detail-value {
                font-weight: 600;
                color: var(--text-primary);
                text-align: right;
            }

            @media (max-width: 768px) {
                .order-detail-grid {
                    grid-template-columns: 1fr;
                    gap: 16px;
                }

                .detail-section {
                    padding: 16px;
                }

                .detail-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;
                }

                .detail-value {
                    text-align: left;
                }
            }
        </style>
    `;
}

/**
 * 生成订单历史HTML
 */
function generateOrderHistoryHTML(historyData) {
    if (!historyData || !Array.isArray(historyData) || historyData.length === 0) {
        return `
            <div class="order-history-container">
                <div class="history-header">
                    <button class="back-btn" onclick="showOrderDetail()">
                        <i class="fas fa-arrow-left"></i>
                        返回订单详情
                    </button>
                    <h3><i class="fas fa-history"></i> 订单历史记录</h3>
                </div>
                <div class="empty-history">
                    <i class="fas fa-clock"></i>
                    <p>暂无历史记录</p>
                </div>
            </div>
        `;
    }

    // 严格按照原始数组顺序显示历史记录
    const historyItems = historyData.map((item) => {
        const statusClass = getHistoryStatusClass(item.status);
        const actionText = getHistoryActionText(item.action);

        return `
            <div class="timeline-item">
                <div class="timeline-marker ${statusClass}">
                    <i class="${getHistoryActionIcon(item.action)}"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <h5 class="timeline-title">${actionText}</h5>
                        <span class="status-badge status-${item.status}">${getStatusText(item.status)}</span>
                        <span class="timeline-time">${formatDateTime(item.timestamp)}</span>
                    </div>
                    <div class="timeline-description">
                        ${item.description || '无描述'}
                    </div>
                    ${item.details ? `
                        <div class="timeline-details">
                            ${formatHistoryDetails(item.details)}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="order-history-container">
            <div class="history-header">
                <button class="btn btn-light" onclick="showOrderDetail()">
                    <i class="fas fa-arrow-left"></i>
                    返回订单详情
                </button>
                <h3><i class="fas fa-history"></i> 订单历史记录</h3>
            </div>
            <div class="timeline">
                ${historyItems}
            </div>
        </div>
        ${getHistoryStyles()}
    `;
}

/**
 * 获取历史记录状态样式类
 */
function getHistoryStatusClass(status) {
    const statusClassMap = {
        'pending': 'status-pending',
        'paid': 'status-success',
        'processing': 'status-processing',
        'completed': 'status-success',
        'failed': 'status-error',
        'refunded': 'status-warning',
        'cancelled': 'status-error'
    };
    return statusClassMap[status] || 'status-default';
}

/**
 * 获取历史记录操作文本
 */
function getHistoryActionText(action) {
    const actionTextMap = {
        'order_created': '订单创建',
        'payment_success': '支付成功',
        'payment_failed': '支付失败',
        'order_processing': '订单处理中',
        'order_completed': '订单完成',
        'order_failed': '订单失败',
        'order_cancelled': '订单取消',
        'refund_requested': '申请退款',
        'refund_processed': '退款处理',
        'refund_completed': '退款完成'
    };
    return actionTextMap[action] || action;
}

/**
 * 获取历史记录操作图标
 */
function getHistoryActionIcon(action) {
    const actionIconMap = {
        'order_created': 'fas fa-plus-circle',
        'payment_success': 'fas fa-check-circle',
        'payment_failed': 'fas fa-times-circle',
        'order_processing': 'fas fa-cog',
        'order_completed': 'fas fa-check-double',
        'order_failed': 'fas fa-exclamation-triangle',
        'order_cancelled': 'fas fa-ban',
        'refund_requested': 'fas fa-undo',
        'refund_processed': 'fas fa-sync',
        'refund_completed': 'fas fa-check'
    };
    return actionIconMap[action] || 'fas fa-circle';
}

/**
 * 格式化历史记录详细信息
 */
function formatHistoryDetails(details) {
    if (!details || typeof details !== 'object') {
        return '';
    }

    const detailItems = [];

    // 处理常见的详细信息字段
    if (details.amount) {
        detailItems.push(`<span class="detail-item"><strong>金额:</strong> ¥${details.amount}</span>`);
    }
    if (details.payment_method) {
        detailItems.push(`<span class="detail-item"><strong>支付方式:</strong> ${details.payment_method}</span>`);
    }
    if (details.error_message) {
        detailItems.push(`<span class="detail-item error"><strong>错误信息:</strong> ${details.error_message}</span>`);
    }
    if (details.retry_attempt) {
        detailItems.push(`<span class="detail-item"><strong>重试次数:</strong> ${details.retry_attempt}</span>`);
    }
    if (details.ip_address) {
        detailItems.push(`<span class="detail-item"><strong>IP地址:</strong> ${details.ip_address}</span>`);
    }

    return detailItems.length > 0 ? detailItems.join('') : '';
}

/**
 * 获取历史记录样式
 */
function getHistoryStyles() {
    return `
        <style>
            .order-history-container {
                padding: 0;
            }

            .history-header {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 24px;
                padding: 20px;
                background: linear-gradient(145deg, var(--bg-white) 0%, var(--bg-light) 100%);
                border-radius: var(--border-radius);
                border: 1px solid var(--border-light);
                box-shadow: var(--shadow-light);
            }

            .history-header h3 {
                margin: 0;
                color: var(--text-primary);
                font-size: 20px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .history-header h3 i {
                color: var(--primary-color);
            }

            .timeline {
                position: relative;
                padding-left: 40px;
                margin-top: 20px;
            }

            .timeline::before {
                content: '';
                position: absolute;
                left: 20px;
                top: 0;
                bottom: 0;
                width: 3px;
                background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
                border-radius: 2px;
            }

            .timeline-item {
                position: relative;
                margin-bottom: 32px;
                padding-left: 50px;
            }

            .timeline-marker {
                position: absolute;
                left: -48.5px;
                top: 12px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: white;
                z-index: 2;
                border: 3px solid var(--bg-white);
                box-shadow: var(--shadow-light);
            }

            .timeline-marker.status-success {
                background: linear-gradient(135deg, #28a745, #20c997);
            }

            .timeline-marker.status-error {
                background: linear-gradient(135deg, #dc3545, #e74c3c);
            }

            .timeline-marker.status-warning {
                background: linear-gradient(135deg, #ffc107, #fd7e14);
            }

            .timeline-marker.status-processing {
                background: linear-gradient(135deg, #17a2b8, #20c997);
            }

            .timeline-marker.status-pending {
                background: linear-gradient(135deg, #6c757d, #adb5bd);
            }

            .timeline-marker.status-default {
                background: linear-gradient(135deg, #6c757d, #adb5bd);
            }

            .timeline-content {
                background: linear-gradient(145deg, var(--bg-white) 0%, var(--bg-light) 100%);
                border-radius: var(--border-radius);
                padding: 20px;
                border: 1px solid var(--border-light);
                box-shadow: var(--shadow-light);
                position: relative;
                overflow: hidden;
            }

            .timeline-content::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            }

            .timeline-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                gap: 12px;
            }

            .timeline-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: var(--text-primary);
                line-height: 1.3;
                flex: 1;
            }

            .timeline-time {
                font-size: 13px;
                color: var(--text-muted);
                background: var(--primary-lighter);
                padding: 6px 12px;
                border-radius: 20px;
                white-space: nowrap;
                font-weight: 500;
                flex-shrink: 0;
            }

            .timeline-description {
                color: var(--text-secondary);
                margin-bottom: 8px;
                line-height: 1.6;
                font-size: 15px;
            }

            .timeline-details {
                background: var(--bg-pink);
                border-radius: 8px;
                padding: 12px;
                border: 1px solid rgba(255, 107, 157, 0.1);
            }

            .detail-item {
                display: block;
                margin-bottom: 4px;
                font-size: 14px;
                color: var(--text-secondary);
                padding: 2px 0;
            }

            .detail-item:last-child {
                margin-bottom: 0;
            }

            .detail-item.error {
                color: #dc3545;
                font-weight: 500;
            }

            .detail-item strong {
                color: var(--text-primary);
                font-weight: 600;
            }

            .empty-history {
                text-align: center;
                padding: 80px 20px;
                color: var(--text-muted);
                background: linear-gradient(145deg, var(--bg-white) 0%, var(--bg-light) 100%);
                border-radius: var(--border-radius);
                border: 1px solid var(--border-light);
                margin-top: 20px;
            }

            .empty-history i {
                font-size: 64px;
                margin-bottom: 20px;
                color: var(--primary-color);
                opacity: 0.5;
            }

            .empty-history p {
                font-size: 16px;
                margin: 0;
                font-weight: 500;
            }

            @media (max-width: 768px) {
                .history-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 16px;
                    padding: 16px;
                }

                .order-actions-section {
                    flex-direction: column;
                    gap: 12px;
                    padding: 16px;
                }

                .order-actions-section .btn {
                    width: 100%;
                    justify-content: center;
                }

                .timeline {
                    padding-left: 30px;
                }

                .timeline::before {
                    left: 15px;
                }

                .timeline-item {
                    padding-left: 40px;
                    margin-bottom: 24px;
                }

                .timeline-marker {
                    left: -39.5px;
                    width: 32px;
                    height: 32px;
                    font-size: 14px;
                }

                .timeline-content {
                    padding: 16px;
                }

                .timeline-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 12px;
                }

                .timeline-title {
                    font-size: 16px;
                }

                .timeline-time {
                    align-self: flex-start;
                }

                .empty-history {
                    padding: 60px 16px;
                }

                .empty-history i {
                    font-size: 48px;
                }
            }
        </style>
    `;
}

/**
 * 显示订单历史记录
 */
function showOrderHistory() {
    const content = document.getElementById('orderDetailContent');
    if (!content) return;

    // 获取当前存储的订单详情数据
    const currentOrderData = window.currentOrderDetailData;
    if (!currentOrderData || !currentOrderData.history) {
        content.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                <h3>无法获取历史记录</h3>
                <p>订单历史数据不可用</p>
                <button class="btn btn-primary" onclick="showOrderDetail()">
                    返回订单详情
                </button>
            </div>
        `;
        return;
    }

    // 生成历史记录HTML
    const historyHTML = generateOrderHistoryHTML(currentOrderData.history);
    content.innerHTML = historyHTML;
}

/**
 * 显示订单详情
 */
function showOrderDetail() {
    const content = document.getElementById('orderDetailContent');
    if (!content) return;

    // 获取当前存储的订单数据
    const currentOrder = window.currentOrderData;
    const currentOrderDetailData = window.currentOrderDetailData;

    if (!currentOrder || !currentOrderDetailData) {
        content.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                <h3>无法显示订单详情</h3>
                <p>订单数据不可用</p>
                <button class="btn btn-primary" onclick="closeOrderDetailModal()">
                    关闭
                </button>
            </div>
        `;
        return;
    }

    // 重新生成订单详情HTML
    const detailHTML = generateOrderDetailHTML(currentOrder, currentOrderDetailData);
    content.innerHTML = detailHTML;
}

/**
 * 处理订单退款（暂时无功能）
 */
function handleOrderRefund() {
    alert('订单退款功能正在开发中，敬请期待！');
}

/**
 * 关闭订单详情模态框
 */
function closeOrderDetailModal() {
    const modal = document.getElementById('orderDetailModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';

        // 清理存储的数据
        window.currentOrderData = null;
        window.currentOrderDetailData = null;
    }
}



/**
 * 删除订单 - 显示确认模态框
 */
function deleteOrder(orderId) {
    // 查找订单信息
    const order = allOrders.find(o => o.id === orderId);
    if (!order) {
        alert('订单不存在');
        return;
    }

    // 设置模态框中的订单信息
    document.getElementById('deleteOrderNumber').textContent = order.order_number || order.id;
    document.getElementById('deleteProductName').textContent = order.product_name || '未知商品';

    // 存储要删除的订单ID到全局变量
    window.pendingDeleteOrderId = orderId;

    // 显示删除确认模态框
    showDeleteModal();
}

/**
 * 显示删除确认模态框
 */
function showDeleteModal() {
    const modal = document.getElementById('deleteConfirmModal');
    if (modal) {
        modal.style.display = 'flex';
        // 触发动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // 阻止页面滚动
        document.body.style.overflow = 'hidden';
    }
}

/**
 * 关闭删除确认模态框
 */
function closeDeleteModal() {
    const modal = document.getElementById('deleteConfirmModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }, 300);
    }

    // 清除待删除的订单ID
    window.pendingDeleteOrderId = null;
}

/**
 * 确认删除订单
 */
async function confirmDeleteOrder() {
    const orderId = window.pendingDeleteOrderId;
    if (!orderId) {
        closeDeleteModal();
        return;
    }

    // 关闭模态框
    closeDeleteModal();

    try {
        // 显示加载状态
        showLoadingOverlay();

        // 构建请求数据 - 严格按照API文档要求
        const data = {
            order_ids: [orderId]
        };

        // 发送删除请求 - 不需要特殊头部
        const response = await fetch('/api/orders/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 200) {
            showNotification('订单删除成功', 'success');
            // 刷新订单列表
            refreshOrderList();
        } else {
            showNotification(result.message || '删除失败', 'error');
        }

    } catch (error) {
        showNotification('网络错误，请稍后再试', 'error');
    } finally {
        hideLoadingOverlay();
    }
}

// 确保函数暴露到全局作用域
window.showDeleteModal = showDeleteModal;
window.closeDeleteModal = closeDeleteModal;
window.confirmDeleteOrder = confirmDeleteOrder;
window.clearFilters = clearFilters;
window.showOrderHistory = showOrderHistory;
window.showOrderDetail = showOrderDetail;
window.handleOrderRefund = handleOrderRefund;

/**
 * 处理全选复选框
 */
function handleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const isChecked = selectAllCheckbox.checked;

    // 获取当前页的所有订单复选框
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');

    orderCheckboxes.forEach(checkbox => {
        const orderId = checkbox.getAttribute('data-order-id');
        checkbox.checked = isChecked;

        if (isChecked) {
            selectedOrders.add(orderId);
            checkbox.closest('tr').classList.add('selected');
        } else {
            selectedOrders.delete(orderId);
            checkbox.closest('tr').classList.remove('selected');
        }
    });

    updateSelectionUI();
}

/**
 * 处理单个订单选择
 */
function handleOrderSelection(orderId, isChecked) {
    if (isChecked) {
        selectedOrders.add(orderId);
        document.querySelector(`tr[data-order-id="${orderId}"]`).classList.add('selected');
    } else {
        selectedOrders.delete(orderId);
        document.querySelector(`tr[data-order-id="${orderId}"]`).classList.remove('selected');
    }

    updateSelectionUI();
    updateSelectAllCheckbox();
}

/**
 * 更新全选复选框状态
 */
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.order-checkbox:checked');

    if (orderCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length === orderCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

/**
 * 更新选择状态UI
 */
function updateSelectionUI() {
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedOrders.size > 0) {
        batchActions.style.display = 'flex';
        selectedCount.textContent = `已选择 ${selectedOrders.size} 项`;
    } else {
        batchActions.style.display = 'none';
    }
}

/**
 * 清除所有选择
 */
function clearAllSelection() {
    selectedOrders.clear();

    // 取消所有复选框选中状态
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    orderCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('tr').classList.remove('selected');
    });

    // 更新全选复选框
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;

    updateSelectionUI();
}

/**
 * 显示加载状态
 */
function showLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    } else {
        // 备用方案：在表格中显示加载状态
        const tableBody = document.getElementById('orderListTable');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="15" style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #ff6b9d;"></i>
                        <p style="margin-top: 16px; color: #666;">正在加载订单数据...</p>
                    </td>
                </tr>
            `;
        }
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    } else {
    }
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'pending': '待付款',
        'paid': '已付款',
        'processing': '处理中',
        'completed': '已完成',
        'refunded': '已退款',
        'failed': '失败'
    };
    return statusMap[status] || status;
}

/**
 * 获取来源文本
 */
function getSourceText(source) {
    const sourceMap = {
        'web': '官网',
        'mobile': '手机端',
        'wechat': '微信',
        'app': 'APP',
        'direct': '直营',
        'supplier_a': '供应商A',
        'supplier_b': '供应商B',
        'dropship': '代发',
        'wholesale': '批发'
    };
    return sourceMap[source] || source;
}

/**
 * 获取类型文本
 */
function getTypeText(type) {
    const typeMap = {
        'physical': '实物商品',
        'digital': '数字商品',
        'service': '服务商品',
        'subscription': '订阅商品'
    };
    return typeMap[type] || type;
}

/**
 * 获取商品类型文本（根据数字类型转换）
 */
function getProductTypeText(type) {
    const typeMap = {
        '1': '卡密商品',
        '2': '虚拟商品',
        '3': '对接商品',
        1: '卡密商品',
        2: '虚拟商品',
        3: '对接商品'
    };
    return typeMap[type] || type;
}

/**
 * 获取渠道文本
 */
function getChannelText(channel) {
    const channelMap = {
        'direct': '直营',
        'distributor': '分销商',
        'wholesale': '批发商',
        'retail': '零售商',
        'online': '线上渠道',
        'offline': '线下渠道'
    };
    return channelMap[channel] || channel;
}





/**
 * 清除所有筛选条件
 */
function clearFilters() {
    // 重置筛选条件
    currentFilters = {
        keyword: '',
        status: '',
        order_source: '',
        product_type: '',
        source_channel: ''
    };

    // 重置表单
    const searchKeyword = document.getElementById('searchKeyword');
    if (searchKeyword) searchKeyword.value = '';

    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) statusFilter.value = '';

    const sourceFilter = document.getElementById('sourceFilter');
    if (sourceFilter) sourceFilter.value = '';

    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) typeFilter.value = '';

    // 重置到第一页
    currentPage = 1;

    // 重新应用筛选
    applyFilters();

    // 重新渲染列表和更新分页
    renderOrderList();
    updatePagination();
}

/**
 * 刷新订单列表
 */
async function refreshOrderList() {
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        // 添加加载动画
        refreshBtn.classList.add('loading');
        refreshBtn.disabled = true;
    }

    // 显示加载状态
    showLoading();

    try {
        // 重置筛选和分页
        currentPage = 1;
        currentFilters = {};

        // 清空筛选器
        const searchKeyword = document.getElementById('searchKeyword');
        if (searchKeyword) searchKeyword.value = '';

        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) statusFilter.value = '';

        const sourceFilter = document.getElementById('sourceFilter');
        if (sourceFilter) sourceFilter.value = '';

        const typeFilter = document.getElementById('typeFilter');
        if (typeFilter) typeFilter.value = '';

        // 从API加载真实数据
        await loadRealOrderDataForRefresh();

        // 使用动画渲染列表
        filteredOrders = [...allOrders];
        totalRecords = filteredOrders.length;
        calculatePagination();
        renderOrderListWithAnimation();
        updatePagination();

    } catch (error) {
        alert('刷新失败，请检查网络连接后重试');

        // API失败时显示空状态
        allOrders = [];
        filteredOrders = [];
        totalRecords = 0;
        calculatePagination();
        renderOrderListWithAnimation();
        updatePagination();
    } finally {
        // 移除加载动画
        if (refreshBtn) {
            refreshBtn.classList.remove('loading');
            refreshBtn.disabled = false;
        }
        hideLoading();
    }
}

/**
 * 带动画的渲染订单列表
 */
function renderOrderListWithAnimation() {

    const tableBody = document.getElementById('orderListTable');
    if (!tableBody) {
        return;
    }

    // 先清空表格
    tableBody.innerHTML = '';

    // 计算当前页的数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentPageData = filteredOrders.slice(startIndex, endIndex);

    if (currentPageData.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="15" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                    <i class="fas fa-inbox" style="font-size: 64px; margin-bottom: 20px; color: #dee2e6; display: block;"></i>
                    暂无订单数据
                </td>
            </tr>
        `;
        return;
    }

    // 逐行添加数据，带动画效果
    currentPageData.forEach((order, index) => {
        setTimeout(() => {
            const row = createOrderRow(order);
            row.classList.add('row-loading');
            tableBody.appendChild(row);

            // 最后一行添加完成
            if (index === currentPageData.length - 1) {
                // 所有行加载完成
            }
        }, index * 50); // 每行延迟50ms，创造波浪效果
    });
}

/**
 * 初始化响应式滚动控制
 */
function initializeResponsiveScrolling() {
    checkTableScrollNeed();
}

/**
 * 处理窗口大小变化
 */
function handleWindowResize() {
    // 防抖处理，避免频繁触发
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(() => {
        checkTableScrollNeed();
    }, 250);
}

/**
 * 检查表格是否需要滚动
 */
function checkTableScrollNeed() {
    const tableContainer = document.querySelector('.table-responsive');
    const table = document.querySelector('.orders-table');

    if (!tableContainer || !table) {
        return;
    }

    const containerWidth = tableContainer.clientWidth;
    const tableWidth = table.scrollWidth;
    const windowWidth = window.innerWidth;

    // 移动端始终启用滚动
    if (windowWidth <= 768) {
        tableContainer.classList.add('scrollable');
        return;
    }

    // 桌面端：根据实际需要决定是否启用滚动
    if (tableWidth > containerWidth) {
        tableContainer.classList.add('scrollable');
    } else {
        tableContainer.classList.remove('scrollable');
    }
}

/**
 * 导出全部订单数据
 */
function exportAllOrderData() {
    if (!filteredOrders || filteredOrders.length === 0) {
        alert('没有可导出的数据');
        return;
    }

    exportToExcel(filteredOrders, '全部订单数据');
}

/**
 * 导出选中的订单数据
 */
function exportSelectedOrderData() {
    if (selectedOrders.size === 0) {
        alert('请先选择要导出的订单');
        return;
    }

    // 获取选中的订单数据
    const selectedOrderData = allOrders.filter(order => selectedOrders.has(order.id));
    exportToExcel(selectedOrderData, '选中订单数据');
}

/**
 * 使用SheetJS导出Excel文件
 */
function exportToExcel(data, filename) {
    try {
        // 检查SheetJS是否可用
        if (typeof XLSX === 'undefined') {
            alert('Excel导出功能不可用，请检查网络连接');
            return;
        }

        // 准备导出数据
        const exportData = data.map(order => {
            const profit = order.sale_price - order.purchase_price;
            return {
                '时间': formatDateTime(order.created_at),
                '订单号': order.order_number,
                '状态': getStatusText(order.status),
                '商品编号': order.product_id,
                '商品名称': order.product_name,
                '下单来源': getSourceText(order.order_source),
                '商品类型': getTypeText(order.product_type),
                '货源渠道': getChannelText(order.source_channel),
                '购买客户': order.customer_name,
                '支付订单': order.payment_order,
                '销售价': order.sale_price,
                '进货价': order.purchase_price,
                '利润': profit.toFixed(2)
            };
        });

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);

        // 设置列宽
        const colWidths = [
            { wch: 20 }, // 时间
            { wch: 25 }, // 订单号
            { wch: 10 }, // 状态
            { wch: 20 }, // 商品编号
            { wch: 30 }, // 商品名称
            { wch: 12 }, // 下单来源
            { wch: 12 }, // 商品类型
            { wch: 15 }, // 货源渠道
            { wch: 15 }, // 购买客户
            { wch: 25 }, // 支付订单
            { wch: 12 }, // 销售价
            { wch: 12 }, // 进货价
            { wch: 12 }  // 利润
        ];
        ws['!cols'] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '订单数据');

        // 生成文件名（包含时间戳）
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
        const fullFilename = `${filename}_${timestamp}.xlsx`;

        // 导出文件
        XLSX.writeFile(wb, fullFilename);

        // 显示成功消息
        alert(`成功导出 ${data.length} 条订单数据到文件: ${fullFilename}`);

    } catch (error) {
        alert('导出失败，请重试');
    }
}

// 版本标识 - 用于确认文件更新

// 添加删除模态框的事件监听器
document.addEventListener('DOMContentLoaded', function() {

    // 点击模态框背景关闭删除确认模态框
    const deleteModal = document.getElementById('deleteConfirmModal');
    if (deleteModal) {
        deleteModal.addEventListener('click', function(e) {
            // 只有点击背景遮罩时才关闭模态框
            if (e.target === deleteModal || e.target.classList.contains('delete-modal-overlay')) {
                closeDeleteModal();
            }
        });
    } else {
    }

    // ESC键关闭删除确认模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const deleteModal = document.getElementById('deleteConfirmModal');
            if (deleteModal && deleteModal.classList.contains('show')) {
                closeDeleteModal();
            }
        }
    });

    // 验证editOrder函数是否已被移除
    if (typeof editOrder === 'function') {
    } else {
    }
});
