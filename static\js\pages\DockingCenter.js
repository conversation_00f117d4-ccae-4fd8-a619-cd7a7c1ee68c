// 当页面加载完成后初始化事件
    (function initDockingCenter() {
        // 用于跟踪是否已经设置过事件监听器
        var eventsInitialized = false;

        // 存储网站配置信息的全局对象
        window.dockingSites = {};

        // 获取网站BaseURL的函数
        function getBaseURL() {
            // 方式1: 从当前页面URL获取
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}`;
        }

        // 显示或隐藏聚比价回调地址提示
        function toggleJubijiaCallbackTip(siteType, tipElementId, urlElementId) {
            const tipElement = document.getElementById(tipElementId);
            const urlElement = document.getElementById(urlElementId);

            if (!tipElement || !urlElement) {
                return;
            }

            if (siteType === 'jubijia') {
                // 获取BaseURL并构建回调地址
                const baseUrl = getBaseURL();
                const callbackUrl = `${baseUrl}/api/jbjProductPushCallback`;

                // 设置URL内容
                urlElement.textContent = callbackUrl;

                // 显示提示
                tipElement.style.display = 'block';
            } else {
                // 隐藏提示
                tipElement.style.display = 'none';
            }
        }
        
        // 查询聚比价账户余额
        async function fetchJubijiaBalance(site) {
            try {

                // 准备请求参数
                var requestData = {
                    userId: site.appid
                };

                // 计算签名
                var sign = calculateSign(requestData, site.key);

                // 构建请求URL参数
                var urlParams = 'userId=' + encodeURIComponent(site.appid) + '&sign=' + encodeURIComponent(sign);


                // 发送请求到聚比价余额接口
                var response = await fetch('http://jubijia.com/api/open/balance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: urlParams
                });

                if (!response.ok) {
                    throw new Error('网络请求失败，状态码: ' + response.status);
                }

                var data = await response.json();

                if (data.code === 200) {
                    // 请求成功，返回余额数据
                    return {
                        success: true,
                        balance: data.data || '0.00',
                        siteId: site.id
                    };
                } else {
                    // 请求失败，返回错误信息
                    return {
                        success: false,
                        error: data.msg || '查询失败',
                        siteId: site.id
                    };
                }

            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    siteId: site.id
                };
            }
        }

        // 更新表格中的余额显示
        function updateBalanceInTable(siteId, balanceText, isError) {
            if (typeof isError === 'undefined') isError = false;
            // 查找对应的表格行
            var row = document.querySelector('[data-id="' + siteId + '"]');
            if (row) {
                row = row.closest('tr');
            }
            if (row) {
                // 余额列是第5列（索引4）
                var balanceCell = row.cells[4];
                if (balanceCell) {
                    if (isError) {
                        balanceCell.innerHTML = '<span style="color: #ff6b6b; font-size: 12px;" title="' + balanceText + '">查询失败</span>';
                    } else {
                        balanceCell.innerHTML = '<span style="color: #4cd964; font-weight: 600;">¥' + balanceText + '</span>';
                    }
                }
            }
        }

        // 加载对接网站列表
        function loadDockingSites() {
            var tableBody = document.querySelector('.docking-table tbody');
            if (!tableBody) return;
            
            // 清空表格
            tableBody.innerHTML = '';
            
            // 显示加载中
            showLoading();
            
            // 发送请求获取网站列表
            fetch('/api/getDockingList')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    
                    if (data.code !== 200) {
                        showToast(data.msg || '获取网站列表失败', 'error');
                        return;
                    }
                    
                    // 获取网站列表
                    var sites = data.data.list || [];

                    // 保存到全局存储
                    if (!window.dockingSites) window.dockingSites = {};

                    // 如果没有数据，显示空状态
                    if (sites.length === 0) {
                        tableBody.innerHTML =
                            '<tr>' +
                                '<td colspan="6" class="empty-state">' +
                                    '<div class="empty-state-icon">' +
                                        '<i class="fas fa-globe"></i>' +
                                    '</div>' +
                                    '<div class="empty-state-text">暂无对接网站，点击上方"添加网站"按钮开始添加</div>' +
                                '</td>' +
                            '</tr>';
                        return;
                    }

                    // 填充表格
                    sites.forEach(function(site) {
                        // 保存到全局存储
                        window.dockingSites[site.id] = site;

                        var row = document.createElement('tr');

                        // 初始余额显示
                        var balanceDisplay = '--';
                        if (site.type && site.type.toLowerCase() === 'jubijia') {
                            balanceDisplay = '<span style="color: #999; font-size: 12px;"><i class="fas fa-spinner fa-spin"></i> 查询中...</span>';
                        }

                        row.innerHTML =
                            '<td>' + site.name + '</td>' +
                            '<td>' + site.url + '</td>' +
                            '<td>' + site.type + '</td>' +
                            '<td>' + site.appid + '</td>' +
                            '<td>' + balanceDisplay + '</td>' +
                            '<td class="action-buttons">' +
                                '<button class="action-btn purchase-btn" title="进货" data-id="' + site.id + '">' +
                                    '<i class="fas fa-shopping-cart"></i>' +
                                '</button>' +
                                '<button class="action-btn edit-btn" title="编辑" data-id="' + site.id + '">' +
                                    '<i class="fas fa-edit"></i>' +
                                '</button>' +
                                '<button class="action-btn delete-btn" title="删除" data-id="' + site.id + '">' +
                                    '<i class="fas fa-trash-alt"></i>' +
                                '</button>' +
                            '</td>';
                        tableBody.appendChild(row);
                    });

                    // 绑定操作按钮事件
                    bindActionButtons();

                    // 异步查询聚比价网站的账户余额
                    sites.forEach(function(site) {
                        if (site.type && site.type.toLowerCase() === 'jubijia') {
                            fetchJubijiaBalance(site).then(function(balanceResult) {
                                if (balanceResult.success) {
                                    updateBalanceInTable(site.id, balanceResult.balance, false);
                                } else {
                                    updateBalanceInTable(site.id, balanceResult.error, true);
                                }
                            });
                        }
                    });
                })
                .catch(error => {
                    hideLoading();
                    showToast('获取网站列表失败: ' + error.message, 'error');
                });
        }
        
        // 删除对接网站
        function deleteDockingSite(siteId) {
            if (!siteId) return;

            // 获取网站信息
            var site = window.dockingSites[siteId];
            if (!site) {
                showToast('无法获取网站信息', 'error');
                return;
            }

            // 显示删除确认模态框
            showDeleteConfirmModal(site);
        }

        // 显示删除确认模态框
        function showDeleteConfirmModal(site) {
            var modal = document.getElementById('deleteDockingSiteModal');
            if (!modal) return;

            // 填充网站信息
            document.getElementById('deleteSiteName').textContent = site.name || '-';
            document.getElementById('deleteSiteType').textContent = site.type || '-';
            document.getElementById('deleteSiteUrl').textContent = site.url || '-';

            // 显示模态框
            modal.style.display = 'flex';

            // 绑定事件（如果还没绑定）
            if (!modal.hasAttribute('data-events-bound')) {
                bindDeleteModalEvents(modal);
                modal.setAttribute('data-events-bound', 'true');
            }

            // 保存当前要删除的网站ID
            modal.setAttribute('data-delete-site-id', site.id);
        }

        // 绑定删除模态框事件
        function bindDeleteModalEvents(modal) {
            var closeBtn = modal.querySelector('.close-modal');
            var cancelBtn = document.getElementById('cancelDeleteSite');
            var confirmBtn = document.getElementById('confirmDeleteSite');

            function closeModal() {
                modal.style.display = 'none';
                modal.removeAttribute('data-delete-site-id');
            }

            // 关闭按钮
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }

            // 取消按钮
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeModal);
            }

            // 确认删除按钮
            if (confirmBtn) {
                confirmBtn.addEventListener('click', function() {
                    var siteId = modal.getAttribute('data-delete-site-id');
                    if (siteId) {
                        closeModal();
                        executeDeleteDockingSite(siteId);
                    }
                });
            }

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            });
        }

        // 执行删除操作
        function executeDeleteDockingSite(siteId) {
            if (!siteId) return;

            // 请求数据
            var requestData = {
                id: siteId
            };

            // 计算签名
            try {
                requestData.sign = calculateSignForEdit(requestData);
            } catch (error) {
                showToast('计算签名失败: ' + error.message, 'error');
                return;
            }

            // 显示加载中
            showLoading();

            // 发送请求
            fetch('/api/deleteDocking', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                
                if (data.code !== 200) {
                    showToast(data.msg || '删除失败', 'error');
                    return;
                }
                
                showToast('删除成功', 'success');
                
                // 刷新列表
                loadDockingSites();
            })
            .catch(error => {
                hideLoading();
                showToast('删除失败: ' + error.message, 'error');
            });
        }
        
        // 绑定列表中的操作按钮事件
        function bindActionButtons() {
            // 绑定删除按钮
            document.querySelectorAll('.delete-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    var siteId = this.getAttribute('data-id');
                    deleteDockingSite(siteId);
                });
            });

            // 绑定编辑按钮
            document.querySelectorAll('.edit-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    var siteId = this.getAttribute('data-id');
                    openEditModal(siteId);
                });
            });

            // 绑定进货按钮
            document.querySelectorAll('.purchase-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    var siteId = this.getAttribute('data-id');
                    openPurchaseModal(siteId);
                });
            });
        }
        
        // 打开编辑模态框
        function openEditModal(siteId) {
            // 获取要编辑的对接网站数据
            var site = window.dockingSites[siteId];

            if (!site) {
                showToast('无法加载对接网站数据', 'error');
                return;
            }

            // 填充表单数据
            document.getElementById('editSiteId').value = site.id;
            document.getElementById('editSiteType').value = site.type;
            document.getElementById('editSiteName').value = site.name;
            document.getElementById('editSiteDomain').value = site.url;
            document.getElementById('editSiteAppId').value = site.appid;
            // 密钥不预填充，保持空白
            document.getElementById('editSiteSecret').value = '';

            // 显示模态框
            var modal = document.getElementById('editDockingSiteModal');
            if (modal) {
                modal.style.display = 'flex';
            }

            // 初始化隐藏聚比价提示
            const editJubijiaCallbackTip = document.getElementById('editJubijiaCallbackTip');
            if (editJubijiaCallbackTip) {
                editJubijiaCallbackTip.style.display = 'none';
            }

            // 添加网站类型选择联动效果
            var editSiteTypeSelect = document.getElementById('editSiteType');
            if (editSiteTypeSelect) {
                // 先触发一次以设置初始状态
                toggleJubijiaCallbackTip(editSiteTypeSelect.value, 'editJubijiaCallbackTip', 'editJubijiaCallbackUrl');

                // 绑定change事件
                editSiteTypeSelect.addEventListener('change', function() {
                    // 显示或隐藏聚比价回调地址提示
                    toggleJubijiaCallbackTip(this.value, 'editJubijiaCallbackTip', 'editJubijiaCallbackUrl');
                });
            }

            // 绑定关闭事件
            var closeBtn = modal.querySelector('.close-modal');
            var cancelBtn = document.getElementById('cancelEditSite');
            
            function closeModal() {
                modal.style.display = 'none';
                // 隐藏聚比价提示
                const editJubijiaCallbackTip = document.getElementById('editJubijiaCallbackTip');
                if (editJubijiaCallbackTip) {
                    editJubijiaCallbackTip.style.display = 'none';
                }
            }
            
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }
            
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeModal);
            }
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            });
            
            // 绑定表单提交事件
            const form = document.getElementById('editDockingSiteForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // 获取表单值
                    var siteId = document.getElementById('editSiteId').value;
                    var siteType = document.getElementById('editSiteType').value;
                    var siteName = document.getElementById('editSiteName').value.trim();
                    var siteDomain = document.getElementById('editSiteDomain').value.trim();
                    var siteAppId = document.getElementById('editSiteAppId').value.trim();
                    var siteSecret = document.getElementById('editSiteSecret').value.trim();
                    
                    // 表单验证
                    if (!siteName) {
                        showToast('请输入网站名称', 'error');
                        return;
                    }
                    
                    if (!siteDomain) {
                        showToast('请输入网站域名', 'error');
                        return;
                    }
                    
                    if (!siteAppId) {
                        showToast('请输入网站APPID', 'error');
                        return;
                    }
                    
                    // 准备请求数据
                    var requestData = {
                        id: siteId,
                        name: siteName,
                        url: siteDomain,
                        appid: siteAppId,
                        type: siteType
                    };
                    
                    // 如果用户填写了新密钥，则添加到请求中
                    if (siteSecret) {
                        requestData.key = siteSecret;
                    }

                    // 计算签名
                    try {
                        requestData.sign = calculateSignForEdit(requestData);
                    } catch (error) {
                        showToast('计算签名失败: ' + error.message, 'error');
                        return;
                    }

                    showLoading();

                    // 发送请求
                    fetch('/api/updateDocking', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        
                        if (data.code !== 200) {
                            showToast(data.msg || '修改失败', 'error');
                            return;
                        }
                        
                        showToast('网站信息修改成功', 'success');
                        
                        // 关闭模态框
                        closeModal();
                        
                        // 刷新列表
                        loadDockingSites();
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('修改失败: ' + error.message, 'error');
                    });
                });
            }
        }
        
        // 辅助函数 - 显示加载中
        function showLoading() {
            var loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('show');
            } else {
            }
        }

        // 辅助函数 - 隐藏加载中
        function hideLoading() {
            var loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.remove('show');
            } else {
            }
        }
        
        // 辅助函数 - 显示通知
        function showToast(message, type) {
            if (typeof type === 'undefined') type = 'success';
            // 获取或创建通知容器
            var toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                // 如果容器不存在，创建一个新的
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }

            // 确保通知容器显示在最上层
            toastContainer.style.zIndex = '9999';

            // 防止重复提示：检查是否已存在相同消息的通知
            if (!window.toastMessages) {
                window.toastMessages = {};
            }

            // 创建消息唯一标识（消息 + 类型）
            var messageKey = message + '-' + type;
            
            // 如果相同消息已经在显示中，直接返回
            if (window.toastMessages[messageKey]) {
                return;
            }
            
            // 标记该消息正在显示
            window.toastMessages[messageKey] = true;

            var toast = document.createElement('div');
            toast.className = 'toast toast-' + type;
            toast.innerHTML =
                '<div class="toast-icon">' +
                    '<i class="fas ' + (type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle') + '"></i>' +
                '</div>' +
                '<div class="toast-message">' + message + '</div>';
            
            toastContainer.appendChild(toast);
            
            // 强制重绘
            toast.offsetHeight;
            
            // 显示通知
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                
                // 设置延时删除DOM元素，与CSS过渡时间相匹配
                setTimeout(() => {
                    toast.remove();
                    // 从消息队列中移除，允许再次显示相同消息
                    delete window.toastMessages[messageKey];
                }, 300);
            }, 3000);
        }

        // 在页面加载完成或者页面内容被加载到主页时执行
        function setupEventListeners() {
            // 如果已经初始化过，不再重复执行
            if (eventsInitialized) {
                return;
            }
            
            var addBtn = document.getElementById('addDockingSiteBtn');
            var modal = document.getElementById('addDockingSiteModal');
            var closeBtn = document.querySelector('.close-modal');
            var cancelBtn = document.getElementById('cancelAddSite');
            var form = document.getElementById('addDockingSiteForm');

            if (!addBtn || !modal || !closeBtn || !cancelBtn || !form) {
                // 如果元素没有准备好，稍后再尝试
                setTimeout(setupEventListeners, 100);
                return;
            }


            // 打开模态框
            addBtn.addEventListener('click', function() {
                modal.style.display = 'flex';
                // 初始化隐藏聚比价提示
                const jubijiaCallbackTip = document.getElementById('jubijiaCallbackTip');
                if (jubijiaCallbackTip) {
                    jubijiaCallbackTip.style.display = 'none';
                }
            });

            // 关闭模态框
            function closeModal() {
                modal.style.display = 'none';
                form.reset();
                // 隐藏聚比价提示
                const jubijiaCallbackTip = document.getElementById('jubijiaCallbackTip');
                if (jubijiaCallbackTip) {
                    jubijiaCallbackTip.style.display = 'none';
                }
            }

            closeBtn.addEventListener('click', closeModal);
            cancelBtn.addEventListener('click', closeModal);

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            });

            // 添加网站类型选择联动效果
            var siteTypeSelect = document.getElementById('siteType');
            var siteDomainInput = document.getElementById('siteDomain');
            
            if (siteTypeSelect && siteDomainInput) {
            siteTypeSelect.addEventListener('change', function() {
                if (this.value === 'jubijia') {
                    siteDomainInput.value = 'http://jubijia.com/';
                    showToast('已自动填充域名', 'success');
                }

                // 显示或隐藏聚比价回调地址提示
                toggleJubijiaCallbackTip(this.value, 'jubijiaCallbackTip', 'jubijiaCallbackUrl');
            });
            }

            // 表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // 阻止默认提交行为
                
                // 获取表单值
                var siteType = document.getElementById('siteType').value;
                var siteName = document.getElementById('siteName').value.trim();
                var siteDomain = document.getElementById('siteDomain').value.trim();
                var siteAppId = document.getElementById('siteAppId').value.trim();
                var siteSecret = document.getElementById('siteSecret').value.trim();
                
                // 表单验证
                if (!siteType) {
                    showToast('请选择网站类型', 'error');
                    return;
                }
                
                if (!siteName) {
                    showToast('请输入网站名称', 'error');
                    return;
                }
                
                if (!siteDomain) {
                    showToast('请输入网站域名', 'error');
                    return;
                }
                
                if (!siteAppId) {
                    showToast('请输入网站APPID', 'error');
                    return;
                }
                
                if (!siteSecret) {
                    showToast('请输入网站密钥', 'error');
                    return;
                }
                
                // 准备请求数据
                var requestData = {
                    name: siteName,
                    url: siteDomain,
                    appid: siteAppId,
                    type: siteType,
                    key: siteSecret
                };

                // 计算签名
                try {
                    requestData.sign = calculateSignForEdit(requestData);
                } catch (error) {
                    showToast('计算签名失败: ' + error.message, 'error');
                    return;
                }

                // 显示加载中
                showLoading();

                // 发送请求
                fetch('/api/addDocking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    
                    if (data.code !== 200) {
                        showToast(data.msg || '添加失败', 'error');
                        return;
                    }
                    
                    showToast('网站添加成功！', 'success');
                    closeModal();
                    
                    // 刷新列表
                    loadDockingSites();
                })
                .catch(error => {
                    hideLoading();
                    showToast('添加失败: ' + error.message, 'error');
                });
            });

            // 加载对接网站列表
            loadDockingSites();

            // 标记事件已初始化
            eventsInitialized = true;
        }

        // 如果DOM已经加载完成，直接执行，否则等待DOMContentLoaded事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupEventListeners, {once: true});
        } else {
            setupEventListeners();
        }

        // 只添加一次延迟执行，避免重复
        if (!eventsInitialized) {
            setTimeout(setupEventListeners, 500);
        }
        
        // 公开函数到全局作用域
        window.openPurchaseModal = openPurchaseModal;
        window.showLoading = showLoading;
        window.hideLoading = hideLoading;
        window.showToast = showToast;
    })();

    // 添加打开进货模态框的函数
    function openPurchaseModal(siteId) {
        const purchaseModal = document.getElementById('purchaseModal');
        if (!purchaseModal) return;
        
        // 初始化全局存储对象（如果不存在）
        if (!window.dockingSites) {
            window.dockingSites = {};
        }
        
        // 获取网站信息（类型、appid和key）
        let siteType = '';
        let siteAppId = '';
        let siteKey = '';
        
        // 从全局存储中获取网站信息
        if (siteId && window.dockingSites && window.dockingSites[siteId]) {
            const siteInfo = window.dockingSites[siteId];
            siteType = siteInfo.type;
            siteAppId = siteInfo.appid;
            siteKey = siteInfo.key;
            
            // 加载分类数据
            if (siteType && siteAppId && siteKey) {
                // 保存到模态框属性
                purchaseModal.setAttribute('data-site-key', siteKey);
                // 加载分类数据
                loadCategoryTree(siteType, siteAppId, siteKey);
            } else {
                showToast('网站信息获取失败，无法加载分类数据', 'error');
            }
        } else {
            // 如果全局存储中没有，尝试从DOM获取
            const clickedBtn = document.querySelector(`.purchase-btn[data-id="${siteId}"]`);
            if (clickedBtn) {
                const row = clickedBtn.closest('tr');
                if (row) {
                    // 第3列是网站类型
                    siteType = row.querySelector('td:nth-child(3)').textContent.trim();
                    // 第4列是对接账号/appid
                    siteAppId = row.querySelector('td:nth-child(4)').textContent.trim();
                    
                    // 尝试从/api/getDockingList重新获取完整信息
                    fetch('/api/getDockingList')
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 200 && data.data && data.data.list) {
                                const sites = data.data.list;
                                const site = sites.find(site => site.id === siteId);
                                if (site) {
                                    siteKey = site.key;
                                    
                                    // 保存到全局存储
                                    window.dockingSites[siteId] = {
                                        id: siteId,
                                        type: siteType,
                                        appid: siteAppId,
                                        key: siteKey,
                                        name: site.name,
                                        url: site.url
                                    };
                                    
                                    // 保存到模态框属性
                                    purchaseModal.setAttribute('data-site-key', siteKey);
                                    
                                    // 加载分类数据
                                    loadCategoryTree(siteType, siteAppId, siteKey);
                                } else {
                                    showToast('找不到网站信息，无法加载分类数据', 'error');
                                }
                            } else {
                                showToast('获取网站列表失败，无法加载分类数据', 'error');
                            }
                        })
                        .catch(error => {
                            showToast('获取网站信息失败:' + error.message, 'error');
                        });
                }
            }
        }
        
        // 显示模态框
        purchaseModal.style.display = 'flex';
        
        // 绑定关闭事件
        const closeBtn = purchaseModal.querySelector('.close-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                purchaseModal.style.display = 'none';
            });
        }
        
        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            if (event.target === purchaseModal) {
                purchaseModal.style.display = 'none';
            }
        });
        
        // 保存当前的网站ID和类型到模态框上，方便后续使用
        purchaseModal.setAttribute('data-site-id', siteId || '');
        purchaseModal.setAttribute('data-site-type', siteType || '');
        purchaseModal.setAttribute('data-site-appid', siteAppId || '');
        
        // 初始化分类树事件
        initCategoryTreeEvents();
        
        // 加载商品列表（示例）
        loadProductList(siteId);
        
        // 在加载完成后增强滚动功能
        setTimeout(enhanceScrolling, 500);
        
        // 初始化分页控件
        setTimeout(initPagination, 500);
        
        // 添加窗口调整函数，1秒后执行，确保表格完全加载
        setTimeout(adjustModalWidth, 1000);
    }
    
    // 添加动态调整模态框宽度的函数
    function adjustModalWidth() {
        const purchaseModal = document.getElementById('purchaseModal');
        const modalContent = document.querySelector('.purchase-modal-content');
        const productTable = document.querySelector('.product-table');
        
        if (!modalContent || !productTable) return;
        
        // 获取表格实际宽度
        const tableWidth = productTable.offsetWidth;
        // 获取分类树宽度
        const categoryTreeWidth = document.querySelector('.category-tree-container')?.offsetWidth || 240;
        // 添加一些边距
        const padding = 40;
        
        // 计算模态框的理想宽度 = 表格宽度 + 分类树宽度 + 边距
        const idealWidth = tableWidth + categoryTreeWidth + padding;
        
        // 获取视窗宽度
        const viewportWidth = window.innerWidth;
        
        // 最终宽度取理想宽度和视窗宽度的95%中的较小值
        const finalWidth = Math.min(idealWidth, viewportWidth * 0.95);
        
        // 应用新宽度，但保持最小宽度
        modalContent.style.width = `${finalWidth}px`;
        
        // 模态框宽度调整完成
    }
    
    // 增强调整窗口大小时的响应
    window.addEventListener('resize', function() {
        // 检查进货模态框是否可见
        const purchaseModal = document.getElementById('purchaseModal');
        if (purchaseModal && purchaseModal.style.display === 'flex') {
            // 如果可见，重新调整宽度
            adjustModalWidth();
        }
    });
    
    // 添加全局变量用于分页控制
    var currentPage = 1; // 当前页码
    var pageSize = 10;   // 每页显示数量
    var totalItems = 0;  // 商品总数
    var allProducts = []; // 存储所有商品
    
    // 修改loadProductList函数，实现分页功能
    function loadProductList(siteId, categoryId = '', filters = {}) {
        const purchaseModal = document.getElementById('purchaseModal');
        const productTableContainer = document.querySelector('.product-table-container');
        const productTable = document.querySelector('.product-table');
        const productList = document.getElementById('productList');
        const productLoading = document.querySelector('.product-loading');
        const productEmpty = document.querySelector('.product-empty');
        
        if (!productTableContainer || !productList) return;
        
        // 显示加载中，隐藏表格和空状态
        if (productLoading) productLoading.style.display = 'block';
        if (productTable) productTable.style.display = 'none';
        if (productEmpty) productEmpty.style.display = 'none';
        
        // 清空现有内容
        productList.innerHTML = '';
        
        // 重置分页
        currentPage = 1;
        
        
        // 获取当前网站类型和参数
        const siteType = purchaseModal?.getAttribute('data-site-type') || '';
        const siteAppId = purchaseModal?.getAttribute('data-site-appid') || '';
        const siteKey = purchaseModal?.getAttribute('data-site-key') || '';
        
        // 如果是聚比价网站类型，调用聚比价的商品接口
        if (siteType && siteType.toLowerCase() === 'jubijia' && siteAppId && siteKey) {
            // 准备请求参数
            const requestData = {
                userId: siteAppId,
                categoryId: categoryId || ''
            };
            
            // 如果有搜索关键词，添加name参数
            if (filters.keyword) {
                requestData.name = filters.keyword;
            }
            
            // 计算签名
            try {
                requestData.sign = calculateSign(requestData, siteKey);
                
                // 发送请求到聚比价商品接口
                fetch('http://jubijia.com/api/open/all-product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败，状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    // 隐藏加载中
                    if (productLoading) productLoading.style.display = 'none';
                    
                    
                    if (data.code !== 200) {
                        showToast(data.msg || '获取商品列表失败', 'error');
                        // 显示空状态
                        if (productEmpty) productEmpty.style.display = 'block';
                        return;
                    }
                    
                    // 获取所有商品
                    const products = data.data || [];
                    
                    // 应用价格过滤
                    let filteredProducts = products;
                    if (filters.price && !isNaN(parseFloat(filters.price))) {
                        const maxPrice = parseFloat(filters.price);
                        filteredProducts = products.filter(product => parseFloat(product.price) <= maxPrice);
                    }
                    
                    // 更新全局变量
                    allProducts = filteredProducts;
                    totalItems = filteredProducts.length;
                    
                    // 如果没有数据，显示空状态
                    if (filteredProducts.length === 0) {
                        if (productEmpty) productEmpty.style.display = 'block';
                        if (productTable) productTable.style.display = 'none';
                        
                        // 更新分页信息
                        updatePagination();
                        return;
                    }
                    
                    // 显示表格
                    if (productTable) productTable.style.display = 'table';
                    
                    // 显示当前页的数据
                    renderProductPage();
                    
                    // 更新分页控件
                    updatePagination();
                    
                    // 在商品加载完成后，增强滚动功能
                    setTimeout(enhanceScrolling, 100);
                })
                .catch(error => {
                    // 隐藏加载中
                    if (productLoading) productLoading.style.display = 'none';
                    // 显示空状态
                    if (productEmpty) productEmpty.style.display = 'block';
                    
                    showToast('获取商品列表失败: ' + error.message, 'error');
                });
            } catch (err) {
                // 隐藏加载中
                if (productLoading) productLoading.style.display = 'none';
                // 显示空状态
                if (productEmpty) productEmpty.style.display = 'block';
                
                showToast('计算签名失败: ' + err.message, 'error');
            }
        } else {
            // 非聚比价网站或信息不完整，使用API获取商品列表
            fetch('/api/getProductList', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    siteId: siteId,
                    categoryId: categoryId,
                    filters: filters
                })
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载中
                if (productLoading) productLoading.style.display = 'none';
                
                if (data.code !== 200) {
                    showToast(data.msg || '获取商品列表失败', 'error');
                    // 显示空状态
                    if (productEmpty) productEmpty.style.display = 'block';
                    return;
                }
                
                // 获取商品列表
                const products = data.data || [];
                
                // 更新全局变量
                allProducts = products;
                totalItems = products.length;
                
                // 如果没有数据，显示空状态
                if (products.length === 0) {
                    if (productEmpty) productEmpty.style.display = 'block';
                    if (productTable) productTable.style.display = 'none';
                    
                    // 更新分页信息
                    updatePagination();
                    return;
                }
                
                // 显示表格
                if (productTable) productTable.style.display = 'table';
                
                // 显示当前页的数据
                renderProductPage();
                
                // 更新分页控件
                updatePagination();
                
                // 在商品加载完成后，增强滚动功能
                setTimeout(enhanceScrolling, 100);
            })
            .catch(error => {
                // 隐藏加载中
                if (productLoading) productLoading.style.display = 'none';
                // 显示空状态
                if (productEmpty) productEmpty.style.display = 'block';
                
                showToast('获取商品列表失败: ' + error.message, 'error');
            });
        }
    }
    
    // 渲染当前页的商品数据
    function renderProductPage() {
        const productList = document.getElementById('productList');
        if (!productList) return;
        
        // 清空当前列表
        productList.innerHTML = '';
        
        // 计算当前页的起始和结束索引
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, allProducts.length);
        
        // 获取当前页的数据
        const currentPageData = allProducts.slice(startIndex, endIndex);
        
        // 渲染商品
        currentPageData.forEach((product, index) => {
            const row = document.createElement('tr');
            
            // 商品图片处理
            let imageHtml = '';
            if (product.image) {
                imageHtml = `<img src="${product.image}" alt="${product.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px;">`;
            } else {
                // 无图片时显示占位符
                imageHtml = `<div class="placeholder-image">${product.name.substring(0, 1)}</div>`;
            }
            
            row.innerHTML = `
                <td style="text-align: center; padding: 10px;">
                    <div class="kawaii-checkbox">
                        <input type="checkbox" id="product-${index}-${product.id}" class="kawaii-checkbox-input product-checkbox" data-id="${product.id}">
                        <label for="product-${index}-${product.id}" class="kawaii-checkbox-label">
                            <div class="kawaii-checkbox-face">
                                <div class="kawaii-checkbox-eyes"></div>
                                <div class="kawaii-checkbox-mouth"></div>
                            </div>
                        </label>
                    </div>
                </td>
                <td title="${product.name}">${product.name}</td>
                <td>
                    <div class="product-image">
                        ${imageHtml}
                    </div>
                </td>
                <td>${product.code || product.id}</td>
                <td>¥${parseFloat(product.price).toFixed(2)}</td>
                <td>${product.type || '聚比价商品'}</td>
                <td><span class="status-badge status-active">${product.status || '在售'}</span></td>
                <td><button class="action-btn-small not-docked">${product.dockingStatus || '未对接'}</button></td>
            `;
            
            productList.appendChild(row);
        });
        
        // 绑定复选框事件
        bindCheckboxEvents();
        
        // 在商品列表渲染完成后调整模态框宽度
        setTimeout(adjustModalWidth, 300);
    }
    
    // 更新分页控件
    function updatePagination() {
        const currentPageElement = document.querySelector('.current-page');
        const pageInfo = document.querySelector('.page-info');
        const prevPageBtn = document.querySelector('.page-btn:first-child');
        const nextPageBtn = document.querySelector('.page-btn:nth-child(3)');
        const pageSizeSelect = document.querySelector('.page-size-select');
        
        if (!currentPageElement || !pageInfo) return;
        
        // 计算总页数
        const totalPages = Math.ceil(totalItems / pageSize) || 1;
        
        // 确保当前页在有效范围内
        if (currentPage > totalPages) {
            currentPage = totalPages;
        }
        
        // 更新当前页显示
        currentPageElement.textContent = currentPage;
        
        // 更新总记录数信息
        pageInfo.textContent = `共${totalItems}条`;
        
        // 更新上一页按钮状态
        if (prevPageBtn) {
            if (currentPage <= 1) {
                prevPageBtn.disabled = true;
                prevPageBtn.classList.add('disabled');
            } else {
                prevPageBtn.disabled = false;
                prevPageBtn.classList.remove('disabled');
            }
        }
        
        // 更新下一页按钮状态
        if (nextPageBtn) {
            if (currentPage >= totalPages) {
                nextPageBtn.disabled = true;
                nextPageBtn.classList.add('disabled');
            } else {
                nextPageBtn.disabled = false;
                nextPageBtn.classList.remove('disabled');
            }
        }
        
        // 更新每页显示数量选择器
        if (pageSizeSelect) {
            pageSizeSelect.value = pageSize.toString();
        }
    }
    
    // 初始化分页控件事件
    function initPagination() {
        const prevPageBtn = document.querySelector('.page-btn:first-child');
        const nextPageBtn = document.querySelector('.page-btn:nth-child(3)');
        const pageSizeSelect = document.querySelector('.page-size-select');
        
        // 上一页按钮
        if (prevPageBtn) {
            prevPageBtn.addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    renderProductPage();
                    updatePagination();
                    // 滚动到表格顶部
                    const productTableContainer = document.querySelector('.product-table-container');
                    if (productTableContainer) {
                        productTableContainer.scrollTop = 0;
                    }
                    
                    // 翻页后调整窗口宽度
                    setTimeout(adjustModalWidth, 300);
                }
            });
        }
        
        // 下一页按钮
        if (nextPageBtn) {
            nextPageBtn.addEventListener('click', function() {
                const totalPages = Math.ceil(totalItems / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderProductPage();
                    updatePagination();
                    // 滚动到表格顶部
                    const productTableContainer = document.querySelector('.product-table-container');
                    if (productTableContainer) {
                        productTableContainer.scrollTop = 0;
                    }
                    
                    // 翻页后调整窗口宽度
                    setTimeout(adjustModalWidth, 300);
                }
            });
        }
        
        // 每页显示数量选择器
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', function() {
                const newPageSize = parseInt(this.value);
                if (!isNaN(newPageSize) && newPageSize > 0) {
                    // 计算新的当前页，尽量保持显示的是相同的数据区域
                    const currentStartIndex = (currentPage - 1) * pageSize;
                    pageSize = newPageSize;
                    currentPage = Math.floor(currentStartIndex / pageSize) + 1;
                    
                    renderProductPage();
                    updatePagination();
                    // 滚动到表格顶部
                    const productTableContainer = document.querySelector('.product-table-container');
                    if (productTableContainer) {
                        productTableContainer.scrollTop = 0;
                    }
                    
                    // 页面大小变化后调整窗口宽度
                    setTimeout(adjustModalWidth, 300);
                }
            });
        }
    }
    
    // 绑定复选框事件
    function bindCheckboxEvents() {
        // 单选框事件
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // 如果取消选中了某个复选框，也取消"全选"复选框
                if (!this.checked) {
                    const selectAllCheckbox = document.getElementById('selectAllProducts');
                    if (selectAllCheckbox && selectAllCheckbox.checked) {
                        selectAllCheckbox.checked = false;
                    }
                }
                
                // 检查是否已经全选
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                const selectAllCheckbox = document.getElementById('selectAllProducts');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allChecked;
                }
            });
        });
        
        // 全选复选框事件
        const selectAllCheckbox = document.getElementById('selectAllProducts');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false; // 默认不选中
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.product-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }
        
        // 不再在这里绑定批量添加按钮事件，统一在initBatchAction中处理
    }
    
    // 加载分类树，修改为接受网站类型、appId和key参数
    function loadCategoryTree(siteType, appId, key) {
        const categoryTree = document.getElementById('categoryTreeList');
        if (!categoryTree) return;
        
        // 显示加载中
        showLoading();
        
        
        // 如果类型是jubijia，则调用聚比价分类接口
        if (siteType && siteType.toLowerCase() === 'jubijia') {
            
            // 准备请求参数
            const requestData = {
                userId: appId || '', // 使用appId作为userId
                // id为空表示获取顶级分类
                id: ''
            };
            
            // 计算签名
            try {
                requestData.sign = calculateSign(requestData, key);
                
                // 发送请求到聚比价分类接口 - 使用完整URL
                fetch('http://jubijia.com/api/open/all-category', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams(requestData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败，状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    hideLoading();
                    
                    if (data.code !== 200) {
                        showToast(data.msg || '获取分类列表失败', 'error');
                        return;
                    }
                    
                    const categories = data.data || [];
                    
                    // 清空现有内容
                    categoryTree.innerHTML = '';
                    
                    // 添加"全部商品"选项
                    const allProductsItem = document.createElement('li');
                    allProductsItem.className = 'tree-item';
                    allProductsItem.innerHTML = `
                        <div class="tree-item-header tree-all-item">
                            <i class="fas fa-th-large"></i>
                            <span>全部商品</span>
                        </div>
                    `;
                    categoryTree.appendChild(allProductsItem);
                    
                    // 构建分类树 - 聚比价版本
                    buildCategoryTree(categories, categoryTree);
                    
                    // 初始化事件
                    initCategoryTreeEvents();
                    
                    // 默认选中"全部商品"
                    if (allProductsItem) {
                        allProductsItem.classList.add('selected');
                    }
                    
                    // 加载完成后启用滚动功能
                    setTimeout(enhanceScrolling, 100);
                })
                .catch(error => {
                    hideLoading();
                    showToast('获取分类列表失败: ' + error.message, 'error');
                    
                    // 失败时显示默认分类
                    categoryTree.innerHTML = `
                        <li class="tree-item selected">
                            <div class="tree-item-header">
                                <i class="fas fa-th-large"></i>
                                <span>全部商品</span>
                            </div>
                        </li>
                    `;
                    
                    // 重新初始化事件
                    initCategoryTreeEvents();
                    
                    // 启用滚动功能
                    setTimeout(enhanceScrolling, 100);
                });
            } catch (err) {
                hideLoading();
                showToast('计算签名失败: ' + err.message, 'error');
            }
        } else {
            // 其他类型的对接网站，使用原来的分类获取逻辑
        fetch('/api/get_category_list')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                
                if (data.code !== 200) {
                    showToast(data.msg || '获取分类列表失败', 'error');
                    return;
                }
                
                const categories = data.data || [];
                
                // 清空现有内容
                categoryTree.innerHTML = '';
                
                // 添加"全部商品"选项
                const allProductsItem = document.createElement('li');
                allProductsItem.className = 'tree-item';
                allProductsItem.innerHTML = `
                    <div class="tree-item-header tree-all-item">
                        <i class="fas fa-th-large"></i>
                        <span>全部商品</span>
                    </div>
                `;
                categoryTree.appendChild(allProductsItem);
                
                    // 构建原有格式的分类树
                categories.forEach(category => {
                    const item = document.createElement('li');
                    item.className = 'tree-item';
                    
                    // 如果有子分类，添加展开/折叠功能
                    if (category.children && category.children.length > 0) {
                        item.innerHTML = `
                            <div class="tree-item-header" data-id="${category.id}">
                                <i class="fas fa-caret-right"></i>
                                <span>${category.name}</span>
                            </div>
                            <ul style="display: none;">
                                ${category.children.map(child => `
                                    <li class="tree-item">
                                        <div class="tree-item-header" data-id="${child.id}">
                                            <i class="fas fa-tag"></i>
                                            <span>${child.name}</span>
                                        </div>
                                    </li>
                                `).join('')}
                            </ul>
                        `;
                    } else {
                        item.innerHTML = `
                            <div class="tree-item-header" data-id="${category.id}">
                                <i class="fas fa-tag"></i>
                                <span>${category.name}</span>
                            </div>
                        `;
                    }
                    
                    categoryTree.appendChild(item);
                });
                
                // 初始化事件
                initCategoryTreeEvents();
                
                // 默认选中"全部商品"
                if (allProductsItem) {
                    allProductsItem.classList.add('selected');
                }
                    
                    // 加载完成后启用滚动功能
                    setTimeout(enhanceScrolling, 100);
            })
            .catch(error => {
                hideLoading();
                showToast('获取分类列表失败: ' + error.message, 'error');
                
                // 失败时显示默认分类
                categoryTree.innerHTML = `
                    <li class="tree-item selected">
                        <div class="tree-item-header">
                            <i class="fas fa-th-large"></i>
                            <span>全部商品</span>
                        </div>
                    </li>
                `;
                
                // 重新初始化事件
                initCategoryTreeEvents();
                    
                    // 启用滚动功能
                    setTimeout(enhanceScrolling, 100);
                });
        }
    }
    
    // 构建聚比价分类树 - 递归构建树形结构
    function buildCategoryTree(categories, parentElement) {
        // 使用真正递归的方式构建分类树
        function buildTreeRecursive(parentId, container) {
            // 查找当前parentId下的所有子分类
            const childCategories = categories.filter(cat => cat.parentId === parentId);
            
            // 如果没有子分类则返回
            if (childCategories.length === 0) {
                return;
            }
            
            // 处理每个子分类
            childCategories.forEach(category => {
                // 创建分类项
                const item = document.createElement('li');
                item.className = 'tree-item';
                
                // 检查该分类是否有自己的子分类
                const hasChildren = categories.some(cat => cat.parentId === category.id);
                
                if (hasChildren) {
                    // 如果有子分类，设置为可展开
                    item.innerHTML = `
                        <div class="tree-item-header" data-id="${category.id}">
                            <i class="fas fa-caret-right"></i>
                            <span>${category.name}</span>
                        </div>
                        <ul style="display: none;"></ul>
                    `;
                    
                    // 获取子分类的容器
                    const childContainer = item.querySelector('ul');
                    
                    // 递归构建子分类
                    buildTreeRecursive(category.id, childContainer);
                } else {
                    // 如果没有子分类，使用叶子节点样式
                    item.innerHTML = `
                        <div class="tree-item-header" data-id="${category.id}">
                            <i class="fas fa-tag"></i>
                            <span>${category.name}</span>
                        </div>
                    `;
                }
                
                // 添加到父容器
                container.appendChild(item);
            });
        }
        
        // 先找出所有顶级分类（parentId为空）
        const topLevelCategories = categories.filter(cat => !cat.parentId);
        
        // 处理顶级分类
        topLevelCategories.forEach(category => {
            // 创建分类项
            const item = document.createElement('li');
            item.className = 'tree-item';
            
            // 检查是否有子分类
            const hasChildren = categories.some(cat => cat.parentId === category.id);
            
            if (hasChildren) {
                // 如果有子分类，设置为可展开
                item.innerHTML = `
                    <div class="tree-item-header" data-id="${category.id}">
                        <i class="fas fa-caret-right"></i>
                        <span>${category.name}</span>
                    </div>
                    <ul style="display: none;"></ul>
                `;
                
                // 获取子容器
                const childContainer = item.querySelector('ul');
                
                // 递归构建子分类
                buildTreeRecursive(category.id, childContainer);
            } else {
                // 如果没有子分类
                item.innerHTML = `
                    <div class="tree-item-header" data-id="${category.id}">
                        <i class="fas fa-tag"></i>
                        <span>${category.name}</span>
                    </div>
                `;
            }
            
            // 添加到父元素
            parentElement.appendChild(item);
            });
    }
    
    // 初始化分类树的交互事件
    function initCategoryTreeEvents() {
        // 获取所有树项的头部
        const treeItems = document.querySelectorAll('.tree-item-header');
        
        // 先移除所有现有事件，防止重复绑定
        treeItems.forEach(item => {
            // 移除现有事件
            item.removeEventListener('click', toggleTreeItem);
            // 添加新事件
            item.addEventListener('click', toggleTreeItem);
        });
        
        // 默认选中"全部商品"选项
        const allProductsItem = document.querySelector('.tree-all-item');
        if (allProductsItem) {
            // 移除所有选中状态
            document.querySelectorAll('.tree-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            allProductsItem.closest('.tree-item').classList.add('selected');
            
            // 触发加载全部商品
            loadProductList('');
        }
        
        // 初始化搜索功能
        initSearchEvents();
    }
    
    // 初始化搜索相关事件
    function initSearchEvents() {
        // 分类搜索
        const categorySearch = document.getElementById('categorySearch');
        if (categorySearch) {
            categorySearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                const treeItems = document.querySelectorAll('.tree-item');
                
                treeItems.forEach(item => {
                    const itemText = item.querySelector('span')?.textContent.toLowerCase() || '';
                    if (searchTerm === '' || itemText.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }
        
        // 商品搜索按钮
        const searchBtn = document.querySelector('.search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', function() {
                // 获取搜索条件
                const keyword = document.getElementById('productSearch')?.value || '';
                const price = document.getElementById('priceSearch')?.value || '';
                const status = document.getElementById('stockFilter')?.value || '';
                
                // 获取当前选中的分类ID
                const selectedCategory = document.querySelector('.tree-item.selected .tree-item-header');
                const categoryId = selectedCategory ? selectedCategory.getAttribute('data-id') : '';
                
                // 准备过滤条件
                const filters = {
                    keyword: keyword,
                    price: price,
                    status: status
                };
                
                
                // 重新加载商品列表
                loadProductList('', categoryId, filters);
                
                // 搜索后调整窗口宽度
                setTimeout(adjustModalWidth, 1000);
            });
        }
        
        // 为搜索输入框添加回车触发搜索功能
        const productSearch = document.getElementById('productSearch');
        const priceSearch = document.getElementById('priceSearch');
        
        if (productSearch) {
            productSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchBtn?.click();
                }
            });
        }
        
        if (priceSearch) {
            priceSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchBtn?.click();
                }
            });
        }
        
        // 为批量添加按钮绑定事件
        initBatchAction();
    }
    
    // 初始化批量操作功能
    function initBatchAction() {
        const batchPurchaseBtn = document.querySelector('.batch-purchase-btn');
        if (!batchPurchaseBtn) return;
        
        // 防止重复绑定事件
        if (batchPurchaseBtn.hasAttribute('data-batch-event-bound')) {
            return;
        }
        
        batchPurchaseBtn.addEventListener('click', function() {
            const selectedProducts = [];
            const selectedRows = [];
            const productCodes = []; // 新增：用于存储商品编码的数组
            
            document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
                const productId = checkbox.getAttribute('data-id');
                const row = checkbox.closest('tr');
                
                if (productId && row) {
                    const productName = row.querySelector('td:nth-child(2)').textContent;
                    const productPrice = row.querySelector('td:nth-child(5)').textContent;
                    const productCode = row.querySelector('td:nth-child(4)').textContent; // 获取商品编码
                    
                    // 将商品编码添加到数组
                    productCodes.push(productCode);
                    
                    selectedProducts.push({
                        id: productId,
                        name: productName,
                        price: productPrice,
                        code: productCode // 同时保存在selectedProducts中
                    });
                    
                    selectedRows.push(row);
                }
            });
            
            if (selectedProducts.length === 0) {
                showToast('请至少选择一个商品', 'error');
                return;
            }
            
            // 在控制台输出所有勾选的商品编码，以数组形式展示
            
            // 保存选中的商品数据到临时变量，供模态框提交时使用
            window.selectedBatchProducts = {
                products: selectedProducts,
                rows: selectedRows,
                codes: productCodes
            };
            
            // 直接打开批量对接设置模态框，不显示确认对话框
            openBatchDockingModal();
        });
        
        // 标记按钮已绑定事件
        batchPurchaseBtn.setAttribute('data-batch-event-bound', 'true');
    }
    
    // 打开批量对接设置模态框
    function openBatchDockingModal() {
        const modal = document.getElementById('batchDockingModal');
        if (!modal) return;
        
        // 获取当前对接网站信息并设置到模态框
        const currentDockingSite = getCurrentDockingSite();
        if (currentDockingSite && currentDockingSite.id) {
            // 设置模态框的data-site-id属性
            modal.setAttribute('data-site-id', currentDockingSite.id);
        } else {
            showToast('未找到当前对接网站信息，请返回重新选择', 'error');
            return; // 如果没有网站信息则不打开模态框
        }
        
        // 获取并加载定价模板数据
        loadPriceTemplates();
        
        // 显示模态框
        modal.style.display = 'flex';
        
        // 加载分类数据
        loadCategoriesForDocking();
        
        // 防止重复绑定事件：标记已绑定事件的元素
        if (!modal.hasAttribute('data-events-bound')) {
            // 绑定关闭事件
            const closeBtn = modal.querySelector('.close-modal');
            const cancelBtn = document.getElementById('cancelBatchDocking');
            
            function closeModal() {
                modal.style.display = 'none';
            }
            
            if (closeBtn) {
                closeBtn.addEventListener('click', closeModal);
            }
            
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeModal);
            }
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeModal();
                }
            });
            
            // 绑定表单提交事件
            const form = document.getElementById('batchDockingForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitBatchDocking();
                });
            }
            
            // 绑定一级分类变化时加载对应的二级分类
            const primaryCategory = document.getElementById('primaryCategory');
            if (primaryCategory) {
                primaryCategory.addEventListener('change', function() {
                    loadSecondaryCategories(this.value);
                });
            }
            
            // 标记模态框已绑定事件
            modal.setAttribute('data-events-bound', 'true');
        }
    }
    
    // 获取并加载定价模板列表
    function loadPriceTemplates() {
        const templateSelect = document.getElementById('pricingTemplate');
        if (!templateSelect) return;
        
        // 清除旧选项，只保留默认选项
        while (templateSelect.options.length > 1) {
            templateSelect.remove(1);
        }
        
        // 显示加载中
        const loadingOption = document.createElement('option');
        loadingOption.textContent = '加载中...';
        loadingOption.disabled = true;
        templateSelect.appendChild(loadingOption);
        
        // 发送请求获取定价模板列表
        fetch('/api/GetPriceTemplateList')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败，状态码：' + response.status);
                }
                return response.json();
            })
            .then(data => {
                // 移除加载中选项
                templateSelect.removeChild(loadingOption);
                
                if (data.code !== 200) {
                    showToast(data.msg || '获取定价模板失败', 'error');
                    return;
                }
                
                // 获取模板列表
                const templates = data.data || [];
                
                if (templates.length === 0) {
                    // 如果没有定价模板，添加提示选项
                    const emptyOption = document.createElement('option');
                    emptyOption.textContent = '暂无定价模板';
                    emptyOption.disabled = true;
                    templateSelect.appendChild(emptyOption);
                    
                    showToast('暂无定价模板，请先创建定价模板', 'info');
                    return;
                }
                
                // 添加模板选项
                templates.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    
                    // 创建模板名称和类型描述
                    let typeDesc = '';
                    if (template.type === '1') {
                        typeDesc = '[固定金额]';
                    } else if (template.type === '2') {
                        typeDesc = '[百分比]';
                    }
                    
                    option.textContent = `${template.name} ${typeDesc}`;
                    option.setAttribute('data-template', JSON.stringify(template));
                    templateSelect.appendChild(option);
                });
                
                // 自动选择第一个模板
                if (templateSelect.options.length > 1) {
                    templateSelect.selectedIndex = 1;
                }
            })
            .catch(error => {
                
                // 移除加载中选项
                if (templateSelect.contains(loadingOption)) {
                    templateSelect.removeChild(loadingOption);
                }
                
                // 添加错误选项
                const errorOption = document.createElement('option');
                errorOption.textContent = '加载失败，请重试';
                errorOption.disabled = true;
                templateSelect.appendChild(errorOption);
                
                showToast('获取定价模板失败: ' + error.message, 'error');
            });
    }
    
    // 加载对接用的分类数据
    function loadCategoriesForDocking() {
        // 获取下拉框元素
        const primaryCategorySelect = document.getElementById('primaryCategory');
        if (!primaryCategorySelect) return;
        
        // 如果已经加载过分类数据，且有选项数据，则不重复加载
        if (primaryCategorySelect.options.length > 1 && !primaryCategorySelect.getAttribute('data-force-reload')) {
            return;
        }
        
        // 清空现有选项，仅保留默认选项
        primaryCategorySelect.innerHTML = '<option value="" disabled selected>请选择一级分类</option>';
        
        // 显示加载中
        showLoading();
        
        // 获取分类数据
        fetch('/api/get_category_list')
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.code !== 200) {
                    showToast(data.msg || '获取分类数据失败', 'error');
                    return;
                }
                
                const categories = data.data || [];
                
                // 填充一级分类选项
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    // 保存二级分类数据到option的自定义属性中，方便后续使用
                    if (category.children && category.children.length > 0) {
                        option.setAttribute('data-children', JSON.stringify(category.children));
                    }
                    primaryCategorySelect.appendChild(option);
                });
                
                // 标记已加载数据
                primaryCategorySelect.removeAttribute('data-force-reload');
            })
            .catch(error => {
                hideLoading();
                showToast('获取分类数据失败: ' + error.message, 'error');
            });
    }
    
    // 加载二级分类
    function loadSecondaryCategories(parentId) {
        // 获取二级分类下拉框
        const secondaryCategorySelect = document.getElementById('secondaryCategory');
        if (!secondaryCategorySelect) return;
        
        // 清空现有选项
        secondaryCategorySelect.innerHTML = '<option value="" disabled selected>加载中...</option>';
        
        // 如果没有父级分类ID，则提示先选择一级分类
        if (!parentId) {
            secondaryCategorySelect.innerHTML = '<option value="" disabled selected>请先选择一级分类</option>';
            return;
        }
        
        // 从一级分类选项的自定义属性中获取二级分类数据
        const primaryCategorySelect = document.getElementById('primaryCategory');
        const selectedOption = primaryCategorySelect.options[primaryCategorySelect.selectedIndex];
        
        if (selectedOption && selectedOption.hasAttribute('data-children')) {
            try {
                const childrenData = JSON.parse(selectedOption.getAttribute('data-children'));
                
                // 清空并重建选项
                secondaryCategorySelect.innerHTML = '<option value="" disabled selected>请选择二级分类</option>';
                
                // 如果没有二级分类，添加提示
                if (childrenData.length === 0) {
                    secondaryCategorySelect.innerHTML = '<option value="" disabled selected>暂无二级分类</option>';
                    return;
                }
                
                // 填充二级分类选项
                childrenData.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    secondaryCategorySelect.appendChild(option);
                });
            } catch (error) {
                secondaryCategorySelect.innerHTML = '<option value="" disabled selected>获取二级分类失败</option>';
                showToast('解析二级分类数据失败', 'error');
            }
            
            return;
        }
        
        // 如果选项中没有缓存的二级分类数据，则发起请求获取
        fetch(`/api/getSubCategories?parentId=${parentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.code !== 200) {
                    showToast(data.msg || '获取二级分类失败', 'error');
                    secondaryCategorySelect.innerHTML = '<option value="" disabled selected>获取二级分类失败</option>';
                    return;
                }
                
                const subCategories = data.data || [];
                
                // 清空并重建选项
                secondaryCategorySelect.innerHTML = '<option value="" disabled selected>请选择二级分类</option>';
                
                // 如果没有二级分类，添加提示
                if (subCategories.length === 0) {
                    secondaryCategorySelect.innerHTML = '<option value="" disabled selected>暂无二级分类</option>';
                    return;
                }
                
                // 填充二级分类选项
                subCategories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    secondaryCategorySelect.appendChild(option);
                });
            })
            .catch(error => {
                secondaryCategorySelect.innerHTML = '<option value="" disabled selected>获取二级分类失败</option>';
                showToast('获取二级分类失败: ' + error.message, 'error');
            });
    }
    
    // 提交批量对接表单
    function submitBatchDocking() {
        
        // 获取表单数据
        const primaryCategoryId = document.getElementById('primaryCategory')?.value;
        const secondaryCategoryId = document.getElementById('secondaryCategory')?.value;
        const pricingTemplateId = document.getElementById('pricingTemplate')?.value;
        const imageSaveMode = document.querySelector('input[name="imageSaveMode"]:checked')?.value || 'source';
        
        // 获取当前选中的对接网站信息
        const currentDockingSite = getCurrentDockingSite();
        
        // 检查是否成功获取网站信息
        if (!currentDockingSite || !currentDockingSite.id) {
            showToast('未找到当前对接网站信息，请返回重新选择', 'error');
            return;
        }
        
        
        // 验证表单数据
        if (!primaryCategoryId && !secondaryCategoryId) {
            showToast('请选择分类', 'error');
            return;
        }
        
        if (!pricingTemplateId) {
            showToast('请选择定价模板', 'error');
            return;
        }
        
        // 获取之前保存的选中商品数据
        const selectedProducts = window.selectedBatchProducts?.products || [];
        const selectedRows = window.selectedBatchProducts?.rows || [];
        const productCodes = window.selectedBatchProducts?.codes || [];
        
        if (selectedProducts.length === 0 || productCodes.length === 0) {
            showToast('未找到要对接的商品数据', 'error');
            return;
        }
        
        // 决定使用哪个分类ID (优先使用二级分类，如果二级分类为空则使用一级分类)
        const categoryId = secondaryCategoryId && secondaryCategoryId !== "" ? secondaryCategoryId : primaryCategoryId;
        
        if (!categoryId) {
            showToast('无法获取有效的分类ID', 'error');
            return;
        }
        
        // 构建API请求数据
        const timestamp = Date.now().toString();
        const randomStr = Math.random().toString(36).substring(2, 15);
        
        // 构建请求数据对象
        const requestData = {
            docking_site_id: currentDockingSite.id,
            category_id: categoryId,
            pricing_template: pricingTemplateId,
            image_save_mode: imageSaveMode,
            goods_list: productCodes, // 使用产品编码作为goods_list
            docking_site_type: currentDockingSite.type || 'jubijia', // 如果type为空则默认使用jubijia
            timestamp: timestamp,
            nonce: randomStr
        };
        
        
        // 计算签名
        try {
            // 使用与后端一致的签名计算方法
            requestData.sign = calculateSignForEdit(requestData);
            
            // 输出完整请求数据，方便调试
            
            // 显示加载中
            showLoading();
            
            // 发送批量对接API请求
            fetch('/api/BatchAddDockingProduct', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败，状态码：' + response.status);
                }
                return response.json();
            })
            .then(data => {
                hideLoading();
                
                if (data.code !== 200) {
                    showToast(data.msg || '批量对接失败', 'error');
                    return;
                }
                
                // 关闭模态框
                const modal = document.getElementById('batchDockingModal');
                if (modal) {
                    modal.style.display = 'none';
                }
                
                // 处理成功和失败的统计
                const result = data.data || {};
                const successCount = result.success_count || 0;
                const failCount = result.fail_count || 0;
                
                // 更新UI，将选中的商品状态更改为"已对接"或"对接失败"
                if (successCount > 0 || failCount > 0) {
                    selectedRows.forEach((row, index) => {
                        const productCode = productCodes[index];
                        const failItem = result.fail_items?.find(item => item.id === productCode);
                        
                        const statusCell = row.querySelector('td:last-child');
                        if (statusCell) {
                            const statusBtn = statusCell.querySelector('.action-btn-small');
                            if (statusBtn) {
                                if (!failItem) {
                                    // 对接成功
                                    statusBtn.textContent = '已对接';
                                    statusBtn.classList.remove('not-docked');
                                    statusBtn.classList.add('docked');
                                    // 添加提示
                                    statusBtn.setAttribute('title', '对接成功');
                                } else {
                                    // 对接失败
                                    statusBtn.textContent = '对接失败';
                                    statusBtn.classList.remove('docked');
                                    statusBtn.classList.add('not-docked');
                                    // 添加失败原因提示
                                    statusBtn.setAttribute('title', failItem.reason || '未知错误');
                                }
                            }
                        }
                    });
                }
                
                // 取消所有复选框的选中状态
                document.querySelectorAll('.product-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // 全选复选框也取消选中
                const selectAllCheckbox = document.getElementById('selectAllProducts');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                
                // 显示成功消息
                let message = `批量对接处理完成，`;
                if (successCount > 0) {
                    message += `成功: ${successCount}个`;
                }
                if (failCount > 0) {
                    message += `，失败: ${failCount}个`;
                }
                
                showToast(message, successCount > 0 ? 'success' : 'warning');
                
                // 记录失败项到控制台，方便调试
                if (failCount > 0 && result.fail_items?.length > 0) {
                }
            })
            .catch(error => {
                hideLoading();
                showToast('批量对接请求失败: ' + error.message, 'error');
            });
        } catch (error) {
            showToast('构建请求数据时出错: ' + error.message, 'error');
        }
    }
    
    // 切换树节点展开/折叠状态
    function toggleTreeItem(e) {
        const treeItem = this.closest('.tree-item');
        
        // 处理展开/折叠
        if (treeItem.querySelector('ul')) {
            // 切换展开状态
            treeItem.classList.toggle('expanded');
            
            // 更新图标
            const icon = this.querySelector('i.fa-caret-right, i.fa-caret-down');
            if (icon) {
                if (treeItem.classList.contains('expanded')) {
                    icon.classList.remove('fa-caret-right');
                    icon.classList.add('fa-caret-down');
                } else {
                    icon.classList.remove('fa-caret-down');
                    icon.classList.add('fa-caret-right');
                }
            }
            
            // 显示或隐藏子节点
            const childList = treeItem.querySelector('ul');
            if (childList) {
                childList.style.display = treeItem.classList.contains('expanded') ? 'block' : 'none';
            }
        }
        
        // 处理选中状态
        document.querySelectorAll('.tree-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        treeItem.classList.add('selected');
        
        // 获取分类ID
        const categoryId = this.getAttribute('data-id') || '';
        const categoryName = this.querySelector('span').textContent;
        
        // 加载该分类下的商品
        loadProductList('', categoryId);
        
        // 切换分类后调整窗口宽度
        setTimeout(adjustModalWidth, 1000);
        
        // 阻止事件冒泡，避免多个父级处理同一个点击事件
        e.stopPropagation();
    }
    
    // 修改后的计算签名函数，确保一致性
    function calculateSign(data, key) {
        try {
            // 1. 按ASCII码顺序排序参数
            const sortedKeys = Object.keys(data).sort();
            
            // 2. 构建参数字符串 a=b&c=d 的形式
            let paramString = '';
            sortedKeys.forEach(key => {
                // 空参数不参与签名
                if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                    if (paramString) paramString += '&';
                    paramString += `${key}=${data[key]}`;
                }
            });
            
            // 3. 衔接上key
            const stringToSign = paramString + key;
            
            
            // 4. MD5加密
            const md5Sign = md5(stringToSign);
            
            
            return md5Sign;
        } catch (error) {
            throw error;
        }
    }
    
    // 确保MD5函数存在
    function md5(string) {
        // 一个标准的MD5哈希实现
        var xl;
        var rotateLeft = function(lValue, iShiftBits) {
            return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
        };
        var addUnsigned = function(lX, lY) {
            var lX4, lY4, lX8, lY8, lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        };
        var _F = function(x, y, z) {
            return (x & y) | ((~x) & z);
        };
        var _G = function(x, y, z) {
            return (x & z) | (y & (~z));
        };
        var _H = function(x, y, z) {
            return (x ^ y ^ z);
        };
        var _I = function(x, y, z) {
            return (y ^ (x | (~z)));
        };
        var _FF = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_F(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _GG = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_G(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _HH = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_H(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _II = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_I(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var convertToWordArray = function(str) {
            var lWordCount;
            var lMessageLength = str.length;
            var lNumberOfWords_temp1 = lMessageLength + 8;
            var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
            var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
            var lWordArray = Array(lNumberOfWords - 1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while (lByteCount < lMessageLength) {
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (str.charCodeAt(lByteCount) << lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
            lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
            lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
            return lWordArray;
        };
        var wordToHex = function(lValue) {
            var wordToHexValue = "", wordToHexValue_temp = "", lByte, lCount;
            for (lCount = 0; lCount <= 3; lCount++) {
                lByte = (lValue >>> (lCount * 8)) & 255;
                wordToHexValue_temp = "0" + lByte.toString(16);
                wordToHexValue = wordToHexValue + wordToHexValue_temp.substr(wordToHexValue_temp.length - 2, 2);
            }
            return wordToHexValue;
        };
        var x = [], k, AA, BB, CC, DD, a, b, c, d, S11 = 7, S12 = 12, S13 = 17, S14 = 22, S21 = 5, S22 = 9, S23 = 14, S24 = 20, S31 = 4, S32 = 11, S33 = 16, S34 = 23, S41 = 6, S42 = 10, S43 = 15, S44 = 21;
        var utf8_encode = function(string) {
            string = string.replace(/\r\n/g, "\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                } else if ((c > 127) && (c < 2048)) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                } else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        };
        string = utf8_encode(string);
        x = convertToWordArray(string);
        a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
        xl = x.length;
        for (k = 0; k < xl; k += 16) {
            AA = a; BB = b; CC = c; DD = d;
            a = _FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
            d = _FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
            c = _FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
            b = _FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
            a = _FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
            d = _FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
            c = _FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
            b = _FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
            a = _FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
            d = _FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
            c = _FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
            b = _FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
            a = _FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
            d = _FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
            c = _FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
            b = _FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
            a = _GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
            d = _GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
            c = _GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
            b = _GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
            a = _GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
            d = _GG(d, a, b, c, x[k + 10], S22, 0x2441453);
            c = _GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
            b = _GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
            a = _GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
            d = _GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
            c = _GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
            b = _GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
            a = _GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
            d = _GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
            c = _GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
            b = _GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
            a = _HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
            d = _HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
            c = _HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
            b = _HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
            a = _HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
            d = _HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
            c = _HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
            b = _HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
            a = _HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
            d = _HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
            c = _HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
            b = _HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
            a = _HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
            d = _HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
            c = _HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
            b = _HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
            a = _II(a, b, c, d, x[k + 0], S41, 0xF4292244);
            d = _II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
            c = _II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
            b = _II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
            a = _II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
            d = _II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
            c = _II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
            b = _II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
            a = _II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
            d = _II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
            c = _II(c, d, a, b, x[k + 6], S43, 0xA3014314);
            b = _II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
            a = _II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
            d = _II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
            c = _II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
            b = _II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
            a = addUnsigned(a, AA);
            b = addUnsigned(b, BB);
            c = addUnsigned(c, CC);
            d = addUnsigned(d, DD);
        }
        var temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
        return temp.toLowerCase();
    }
    
    // 为编辑表单计算签名的函数
    function calculateSignForEdit(data) {
        // 1. 删除sign字段
        const dataWithoutSign = { ...data };
        delete dataWithoutSign.sign;
        
        // 2. 按ASCII码顺序排序
        const sortedData = sortObjectKeysForEdit(dataWithoutSign);
        
        // 3. 序列化为JSON字符串，确保格式与后端一致
        const jsonStr = JSON.stringify(sortedData, null, 0);
        // 替换空格，确保与后端格式完全一致
        const formattedJson = jsonStr.replace(/\s/g, '');
        
        // 4. Base64编码
        const base64Str = base64EncodeForEdit(formattedJson);
        
        // 5. MD5签名
        const sign = md5(base64Str);
        
        return sign;
    }
    
    // 编辑表单使用的对象键排序函数
    function sortObjectKeysForEdit(obj) {
        // 如果不是对象或是数组，直接返回
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        // 如果是数组，递归处理每个元素
        if (Array.isArray(obj)) {
            return obj.map(item => sortObjectKeysForEdit(item));
        }
        
        // 创建新的有序对象
        const sortedObj = {};
        // 获取所有键并排序
        const keys = Object.keys(obj).sort();
        
        // 填充排序后的对象
        for (const key of keys) {
            sortedObj[key] = sortObjectKeysForEdit(obj[key]);
        }
        
        return sortedObj;
    }
    
    // 编辑表单使用的Base64编码函数
    function base64EncodeForEdit(str) {
        try {
            // 确保字符串是UTF-8编码
            const utf8Str = unescape(encodeURIComponent(str));
            
            // 进行标准的Base64编码
            let base64 = btoa(utf8Str);
            
            return base64;
        } catch (e) {
            
            // 回退方案：使用纯JavaScript实现Base64编码
            let output = '';
            let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
            let i = 0;
            
            // 确保是UTF-8编码
            const input = unescape(encodeURIComponent(str));
            
            const keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
            
            while (i < input.length) {
                chr1 = input.charCodeAt(i++);
                chr2 = i < input.length ? input.charCodeAt(i++) : NaN;
                chr3 = i < input.length ? input.charCodeAt(i++) : NaN;
                
                enc1 = chr1 >> 2;
                enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                enc3 = isNaN(chr2) ? 64 : ((chr2 & 15) << 2) | (chr3 >> 6);
                enc4 = isNaN(chr3) ? 64 : chr3 & 63;
                
                output += keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4);
            }
            
            return output;
        }
    }
    
    // 编辑表单使用的MD5函数
    function md5ForEdit(str) {
        // 使用与添加表单相同的MD5算法
        // 一个标准的MD5哈希实现
        var xl;
        var rotateLeft = function(lValue, iShiftBits) {
            return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
        };
        var addUnsigned = function(lX, lY) {
            var lX4, lY4, lX8, lY8, lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        };
        var _F = function(x, y, z) {
            return (x & y) | ((~x) & z);
        };
        var _G = function(x, y, z) {
            return (x & z) | (y & (~z));
        };
        var _H = function(x, y, z) {
            return (x ^ y ^ z);
        };
        var _I = function(x, y, z) {
            return (y ^ (x | (~z)));
        };
        var _FF = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_F(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _GG = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_G(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _HH = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_H(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var _II = function(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(_I(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
        };
        var convertToWordArray = function(str) {
            var lWordCount;
            var lMessageLength = str.length;
            var lNumberOfWords_temp1 = lMessageLength + 8;
            var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
            var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
            var lWordArray = Array(lNumberOfWords - 1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while (lByteCount < lMessageLength) {
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (str.charCodeAt(lByteCount) << lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
            lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
            lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
            return lWordArray;
        };
        var wordToHex = function(lValue) {
            var wordToHexValue = "", wordToHexValue_temp = "", lByte, lCount;
            for (lCount = 0; lCount <= 3; lCount++) {
                lByte = (lValue >>> (lCount * 8)) & 255;
                wordToHexValue_temp = "0" + lByte.toString(16);
                wordToHexValue = wordToHexValue + wordToHexValue_temp.substr(wordToHexValue_temp.length - 2, 2);
            }
            return wordToHexValue;
        };
        var x = [], k, AA, BB, CC, DD, a, b, c, d, S11 = 7, S12 = 12, S13 = 17, S14 = 22, S21 = 5, S22 = 9, S23 = 14, S24 = 20, S31 = 4, S32 = 11, S33 = 16, S34 = 23, S41 = 6, S42 = 10, S43 = 15, S44 = 21;
        var utf8_encode = function(string) {
            string = string.replace(/\r\n/g, "\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                } else if ((c > 127) && (c < 2048)) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                } else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        };
        str = utf8_encode(str);
        x = convertToWordArray(str);
        a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
        xl = x.length;
        for (k = 0; k < xl; k += 16) {
            AA = a; BB = b; CC = c; DD = d;
            a = _FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
            d = _FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
            c = _FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
            b = _FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
            a = _FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
            d = _FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
            c = _FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
            b = _FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
            a = _FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
            d = _FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
            c = _FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
            b = _FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
            a = _FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
            d = _FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
            c = _FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
            b = _FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
            a = _GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
            d = _GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
            c = _GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
            b = _GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
            a = _GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
            d = _GG(d, a, b, c, x[k + 10], S22, 0x2441453);
            c = _GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
            b = _GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
            a = _GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
            d = _GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
            c = _GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
            b = _GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
            a = _GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
            d = _GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
            c = _GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
            b = _GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
            a = _HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
            d = _HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
            c = _HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
            b = _HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
            a = _HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
            d = _HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
            c = _HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
            b = _HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
            a = _HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
            d = _HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
            c = _HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
            b = _HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
            a = _HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
            d = _HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
            c = _HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
            b = _HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
            a = _II(a, b, c, d, x[k + 0], S41, 0xF4292244);
            d = _II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
            c = _II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
            b = _II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
            a = _II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
            d = _II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
            c = _II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
            b = _II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
            a = _II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
            d = _II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
            c = _II(c, d, a, b, x[k + 6], S43, 0xA3014314);
            b = _II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
            a = _II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
            d = _II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
            c = _II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
            b = _II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
            a = addUnsigned(a, AA);
            b = addUnsigned(b, BB);
            c = addUnsigned(c, CC);
            d = addUnsigned(d, DD);
        }
        
        var temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
        
        return temp.toLowerCase();
    }
    
    // 获取当前的对接网站列表数据
    function getSitesData() {
        // 这里应该返回当前页面上的对接网站数据
        // 实际应用中可能需要从全局变量或重新请求API获取
        const sites = [];
        const rows = document.querySelectorAll('.docking-table tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const actionBtn = row.querySelector('.edit-btn');
            
            if (cells.length >= 5 && actionBtn) {
                sites.push({
                    id: actionBtn.getAttribute('data-id'),
                    name: cells[0].textContent,
                    url: cells[1].textContent,
                    type: cells[2].textContent,
                    appid: cells[3].textContent
                    // 密钥不从界面获取
                });
            }
        });
        
        return sites;
    }
    
    // 更新对接网站列表函数
    function updateDockingListForEdit(sites) {
        if (!sites || !Array.isArray(sites) || sites.length === 0) {
            // 调用loadDockingSites函数重新加载全部列表
            loadDockingSites();
            return;
        }
        
        
        const tableBody = document.querySelector('.docking-table tbody');
        const emptyState = document.querySelector('.empty-state');
        
        if (!tableBody) {
            return;
        }
        
        // 清空现有内容
        tableBody.innerHTML = '';
        
        // 隐藏空状态
        if (emptyState) {
            emptyState.style.display = 'none';
        }
        
        // 渲染数据
        sites.forEach(site => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${site.name}</td>
                <td>${site.url}</td>
                <td>${site.type}</td>
                <td>${site.appid}</td>
                <td>--</td>
                <td class="action-buttons">
                    <button class="action-btn purchase-btn" title="进货" data-id="${site.id}">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <button class="action-btn edit-btn" title="编辑" data-id="${site.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" title="删除" data-id="${site.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // 直接在此函数中实现按钮事件绑定
        // 绑定删除按钮
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const siteId = this.getAttribute('data-id');
                const confirmDelete = confirm('确定要删除这个对接网站吗？此操作不可恢复。');
                if (confirmDelete) {
                    // 构造请求数据
                    const requestData = {
                        id: siteId
                    };
                    
                    // 计算签名
                    try {
                        const sign = calculateSignForEdit(requestData);
                        requestData.sign = sign;
                        
                        // 显示加载中
                        const loadingIndicator = document.getElementById('loading-indicator');
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('show');
                        }
                        
                        // 发送删除请求
                        fetch('/api/deletetDocking', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(requestData)
                        })
                        .then(response => response.json())
                        .then(data => {
                            // 隐藏加载中
                            if (loadingIndicator) {
                                loadingIndicator.classList.remove('show');
                            }
                            
                            if (data.code === 200) {
                                showCustomToast('删除成功', 'success');
                                // 更新列表
                                updateDockingListForEdit(data.data?.list || []);
                            } else {
                                showCustomToast(data.msg || '删除失败', 'error');
                            }
                        })
                        .catch(error => {
                            // 隐藏加载中
                            if (loadingIndicator) {
                                loadingIndicator.classList.remove('show');
                            }
                            showCustomToast('删除失败: ' + error.message, 'error');
                        });
                    } catch (error) {
                        // 隐藏加载中
                        if (loadingIndicator) {
                            loadingIndicator.classList.remove('show');
                        }
                        showCustomToast('删除失败: ' + error.message, 'error');
                    }
                }
            });
        });
        
        // 绑定编辑按钮
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const siteId = this.getAttribute('data-id');
                openEditModal(siteId);
            });
        });
        
        // 绑定进货按钮
        document.querySelectorAll('.purchase-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const siteId = this.getAttribute('data-id');
                openPurchaseModal(siteId);
            });
        });
    }

    // 增强滚动功能
    function enhanceScrolling() {
        // 获取需要增强滚动的元素
        const categoryTree = document.querySelector('.category-tree');
        const productTable = document.querySelector('.product-table-container');
        
        // 检查是否需要添加鼠标滚轮事件
        if (categoryTree) {
            // 为分类树添加鼠标滚轮事件
            categoryTree.addEventListener('wheel', function(e) {
                // 阻止事件冒泡，避免同时滚动父元素
                e.stopPropagation();
                
                // 如果内容不需要滚动，则不阻止默认行为
                const scrollHeight = this.scrollHeight;
                const clientHeight = this.clientHeight;
                
                if (scrollHeight <= clientHeight) {
                    return;
                }

                
                
                // 计算新的滚动位置
                const delta = e.deltaY;
                const newScrollTop = this.scrollTop + delta;
                
                // 检查是否已经到达顶部或底部
                if ((newScrollTop <= 0 && delta < 0) || 
                    (newScrollTop + clientHeight >= scrollHeight && delta > 0)) {
                    // 到达边界，不阻止默认行为，允许父元素滚动
                    return;
                }
                
                // 否则阻止默认行为，防止页面滚动
                e.preventDefault();
                this.scrollTop = newScrollTop;
            }, { passive: false });
        }
        
        if (productTable) {
            // 为商品表格添加鼠标滚轮事件
            productTable.addEventListener('wheel', function(e) {
                // 阻止事件冒泡，避免同时滚动父元素
                e.stopPropagation();
                
                // 如果内容不需要滚动，则不阻止默认行为
                const scrollHeight = this.scrollHeight;
                const clientHeight = this.clientHeight;
                
                if (scrollHeight <= clientHeight) {
                    return;
                }
                
                // 计算新的滚动位置
                const delta = e.deltaY;
                const newScrollTop = this.scrollTop + delta;
                
                // 检查是否已经到达顶部或底部
                if ((newScrollTop <= 0 && delta < 0) || 
                    (newScrollTop + clientHeight >= scrollHeight && delta > 0)) {
                    // 到达边界，不阻止默认行为，允许父元素滚动
                    return;
                }
                
                // 否则阻止默认行为，防止页面滚动
                e.preventDefault();
                this.scrollTop = newScrollTop;
            }, { passive: false });
        }
        
        // 为模态框添加打开时的滚动重置
        const purchaseModal = document.getElementById('purchaseModal');
        if (purchaseModal) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'style' && 
                        purchaseModal.style.display === 'flex') {
                        // 模态框显示时，重置滚动位置
                        if (categoryTree) categoryTree.scrollTop = 0;
                        if (productTable) productTable.scrollTop = 0;
                    }
                });
            });
            
            observer.observe(purchaseModal, { attributes: true });
        }
        
        // 检查触摸设备
        if ('ontouchstart' in window) {
            // 添加触摸滚动支持
            addTouchScrolling(categoryTree);
            addTouchScrolling(productTable);
        }
    }

    // 为元素添加触摸滚动支持
    function addTouchScrolling(element) {
        if (!element) return;
        
        let startY, startScrollTop;
        
        element.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
            startScrollTop = element.scrollTop;
            
            // 如果内容不需要滚动，不阻止默认行为
            if (element.scrollHeight <= element.clientHeight) {
                return;
            }
            
            // 阻止默认行为，防止页面滚动
            e.preventDefault();
        }, { passive: false });
        
        element.addEventListener('touchmove', function(e) {
            if (!startY) return;
            
            const deltaY = startY - e.touches[0].clientY;
            element.scrollTop = startScrollTop + deltaY;
            
            // 如果内容不需要滚动，不阻止默认行为
            if (element.scrollHeight <= element.clientHeight) {
                return;
            }
            
            // 检查是否已经到达顶部或底部
            if ((element.scrollTop <= 0 && deltaY < 0) || 
                (element.scrollTop + element.clientHeight >= element.scrollHeight && deltaY > 0)) {
                // 到达边界，不阻止默认行为，允许页面滚动
                return;
            }
            
            // 阻止默认行为，防止页面滚动
            e.preventDefault();
        }, { passive: false });
        
        element.addEventListener('touchend', function() {
            startY = null;
            startScrollTop = null;
        });
    }

    // 计算MD5签名
    function calculateMd5(str) {
        return md5(str);
    }

    // 获取当前对接网站信息函数
    function getCurrentDockingSite() {
        // 首先检查全局变量
        if (window.currentDockingSite && window.currentDockingSite.id) {
            return window.currentDockingSite;
        }
        
        // 如果没有，尝试从采购模态框获取
        const purchaseModal = document.getElementById('purchaseModal');
        const siteId = purchaseModal?.getAttribute('data-site-id');
        
        if (siteId && window.dockingSites && window.dockingSites[siteId]) {
            const site = window.dockingSites[siteId];
            // 保存到全局变量
            window.currentDockingSite = site;
            return site;
        }
        
        // 尝试从批量对接模态框获取
        const batchDockingModal = document.getElementById('batchDockingModal');
        const batchSiteId = batchDockingModal?.getAttribute('data-site-id');
        
        if (batchSiteId && window.dockingSites && window.dockingSites[batchSiteId]) {
            const site = window.dockingSites[batchSiteId];
            window.currentDockingSite = site;
            return site;
        }
        
        // 尝试从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const urlSiteId = urlParams.get('site_id');
        
        if (urlSiteId && window.dockingSites && window.dockingSites[urlSiteId]) {
            const site = window.dockingSites[urlSiteId];
            window.currentDockingSite = site;
            return site;
        }
        
        // 尝试从页面标记获取
        const siteElement = document.querySelector('[data-current-site-id]');
        const markedSiteId = siteElement?.getAttribute('data-current-site-id');
        
        if (markedSiteId && window.dockingSites && window.dockingSites[markedSiteId]) {
            const site = window.dockingSites[markedSiteId];
            window.currentDockingSite = site;
            return site;
        }
        
        // 如果只有一个对接网站，就使用它
        if (window.dockingSites) {
            const siteIds = Object.keys(window.dockingSites);
            if (siteIds.length === 1) {
                const site = window.dockingSites[siteIds[0]];
                window.currentDockingSite = site;
                return site;
            }
        }
        
        // 尝试获取第一个激活的网站卡片
        const activeCard = document.querySelector('.docking-site-card.active');
        const activeCardId = activeCard?.getAttribute('data-id');
        
        if (activeCardId && window.dockingSites && window.dockingSites[activeCardId]) {
            const site = window.dockingSites[activeCardId];
            window.currentDockingSite = site;
            return site;
        }
        
        return null;
    }

    // 加载插件管理器
    document.addEventListener('DOMContentLoaded', function() {
        // 加载插件管理器脚本
        const pluginScript = document.createElement('script');
        pluginScript.src = '/static/js/plugin-manager.js';
        pluginScript.onload = function() {
            
            // 如果存在全局插件管理器实例，初始化插件
            if (window.pluginManager) {
                window.pluginManager.initPlugins();
                
                // 初始化网站类型插件注入点
                initSiteTypePluginInjection();
            }
        };
        document.body.appendChild(pluginScript);
    });
    
    // 初始化网站类型插件注入点
    function initSiteTypePluginInjection() {
        // 处理添加模态框
        const addSiteTypeSelect = document.getElementById('siteType');
        const addSiteTypeInjectionPoint = document.getElementById('plugin-injection-site-types');
        
        // 处理编辑模态框
        const editSiteTypeSelect = document.getElementById('editSiteType');
        const editSiteTypeInjectionPoint = document.getElementById('plugin-injection-edit-site-types');
        
        if (!addSiteTypeSelect || !addSiteTypeInjectionPoint || !editSiteTypeSelect || 
            !editSiteTypeInjectionPoint || !window.pluginManager) {
            return;
        }
        
        // 注册处理网站类型插件的方法
        window.pluginManager.registerSiteTypeHandler = function(siteTypeData) {
            if (!siteTypeData || !siteTypeData.value || !siteTypeData.name) {
                return false;
            }
            
            // 添加到新增模态框
            addSiteTypeOption(addSiteTypeSelect, siteTypeData);
            
            // 添加到编辑模态框
            addSiteTypeOption(editSiteTypeSelect, siteTypeData);
            
            return true;
        };
        
        // 辅助函数：向select添加选项
        function addSiteTypeOption(selectElement, siteTypeData) {
            // 检查值是否已存在
            const existingOption = selectElement.querySelector(`option[value="${siteTypeData.value}"]`);
            if (existingOption) {
                return false;
            }
            
            // 创建新的选项
            const option = document.createElement('option');
            option.value = siteTypeData.value;
            option.textContent = siteTypeData.name;
            
            // 如果有其他属性，添加为data属性
            if (siteTypeData.attributes) {
                for (const key in siteTypeData.attributes) {
                    option.setAttribute(`data-${key}`, siteTypeData.attributes[key]);
                }
            }
            
            // 添加到下拉框
            selectElement.appendChild(option);
            
            // 添加网站类型特定联动效果（如果有）
            if (siteTypeData.onSelect && typeof siteTypeData.onSelect === 'function') {
                selectElement.addEventListener('change', function() {
                    if (this.value === siteTypeData.value) {
                        siteTypeData.onSelect(this);
                    }
                });
            }
            
            return true;
        }
        
        // 触发插件系统的网站类型注入点事件
        window.pluginManager.triggerHook('siteTypeInjection', {
            register: window.pluginManager.registerSiteTypeHandler,
            addSelectElement: addSiteTypeSelect,
            editSelectElement: editSiteTypeSelect
        });
        
    }