// 全局变量，记录初始化状态
var cardStockInitialized = false;

// 主初始化函数
function initCardStockPage() {
    // 防止重复初始化
    if (cardStockInitialized) {
        return;
    }

    cardStockInitialized = true;
    
    // 绑定基本事件
    setupBasicEvents();
    
    // 加载数据
    loadCardLibraryList();
}

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    initCardStockPage();
});

// 设置基本事件处理
function setupBasicEvents() {
    
    // 添加卡库按钮
    var addBtn = document.getElementById('addRepoBtn');
    if (addBtn) {
        addBtn.onclick = function() {
            openAddCardLibraryModal();
        };
    } else {
    }
    
    // 刷新按钮
    var refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.onclick = function() {
            // 添加按钮旋转动画
            const icon = refreshBtn.querySelector('i');
            if (icon) {
                icon.style.transition = 'transform 0.5s ease';
                icon.style.transform = 'rotate(360deg)';
                
                setTimeout(() => {
                    icon.style.transform = '';
                }, 500);
            }
            loadCardLibraryList();
        };
    } else {
    }
    
    // 保存按钮
    var saveBtn = document.getElementById('saveRepoBtn');
    if (saveBtn) {
        saveBtn.onclick = handleSaveRepo;
    } else {
    }
    
    // 删除确认按钮
    var confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.onclick = handleDeleteRepo;
    } else {
    }
    
    // 取消删除按钮
    var cancelDeleteBtn = document.getElementById('cancelDelete');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.onclick = function() {
            hideModal('deleteModal');
        };
    }
    
    // 关闭按钮
    var closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
    for (var i = 0; i < closeButtons.length; i++) {
        closeButtons[i].onclick = function() {
            var modalId = this.closest('.modal').id;
            hideModal(modalId);
        };
    }
    
}

// 打开添加卡库模态框
function openAddCardLibraryModal() {
    // 重置表单
    document.getElementById('repoId').value = '';
    document.getElementById('repoName').value = '';
    document.getElementById('cardKeys').value = '';
    
    // 移除验证状态
    document.getElementById('repoName').classList.remove('is-invalid');
    document.getElementById('cardKeys').classList.remove('is-invalid');
    
    // 隐藏删除选项
    var deleteOption = document.getElementById('deleteCardKeysOption');
    if (deleteOption) {
        deleteOption.style.display = 'none';
    }

    // 设置标题
    var title = document.getElementById('repoModalLabel');
    if (title) {
        title.textContent = '添加卡库';
    }
    
    // 选中"添加卡密"单选按钮
    document.getElementById('addCardKeys').checked = true;
    
    // 显示模态框
    showModal('repoModal');
}

// 当前选中的要删除的卡库ID
var currentDeleteId = null;

// 显示通知
function showNotification(message, type) {
    const notification = document.getElementById('notification');
    const notificationMessage = document.getElementById('notification-message');
    const notificationIcon = notification.querySelector('.notification-icon');
    
    // 设置通知类型和图标
    notification.className = 'notification';
    notification.classList.add(type);
    
    if (type === 'success') {
        notificationIcon.className = 'notification-icon fas fa-check-circle';
    } else if (type === 'error') {
        notificationIcon.className = 'notification-icon fas fa-exclamation-circle';
    }
    
    // 设置通知消息
    notificationMessage.textContent = message;
    
    // 添加/清除其他类来触发动画
    notification.classList.add('show');
    
    // 添加抖动效果
    if (type === 'error') {
        notification.style.animation = 'shake 0.5s ease';
        setTimeout(() => {
            notification.style.animation = '';
        }, 500);
    }
    
    // 3秒后自动隐藏
    setTimeout(function() {
        notification.classList.remove('show');
    }, 3000);
}

// 显示模态框
function showModal(modalId) {
    var modal = document.getElementById(modalId);
    if (!modal) return;
    
    if (modalId === 'repoModal') {
        // 模态框显示动画
        modal.style.display = 'flex';
        modal.classList.add('show');
    } else if (modalId === 'deleteModal') {
        // 确认对话框显示动画
        modal.classList.add('show');
    }
}

// 隐藏模态框
function hideModal(modalId) {
    var modal = document.getElementById(modalId);
    if (!modal) return;

    if (modalId === 'repoModal') {
        modal.style.display = 'none';
        modal.classList.remove('show');
    } else if (modalId === 'deleteModal') {
        modal.classList.remove('show');
    }
}

// 记录原始卡库名称，用于检测是否有更改
var originalRepoName = '';

// 编辑卡库
function editRepo(id, name) {
    
    // 记录原始名称
    originalRepoName = name;
    
    // 填充表单
    document.getElementById('repoId').value = id;
    document.getElementById('repoName').value = name;
    document.getElementById('cardKeys').value = '';
    
    // 移除验证状态
    document.getElementById('repoName').classList.remove('is-invalid');
    document.getElementById('cardKeys').classList.remove('is-invalid');
    
    // 显示删除选项
    var deleteOption = document.getElementById('deleteCardKeysOption');
    if (deleteOption) {
        deleteOption.style.display = 'block';
    }
    
    // 设置标题
    var title = document.getElementById('repoModalLabel');
    if (title) {
        title.textContent = '编辑卡库';
    }
    
    // 选中"添加卡密"单选按钮
    document.getElementById('addCardKeys').checked = true;
    
    // 显示模态框
    showModal('repoModal');
}

// 加载卡库列表
function loadCardLibraryList() {
    
    // 获取表格体
    var tableBody = document.getElementById('repoTableBody');
    if (!tableBody) {
        return;
    }

    // 显示加载中
    tableBody.innerHTML = `
        <tr>
            <td colspan="4">
                <div class="empty-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <h3>正在加载卡库数据</h3>
                    <p>请稍候，正在从服务器获取数据...</p>
                </div>
            </td>
        </tr>
    `;
    
    // 显示加载中指示器
    var loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('show');
        var spinner = loadingOverlay.querySelector('.spinner');
        if (spinner) spinner.style.display = 'block';
    }
    
    // 记录请求开始时间
    var startTime = new Date().getTime();
    
    // 直接调用API
    fetch('/api/GetCardLibraryList')
        .then(function(response) { 
            var endTime = new Date().getTime();
            return response.json(); 
        })
        .then(function(result) {
            
            // 隐藏加载中指示器
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                var spinner = loadingOverlay.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';
            }
            
            if (result.code === 200 && Array.isArray(result.data)) {
                if (result.data.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="4">
                                <div class="empty-state">
                                    <i class="fas fa-box-open"></i>
                                    <h3>暂无卡库数据</h3>
                                    <p>点击"添加卡库"按钮创建您的第一个卡库</p>
                                </div>
                            </td>
                        </tr>
                    `;
                } else {
                    // 清空表格
                    tableBody.innerHTML = '';

                    // 填充数据
                    result.data.forEach(function(repo, index) {
                        // 根据库存数量设置不同的样式
                        let stockClass = '';
                        let count = repo.count || 0;
                        
                        if (count <= 10) {
                            stockClass = 'stock-low';
                        } else if (count <= 50) {
                            stockClass = 'stock-medium';
                        } else {
                            stockClass = 'stock-high';
                        }
                        
                        tableBody.innerHTML += `
                            <tr data-id="${repo.id}" style="animation-delay: ${0.05 * (index + 1)}s">
                                <td>${repo.id}</td>
                                <td>${repo.name}</td>
                                <td class="${stockClass}">${count}</td>
                                <td>
                                    <div class="repo-actions">
                                        <button class="action-btn edit-btn" onclick="editRepo('${repo.id}', '${repo.name}')" title="编辑卡库">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn export-btn" onclick="exportCardKeys('${repo.id}')" title="导出卡密">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="action-btn delete-btn" onclick="confirmDelete('${repo.id}')" title="删除卡库">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                    
                    // 为表格行添加鼠标悬停效果
                    const rows = tableBody.querySelectorAll('tr');
                    rows.forEach(row => {
                        row.addEventListener('mouseenter', function() {
                            this.style.transform = 'translateY(-2px)';
                            this.style.boxShadow = '0 4px 8px rgba(255, 158, 210, 0.15)';
                            this.style.transition = 'all 0.3s ease';
                        });
                        
                        row.addEventListener('mouseleave', function() {
                            this.style.transform = '';
                            this.style.boxShadow = '';
                        });
                    });
                }
            } else {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="4">
                            <div class="empty-state">
                                <i class="fas fa-exclamation-circle" style="color: #ff6b6b;"></i>
                                <h3 style="color: #ff6b6b;">加载失败</h3>
                                <p>${result.msg || '未知错误'}</p>
                                <button onclick="loadCardLibraryList()" class="btn btn-primary mt-3">
                                    <i class="fas fa-sync-alt"></i> 重试
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }
        })
        .catch(function(error) {
            
            // 隐藏加载中指示器
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                var spinner = loadingOverlay.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';
            }
            
            tableBody.innerHTML = `
                <tr>
                    <td colspan="4">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle" style="color: #ff6b6b;"></i>
                            <h3 style="color: #ff6b6b;">网络错误</h3>
                            <p>${error.message || '请求失败，请检查网络连接'}</p>
                            <button onclick="loadCardLibraryList()" class="btn btn-primary mt-3">
                                <i class="fas fa-sync-alt"></i> 重试
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
}

// 签名函数
function generateSign(data) {
    // 删除sign字段
    const dataCopy = {...data};
    delete dataCopy.sign;
    
    // 将字段按ASCII码顺序排序
    const sortedKeys = Object.keys(dataCopy).sort();
    const sortedData = {};
    sortedKeys.forEach(key => {
        sortedData[key] = dataCopy[key];
    });
    
    // 转换为JSON字符串
    const jsonStr = JSON.stringify(sortedData);
    
    // Base64编码
    const base64Str = btoa(unescape(encodeURIComponent(jsonStr)));
    
    // MD5签名
    return md5(base64Str);
}

// 生成唯一ID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 当前是否正在进行请求的标志
var isProcessingRequest = false;

// 处理保存
function handleSaveRepo() {
    // 严格检查是否已经在处理请求
    if (isProcessingRequest) {
        return;
    }

    // 防止重复点击
    var saveBtn = document.getElementById('saveRepoBtn');
    if (saveBtn && saveBtn.disabled) {
        return;
    }
    
    var repoId = document.getElementById('repoId').value;
    var repoName = document.getElementById('repoName').value.trim();
    var repoNameInput = document.getElementById('repoName');
    var cardKeysInput = document.getElementById('cardKeys');
    
    // 表单验证
    let isValid = true;
    
    if (!repoName) {
        repoNameInput.classList.add('is-invalid');
        isValid = false;
    } else {
        repoNameInput.classList.remove('is-invalid');
    }

    var cardKeysText = cardKeysInput.value.trim();
    var cardKeys = cardKeysText ? cardKeysText.split('\n').filter(function(k) { return k.trim(); }) : [];
    
    // 如果表单无效，不继续处理
    if (!isValid) {
        showNotification('请完善必填信息', 'error');
        return;
    }
    
    var operationType = document.querySelector('input[name="operationType"]:checked').value;
    
    // 设置请求处理标志
    isProcessingRequest = true;
    
    // 显示保存中的加载状态
    var saveBtnText = document.getElementById('saveBtnText');
    var saveSpinner = document.getElementById('saveSpinner');
    
    // 立即禁用按钮防止重复提交
    if (saveBtn) saveBtn.disabled = true;
    if (saveBtnText) saveBtnText.textContent = repoId ? '更新中...' : '创建中...';
    if (saveSpinner) saveSpinner.style.display = 'inline-block';
    
    // 禁用模态框中的所有表单元素
    var formElements = document.querySelectorAll('#repoModal input, #repoModal textarea, #repoModal button:not(#saveRepoBtn)');
    formElements.forEach(function(el) {
        el.disabled = true;
    });
    
    try {
        if (repoId) {
            // 编辑模式 - 更新卡库
            handleUpdateCardLibrary(repoId, repoName, cardKeys, operationType);
        } else {
            // 添加模式 - 创建卡库
            handleAddCardLibrary(repoName, cardKeys);
        }
    } catch (error) {
        resetModalState();
        isProcessingRequest = false;
        showNotification('操作发生错误: ' + error.message, 'error');
    }
}

// 处理更新卡库
function handleUpdateCardLibrary(repoId, repoName, cardKeys, operationType) {
    if (!repoId) {
        resetModalState();
        isProcessingRequest = false;
        return;
    }

    
    // 创建处理链 - Promise chain
    let processChain = Promise.resolve();
    
    // 如果名称有更改，则更新卡库名称
    if (repoName !== originalRepoName) {
        
        const updateData = {id: repoId, name: repoName};
        updateData.sign = generateSign(updateData);
        
        processChain = fetch('/api/UpdateCardLibrary', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(updateData)
        })
        .then(function(response) { return response.json(); })
        .then(function(result) {
            
            if (result.code !== 200) {
                throw new Error(result.msg || '更新失败');
            }
            
            return result;
        });
    } else {
    }
    
    // 如果有卡密需要处理，添加到处理链
    if (cardKeys.length > 0) {
        processChain = processChain.then(function() {
            var url = operationType === 'add' ? 
                '/api/AddCardLibraryCard' : '/api/DelCardLibraryCard';
            
            
            const cardData = {id: repoId, card: cardKeys};
            cardData.sign = generateSign(cardData);
            
            return fetch(url, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(cardData)
            }).then(function(res) { return res.json(); });
        });
    }
    
    // 最终处理结果
    processChain
    .then(function(result) {
        if (result && result.code !== 200) {
            throw new Error(result.msg || '操作失败');
        }
        
        showNotification('卡库更新成功', 'success');
        hideModal('repoModal');
        loadCardLibraryList();
    })
    .catch(function(error) {
        showNotification('更新失败: ' + error.message, 'error');
    })
    .finally(function() {
        // 恢复按钮状态和表单元素
        resetModalState();
        // 重置请求标志
        isProcessingRequest = false;
        // 重置原始名称
        originalRepoName = '';
    });
}

// 处理添加卡库
function handleAddCardLibrary(repoName, cardKeys) {
    if (!repoName) {
        resetModalState();
        isProcessingRequest = false;
        return;
    }
    
    // 再次确认不是编辑模式
    if (document.getElementById('repoId').value) {
        resetModalState();
        isProcessingRequest = false;
        return;
    }

    
    const createData = {name: repoName};
    createData.sign = generateSign(createData);
    
    fetch('/api/AddCardLibrary', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(createData)
    })
    .then(function(response) { return response.json(); })
    .then(function(result) {
        
        if (result.code !== 200) {
            throw new Error(result.msg || '创建失败');
        }
        
        // 获取后端返回的ID
        const newRepoId = result.id;
        if (!newRepoId) {
            throw new Error('未获取到卡库ID');
        }
        
        if (cardKeys.length > 0) {
            
            const cardData = {id: newRepoId, card: cardKeys};
            cardData.sign = generateSign(cardData);
            
            return fetch('/api/AddCardLibraryCard', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(cardData)
            }).then(function(res) { return res.json(); });
        }
        
        return {code: 200};
    })
    .then(function(result) {
        if (result.code !== 200) {
            throw new Error(result.msg || '添加卡密失败');
        }
        
        showNotification('卡库创建成功', 'success');
        hideModal('repoModal');
        loadCardLibraryList();
    })
    .catch(function(error) {
        showNotification('创建失败: ' + error.message, 'error');
    })
    .finally(function() {
        // 恢复按钮状态和表单元素
        resetModalState();
        // 重置请求标志
        isProcessingRequest = false;
    });
}

// 重置模态框状态
function resetModalState() {
    // 恢复按钮状态
    var saveBtn = document.getElementById('saveRepoBtn');
    var saveBtnText = document.getElementById('saveBtnText');
    var saveSpinner = document.getElementById('saveSpinner');
    
    if (saveBtn) saveBtn.disabled = false;
    if (saveBtnText) saveBtnText.textContent = '保存';
    if (saveSpinner) saveSpinner.style.display = 'none';
    
    // 恢复所有表单元素
    var formElements = document.querySelectorAll('#repoModal input, #repoModal textarea, #repoModal button:not(#saveRepoBtn)');
    formElements.forEach(function(el) {
        el.disabled = false;
    });
}

// 处理删除
function handleDeleteRepo() {
    if (!currentDeleteId) return;
    
    // 显示加载状态
    var deleteBtn = document.getElementById('confirmDeleteBtn');
    var deleteBtnText = document.getElementById('deleteBtnText');
    var deleteSpinner = document.getElementById('deleteSpinner');
    
    if (deleteBtn) deleteBtn.disabled = true;
    if (deleteBtnText) deleteBtnText.textContent = '删除中...';
    if (deleteSpinner) deleteSpinner.style.display = 'inline-block';
    
    const deleteData = {id: currentDeleteId};
    deleteData.sign = generateSign(deleteData);
    
    fetch('/api/DelCardLibrary', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(deleteData)
    })
    .then(function(response) { return response.json(); })
    .then(function(result) {
        if (result.code === 200) {
            showNotification('卡库删除成功', 'success');
            hideModal('deleteModal');
            loadCardLibraryList();
        } else {
            throw new Error(result.msg || '删除失败');
        }
    })
    .catch(function(error) {
        showNotification('删除失败: ' + error.message, 'error');
    })
    .finally(function() {
        // 恢复按钮状态
        if (deleteBtn) deleteBtn.disabled = false;
        if (deleteBtnText) deleteBtnText.textContent = '确认删除';
        if (deleteSpinner) deleteSpinner.style.display = 'none';
        
        // 重置当前选中的ID
        currentDeleteId = null;
    });
}

// 导出卡密
function exportCardKeys(id) {
    // 显示加载中指示器
    var loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('show');
        var spinner = loadingOverlay.querySelector('.spinner');
        if (spinner) spinner.style.display = 'block';
    }
    
    fetch(`/api/GetCardLibraryCardList?id=${id}`)
        .then(function(response) { return response.json(); })
        .then(function(result) {
            // 隐藏加载中指示器
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                var spinner = loadingOverlay.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';
            }
            
            if (result.code === 200) {
                var keys = result.data || [];
                
                if (keys.length === 0) {
                    showNotification('没有可导出的卡密', 'error');
                    return;
                }

                var content = keys.join('\n');
                var blob = new Blob([content], {type: 'text/plain'});
                var url = URL.createObjectURL(blob);
                
                var link = document.createElement('a');
                link.href = url;
                link.download = `卡密_${id}_${new Date().toISOString().slice(0,10)}.txt`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showNotification(`成功导出 ${keys.length} 个卡密`, 'success');
            } else {
                throw new Error(result.msg || '获取卡密失败');
            }
        })
        .catch(function(error) {
            
            // 隐藏加载中指示器
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                var spinner = loadingOverlay.querySelector('.spinner');
                if (spinner) spinner.style.display = 'none';
            }
            
            showNotification('导出失败: ' + error.message, 'error');
        });
}

// 确认删除
function confirmDelete(id) {
    currentDeleteId = id;
    document.getElementById('deleteModal').classList.add('show');
}

// 必须将这些函数暴露到全局作用域
window.showModal = showModal;
window.hideModal = hideModal;
window.editRepo = editRepo;
window.confirmDelete = confirmDelete;
window.exportCardKeys = exportCardKeys;
window.showNotification = showNotification;

// 备用初始化
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initCardStockPage, 0);
} else {
    document.addEventListener('DOMContentLoaded', initCardStockPage);
}