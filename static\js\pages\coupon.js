// 卡券管理JavaScript功能 - 使用卡券代码作为主键
(function() {
    'use strict';

    // 全局变量
    let coupons = [];
    let filteredCoupons = [];
    let currentPage = 1;
    let pageSize = 20;
    let totalRecords = 0; // 服务器端总记录数
    let selectedCoupons = new Set(); // 存储选中的卡券代码

    // DOM元素
    const elements = {
        // 按钮
        addCouponBtn: document.getElementById('addCouponBtn'),
        exportSelectedBtn: document.getElementById('exportSelectedBtn'),
        deleteSelectedBtn: document.getElementById('deleteSelectedBtn'),
        resetFiltersBtn: document.getElementById('resetFiltersBtn'),

        // 筛选
        searchInput: document.getElementById('searchInput'),
        typeFilter: document.getElementById('typeFilter'),
        statusFilter: document.getElementById('statusFilter'),

        // 表格
        couponTableBody: document.getElementById('couponTableBody'),
        selectAllCoupons: document.getElementById('selectAllCoupons'),
        emptyState: document.getElementById('emptyState'),
        loadingState: document.getElementById('loadingState'),
        tableContent: document.getElementById('tableContent'),
        skeletonLoader: document.getElementById('skeletonLoader'),

        // 统计
        totalCoupons: document.getElementById('totalCoupons'),
        activeCoupons: document.getElementById('activeCoupons'),
        usedCoupons: document.getElementById('usedCoupons'),
        expiredCoupons: document.getElementById('expiredCoupons'),

        // 分页
        totalCount: document.getElementById('totalCount'),
        prevPageBtn: document.getElementById('prevPageBtn'),
        nextPageBtn: document.getElementById('nextPageBtn'),
        pageNumbers: document.getElementById('pageNumbers'),
        pageSizeSelect: document.getElementById('pageSizeSelect'),

        // 模态框
        addCouponModal: document.getElementById('addCouponModal'),
        deleteCouponModal: document.getElementById('deleteCouponModal'),
        couponDetailModal: document.getElementById('couponDetailModal'),

        // 表单
        addCouponForm: document.getElementById('addCouponForm'),
        couponCount: document.getElementById('couponCount'),
        discountType: document.getElementById('discountType'),
        discountValue: document.getElementById('discountValue'),
        validDays: document.getElementById('validDays'),
        minOrderAmount: document.getElementById('minOrderAmount'),
        couponDescription: document.getElementById('couponDescription'),
        discountHint: document.getElementById('discountHint')
    };

    // 初始化
    function init() {
        // 检查关键元素是否存在
        const criticalElements = [
            'addCouponBtn', 'searchInput', 'typeFilter', 'couponTableBody'
        ];

        let missingElements = [];
        criticalElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (!element) {
                missingElements.push(elementId);
            }
        });

        if (missingElements.length > 0) {
            // 延迟重试
            setTimeout(() => {
                init();
            }, 100);
            return;
        }

        bindEvents();
        loadCoupons();
        updateStats();
    }

    // 绑定事件
    function bindEvents() {
        try {
            // 按钮事件
            if (elements.addCouponBtn) {
                elements.addCouponBtn.addEventListener('click', showAddCouponModal);
            }

            if (elements.exportSelectedBtn) {
                elements.exportSelectedBtn.addEventListener('click', exportSelectedCoupons);
            }

            if (elements.deleteSelectedBtn) {
                elements.deleteSelectedBtn.addEventListener('click', showDeleteConfirmModal);
            }

            if (elements.resetFiltersBtn) {
                elements.resetFiltersBtn.addEventListener('click', resetFilters);
            }

            // 绑定布局修复按钮
            const fixLayoutBtn = document.getElementById('fixLayoutBtn');
            if (fixLayoutBtn) {
                fixLayoutBtn.addEventListener('click', () => {
                    applyEmergencyFix();
                    showToast('布局修复已应用', 'success');
                });
            }

            // 筛选事件
            if (elements.searchInput) {
                elements.searchInput.addEventListener('input', debounce(applyFilters, 300));
            }

            if (elements.typeFilter) {
                elements.typeFilter.addEventListener('change', applyFilters);
            }

            if (elements.statusFilter) {
                elements.statusFilter.addEventListener('change', applyFilters);
            }

            // 全选事件
            if (elements.selectAllCoupons) {
                elements.selectAllCoupons.addEventListener('change', toggleSelectAll);
            }

            // 分页事件
            if (elements.prevPageBtn) {
                elements.prevPageBtn.addEventListener('click', () => changePage(currentPage - 1));
            }
            if (elements.nextPageBtn) {
                elements.nextPageBtn.addEventListener('click', () => changePage(currentPage + 1));
            }
            if (elements.pageSizeSelect) {
                elements.pageSizeSelect.addEventListener('change', changePageSize);
            }

            // 表单事件
            if (elements.addCouponForm) {
                elements.addCouponForm.addEventListener('submit', handleAddCoupon);
            }

            if (elements.discountType) {
                elements.discountType.addEventListener('change', updateDiscountHint);
            }

            // 模态框关闭事件
            document.querySelectorAll('.close-modal').forEach(btn => {
                btn.addEventListener('click', closeModals);
            });

            document.querySelectorAll('.btn-cancel').forEach(btn => {
                btn.addEventListener('click', closeModals);
            });

            // 点击模态框外部关闭
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        closeModals();
                    }
                });
            });

            // 删除确认
            const confirmDeleteBtn = document.getElementById('confirmDeleteCoupon');
            const cancelDeleteBtn = document.getElementById('cancelDeleteCoupon');

            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', confirmDeleteCoupons);
            }
            if (cancelDeleteBtn) {
                cancelDeleteBtn.addEventListener('click', closeModals);
            }

        } catch (error) {
            // 静默处理错误
        }
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 生成随机卡券代码
    function generateCouponCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = 'COUP-' + new Date().getFullYear() + '-';
        for (let i = 0; i < 4; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 格式化日期
    function formatDate(date) {
        return new Date(date).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // 格式化日期时间
    function formatDateTime(date) {
        return new Date(date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 计算卡券状态
    function getCouponStatus(coupon) {
        const now = new Date();
        const expiryDate = new Date(coupon.expiryDate);

        if (coupon.isUsed) return 'used';
        if (expiryDate < now) return 'expired';
        return 'active';
    }

    // 获取状态显示文本
    function getStatusText(status) {
        const statusMap = {
            'active': '有效',
            'used': '已使用',
            'expired': '已过期'
        };
        return statusMap[status] || '未知';
    }

    // 获取优惠类型显示文本
    function getDiscountTypeText(type) {
        const typeMap = {
            'fixed': '固定金额',
            'percentage': '百分比折扣'
        };
        return typeMap[type] || '未知';
    }

    // 格式化优惠值显示
    function formatDiscountValue(type, value) {
        if (type === 'fixed') {
            return `¥${value}`;
        } else if (type === 'percentage') {
            return `${value}%`;
        }
        return value;
    }

    // 显示Toast通知 - 仿照 DockingCenter.html
    function showToast(message, type) {
        if (typeof type === 'undefined') type = 'success';
        // 获取或创建通知容器
        var toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            // 如果容器不存在，创建一个新的
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        // 确保通知容器显示在最上层
        toastContainer.style.zIndex = '9999';

        // 防止重复提示：检查是否已存在相同消息的通知
        if (!window.toastMessages) {
            window.toastMessages = {};
        }

        // 创建消息唯一标识（消息 + 类型）
        var messageKey = message + '-' + type;

        // 如果相同消息已经在显示中，直接返回
        if (window.toastMessages[messageKey]) {
            return;
        }

        // 标记该消息正在显示
        window.toastMessages[messageKey] = true;

        var toast = document.createElement('div');
        toast.className = 'toast toast-' + type;

        // 根据类型选择图标
        var iconClass = 'fa-check-circle';
        if (type === 'error') {
            iconClass = 'fa-exclamation-circle';
        } else if (type === 'info') {
            iconClass = 'fa-info-circle';
        } else if (type === 'warning') {
            iconClass = 'fa-exclamation-triangle';
        }

        toast.innerHTML =
            '<div class="toast-icon">' +
                '<i class="fas ' + iconClass + '"></i>' +
            '</div>' +
            '<div class="toast-message">' + message + '</div>';

        toastContainer.appendChild(toast);

        // 强制重绘
        toast.offsetHeight;

        // 显示通知
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');

            // 设置延时删除DOM元素，与CSS过渡时间相匹配
            setTimeout(() => {
                toast.remove();
                // 从消息队列中移除，允许再次显示相同消息
                delete window.toastMessages[messageKey];
            }, 300);
        }, 3000);
    }

    // 加载卡券数据
    function loadCoupons() {
        // 显示骨架屏和加载状态
        elements.skeletonLoader.classList.add('active');
        elements.tableContent.classList.add('loading');
        elements.emptyState.style.display = 'none';

        // 构建请求数据
        const requestData = {
            page: currentPage,
            pageSize: pageSize,
            search: elements.searchInput.value.trim(),
            discountType: elements.typeFilter.value,
            status: elements.statusFilter.value
        };

        // 调用卡券列表API
        fetch('/api/coupons/list/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // 延迟隐藏骨架屏，让用户看到加载效果
            setTimeout(() => {
                elements.skeletonLoader.classList.remove('active');
                elements.tableContent.classList.remove('loading');

                if (data.code === 200) {
                    // 更新全局数据
                    coupons = data.data.list || [];
                    filteredCoupons = [...coupons];
                    totalRecords = data.data.total || 0; // 更新总记录数

                    // 更新统计信息
                    if (data.data.stats) {
                        animateCounterUpdate(elements.totalCoupons, data.data.stats.total);
                        animateCounterUpdate(elements.activeCoupons, data.data.stats.active);
                        animateCounterUpdate(elements.usedCoupons, data.data.stats.used);
                        animateCounterUpdate(elements.expiredCoupons, data.data.stats.expired);
                    }

                    // 渲染页面
                    renderCouponsWithAnimation();
                    updatePagination();
                } else {
                    showToast(data.msg || '加载卡券数据失败', 'error');
                    elements.emptyState.style.display = 'block';
                }
            }, 300); // 300ms延迟让骨架屏显示一会儿
        })
        .catch(error => {
            elements.skeletonLoader.classList.remove('active');
            elements.tableContent.classList.remove('loading');
            elements.emptyState.style.display = 'block';
            showToast('网络错误，请稍后重试', 'error');
        });
    }

    // 数字动画更新
    function animateCounterUpdate(element, targetValue) {
        const currentValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - currentValue) / 20;
        let current = currentValue;

        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
                element.textContent = targetValue;
                clearInterval(timer);
            } else {
                element.textContent = Math.round(current);
            }
        }, 50);
    }

    // 带动画的渲染卡券列表
    function renderCouponsWithAnimation() {
        // 先清空表格内容
        elements.couponTableBody.innerHTML = '';

        // 延迟渲染以创建动画效果
        setTimeout(() => {
            renderCoupons();
        }, 100);
    }



    // 渲染卡券列表
    function renderCoupons() {
        // 服务器端分页，直接使用返回的数据
        const pageData = filteredCoupons;

        if (pageData.length === 0) {
            elements.couponTableBody.innerHTML = '';
            elements.emptyState.style.display = 'block';
            return;
        }

        elements.emptyState.style.display = 'none';

        const html = pageData.map(coupon => {
            const status = getCouponStatus(coupon);
            const isSelected = selectedCoupons.has(coupon.code);

            return `
                <tr data-coupon-code="${coupon.code}">
                    <td class="checkbox-column">
                        <div class="kawaii-checkbox">
                            <input type="checkbox" id="coupon_${coupon.code}" class="kawaii-checkbox-input coupon-checkbox"
                                   ${isSelected ? 'checked' : ''} data-coupon-code="${coupon.code}">
                            <label for="coupon_${coupon.code}" class="kawaii-checkbox-label">
                                <div class="kawaii-checkbox-face">
                                    <div class="kawaii-checkbox-eyes"></div>
                                    <div class="kawaii-checkbox-mouth"></div>
                                </div>
                            </label>
                        </div>
                    </td>
                    <td>
                        <span class="coupon-code">${coupon.code}</span>
                    </td>
                    <td>${getDiscountTypeText(coupon.discountType)}</td>
                    <td>${formatDiscountValue(coupon.discountType, coupon.discountValue)}</td>
                    <td>${formatDate(coupon.createTime)}</td>
                    <td>${formatDate(coupon.expiryDate)}</td>
                    <td>
                        <span class="status-badge status-${status}">${getStatusText(status)}</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view-btn" onclick="showCouponDetail('${coupon.code}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>

                            <button class="action-btn delete-btn" onclick="deleteSingleCoupon('${coupon.code}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        elements.couponTableBody.innerHTML = html;

        // 绑定复选框事件
        document.querySelectorAll('.coupon-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', handleCouponSelect);
        });

        updateSelectAllState();
    }

    // 更新统计信息
    function updateStats() {
        const stats = coupons.reduce((acc, coupon) => {
            const status = getCouponStatus(coupon);
            acc.total++;
            acc[status]++;
            return acc;
        }, { total: 0, active: 0, used: 0, expired: 0 });

        elements.totalCoupons.textContent = stats.total;
        elements.activeCoupons.textContent = stats.active;
        elements.usedCoupons.textContent = stats.used;
        elements.expiredCoupons.textContent = stats.expired;
    }

    // 应用筛选
    function applyFilters() {
        // 重置到第一页
        currentPage = 1;
        // 重新加载数据（会自动应用当前的筛选条件）
        loadCoupons();
    }

    // 重置筛选
    function resetFilters() {
        elements.searchInput.value = '';
        elements.typeFilter.value = '';
        elements.statusFilter.value = '';

        currentPage = 1;
        loadCoupons();
    }

    // 处理卡券选择
    function handleCouponSelect(event) {
        const couponCode = event.target.dataset.couponCode;

        if (event.target.checked) {
            selectedCoupons.add(couponCode);
        } else {
            selectedCoupons.delete(couponCode);
        }

        updateSelectAllState();
        updateActionButtons();
    }

    // 切换全选
    function toggleSelectAll() {
        const isChecked = elements.selectAllCoupons.checked;
        const currentPageCoupons = getCurrentPageCoupons();

        currentPageCoupons.forEach(coupon => {
            if (isChecked) {
                selectedCoupons.add(coupon.code);
            } else {
                selectedCoupons.delete(coupon.code);
            }
        });

        renderCoupons();
        updateActionButtons();
    }

    // 获取当前页卡券
    function getCurrentPageCoupons() {
        // 服务器端分页，filteredCoupons就是当前页的数据
        return filteredCoupons;
    }

    // 更新全选状态
    function updateSelectAllState() {
        const currentPageCoupons = getCurrentPageCoupons();
        const selectedInCurrentPage = currentPageCoupons.filter(coupon => selectedCoupons.has(coupon.code));

        if (selectedInCurrentPage.length === 0) {
            elements.selectAllCoupons.checked = false;
            elements.selectAllCoupons.indeterminate = false;
        } else if (selectedInCurrentPage.length === currentPageCoupons.length) {
            elements.selectAllCoupons.checked = true;
            elements.selectAllCoupons.indeterminate = false;
        } else {
            elements.selectAllCoupons.checked = false;
            elements.selectAllCoupons.indeterminate = true;
        }
    }

    // 更新操作按钮状态
    function updateActionButtons() {
        const hasSelected = selectedCoupons.size > 0;
        elements.exportSelectedBtn.disabled = !hasSelected;
        elements.deleteSelectedBtn.disabled = !hasSelected;
    }

    // 更新分页
    function updatePagination() {
        const totalPages = Math.ceil(totalRecords / pageSize);

        elements.totalCount.textContent = totalRecords;
        elements.prevPageBtn.disabled = currentPage <= 1;
        elements.nextPageBtn.disabled = currentPage >= totalPages;

        // 生成页码
        let pageNumbersHtml = '';
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            pageNumbersHtml += `
                <button class="page-number ${i === currentPage ? 'active' : ''}"
                        onclick="changePage(${i})">${i}</button>
            `;
        }

        elements.pageNumbers.innerHTML = pageNumbersHtml;
    }

    // 切换页面
    function changePage(page) {
        if (page < 1) return;

        currentPage = page;
        loadCoupons();
    }

    // 改变页面大小
    function changePageSize() {
        pageSize = parseInt(elements.pageSizeSelect.value);
        currentPage = 1;
        loadCoupons();
    }

    // 显示添加卡券模态框
    function showAddCouponModal() {
        if (!elements.addCouponModal) {
            return;
        }

        elements.addCouponModal.style.display = 'flex';

        if (elements.couponCount) {
            elements.couponCount.focus();
        }
    }

    // 更新优惠值提示
    function updateDiscountHint() {
        const type = elements.discountType.value;
        if (type === 'fixed') {
            elements.discountHint.textContent = '请输入固定减免金额（元）';
            elements.discountValue.placeholder = '例如：10.00';
        } else if (type === 'percentage') {
            elements.discountHint.textContent = '请输入折扣百分比（1-100）';
            elements.discountValue.placeholder = '例如：20';
        } else {
            elements.discountHint.textContent = '请先选择优惠类型';
            elements.discountValue.placeholder = '请输入优惠值';
        }
    }

    // 处理添加卡券
    function handleAddCoupon(event) {
        event.preventDefault();

        const formData = {
            count: parseInt(elements.couponCount.value),
            discountType: elements.discountType.value,
            discountValue: parseFloat(elements.discountValue.value),
            validDays: parseInt(elements.validDays.value),
            minOrderAmount: parseFloat(elements.minOrderAmount.value) || 0,
            description: elements.couponDescription.value.trim()
        };

        // 验证表单
        if (!validateCouponForm(formData)) {
            return;
        }

        // 生成卡券
        generateCoupons(formData);

        // 关闭模态框并重置表单
        closeModals();
        elements.addCouponForm.reset();
        updateDiscountHint();
    }

    // 验证卡券表单
    function validateCouponForm(data) {
        if (!data.count || data.count < 1 || data.count > 1000) {
            showToast('生成数量必须在1-1000之间', 'error');
            return false;
        }

        if (!data.discountType) {
            showToast('请选择优惠类型', 'error');
            return false;
        }

        if (!data.discountValue || data.discountValue <= 0) {
            showToast('请输入有效的优惠值', 'error');
            return false;
        }

        if (data.discountType === 'percentage' && data.discountValue > 100) {
            showToast('百分比折扣不能大于100%', 'error');
            return false;
        }

        if (!data.validDays || data.validDays < 1 || data.validDays > 365) {
            showToast('有效天数必须在1-365之间', 'error');
            return false;
        }

        return true;
    }

    // 生成卡券
    function generateCoupons(data) {
        const addBtn = document.querySelector('.btn-confirm');

        // 添加按钮加载状态
        addBtn.classList.add('btn-loading');
        addBtn.textContent = '生成中...';

        // 调用生成卡券API
        fetch('/api/coupons/generate/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            addBtn.classList.remove('btn-loading');

            if (result.code === 200) {
                // 成功动画
                addBtn.classList.add('btn-success');
                addBtn.textContent = '生成成功！';

                setTimeout(() => {
                    addBtn.classList.remove('btn-success');
                    addBtn.textContent = '确认生成';
                }, 1000);

                showToast(result.msg || `成功生成 ${data.count} 张卡券！`, 'success');
                // 重新加载卡券列表
                loadCoupons();
            } else {
                addBtn.textContent = '确认生成';
                showToast(result.msg || '生成卡券失败', 'error');
            }
        })
        .catch(error => {
            addBtn.classList.remove('btn-loading');
            addBtn.textContent = '确认生成';
            showToast('网络错误，请稍后重试', 'error');
        });
    }

    // 显示删除确认模态框
    function showDeleteConfirmModal() {
        if (selectedCoupons.size === 0) return;

        document.getElementById('deleteCount').textContent = selectedCoupons.size;
        elements.deleteCouponModal.style.display = 'flex';
    }

    // 确认删除卡券
    async function confirmDeleteCoupons() {
        const selectedCodes = Array.from(selectedCoupons);

        if (selectedCodes.length === 0) {
            showToast('请选择要删除的卡券', 'error');
            return;
        }

        const deleteBtn = document.querySelector('.btn-delete-confirm');

        // 添加按钮加载状态
        deleteBtn.classList.add('btn-loading');
        deleteBtn.textContent = '删除中...';

        // 先添加删除动画到选中的行
        selectedCodes.forEach(code => {
            const row = document.querySelector(`tr[data-coupon-code="${code}"]`);
            if (row) {
                row.classList.add('removing');
            }
        });

        try {
            // 准备要签名的数据
            const requestData = {
                couponCodes: selectedCodes
            };

            // 检查增强型签名生成器是否可用
            if (!window.EnhancedSignatureGenerator) {
                throw new Error('签名生成器未加载，请刷新页面重试');
            }

            // 生成增强型签名
            const signedData = await window.EnhancedSignatureGenerator.generateSignature(requestData);

            console.log('生成的签名数据:', {
                timestamp: signedData.timestamp,
                nonce: signedData.nonce,
                signature: signedData.signature.substring(0, 16) + '...' // 只显示前16位用于调试
            });

            // 调用删除卡券API
            const response = await fetch('/api/coupons/delete/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(signedData)
            });

            const result = await response.json();
            deleteBtn.classList.remove('btn-loading');

            if (result.code === 200) {
                // 成功动画
                deleteBtn.classList.add('btn-success');
                deleteBtn.textContent = '删除成功！';

                setTimeout(() => {
                    deleteBtn.classList.remove('btn-success');
                    deleteBtn.textContent = '确认删除';
                }, 1000);

                // 清空选择
                selectedCoupons.clear();
                updateActionButtons();

                closeModals();
                showToast(result.msg || `成功删除 ${selectedCodes.length} 张卡券！`, 'success');

                // 延迟重新加载，让删除动画完成
                setTimeout(() => {
                    loadCoupons();
                }, 300);
            } else {
                deleteBtn.textContent = '确认删除';
                // 移除删除动画
                selectedCodes.forEach(code => {
                    const row = document.querySelector(`tr[data-coupon-code="${code}"]`);
                    if (row) {
                        row.classList.remove('removing');
                    }
                });

                // 特殊处理认证相关错误
                if (result.code === 403 && result.error_code === 'MISSING_SIGNATURE') {
                    showToast('签名验证失败，请刷新页面重试', 'error');
                } else if (result.code === 401) {
                    showToast('登录已过期，请重新登录', 'error');
                    // 可以在这里触发重新登录逻辑
                } else {
                    showToast(result.msg || '删除卡券失败', 'error');
                }
            }
        } catch (error) {
            deleteBtn.classList.remove('btn-loading');
            deleteBtn.textContent = '确认删除';
            // 移除删除动画
            selectedCodes.forEach(code => {
                const row = document.querySelector(`tr[data-coupon-code="${code}"]`);
                if (row) {
                    row.classList.remove('removing');
                }
            });

            console.error('删除卡券时发生错误:', error);

            // 根据错误类型提供不同的提示
            if (error.message.includes('签名生成器未加载')) {
                showToast('系统初始化未完成，请刷新页面重试', 'error');
            } else if (error.message.includes('签名生成失败')) {
                showToast('签名生成失败，请检查浏览器兼容性', 'error');
            } else {
                showToast('网络错误，请稍后重试', 'error');
            }
        }
    }

    // 导出选中卡券
    function exportSelectedCoupons() {
        if (selectedCoupons.size === 0) return;

        const selectedData = coupons.filter(coupon => selectedCoupons.has(coupon.code));

        let content = '卡券代码\t优惠类型\t优惠值\t最低消费\t创建时间\t有效期\t状态\t说明\n';

        selectedData.forEach(coupon => {
            const status = getCouponStatus(coupon);
            content += [
                coupon.code,
                getDiscountTypeText(coupon.discountType),
                formatDiscountValue(coupon.discountType, coupon.discountValue),
                coupon.minOrderAmount > 0 ? `¥${coupon.minOrderAmount}` : '无限制',
                formatDateTime(coupon.createTime),
                formatDate(coupon.expiryDate),
                getStatusText(status),
                coupon.description
            ].join('\t') + '\n';
        });

        // 创建并下载文件
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `卡券导出_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showToast(`成功导出 ${selectedData.length} 张卡券！`);
    }

    // 显示卡券详情
    function showCouponDetail(couponCode) {
        if (!couponCode) {
            showToast('卡券代码不能为空', 'error');
            return;
        }

        // 显示加载状态
        elements.loadingState.style.display = 'flex';

        // 调用卡券详情API
        fetch(`/api/coupons/detail/?coupon=${encodeURIComponent(couponCode)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            elements.loadingState.style.display = 'none';

            if (result.code === 200) {
                const coupon = result.data;

                // 设置优惠值和单位
                if (coupon.discountType === 'fixed') {
                    document.getElementById('detailDiscountValue').textContent = coupon.discountValue;
                    document.getElementById('detailDiscountUnit').textContent = '元';
                } else {
                    document.getElementById('detailDiscountValue').textContent = coupon.discountValue;
                    document.getElementById('detailDiscountUnit').textContent = '%';
                }

                // 设置卡券代码
                document.getElementById('detailCouponCode').textContent = coupon.code;

                // 设置有效期
                document.getElementById('detailExpiryDate').textContent = formatDate(coupon.expiryDate);

                // 设置最低消费
                const minAmount = parseFloat(coupon.minOrderAmount);
                document.getElementById('detailMinAmount').textContent =
                    minAmount > 0 ? `¥${minAmount}` : '无限制';

                // 设置创建时间
                document.getElementById('detailCreateTime').textContent = formatDateTime(coupon.createTime);

                // 设置状态
                const statusElement = document.getElementById('detailStatus');
                statusElement.textContent = getStatusText(coupon.status);
                statusElement.className = `status-badge status-${coupon.status}`;

                // 设置描述
                document.getElementById('detailDescription').textContent =
                    coupon.description || '暂无说明';

                // 显示模态框
                elements.couponDetailModal.style.display = 'flex';
            } else {
                showToast(result.msg || '获取卡券详情失败', 'error');
            }
        })
        .catch(error => {
            elements.loadingState.style.display = 'none';
            showToast('网络错误，请稍后重试', 'error');
        });
    }



    // 删除单个卡券
    function deleteSingleCoupon(couponCode) {
        selectedCoupons.clear();
        selectedCoupons.add(couponCode);
        showDeleteConfirmModal();
    }

    // 关闭所有模态框
    function closeModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.style.display = 'none';
        });
    }

    // 暴露函数到全局作用域
    window.changePage = changePage;
    window.showCouponDetail = showCouponDetail;
    window.deleteSingleCoupon = deleteSingleCoupon;

    // 添加CSS调试函数
    function debugCSSLayout() {
        const searchBox = document.querySelector('.search-box');
        const typeFilter = document.getElementById('typeFilter');
        const filterGroup = document.querySelector('.filter-group');

        if (searchBox && typeFilter && filterGroup) {
            const searchBoxRect = searchBox.getBoundingClientRect();
            const typeFilterRect = typeFilter.getBoundingClientRect();

            // 检查是否重叠
            const isOverlapping = !(searchBoxRect.bottom <= typeFilterRect.top ||
                                   typeFilterRect.bottom <= searchBoxRect.top ||
                                   searchBoxRect.right <= typeFilterRect.left ||
                                   typeFilterRect.right <= searchBoxRect.left);

            // 如果检测到重叠，强制修复
            if (isOverlapping && window.innerWidth > 768) {
                forceFixLayout();
            }
        }
    }

    // 强制修复布局函数
    function forceFixLayout() {
        const filterGroup = document.querySelector('.filter-group');
        const searchBox = document.querySelector('.search-box');
        const typeFilter = document.getElementById('typeFilter');
        const statusFilter = document.getElementById('statusFilter');
        const resetBtn = document.querySelector('.reset-btn');

        if (filterGroup && window.innerWidth <= 768) {
            // 移动端强制修复
            filterGroup.style.cssText = `
                display: block !important;
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            `;

            if (searchBox) {
                searchBox.style.cssText = `
                    display: block !important;
                    width: 100% !important;
                    margin: 0 0 25px 0 !important;
                `;
            }

            if (typeFilter) {
                typeFilter.style.cssText = `
                    display: block !important;
                    width: 100% !important;
                    margin: 0 0 25px 0 !important;
                `;
            }

            if (statusFilter) {
                statusFilter.style.cssText = `
                    display: block !important;
                    width: 100% !important;
                    margin: 0 0 25px 0 !important;
                `;
            }
        }
    }

    // 紧急修复函数 - 直接用JavaScript强制修复重叠问题
    function applyEmergencyFix() {
        const filterGroup = document.querySelector('.filter-group');
        const searchBox = document.querySelector('.search-box');
        const typeFilter = document.getElementById('typeFilter');
        const statusFilter = document.getElementById('statusFilter');
        const resetBtn = document.querySelector('.reset-btn');
        const pageHeader = document.querySelector('.page-header');
        const headerCenter = document.querySelector('.header-center');

        if (window.innerWidth > 768) {
            // 电脑端修复
            // 强制设置页面头部样式
            if (pageHeader) {
                pageHeader.style.cssText = `
                    display: flex !important;
                    flex-direction: row !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                    margin-bottom: 25px !important;
                    width: 100% !important;
                    min-width: auto !important;
                    flex-wrap: nowrap !important;
                    gap: 20px !important;
                `;
            }

            // 强制设置头部中心区域
            if (headerCenter) {
                headerCenter.style.cssText = `
                    flex: 1 !important;
                    display: flex !important;
                    justify-content: center !important;
                    margin: 0 15px !important;
                    min-width: 400px !important;
                    max-width: 600px !important;
                `;
            }

            // 强制设置筛选组为表格布局
            if (filterGroup) {
                filterGroup.style.cssText = `
                    display: table !important;
                    width: 100% !important;
                    table-layout: fixed !important;
                    border-spacing: 15px 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                `;
            }

            // 强制设置搜索框为表格单元格
            if (searchBox) {
                searchBox.style.cssText = `
                    display: table-cell !important;
                    width: 35% !important;
                    vertical-align: middle !important;
                    position: relative !important;
                    padding-right: 10px !important;
                `;

                const searchInput = searchBox.querySelector('.search-input');
                if (searchInput) {
                    searchInput.style.cssText = `
                        width: 100% !important;
                        padding: 10px 15px 10px 45px !important;
                        border: 1px solid #ffdfed !important;
                        border-radius: 8px !important;
                        font-size: 14px !important;
                        box-sizing: border-box !important;
                    `;
                }

                const searchIcon = searchBox.querySelector('i');
                if (searchIcon) {
                    searchIcon.style.cssText = `
                        position: absolute !important;
                        left: 15px !important;
                        top: 50% !important;
                        transform: translateY(-50%) !important;
                        color: #ff7eb9 !important;
                        font-size: 16px !important;
                        z-index: 2 !important;
                    `;
                }
            }

            // 强制设置类型筛选器为表格单元格
            if (typeFilter) {
                typeFilter.style.cssText = `
                    display: table-cell !important;
                    width: 25% !important;
                    vertical-align: middle !important;
                    padding: 10px 15px !important;
                    border: 1px solid #ffdfed !important;
                    border-radius: 8px !important;
                    font-size: 14px !important;
                    background: white !important;
                    color: #333 !important;
                    box-sizing: border-box !important;
                    margin-right: 10px !important;
                `;
            }

            // 强制设置状态筛选器为表格单元格
            if (statusFilter) {
                statusFilter.style.cssText = `
                    display: table-cell !important;
                    width: 25% !important;
                    vertical-align: middle !important;
                    padding: 10px 15px !important;
                    border: 1px solid #ffdfed !important;
                    border-radius: 8px !important;
                    font-size: 14px !important;
                    background: white !important;
                    color: #333 !important;
                    box-sizing: border-box !important;
                    margin-right: 10px !important;
                `;
            }

            // 强制设置重置按钮为表格单元格
            if (resetBtn) {
                resetBtn.style.cssText = `
                    display: table-cell !important;
                    width: 36px !important;
                    height: 36px !important;
                    vertical-align: middle !important;
                    text-align: center !important;
                    border-radius: 50% !important;
                    border: 1px solid #ff7eb9 !important;
                    background: #ff7eb9 !important;
                    color: white !important;
                    font-size: 14px !important;
                    cursor: pointer !important;
                    flex-shrink: 0 !important;
                `;
            }

            // 再次检查是否还有重叠
            setTimeout(() => {
                const searchBox = document.querySelector('.search-box');
                const typeFilter = document.getElementById('typeFilter');
                if (searchBox && typeFilter) {
                    const searchBoxRect = searchBox.getBoundingClientRect();
                    const typeFilterRect = typeFilter.getBoundingClientRect();

                    const isOverlapping = !(searchBoxRect.bottom <= typeFilterRect.top ||
                                           typeFilterRect.bottom <= searchBoxRect.top ||
                                           searchBoxRect.right <= typeFilterRect.left ||
                                           typeFilterRect.right <= searchBoxRect.left);

                    if (isOverlapping) {
                        applyAbsolutePositionFix();
                    }
                }
            }, 100);
        }
    }

    // 绝对定位备用修复方案
    function applyAbsolutePositionFix() {
        const filterGroup = document.querySelector('.filter-group');
        const searchBox = document.querySelector('.search-box');
        const typeFilter = document.getElementById('typeFilter');
        const statusFilter = document.getElementById('statusFilter');
        const resetBtn = document.querySelector('.reset-btn');

        if (filterGroup && window.innerWidth > 768) {
            // 设置筛选组为相对定位容器
            filterGroup.style.cssText = `
                position: relative !important;
                height: 50px !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                display: block !important;
            `;

            // 搜索框绝对定位
            if (searchBox) {
                searchBox.style.cssText = `
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 200px !important;
                    height: 40px !important;
                `;

                const searchInput = searchBox.querySelector('.search-input');
                if (searchInput) {
                    searchInput.style.cssText = `
                        width: 100% !important;
                        height: 40px !important;
                        padding: 10px 15px 10px 45px !important;
                        border: 1px solid #ffdfed !important;
                        border-radius: 8px !important;
                        font-size: 14px !important;
                        box-sizing: border-box !important;
                    `;
                }
            }

            // 类型筛选器绝对定位
            if (typeFilter) {
                typeFilter.style.cssText = `
                    position: absolute !important;
                    left: 220px !important;
                    top: 0 !important;
                    width: 130px !important;
                    height: 40px !important;
                    padding: 10px 15px !important;
                    border: 1px solid #ffdfed !important;
                    border-radius: 8px !important;
                    font-size: 14px !important;
                    background: white !important;
                    color: #333 !important;
                    box-sizing: border-box !important;
                `;
            }

            // 状态筛选器绝对定位
            if (statusFilter) {
                statusFilter.style.cssText = `
                    position: absolute !important;
                    left: 370px !important;
                    top: 0 !important;
                    width: 130px !important;
                    height: 40px !important;
                    padding: 10px 15px !important;
                    border: 1px solid #ffdfed !important;
                    border-radius: 8px !important;
                    font-size: 14px !important;
                    background: white !important;
                    color: #333 !important;
                    box-sizing: border-box !important;
                `;
            }

            // 重置按钮绝对定位
            if (resetBtn) {
                resetBtn.style.cssText = `
                    position: absolute !important;
                    right: 0 !important;
                    top: 2px !important;
                    width: 36px !important;
                    height: 36px !important;
                    border-radius: 50% !important;
                    border: 1px solid #ff7eb9 !important;
                    background: #ff7eb9 !important;
                    color: white !important;
                    font-size: 14px !important;
                    cursor: pointer !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                `;
            }
        }
    }

    // 实时布局监控
    function startLayoutMonitoring() {
        let monitoringInterval = setInterval(() => {
            const searchBox = document.querySelector('.search-box');
            const typeFilter = document.getElementById('typeFilter');

            if (searchBox && typeFilter && window.innerWidth > 768) {
                const searchBoxRect = searchBox.getBoundingClientRect();
                const typeFilterRect = typeFilter.getBoundingClientRect();

                const isOverlapping = !(searchBoxRect.bottom <= typeFilterRect.top ||
                                       typeFilterRect.bottom <= searchBoxRect.top ||
                                       searchBoxRect.right <= typeFilterRect.left ||
                                       typeFilterRect.right <= searchBoxRect.left);

                if (isOverlapping) {
                    // 显示手动修复按钮
                    const fixLayoutBtn = document.getElementById('fixLayoutBtn');
                    if (fixLayoutBtn) {
                        fixLayoutBtn.style.display = 'flex';
                    }

                    applyEmergencyFix();
                } else {
                    // 隐藏修复按钮
                    const fixLayoutBtn = document.getElementById('fixLayoutBtn');
                    if (fixLayoutBtn) {
                        fixLayoutBtn.style.display = 'none';
                    }
                }
            }
        }, 2000); // 每2秒检查一次

        // 5分钟后停止监控
        setTimeout(() => {
            clearInterval(monitoringInterval);
        }, 300000);
    }

    // 在单页面应用中立即初始化，而不是等待DOMContentLoaded













    // 使用setTimeout确保DOM元素已渲染
    setTimeout(() => {
        init();

        // 延迟执行CSS调试
        setTimeout(() => {
            debugCSSLayout();

            // 强制应用紧急修复
            applyEmergencyFix();
        }, 200);

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            setTimeout(() => {
                debugCSSLayout();
                applyEmergencyFix();
            }, 100);
        });

        // 启动实时监控
        startLayoutMonitoring();

        // 监听设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                debugCSSLayout();
            }, 300);
        });
    }, 50);

})();