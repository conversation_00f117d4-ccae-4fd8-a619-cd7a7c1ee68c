// 主要初始化函数
function initializeHomePage() {
    console.log('Home: 开始初始化首页');

    // 检查必要的依赖
    if (typeof Chart === 'undefined') {
        console.error('Home: Chart.js 未加载，无法初始化图表');
        return;
    }
        // 获取必要的DOM元素
        const pageContent = document.getElementById('page-content');
        const dashboardContainer = document.getElementById('dashboard-container');
        const contentElement = document.querySelector('.content');

        // 自适应调整布局函数
        function adjustLayout() {
            if (!pageContent || !dashboardContainer) return;

            // 确保canvas元素适应容器大小
            const chartContainer = document.querySelector('.chart-container');
            if (chartContainer) {
                chartContainer.style.width = '100%';
                const chartCard = chartContainer.closest('.chart-card');
                if (chartCard) {
                    // 计算图表容器的合适高度
                    chartCard.style.height = '350px';
                }
            }

            // 应用布局后更新图表
            if (window.salesChart && typeof window.salesChart.update === 'function') {
                window.salesChart.update();
            }
        }

        // 销售趋势图表
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        window.salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [
                    {
                        label: '销售额',
                        data: [4500, 5200, 4800, 7300, 9200, 12500, 10800],
                        borderColor: '#ff7eb9',
                        backgroundColor: 'rgba(255, 126, 185, 0.05)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '订单量',
                        data: [36, 42, 38, 59, 74, 98, 86],
                        borderColor: '#7eb8ff',
                        backgroundColor: 'rgba(126, 184, 255, 0.05)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                hover: {
                    mode: 'nearest',
                    intersect: true
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.03)'
                        },
                        title: {
                            display: true,
                            text: '销售额 (¥)'
                        }
                    },
                    y1: {
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: '订单量'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // 添加交互
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                const parent = this.parentElement;
                parent.querySelector('.active').classList.remove('active');
                this.classList.add('active');
            });
        });

        // 阻止事件冒泡到父级元素，防止点击卡片时触发侧边导航栏
        function preventEventPropagation() {
            const dashboardContainer = document.getElementById('dashboard-container');
            if (!dashboardContainer) return;

            // 为整个仪表盘容器添加事件拦截 - 使用捕获阶段
            ['click', 'mousedown', 'mouseup'].forEach(eventType => {
                dashboardContainer.addEventListener(eventType, function (e) {
                    e.stopPropagation();
                }, true);
            });

            // 为所有卡片类元素单独添加事件拦截
            const cardElements = [
                ...document.querySelectorAll('.stat-card'),
                ...document.querySelectorAll('.quick-nav-card'),
                ...document.querySelectorAll('.notice-card'),
                ...document.querySelectorAll('.chart-card'),
                ...document.querySelectorAll('.chart-container'),
                ...document.querySelectorAll('.period-btn')
            ];

            cardElements.forEach(card => {
                ['click', 'mousedown', 'mouseup'].forEach(eventType => {
                    card.addEventListener(eventType, function (e) {
                        e.stopPropagation();
                        e.stopImmediatePropagation(); // 阻止事件在当前元素上进一步传播
                    }, true); // 添加捕获阶段处理
                });
            });

            // 特别处理导航卡片的点击事件
            document.querySelectorAll('.quick-nav-card').forEach(card => {
                card.addEventListener('click', function (e) {
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    const page = this.getAttribute('data-page');
                    if (page && typeof window.loadPage === 'function') {
                        const fullPath = `pages/${page}`;
                        try {
                            window.loadPage(fullPath);
                        } catch (error) {
                            location.href = `index.html?page=${page}`;
                        }
                    }
                }, true); // 使用捕获阶段
            });

        }

        // 在文档加载完成或内容更新时应用事件处理
        preventEventPropagation();

        // 监听DOM变化，以防止页面动态加载后事件监听丢失
        const observer = new MutationObserver(function (mutations) {
            // 确保在改变后立即应用事件处理
            preventEventPropagation();
        });

        // 观察整个body的变化，确保能捕获到所有动态添加的内容
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 调整布局
        setTimeout(adjustLayout, 100);

        // 监听窗口尺寸变化
        window.addEventListener('resize', function () {
            setTimeout(adjustLayout, 100);
        });

        // 创建一个MutationObserver来监听内容区域的变化
        if (pageContent) {
            const observer = new MutationObserver(adjustLayout);
            observer.observe(pageContent, {
                attributes: true,
                attributeFilter: ['style', 'class']
            });

            // 同时监听侧边栏状态变化
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                observer.observe(sidebar, {
                    attributes: true,
                    attributeFilter: ['class']
                });
            }
        }
}

// 导出初始化函数到全局
window.initializeHomePage = initializeHomePage;

// 兼容原有的DOMContentLoaded方式
document.addEventListener('DOMContentLoaded', function () {
    // 检查是否在框架环境中
    const isInFramework = document.getElementById('afPageContent') !== null ||
                         typeof window.AdminFramework !== 'undefined';

    if (!isInFramework) {
        // 非框架环境，直接初始化
        initializeHomePage();
    }
    // 框架环境中，初始化将由框架系统调用
});