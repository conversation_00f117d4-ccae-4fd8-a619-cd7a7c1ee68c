function loadOrderListScript() {
            return new Promise((resolve, reject) => {
                // 检查脚本是否已经加载
                if (typeof window.initializeOrderListPage === 'function') {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = '{% static "js/orderlist.js" %}';
                script.onload = function() {
                    resolve();
                };
                script.onerror = function() {
                    reject(new Error('JavaScript文件加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        // 测试静态文件是否可访问
        fetch('{% static "js/orderlist.js" %}')
            .then(response => {
                if (response.ok) {
                    return response.text();
                } else {
                    throw new Error('静态文件访问失败');
                }
            })
            .then(jsContent => {
                // 动态加载脚本
                return loadOrderListScript();
            })
            .catch(error => {
                // 静态文件测试失败
            });

// 全局错误捕获
        window.addEventListener('error', function(event) {
            // JavaScript错误处理
        });

        function initializeOrderListPageNow() {
            // 初始化全局变量（如果不存在）
            window.currentPage = window.currentPage || 1;
            window.pageSize = window.pageSize || 10;
            window.totalRecords = window.totalRecords || 0;
            window.totalPages = window.totalPages || 0;
            window.currentFilters = window.currentFilters || {};
            window.allOrders = window.allOrders || [];
            window.filteredOrders = window.filteredOrders || [];
            window.selectedOrders = window.selectedOrders || new Set();

            // 检查JavaScript文件是否加载
            const jsVariables = ['currentPage', 'allOrders', 'filteredOrders'];
            jsVariables.forEach(varName => {
                if (typeof window[varName] === 'undefined') {
                    // 全局变量检查
                }
            });

            const jsFunctions = ['initializeOrderListPage', 'loadOrderData', 'showLoading', 'hideLoading'];
            jsFunctions.forEach(funcName => {
                if (typeof window[funcName] !== 'function') {
                    // 函数检查
                }
            });

            // 检查必要的DOM元素是否存在
            const requiredElements = [
                'orderListTable',
                'loading-overlay',
                'searchKeyword',
                'statusFilter',
                'sourceFilter',
                'typeFilter',
                'prevPageBtn',
                'nextPageBtn',
                'pageNumbers',
                'paginationInfo'
            ];

            let missingElements = [];
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    missingElements.push(id);
                }
            });

            if (missingElements.length > 0) {
                alert('页面初始化失败：缺少必要的页面元素: ' + missingElements.join(', '));
                return;
            }

            // 调用JavaScript文件中的初始化函数
            if (typeof initializeOrderListPage === 'function') {
                try {
                    initializeOrderListPage();
                } catch (error) {
                    alert('页面初始化出错: ' + error.message);
                }
            } else {
                alert('页面初始化失败：JavaScript文件加载异常');
            }

            // 5秒后检查加载状态
            setTimeout(() => {
                const loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay && loadingOverlay.style.display !== 'none') {

                    // 强制隐藏加载状态并显示错误信息
                    loadingOverlay.style.display = 'none';
                    const tableBody = document.getElementById('orderListTable');
                    if (tableBody) {
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="14" style="text-align: center; padding: 40px; color: #dc3545;">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 16px;"></i>
                                    <h3>数据加载超时</h3>
                                    <p>页面加载超过5秒，请刷新重试</p>
                                    <button onclick="window.location.reload()" style="background: #ff6b9d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin: 5px;">
                                        <i class="fas fa-redo"></i> 刷新页面
                                    </button>
                                </td>
                            </tr>
                        `;
                    }
                }
            }, 5000);
        }

        // 等待JavaScript文件加载完成后再执行初始化
        function waitForScriptAndInitialize() {
            // 检查JavaScript文件是否已加载
            if (typeof window.initializeOrderListPage === 'function') {
                initializeOrderListPageNow();
            } else {
                setTimeout(waitForScriptAndInitialize, 100); // 每100ms检查一次
            }
        }

        // 立即执行初始化，或者在DOM准备好时执行
        if (document.readyState === 'loading') {
            // DOM还在加载中，等待DOMContentLoaded事件
            document.addEventListener('DOMContentLoaded', waitForScriptAndInitialize);
        } else {
            // DOM已经加载完成，等待脚本加载后执行
            waitForScriptAndInitialize();
        }

// 版本检查 - 订单列表HTML模板版本: 2024-01-07-v2.0 - 删除确认模态框已添加