// 打开添加支付渠道模态框
    window.openAddModal = function() {
        var modal = document.getElementById('addPaymentChannelModal');
        if(modal) {
            modal.style.display = 'flex';
        } else {
            alert("找不到模态框元素: addPaymentChannelModal");
        }
    }
    
    // 关闭所有模态框
    window.closeAllModals = function() {
        var modals = document.querySelectorAll('.modal');
        for(var i = 0; i < modals.length; i++) {
            modals[i].style.display = 'none';
        }
    }
    
    // 页面加载完成后执行
    window.onload = function() {
        // 为所有关闭按钮添加事件
        var closeButtons = document.querySelectorAll('.close-modal');
        for(var i = 0; i < closeButtons.length; i++) {
            closeButtons[i].onclick = function() {
                closeAllModals();
            }
        }
    }

// 在控制台输出调试信息

    // 全局变量缓存支付渠道列表数据
    var cachedPaymentChannels = [];

    // 防重复执行标志
    var isDataLoaded = false;
    var isLoadingData = false;

    // 统一的数据加载检查函数
    window.ensureDataLoaded = function() {

        // 如果数据已加载或正在加载，则跳过
        if (window.isDataLoaded || window.isLoadingData) {
            return;
        }

        // 检查DOM是否准备就绪
        if (document.readyState === 'loading') {
            return;
        }

        // 检查关键元素是否存在
        var tableBody = document.querySelector('.payment-table tbody');
        if (!tableBody) {
            setTimeout(ensureDataLoaded, 100);
            return;
        }


        try {
            // 初始化所有事件
            initPaymentEvents();

            // 加载支付渠道列表
            loadPaymentChannels();

        } catch (error) {
            // 重置标志以允许重试
            isLoadingData = false;
        }
    }

    // 多重事件监听确保在各种情况下都能加载数据
    document.addEventListener('DOMContentLoaded', function() {
        ensureDataLoaded();
    });

    // 监听页面完全加载事件
    window.addEventListener('load', function() {
        ensureDataLoaded();
    });

    // 监听文档状态变化
    document.addEventListener('readystatechange', function() {
        if (document.readyState === 'interactive' || document.readyState === 'complete') {
            ensureDataLoaded();
        }
    });

    // 立即检查当前状态（处理从缓存加载的情况）
    if (document.readyState === 'interactive' || document.readyState === 'complete') {
        // 使用setTimeout确保在当前执行栈完成后执行
        setTimeout(ensureDataLoaded, 0);
    }

    // 备用的延迟检查机制
    setTimeout(function() {
        ensureDataLoaded();
    }, 100);

    // 最后的保障机制
    setTimeout(function() {
        ensureDataLoaded();
    }, 1000);

    // 页面可见性API支持（处理标签页切换等情况）
    if (typeof document.visibilityState !== 'undefined') {
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面变为可见时检查数据是否需要加载
                setTimeout(ensureDataLoaded, 50);
            }
        });
    }
    
    // 添加支付渠道按钮点击事件
    function initPaymentEvents() {

        // 关闭模态框按钮事件
        var closeButtons = document.querySelectorAll('.close-modal');

        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 点击模态框外部关闭模态框
        var modals = document.querySelectorAll('.modal');

        modals.forEach(function(modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.style.display = 'none';
                }
            });
        });

        // 取消按钮点击事件
        var cancelButtons = document.querySelectorAll('#cancelAddPayment, #cancelEditPayment');

        cancelButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 绑定添加表单提交事件
        var addForm = document.getElementById('addPaymentChannelForm');
        if (addForm) {
            addForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitAddForm();
            });

            // 给确认添加按钮也单独绑定点击事件
            var addSubmitBtn = addForm.querySelector('button[type="submit"]');
            if (addSubmitBtn) {
                addSubmitBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    submitAddForm();
                });
            } else {
            }
        } else {
        }

        // 绑定编辑表单提交事件
        var editForm = document.getElementById('editPaymentChannelForm');
        if (editForm) {
            editForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitEditForm();
            });

            // 给确认修改按钮也单独绑定点击事件
            var editSubmitBtn = editForm.querySelector('button[type="submit"]');
            if (editSubmitBtn) {
                editSubmitBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    submitEditForm();
                });
            } else {
            }
        } else {
        }

        // 添加支付渠道按钮点击事件
        var addPaymentBtn = document.getElementById('addPaymentChannelBtn');
        if (addPaymentBtn) {

            addPaymentBtn.addEventListener('click', function() {
                var modal = document.getElementById('addPaymentChannelModal');
                if (modal) {
                    modal.style.display = 'flex';

                    // 绑定支付类型选择事件
                    var paymentTypeSelect = document.getElementById('paymentType');
                    if (paymentTypeSelect) {

                        // 移除之前的事件监听器（如果存在）
                        paymentTypeSelect.removeEventListener('change', handleAddPaymentTypeChange);

                        // 添加支付类型切换事件监听器
                        paymentTypeSelect.addEventListener('change', handleAddPaymentTypeChange);

                        // 默认显示对应的配置区域
                        togglePaymentConfig('add', paymentTypeSelect.value);
                    }

                    // 初始化文件上传事件
                    initFileUploadEvents('add');

                    // 初始化密钥输入方式切换事件
                    initKeyInputTabs('add');
                } else {
                }
            });
        } else {
        }

        // 注意：密钥输入方式切换功能在模态框显示时单独初始化
    }

    // 处理添加模态框的支付类型切换
    function handleAddPaymentTypeChange() {
        togglePaymentConfig('add', this.value);
    }

    // 处理编辑模态框的支付类型切换
    function handleEditPaymentTypeChange() {
        togglePaymentConfig('edit', this.value);
    }

    // 切换支付配置区域显示
    function togglePaymentConfig(mode, paymentType) {

        var epayConfigId, alipayOfficialConfigId;

        if (mode === 'edit') {
            epayConfigId = 'editEpayConfig';
            alipayOfficialConfigId = 'editAlipayOfficialConfig';
        } else {
            epayConfigId = 'epayConfig';
            alipayOfficialConfigId = 'alipayOfficialConfig';
        }

        var epayConfig = document.getElementById(epayConfigId);
        var alipayOfficialConfig = document.getElementById(alipayOfficialConfigId);


        // 隐藏所有配置区域
        if (epayConfig) epayConfig.style.display = 'none';
        if (alipayOfficialConfig) alipayOfficialConfig.style.display = 'none';

        // 显示对应的配置区域
        if (paymentType === 'epay' && epayConfig) {
            epayConfig.style.display = 'block';
        } else if (paymentType === 'alipay_official' && alipayOfficialConfig) {
            alipayOfficialConfig.style.display = 'block';

            // 延迟初始化密钥输入方式切换功能，确保DOM元素已显示
            setTimeout(function() {

                // 检查DOM元素是否存在
                var prefix = mode === 'edit' ? 'edit' : '';
                var privateKeyFile = document.getElementById(prefix + 'alipayPrivateKeyFile');
                var privateKeyText = document.getElementById(prefix + 'alipayPrivateKeyText');
                var publicKeyFile = document.getElementById(prefix + 'alipayPublicKeyFile');
                var publicKeyText = document.getElementById(prefix + 'alipayPublicKeyText');


                if (privateKeyFile && privateKeyText && publicKeyFile && publicKeyText) {
                    initKeyInputTabs(mode);
                } else {
                }
            }, 200);
        }
    }

    // 初始化文件上传事件
    function initFileUploadEvents(mode) {

        var prefix = mode === 'edit' ? 'edit' : '';
        var privateKeyInput = document.getElementById(prefix + 'AlipayPrivateKey');
        var publicKeyInput = document.getElementById(prefix + 'AlipayPublicKey');

        if (privateKeyInput) {
            privateKeyInput.addEventListener('change', function() {
                handleFileUpload(this, prefix + 'AlipayPrivateKey');
            });
        }

        if (publicKeyInput) {
            publicKeyInput.addEventListener('change', function() {
                handleFileUpload(this, prefix + 'AlipayPublicKey');
            });
        }
    }

    // 处理文件上传
    function handleFileUpload(input, fieldId) {
        var file = input.files[0];
        var label = document.querySelector('label[for="' + fieldId + '"]');
        var info = label.nextElementSibling;

        if (file) {
            // 验证文件类型
            if (!file.name.toLowerCase().endsWith('.pem')) {
                info.textContent = '请选择 .pem 格式的文件';
                info.className = 'file-upload-info error';
                label.classList.remove('has-file');
                input.value = '';
                return;
            }

            // 验证文件大小 (最大 1MB)
            if (file.size > 1024 * 1024) {
                info.textContent = '文件大小不能超过 1MB';
                info.className = 'file-upload-info error';
                label.classList.remove('has-file');
                input.value = '';
                return;
            }

            // 读取文件内容
            var reader = new FileReader();
            reader.onload = function(e) {
                var fileContent = e.target.result;
                // 将文件内容存储到input的data属性中
                input.setAttribute('data-file-content', fileContent);
            };
            reader.onerror = function() {
                info.textContent = '文件读取失败，请重新选择';
                info.className = 'file-upload-info error';
                label.classList.remove('has-file');
                input.value = '';
                return;
            };
            reader.readAsText(file);

            // 显示成功信息
            info.textContent = '已选择: ' + file.name + ' (' + (file.size / 1024).toFixed(1) + ' KB)';
            info.className = 'file-upload-info success';
            label.classList.add('has-file');
            label.querySelector('.file-upload-text').textContent = file.name;
        } else {
            // 重置显示
            info.textContent = '';
            info.className = 'file-upload-info';
            label.classList.remove('has-file');
            var originalText = fieldId.includes('Private') ? '选择应用私钥文件 (.pem)' : '选择支付宝公钥文件 (.pem)';
            label.querySelector('.file-upload-text').textContent = originalText;
            // 清除文件内容
            input.removeAttribute('data-file-content');
        }
    }

    // 初始化密钥输入方式切换功能
    function initKeyInputTabs(mode) {

        var prefix = mode === 'edit' ? 'edit' : '';
        var privateKeyFileSelector = '[data-target="' + prefix + 'alipayPrivateKeyFile"]';
        var privateKeyTextSelector = '[data-target="' + prefix + 'alipayPrivateKeyText"]';
        var publicKeyFileSelector = '[data-target="' + prefix + 'alipayPublicKeyFile"]';
        var publicKeyTextSelector = '[data-target="' + prefix + 'alipayPublicKeyText"]';

        var privateKeyTabs = document.querySelectorAll(privateKeyFileSelector + ', ' + privateKeyTextSelector);
        var publicKeyTabs = document.querySelectorAll(publicKeyFileSelector + ', ' + publicKeyTextSelector);


        // 移除之前的事件监听器，避免重复绑定
        privateKeyTabs.forEach(function(tab) {
            // 克隆节点来移除所有事件监听器
            var newTab = tab.cloneNode(true);
            tab.parentNode.replaceChild(newTab, tab);
        });

        publicKeyTabs.forEach(function(tab) {
            // 克隆节点来移除所有事件监听器
            var newTab = tab.cloneNode(true);
            tab.parentNode.replaceChild(newTab, tab);
        });

        // 重新获取替换后的元素
        privateKeyTabs = document.querySelectorAll(privateKeyFileSelector + ', ' + privateKeyTextSelector);
        publicKeyTabs = document.querySelectorAll(publicKeyFileSelector + ', ' + publicKeyTextSelector);

        // 绑定私钥标签页切换事件
        privateKeyTabs.forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                handleKeyInputTabSwitch(this, mode, 'PrivateKey');
            });
        });

        // 绑定公钥标签页切换事件
        publicKeyTabs.forEach(function(tab) {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                handleKeyInputTabSwitch(this, mode, 'PublicKey');
            });
        });

    }

    // 处理密钥输入方式标签页切换
    function handleKeyInputTabSwitch(clickedTab, mode, keyType) {

        var prefix = mode === 'edit' ? 'edit' : '';
        var targetId = clickedTab.getAttribute('data-target');


        // 获取同组的所有标签页和内容区域
        var tabsContainer = clickedTab.parentElement;
        var contentContainer = tabsContainer.parentElement;


        var allTabs = tabsContainer.querySelectorAll('.key-input-tab');
        var allContents = contentContainer.querySelectorAll('.key-input-content');


        // 移除所有标签页的active状态
        allTabs.forEach(function(tab, index) {
            tab.classList.remove('active');
        });

        // 隐藏所有内容区域
        allContents.forEach(function(content, index) {
            content.classList.remove('active');
        });

        // 激活当前点击的标签页
        clickedTab.classList.add('active');

        // 显示对应的内容区域
        var targetContent = document.getElementById(targetId);
        if (targetContent) {
            targetContent.classList.add('active');
        } else {
        }

    }

    // 获取密钥内容（支持文件上传和直接粘贴两种方式）
    function getKeyContent(mode, keyType) {
        var prefix = mode === 'edit' ? 'edit' : '';
        var fileInput = document.getElementById(prefix + 'alipay' + keyType);
        var textArea = document.getElementById(prefix + 'alipay' + keyType + 'Textarea');

        // 检查当前激活的是哪种输入方式
        var fileTab = document.querySelector('[data-target="' + prefix + 'alipay' + keyType + 'File"]');
        var textTab = document.querySelector('[data-target="' + prefix + 'alipay' + keyType + 'Text"]');

        if (fileTab && fileTab.classList.contains('active')) {
            // 文件上传方式
            var fileContent = fileInput.getAttribute('data-file-content');
            return fileContent || '';
        } else if (textTab && textTab.classList.contains('active')) {
            // 直接粘贴方式 - 自动格式化密钥内容
            var rawContent = textArea.value.trim();
            if (rawContent) {
                // 自动格式化密钥内容
                var formattedContent = formatKeyContent(rawContent, keyType);
                return formattedContent;
            }
            return '';
        }

        return '';
    }

    // 自动格式化密钥内容
    function formatKeyContent(keyContent, keyType) {
        if (!keyContent || keyContent.trim() === '') {
            return '';
        }

        var trimmedContent = keyContent.trim();

        // 如果已经是完整的PEM格式，直接返回
        if (trimmedContent.includes('-----BEGIN') && trimmedContent.includes('-----END')) {
            return trimmedContent;
        }

        // 如果是纯密钥内容，自动添加PEM格式标记
        var formattedContent = '';

        if (keyType === 'PrivateKey') {
            // 添加私钥的PEM格式标记
            formattedContent = '-----BEGIN RSA PRIVATE KEY-----\n' +
                              trimmedContent + '\n' +
                              '-----END RSA PRIVATE KEY-----';
        } else if (keyType === 'PublicKey') {
            // 添加公钥的PEM格式标记
            formattedContent = '-----BEGIN PUBLIC KEY-----\n' +
                              trimmedContent + '\n' +
                              '-----END PUBLIC KEY-----';
        }

        return formattedContent;
    }

    // 验证密钥格式（支持自动格式化）
    function validateKeyFormat(keyContent, keyType) {
        if (!keyContent || keyContent.trim() === '') {
            return { valid: false, message: '密钥内容不能为空', formattedContent: '' };
        }

        // 自动格式化密钥内容
        var formattedContent = formatKeyContent(keyContent, keyType);

        if (!formattedContent) {
            return { valid: false, message: '密钥内容格式化失败', formattedContent: '' };
        }

        // 验证格式化后的内容
        if (keyType === 'PrivateKey') {
            if (!formattedContent.includes('PRIVATE KEY')) {
                return { valid: false, message: '私钥内容格式不正确', formattedContent: formattedContent };
            }
        } else if (keyType === 'PublicKey') {
            if (!formattedContent.includes('PUBLIC KEY')) {
                return { valid: false, message: '公钥内容格式不正确', formattedContent: formattedContent };
            }
        }

        // 基本的密钥内容验证（检查是否包含base64字符）
        var keyContentOnly = formattedContent.replace(/-----BEGIN.*-----/g, '')
                                           .replace(/-----END.*-----/g, '')
                                           .replace(/\s/g, '');

        if (keyContentOnly.length < 100) {
            return { valid: false, message: '密钥内容过短，请检查是否完整', formattedContent: formattedContent };
        }

        // 检查是否包含有效的base64字符
        var base64Pattern = /^[A-Za-z0-9+/=]+$/;
        if (!base64Pattern.test(keyContentOnly)) {
            return { valid: false, message: '密钥内容包含无效字符，请确保只包含密钥数据', formattedContent: formattedContent };
        }

        return { valid: true, message: '密钥格式正确', formattedContent: formattedContent };
    }

    // 计算MD5哈希
    function md5(string) {
        function md5cycle(x, k) {
            var a = x[0], b = x[1], c = x[2], d = x[3];
            
            a = ff(a, b, c, d, k[0], 7, -680876936);
            d = ff(d, a, b, c, k[1], 12, -389564586);
            c = ff(c, d, a, b, k[2], 17,  606105819);
            b = ff(b, c, d, a, k[3], 22, -1044525330);
            a = ff(a, b, c, d, k[4], 7, -176418897);
            d = ff(d, a, b, c, k[5], 12,  1200080426);
            c = ff(c, d, a, b, k[6], 17, -1473231341);
            b = ff(b, c, d, a, k[7], 22, -45705983);
            a = ff(a, b, c, d, k[8], 7,  1770035416);
            d = ff(d, a, b, c, k[9], 12, -1958414417);
            c = ff(c, d, a, b, k[10], 17, -42063);
            b = ff(b, c, d, a, k[11], 22, -1990404162);
            a = ff(a, b, c, d, k[12], 7,  1804603682);
            d = ff(d, a, b, c, k[13], 12, -40341101);
            c = ff(c, d, a, b, k[14], 17, -1502002290);
            b = ff(b, c, d, a, k[15], 22,  1236535329);
            
            a = gg(a, b, c, d, k[1], 5, -165796510);
            d = gg(d, a, b, c, k[6], 9, -1069501632);
            c = gg(c, d, a, b, k[11], 14,  643717713);
            b = gg(b, c, d, a, k[0], 20, -373897302);
            a = gg(a, b, c, d, k[5], 5, -701558691);
            d = gg(d, a, b, c, k[10], 9,  38016083);
            c = gg(c, d, a, b, k[15], 14, -660478335);
            b = gg(b, c, d, a, k[4], 20, -405537848);
            a = gg(a, b, c, d, k[9], 5,  568446438);
            d = gg(d, a, b, c, k[14], 9, -1019803690);
            c = gg(c, d, a, b, k[3], 14, -187363961);
            b = gg(b, c, d, a, k[8], 20,  1163531501);
            a = gg(a, b, c, d, k[13], 5, -1444681467);
            d = gg(d, a, b, c, k[2], 9, -51403784);
            c = gg(c, d, a, b, k[7], 14,  1735328473);
            b = gg(b, c, d, a, k[12], 20, -1926607734);
            
            a = hh(a, b, c, d, k[5], 4, -378558);
            d = hh(d, a, b, c, k[8], 11, -2022574463);
            c = hh(c, d, a, b, k[11], 16,  1839030562);
            b = hh(b, c, d, a, k[14], 23, -35309556);
            a = hh(a, b, c, d, k[1], 4, -1530992060);
            d = hh(d, a, b, c, k[4], 11,  1272893353);
            c = hh(c, d, a, b, k[7], 16, -155497632);
            b = hh(b, c, d, a, k[10], 23, -1094730640);
            a = hh(a, b, c, d, k[13], 4,  681279174);
            d = hh(d, a, b, c, k[0], 11, -358537222);
            c = hh(c, d, a, b, k[3], 16, -722521979);
            b = hh(b, c, d, a, k[6], 23,  76029189);
            a = hh(a, b, c, d, k[9], 4, -640364487);
            d = hh(d, a, b, c, k[12], 11, -421815835);
            c = hh(c, d, a, b, k[15], 16,  530742520);
            b = hh(b, c, d, a, k[2], 23, -995338651);
            
            a = ii(a, b, c, d, k[0], 6, -198630844);
            d = ii(d, a, b, c, k[7], 10,  1126891415);
            c = ii(c, d, a, b, k[14], 15, -1416354905);
            b = ii(b, c, d, a, k[5], 21, -57434055);
            a = ii(a, b, c, d, k[12], 6,  1700485571);
            d = ii(d, a, b, c, k[3], 10, -1894986606);
            c = ii(c, d, a, b, k[10], 15, -1051523);
            b = ii(b, c, d, a, k[1], 21, -2054922799);
            a = ii(a, b, c, d, k[8], 6,  1873313359);
            d = ii(d, a, b, c, k[15], 10, -30611744);
            c = ii(c, d, a, b, k[6], 15, -1560198380);
            b = ii(b, c, d, a, k[13], 21,  1309151649);
            a = ii(a, b, c, d, k[4], 6, -145523070);
            d = ii(d, a, b, c, k[11], 10, -1120210379);
            c = ii(c, d, a, b, k[2], 15,  718787259);
            b = ii(b, c, d, a, k[9], 21, -343485551);
            
            x[0] = add32(a, x[0]);
            x[1] = add32(b, x[1]);
            x[2] = add32(c, x[2]);
            x[3] = add32(d, x[3]);
        }
        
        function cmn(q, a, b, x, s, t) {
            a = add32(add32(a, q), add32(x, t));
            return add32((a << s) | (a >>> (32 - s)), b);
        }
        
        function ff(a, b, c, d, x, s, t) {
            return cmn((b & c) | ((~b) & d), a, b, x, s, t);
        }
        
        function gg(a, b, c, d, x, s, t) {
            return cmn((b & d) | (c & (~d)), a, b, x, s, t);
        }
        
        function hh(a, b, c, d, x, s, t) {
            return cmn(b ^ c ^ d, a, b, x, s, t);
        }
        
        function ii(a, b, c, d, x, s, t) {
            return cmn(c ^ (b | (~d)), a, b, x, s, t);
        }
        
        function md51(s) {
            var n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
            for (i=64; i<=s.length; i+=64) {
                md5cycle(state, md5blk(s.substring(i-64, i)));
            }
            s = s.substring(i-64);
            var tail = [0,0,0,0, 0,0,0,0, 0,0,0,0, 0,0,0,0];
            for (i=0; i<s.length; i++)
                tail[i>>2] |= s.charCodeAt(i) << ((i%4) << 3);
            tail[i>>2] |= 0x80 << ((i%4) << 3);
            if (i > 55) {
                md5cycle(state, tail);
                for (i=0; i<16; i++) tail[i] = 0;
            }
            tail[14] = n*8;
            md5cycle(state, tail);
            return state;
        }
        
        function md5blk(s) {
            var md5blks = [], i;
            for (i=0; i<64; i+=4) {
                md5blks[i>>2] = s.charCodeAt(i) + (s.charCodeAt(i+1) << 8) + (s.charCodeAt(i+2) << 16) + (s.charCodeAt(i+3) << 24);
            }
            return md5blks;
        }
        
        var hex_chr = '0123456789abcdef'.split('');
        
        function rhex(n) {
            var s='', j=0;
            for(; j<4; j++)
                s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];
            return s;
        }
        
        function hex(x) {
            for (var i=0; i<x.length; i++)
                x[i] = rhex(x[i]);
            return x.join('');
        }
        
        function md5(s) {
            return hex(md51(s));
        }
        
        function add32(a, b) {
            return (a + b) & 0xFFFFFFFF;
        }
        
        return md5(string);
    }
    
    // Base64编码函数
    function base64Encode(str) {
        // 使用浏览器自带的btoa函数
        return btoa(unescape(encodeURIComponent(str)));
    }
    
    // 生成签名
    function generateSign(data) {

        // 如果已经有sign字段，先移除它
        var dataCopy = Object.assign({}, data);
        if (dataCopy.sign) {
            delete dataCopy.sign;
        }

        // 递归对对象中的所有属性进行ASCII排序
        function sortObjectByKey(obj) {
            // 如果不是对象或是null，直接返回
            if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
                return obj;
            }

            // 对对象的所有键进行排序
            var sortedKeys = Object.keys(obj).sort();
            var sortedObj = {};

            // 按照排序后的键重新构建对象
            for (var i = 0; i < sortedKeys.length; i++) {
                var key = sortedKeys[i];
                sortedObj[key] = sortObjectByKey(obj[key]); // 递归处理嵌套对象
            }

            return sortedObj;
        }

        // 对整个对象进行排序
        var sortedData = sortObjectByKey(dataCopy);

        // 将数据转换为JSON字符串
        var jsonStr = JSON.stringify(sortedData);

        // 对JSON字符串进行Base64编码
        var base64Str = base64Encode(jsonStr);

        // 计算MD5哈希值
        var sign = md5(base64Str);

        return sign;
    }
    
    // 添加表单提交
    function submitAddForm() {

        // 表单验证
        var paymentType = document.getElementById('paymentType').value;
        var name = document.getElementById('paymentName').value.trim();
        var fee = parseFloat(document.getElementById('paymentFee').value) || 0;
        var isActive = document.getElementById('paymentStatus').checked;

        // 验证支付名称
        if (!name) {
            showToast('支付名称不能为空', 'error');
            return;
        }

        // 码/易支付通用渠道的特定验证
        if (paymentType === 'epay') {
            var domain = document.getElementById('epayDomain').value.trim();
            var appid = document.getElementById('epayAppId').value.trim();
            var key = document.getElementById('epayKey').value.trim();
            var hasWechat = document.getElementById('epayWechat').checked;
            var hasAlipay = document.getElementById('epayAlipay').checked;
            var hasQQ = document.getElementById('epayQQ').checked;

            // 验证必填字段
            if (!domain) {
                showToast('对接域名不能为空', 'error');
                return;
            }

            if (!appid) {
                showToast('对接APPID不能为空', 'error');
                return;
            }

            if (!key) {
                showToast('对接Key不能为空', 'error');
                return;
            }

            // 验证至少选择一个支付渠道
            if (!hasWechat && !hasAlipay && !hasQQ) {
                showToast('请至少选择一个支付渠道', 'error');
                return;
            }
        }

        // 支付宝官方支付渠道的特定验证
        if (paymentType === 'alipay_official') {
            var alipayAppId = document.getElementById('alipayAppId').value.trim();

            // 验证必填字段
            if (!alipayAppId) {
                showToast('支付宝应用ID不能为空', 'error');
                return;
            }

            // 获取私钥内容（支持文件上传和直接粘贴）
            var privateKeyContent = getKeyContent('add', 'PrivateKey');
            if (!privateKeyContent) {
                showToast('请提供支付宝应用私钥（上传文件或直接粘贴）', 'error');
                return;
            }

            // 验证私钥格式
            var privateKeyValidation = validateKeyFormat(privateKeyContent, 'PrivateKey');
            if (!privateKeyValidation.valid) {
                showToast('应用私钥格式错误：' + privateKeyValidation.message, 'error');
                return;
            }

            // 获取公钥内容（支持文件上传和直接粘贴）
            var publicKeyContent = getKeyContent('add', 'PublicKey');
            if (!publicKeyContent) {
                showToast('请提供支付宝公钥（上传文件或直接粘贴）', 'error');
                return;
            }

            // 验证公钥格式
            var publicKeyValidation = validateKeyFormat(publicKeyContent, 'PublicKey');
            if (!publicKeyValidation.valid) {
                showToast('支付宝公钥格式错误：' + publicKeyValidation.message, 'error');
                return;
            }
        }
        
        showLoading();

        var paymentData = {};
        var requestType = "";

        // 根据支付类型构建不同的数据结构
        if (paymentType === 'epay') {
            // 收集对接渠道信息
            var channels = [];
            if (document.getElementById('epayWechat').checked) {
                channels.push('wxpay');
            }
            if (document.getElementById('epayAlipay').checked) {
                channels.push('alipay');
            }
            if (document.getElementById('epayQQ').checked) {
                channels.push('qqpay');
            }

            // 构建易支付data字段
            paymentData = {
                api_type: document.querySelector('input[name="apiType"]:checked').value,
                domain: document.getElementById('epayDomain').value,
                appid: document.getElementById('epayAppId').value,
                key: document.getElementById('epayKey').value,
                channel: channels
            };
            requestType = "code_e_payment";
        } else if (paymentType === 'alipay_official') {
            // 获取密钥内容（支持文件上传和直接粘贴两种方式）
            var privateKeyContent = getKeyContent('add', 'PrivateKey');
            var publicKeyContent = getKeyContent('add', 'PublicKey');

            // 构建支付宝官方data字段（使用格式化后的密钥内容）
            paymentData = {
                appid: document.getElementById('alipayAppId').value,
                app_private_key: privateKeyValidation.formattedContent,
                alipay_public_key: publicKeyValidation.formattedContent,
                sandbox: document.getElementById('alipaySandbox').checked,
                channel:['alipay']
            };
            requestType = "alipay";
        }

        // 构建完整的请求数据
        var requestData = {
            name: name,
            type: requestType,
            serviceCharge: fee,
            is_active: isActive,
            data: paymentData
        };

        // 生成并添加签名
        var sign = generateSign(requestData);
        requestData.sign = sign;
        
        
        // 发送数据到API
        fetch('/api/add_payment_method', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                showToast('添加支付渠道成功', 'success');
                
                // 关闭模态框
                closeAllModals();
                
                // 如果返回了列表数据，直接更新列表
                if (data.list && Array.isArray(data.list)) {
                    updatePaymentList(data.list);
                } else {
                    // 否则重新加载列表
                    loadPaymentChannels();
                }
                
                // 重置表单
                document.getElementById('addPaymentChannelForm').reset();
            } else {
                showToast(data.msg || '添加失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('添加支付渠道失败: ' + error.message, 'error');
        });
    }

    // 将 submitAddForm 函数添加到全局作用域，使其可以被HTML onclick属性访问
    window.submitAddForm = submitAddForm;



    // 重置文件上传显示
    function resetFileUploadDisplay(mode) {
        var prefix = mode === 'edit' ? 'edit' : '';
        var privateKeyInput = document.getElementById(prefix + 'AlipayPrivateKey');
        var publicKeyInput = document.getElementById(prefix + 'AlipayPublicKey');

        if (privateKeyInput) {
            var privateLabel = document.querySelector('label[for="' + prefix + 'AlipayPrivateKey"]');
            var privateInfo = privateLabel.nextElementSibling;
            privateInfo.textContent = '';
            privateInfo.className = 'file-upload-info';
            privateLabel.classList.remove('has-file');
            privateLabel.querySelector('.file-upload-text').textContent = '选择应用私钥文件 (.pem)';
        }

        if (publicKeyInput) {
            var publicLabel = document.querySelector('label[for="' + prefix + 'AlipayPublicKey"]');
            var publicInfo = publicLabel.nextElementSibling;
            publicInfo.textContent = '';
            publicInfo.className = 'file-upload-info';
            publicLabel.classList.remove('has-file');
            publicLabel.querySelector('.file-upload-text').textContent = '选择支付宝公钥文件 (.pem)';
        }
    }

    // 更新支付渠道列表
    function updatePaymentList(list) {

        // 缓存支付渠道数据
        cachedPaymentChannels = list || [];

        var tableBody = document.querySelector('.payment-table tbody');
        var emptyState = document.querySelector('.empty-state');

        if (!tableBody) {
            return;
        }

        // 清空现有内容
        tableBody.innerHTML = '';

        // 判断是否有数据
        if (!list || list.length === 0) {
            if (emptyState) {
                emptyState.style.display = 'block';
            }
            return;
        }

        // 隐藏空状态
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // 渲染数据
        list.forEach(function(item) {
            var row = document.createElement('tr');
            
            // 格式化支付类型显示
            var typeText = item.type;
            if (item.type === 'code_e_payment') {
                typeText = '码/易支付通用';
            } else if (item.type === 'alipay_official') {
                typeText = '支付宝官方';
            }
            
                                row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${typeText}</td>
                        <td>${item.fee}%</td>
                        <td>
                            <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                                ${item.status === 'active' ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td class="action-buttons">
                            <button class="action-btn edit-btn" title="编辑" data-id="${item.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" title="删除" data-id="${item.id}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    `;
            
            tableBody.appendChild(row);
        });
        
        // 绑定操作按钮事件
        bindActionButtons();
    }
    
    // 编辑表单提交
    function submitEditForm() {

        var channelId = document.getElementById('editPaymentId').value;
        if (!channelId) {
            showToast('支付渠道ID无效', 'error');
            return;
        }

        // 表单验证
        var paymentType = document.getElementById('editPaymentType').value;
        var name = document.getElementById('editPaymentName').value.trim();
        var fee = parseFloat(document.getElementById('editPaymentFee').value) || 0;
        var isActive = document.getElementById('editPaymentStatus').checked;

        // 验证支付名称
        if (!name) {
            showToast('支付名称不能为空', 'error');
            return;
        }

        // 根据支付类型进行不同的验证
        if (paymentType === 'epay') {
            // 验证易支付配置
            var domain = document.getElementById('editEpayDomain').value.trim();
            var appid = document.getElementById('editEpayAppId').value.trim();
            var key = document.getElementById('editEpayKey').value.trim();
            var hasWechat = document.getElementById('editEpayWechat').checked;
            var hasAlipay = document.getElementById('editEpayAlipay').checked;
            var hasQQ = document.getElementById('editEpayQQ').checked;

            // 验证必填字段
            if (!domain) {
                showToast('对接域名不能为空', 'error');
                return;
            }

            if (!appid) {
                showToast('对接APPID不能为空', 'error');
                return;
            }

            if (!key) {
                showToast('对接Key不能为空', 'error');
                return;
            }

            // 验证至少选择一个支付渠道
            if (!hasWechat && !hasAlipay && !hasQQ) {
                showToast('请至少选择一个支付渠道', 'error');
                return;
            }
        } else if (paymentType === 'alipay_official') {
            // 验证支付宝官方配置
            var alipayAppId = document.getElementById('editAlipayAppId').value.trim();

            // 验证必填字段
            if (!alipayAppId) {
                showToast('支付宝应用ID不能为空', 'error');
                return;
            }

            // 检查是否有新的密钥内容（编辑时可能不需要重新提供密钥）
            var privateKeyContent = getKeyContent('edit', 'PrivateKey');
            var publicKeyContent = getKeyContent('edit', 'PublicKey');

            // 如果提供了新的密钥内容，需要验证格式
            var privateKeyValidation = null;
            var publicKeyValidation = null;

            if (privateKeyContent) {
                privateKeyValidation = validateKeyFormat(privateKeyContent, 'PrivateKey');
                if (!privateKeyValidation.valid) {
                    showToast('应用私钥格式错误：' + privateKeyValidation.message, 'error');
                    return;
                }
            }

            if (publicKeyContent) {
                publicKeyValidation = validateKeyFormat(publicKeyContent, 'PublicKey');
                if (!publicKeyValidation.valid) {
                    showToast('支付宝公钥格式错误：' + publicKeyValidation.message, 'error');
                    return;
                }
            }
        }

        showLoading();

        var paymentData = {};
        var requestType = "";

        // 根据支付类型构建不同的数据结构
        if (paymentType === 'alipay_official') {
            // 构建支付宝官方data字段
            var appId = document.getElementById('editAlipayAppId').value;

            // 获取密钥内容（支持文件上传和直接粘贴两种方式）
            var privateKeyContent = getKeyContent('edit', 'PrivateKey');
            var publicKeyContent = getKeyContent('edit', 'PublicKey');

            paymentData = {
                appid: appId
            };

            // 只有在有新密钥内容时才更新（使用格式化后的内容）
            if (privateKeyValidation && privateKeyValidation.formattedContent) {
                paymentData.app_private_key = privateKeyValidation.formattedContent;
            }
            if (publicKeyValidation && publicKeyValidation.formattedContent) {
                paymentData.alipay_public_key = publicKeyValidation.formattedContent;
            }

            // 添加沙箱模式配置
            paymentData.sandbox = document.getElementById('editAlipaySandbox').checked;

            requestType = "alipay";
        } else {
            // 易支付渠道的处理逻辑
            var domain = document.getElementById('editEpayDomain').value.trim();
            var appid = document.getElementById('editEpayAppId').value.trim();
            var key = document.getElementById('editEpayKey').value.trim();
            var hasWechat = document.getElementById('editEpayWechat').checked;
            var hasAlipay = document.getElementById('editEpayAlipay').checked;
            var hasQQ = document.getElementById('editEpayQQ').checked;

            // 收集对接渠道信息
            var channels = [];
            if (hasWechat) {
                channels.push('wxpay');
            }
            if (hasAlipay) {
                channels.push('alipay');
            }
            if (hasQQ) {
                channels.push('qqpay');
            }

            // 构建data字段
            paymentData = {
                api_type: document.querySelector('input[name="editApiType"]:checked').value,
                domain: domain,
                appid: appid,
                key: key,
                channel: channels
            };
            requestType = "code_e_payment";
        }

        // 构建请求数据
        var requestData = {
            id: channelId,
            name: name,
            type: requestType,
            serviceCharge: fee,
            is_active: isActive,
            data: paymentData
        };

        // 生成并添加签名
        var sign = generateSign(requestData);
        requestData.sign = sign;

        // 发送数据到API
        fetch('/api/update_payment_method', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                showToast('更新支付渠道成功', 'success');
                
                // 关闭模态框
                closeAllModals();
                
                // 如果返回了列表数据，直接更新列表
                if (data.list && Array.isArray(data.list)) {
                    updatePaymentList(data.list);
                } else {
                    // 否则重新加载列表
                    loadPaymentChannels();
                }
            } else {
                showToast(data.msg || '更新失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('更新支付渠道失败: ' + error.message, 'error');
        });
    }

    // 将 submitEditForm 函数添加到全局作用域，使其可以被HTML onclick属性访问
    window.submitEditForm = submitEditForm;

    // 加载支付渠道列表
    function loadPaymentChannels() {

        // 设置加载状态标志
        isLoadingData = true;

        // 显示加载中
        showLoading();
        
        // 获取列表容器
        var tableBody = document.querySelector('.payment-table tbody');
        var emptyState = document.querySelector('.empty-state');

        if (!tableBody) {
            hideLoading();
            return;
        }

        // 清空现有内容
        tableBody.innerHTML = '';

        // 通过API获取数据
        fetch('/api/get_payment_method_list')
            .then(response => response.json())
            .then(result => {
                hideLoading();

                if (result.code !== 200) {
                    showToast(result.msg || '获取支付渠道列表失败', 'error');
                    return;
                }

                var channels = result.data || [];

                // 缓存支付渠道数据
                cachedPaymentChannels = channels;

                // 设置数据已加载标志
                isDataLoaded = true;
                isLoadingData = false;

                // 判断是否有数据
                if (channels.length === 0) {
                    if (emptyState) {
                        emptyState.style.display = 'block';
                    }
                    return;
                }

                // 隐藏空状态
                if (emptyState) {
                    emptyState.style.display = 'none';
                }
                
                // 渲染数据
                channels.forEach(function(channel) {
                    var row = document.createElement('tr');
                    
                    // 格式化支付类型显示
                    var typeText = channel.type;
                    if (channel.type === 'code_e_payment') {
                        typeText = '码/易支付通用';
                    }
                    
                    row.innerHTML = `
                        <td>${channel.id}</td>
                        <td>${channel.name}</td>
                        <td>${typeText}</td>
                        <td>${channel.fee}%</td>
                        <td>
                            <span class="status-badge ${channel.status === 'active' ? 'status-active' : 'status-inactive'}">
                                ${channel.status === 'active' ? '启用' : '禁用'}
                            </span>
                        </td>
                        <td class="action-buttons">
                            <button class="action-btn edit-btn" title="编辑" data-id="${channel.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" title="删除" data-id="${channel.id}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    `;
                    
                    tableBody.appendChild(row);
                });
                
                // 绑定操作按钮事件
                bindActionButtons();
            })
            .catch(error => {
                hideLoading();
                showToast('获取支付渠道列表失败', 'error');

                // 重置加载状态标志以允许重试
                isLoadingData = false;

                // 显示空状态
                if (emptyState) {
                    emptyState.style.display = 'block';
                }
            });
    }
    
    // 绑定列表中的操作按钮事件
    function bindActionButtons() {
        
        // 编辑按钮
        var editButtons = document.querySelectorAll('.edit-btn');

        editButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var channelId = this.getAttribute('data-id');
                openEditModal(channelId);
            });
        });

        // 删除按钮
        var deleteButtons = document.querySelectorAll('.delete-btn');

        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var channelId = this.getAttribute('data-id');
                deletePaymentChannel(channelId);
            });
        });
    }
    
    // 打开编辑模态框
    function openEditModal(channelId) {

        // 从缓存数据中查找对应的支付渠道
        var channel = cachedPaymentChannels.find(function(item) { return item.id === channelId; });

        if (!channel) {
            showToast('支付渠道不存在', 'error');
            return;
        }


        try {
            // 填充基本表单数据
            document.getElementById('editPaymentId').value = channel.id;
            document.getElementById('editPaymentName').value = channel.name || '';
            document.getElementById('editPaymentFee').value = channel.fee || '0';
            document.getElementById('editPaymentStatus').checked = channel.status === 'active';

            // 设置支付类型
            var editPaymentType = document.getElementById('editPaymentType');
            editPaymentType.value = channel.type || 'epay';

            // 解析data字段中的配置信息
            var configData = {};
            if (channel.data) {
                try {
                    configData = JSON.parse(channel.data);
                } catch (e) {
                }
            }

            // 根据支付类型填充不同的配置字段
            if (channel.type === 'code_e_payment') {
                // 填充易支付配置字段
                if (configData.api_type) {
                    var apiTypeRadio = document.querySelector('input[name="editApiType"][value="' + configData.api_type + '"]');
                    if (apiTypeRadio) {
                        apiTypeRadio.checked = true;
                    }
                }

                document.getElementById('editEpayDomain').value = configData.domain || '';
                document.getElementById('editEpayAppId').value = configData.appid || '';
                document.getElementById('editEpayKey').value = configData.key || '';

                // 填充支付渠道选择
                var channels = configData.channel || [];
                document.getElementById('editEpayWechat').checked = channels.includes('wxpay');
                document.getElementById('editEpayAlipay').checked = channels.includes('alipay');
                document.getElementById('editEpayQQ').checked = channels.includes('qqpay');
            } else if (channel.type === 'alipay_official') {
                // 填充支付宝官方配置字段
                document.getElementById('editAlipayAppId').value = configData.appid || '';
                // 填充沙箱模式开关
                document.getElementById('editAlipaySandbox').checked = configData.sandbox || false;

                // 填充密钥内容到文本框（如果存在）
                if (configData.app_private_key) {
                    document.getElementById('editAlipayPrivateKeyTextarea').value = configData.app_private_key;
                    // 切换到文本输入模式
                    var privateTextTab = document.querySelector('[data-target="editAlipayPrivateKeyText"]');
                    if (privateTextTab) {
                        handleKeyInputTabSwitch(privateTextTab, 'edit', 'PrivateKey');
                    }
                }

                if (configData.alipay_public_key) {
                    document.getElementById('editAlipayPublicKeyTextarea').value = configData.alipay_public_key;
                    // 切换到文本输入模式
                    var publicTextTab = document.querySelector('[data-target="editAlipayPublicKeyText"]');
                    if (publicTextTab) {
                        handleKeyInputTabSwitch(publicTextTab, 'edit', 'PublicKey');
                    }
                }

                // 如果没有密钥内容，显示提示信息
                if (!configData.app_private_key && !configData.alipay_public_key) {
                    var privateKeyInfo = document.querySelector('#editAlipayPrivateKeyFile .file-upload-info');
                    var publicKeyInfo = document.querySelector('#editAlipayPublicKeyFile .file-upload-info');
                    if (privateKeyInfo) {
                        privateKeyInfo.textContent = '当前已有私钥配置，如需更换请重新提供';
                        privateKeyInfo.className = 'file-upload-info';
                    }
                    if (publicKeyInfo) {
                        publicKeyInfo.textContent = '当前已有公钥配置，如需更换请重新提供';
                        publicKeyInfo.className = 'file-upload-info';
                    }
                }
            }

            // 切换配置区域显示
            togglePaymentConfig('edit', channel.type === 'alipay_official' ? 'alipay_official' : 'epay');

            // 绑定支付类型切换事件
            editPaymentType.removeEventListener('change', handleEditPaymentTypeChange);
            editPaymentType.addEventListener('change', handleEditPaymentTypeChange);

            // 初始化文件上传事件
            initFileUploadEvents('edit');

            // 初始化密钥输入方式切换事件
            initKeyInputTabs('edit');

            // 显示模态框
            document.getElementById('editPaymentChannelModal').style.display = 'flex';

        } catch (error) {
            showToast('填充表单数据失败', 'error');
        }
    }
    
    // 删除支付渠道
    function deletePaymentChannel(channelId) {
        
        // 确认删除
        if (!confirm('确定要删除这个支付渠道吗？删除后将无法恢复。')) {
            return;
        }
        
        showLoading();
        
        // 构建请求数据
        var requestData = {
            id: channelId
        };
        
        // 生成并添加签名
        var sign = generateSign(requestData);
        requestData.sign = sign;
        
        fetch('/api/delete_payment_method', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                showToast('删除支付渠道成功', 'success');
                
                // 如果返回了列表数据，直接更新列表
                if (data.list && Array.isArray(data.list)) {
                    updatePaymentList(data.list);
                } else {
                    // 否则重新加载列表
                    loadPaymentChannels();
                }
            } else {
                showToast(data.msg || '删除失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('删除支付渠道失败: ' + error.message, 'error');
        });
    }
    
    // 显示通知
    function showToast(message, type = 'success') {
        
        // 获取通知容器
        var toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 创建通知元素
        var toast = document.createElement('div');
        toast.className = 'toast toast-' + type;
        toast.innerHTML =
            '<div class="toast-icon">' +
                '<i class="fas ' + (type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle') + '"></i>' +
            '</div>' +
            '<div class="toast-message">' + message + '</div>';

        // 添加到容器
        toastContainer.appendChild(toast);

        // 显示通知
        setTimeout(function() {
            toast.classList.add('show');
        }, 10);

        // 自动关闭
        setTimeout(function() {
            toast.classList.remove('show');

            // 移除元素
            setTimeout(function() {
                toast.remove();
            }, 300);
        }, 3000);
    }
    
    // 显示加载中
    function showLoading() {

        var loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.add('show');
        }
    }

    // 隐藏加载中
    function hideLoading() {

        var loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('show');
        }
    }