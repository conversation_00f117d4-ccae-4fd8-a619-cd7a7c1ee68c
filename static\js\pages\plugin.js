// 设置全局API基础URL
    window.BASE_URL = window.BASE_URL || "http://127.0.0.1:8070";

    // 全局令牌和用户信息对象初始化
    if (!window.authData) {
        window.authData = {
            token: null,
            refreshToken: null,
            timestamp: null,
            key: null,
            userId: null,
            userEmail: null,
            userInfo: null
        };
    }
    
    // 立即执行函数，在页面解析时就检查令牌状态
    (function() {
        // 从存储恢复令牌
        let token = sessionStorage.getItem('access_token');
        let refreshToken = localStorage.getItem('refresh_token');
        let timestamp = sessionStorage.getItem('timestamp');
        let key = sessionStorage.getItem('key');
        
        // 保存到全局对象
        window.authData.token = token;
        window.authData.refreshToken = refreshToken;
        window.authData.timestamp = timestamp;
        window.authData.key = key;
        
        // 恢复用户信息
        const userId = localStorage.getItem('userId');
        const userEmail = localStorage.getItem('userEmail');
        let userInfo = null;
        
        try {
            const userInfoStr = localStorage.getItem('userInfo');
            if (userInfoStr) {
                userInfo = JSON.parse(userInfoStr);
            }
        } catch (e) {
        }
        
        window.authData.userId = userId;
        window.authData.userEmail = userEmail;
        window.authData.userInfo = userInfo;
        
        // 检查令牌是否已过期
        if (token && timestamp) {
            const tokenTime = parseInt(timestamp);
            const now = Math.floor(Date.now() / 1000);
            const expiresIn = tokenTime + 3600 - now; // 令牌有效期1小时
            
            
            if (expiresIn <= 0) {
                // 令牌已过期，但有刷新令牌，将在页面加载后尝试刷新
                window.tokenStatus = 'expired';
            } else if (expiresIn < 300) {
                // 令牌即将过期，将在页面加载后刷新
                window.tokenStatus = 'expiring';
            } else {
                // 令牌有效
                window.tokenStatus = 'valid';
                
                // 如果没有用户信息但有用户ID和邮箱，创建最小用户信息对象
                if (!userInfo && userId && userEmail) {
                    userInfo = { id: userId, email: userEmail };
                    window.authData.userInfo = userInfo;
                }
                
                // 如果已有用户信息，立即尝试更新UI
                if (userInfo) {
                    // 使用setTimeout确保DOM元素已经加载
                    setTimeout(function() {
                        const developerBtn = document.getElementById('developerBtn');
                        if (developerBtn) {
                            // 如果用户已登录，改变按钮显示
                            developerBtn.innerHTML = `
                                <i class="fas fa-user-circle"></i> ${userInfo.email.split('@')[0]}
                                <span class="btn-sparkle"></span>
                            `;
                            
                            // 修改点击事件为显示用户菜单
                            developerBtn.onclick = function() {
                                showUserMenu(userInfo);
                                return false;
                            };
                        } else {
                            // 使用MutationObserver监听DOM变化
                            setupButtonObserver();
                        }
                    }, 0);
                }
            }
        } else if (refreshToken) {
            // 有刷新令牌但无访问令牌
            window.tokenStatus = 'refresh_only';
        } else {
            // 无令牌，用户未登录
            window.tokenStatus = 'no_token';
        }
    })();
    
    // 设置DOM观察器，等待按钮元素加载
    function setupButtonObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 如果有节点被添加
                if (mutation.addedNodes.length) {
                    const developerBtn = document.getElementById('developerBtn');
                    if (developerBtn && window.authData.userInfo) {
                        // 如果找到按钮并且有用户信息，更新界面
                        developerBtn.innerHTML = `
                            <i class="fas fa-user-circle"></i> ${window.authData.userInfo.email.split('@')[0]}
                            <span class="btn-sparkle"></span>
                        `;
                        
                        // 修改点击事件为显示用户菜单
                        developerBtn.onclick = function() {
                            showUserMenu(window.authData.userInfo);
                            return false;
                        };
                        
                        // 停止观察
                        observer.disconnect();
                    }
                }
            });
        });
        
        // 开始观察document.body，监听子节点变化
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
    // 立即定义紧急函数，确保按钮事件可以工作
    // 使用window属性而不是普通函数声明，避免重复声明问题
    window.emergencyShowModal = function() {
        
        var modal = document.getElementById('developerModal');
        if (modal) {
            modal.style.display = "flex";
            
            // 直接初始化模态框内部功能
            initModalUI();
            
            // 将原始初始化逻辑提取到函数中，避免代码重复
            function initModalUI() {
                // 初始化标签切换
                var tabBtns = document.querySelectorAll('.developer-tab-btn');
                var tabContents = document.querySelectorAll('.developer-tab-content');
                var tabIndicator = document.querySelector('.tab-indicator');
                
                if (tabBtns.length && tabContents.length && tabIndicator) {
                    // 确保正确的标签被激活
                    var activeTab = document.querySelector('.developer-tab-btn.active');
                    var activeContent = document.querySelector('.developer-tab-content.active');
                    
                    if (!activeTab) {
                        tabBtns[0].classList.add('active');
                        activeTab = tabBtns[0];
                    }
                    
                    if (!activeContent) {
                        tabContents[0].classList.add('active');
                    }
                    
                    // 设置标签指示器
                    var tabWidth = activeTab.offsetWidth;
                    var tabIndex = Array.from(activeTab.parentNode.children).indexOf(activeTab);
                    var tabLeft = tabIndex * tabWidth;
                    
                    tabIndicator.style.width = tabWidth + 'px';
                    tabIndicator.style.left = tabLeft + 'px';
                    
                    // 绑定标签切换事件
                    tabBtns.forEach(function(btn) {
                        btn.onclick = function() {
                            // 移除所有活动状态
                            tabBtns.forEach(function(b) { b.classList.remove('active'); });
                            tabContents.forEach(function(c) { c.classList.remove('active'); });
                            
                            // 添加当前活动状态
                            this.classList.add('active');
                            var tabName = this.getAttribute('data-tab');
                            var targetContent = document.getElementById(tabName + '-tab');
                            if (targetContent) {
                                targetContent.classList.add('active');
                            }
                            
                            // 更新标签指示器
                            var width = this.offsetWidth;
                            var index = Array.from(this.parentNode.children).indexOf(this);
                            var left = index * width;
                            
                            tabIndicator.style.width = width + 'px';
                            tabIndicator.style.left = left + 'px';
                        };
                    });
                }
                
                // 初始化密码显示/隐藏切换
                var toggleBtns = document.querySelectorAll('.toggle-password-btn');
                if (toggleBtns.length) {
                    toggleBtns.forEach(function(btn) {
                        btn.onclick = function() {
                            var input = this.parentNode.querySelector('input');
                            var icon = this.querySelector('i');
                            
                            if (input.type === 'password') {
                                input.type = 'text';
                                icon.classList.remove('fa-eye');
                                icon.classList.add('fa-eye-slash');
                            } else {
                                input.type = 'password';
                                icon.classList.remove('fa-eye-slash');
                                icon.classList.add('fa-eye');
                            }
                        };
                    });
                }
                
                // 初始化表单提交事件
                var loginForm = document.getElementById('loginForm');
                var registerForm = document.getElementById('registerForm');
                
                if (loginForm) {
                    loginForm.onsubmit = function(e) {
                        e.preventDefault();
                        var email = document.getElementById('loginEmail').value;
                        var password = document.getElementById('loginPassword').value;
                        
                        // 检查空值
                        if (!email.trim()) {
                            showToast('请输入邮箱', 'error');
                            return false;
                        }
                        
                        if (!password.trim()) {
                            showToast('请输入密码', 'error');
                            return false;
                        }
                        
                        // 验证邮箱格式
                        if(!validateEmail(email)) {
                            showToast('邮箱格式不正确', 'error');
                            return false;
                        }
                        
                        // 验证密码长度
                        if(password.length < 8) {
                            showToast('密码长度不能少于8位', 'error');
                            return false;
                        }

                        // 发送登录请求
                        loginUser(email, password);
                        return false;
                    };
                }
                
                if (registerForm) {
                    registerForm.onsubmit = function(e) {
                        e.preventDefault();
                        var email = document.getElementById('registerEmail').value;
                        var qq = document.getElementById('registerQQ').value;
                        var password = document.getElementById('registerPassword').value;
                        var passwordConfirm = document.getElementById('registerPasswordConfirm').value;
                        var agreeTerms = document.getElementById('agreeTerms').checked;
                        
                        // 检查空值
                        if (!email.trim()) {
                            showToast('请输入邮箱', 'error');
                            return false;
                        }
                        
                        if (!qq.trim()) {
                            showToast('请输入QQ号', 'error');
                            return false;
                        }
                        
                        if (!password.trim()) {
                            showToast('请设置密码', 'error');
                            return false;
                        }
                        
                        if (!passwordConfirm.trim()) {
                            showToast('请确认密码', 'error');
                            return false;
                        }
                        
                        if (!agreeTerms) {
                            showToast('请同意服务条款和隐私政策', 'error');
                            return false;
                        }
                        
                        // 验证邮箱格式
                        if(!validateEmail(email)) {
                            showToast('邮箱格式不正确', 'error');
                            return false;
                        }
                        
                        // 验证QQ号格式
                        if(!validateQQ(qq)) {
                            showToast('QQ号格式不正确，必须为5-12位纯数字', 'error');
                            return false;
                        }
                        
                        // 验证密码长度
                        if(password.length < 8) {
                            showToast('密码长度不能少于8位', 'error');
                            return false;
                        }
                        
                        // 验证密码复杂度
                        if(!validatePasswordComplexity(password)) {
                            showToast('密码必须包含数字、字母和特殊字符中的至少两种', 'error');
                            return false;
                        }
                        
                        // 验证两次密码是否一致
                        if (password !== passwordConfirm) {
                            showToast('两次输入的密码不一致', 'error');
                            return false;
                        }
                        
                        // 发送注册请求
                        registerUser(email, qq, password);
                        return false;
                    };
                }
                
                // 初始化关闭按钮和背景点击事件
                var closeBtn = document.querySelector('.developer-modal-close-btn');
                if (closeBtn) {
                    closeBtn.onclick = function() {
                        var modalElem = document.getElementById('developerModal');
                        if (modalElem) modalElem.style.display = "none";
                    };
                }
                
                modal.onclick = function(e) {
                    if (e.target === modal) {
                        modal.style.display = "none";
                    }
                };
            }
        } else {
        }
    };
    
    // 初始化二维码上传功能
    function initQrCodeUpload() {
        const qrCodeUpload = document.getElementById('qrCodeUpload');
        const qrCodePreview = document.getElementById('qrCodePreview');
        const deleteQrCode = document.getElementById('deleteQrCode');

        if (!qrCodeUpload || !qrCodePreview || !deleteQrCode) return;

        // 优先显示后端二维码
        loadUserQrCode();

        // 监听文件上传
        qrCodeUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // 验证文件类型
            if (!file.type.match('image.*')) {
                showToast('请上传图片文件', 'error');
                return;
            }
            // 验证文件大小（2MB）
            if (file.size > 2 * 1024 * 1024) {
                showToast('图片大小不能超过2MB', 'error');
                return;
            }

            // 上传到后端
            const formData = new FormData();
            formData.append('file', file);
            showLoading();
            fetch(window.BASE_URL + '/api/upload_qrcode', {
                method: 'POST',
                headers: {
                    'Token': sessionStorage.getItem('access_token') || ''
                },
                body: formData,
                mode: 'cors'
            })
            .then(res => res.json())
            .then(data => {
                hideLoading();
                if (data.code === 200 && data.url) {
                    // 显示二维码
                    displayQrCode(data.url);
                    // 存储url到本地
                    localStorage.setItem('withdrawQrCodeUrl', data.url);
                    showToast('二维码上传成功', 'success');
                } else {
                    showToast(data.msg || '上传失败', 'error');
                }
            })
            .catch(err => {
                hideLoading();
                showToast('上传失败: ' + err.message, 'error');
            });
        });

        // 删除二维码
        deleteQrCode.addEventListener('click', function() {
            showConfirmDialog('删除', 'qrcode', '提现二维码');
            window.confirmAction = function() {
                localStorage.removeItem('withdrawQrCodeUrl');
                resetQrCodePreview();
                showToast('二维码已删除', 'success');
            };
        });
    }

    // 显示二维码（url）
    function displayQrCode(imageUrl) {
        const qrCodePreview = document.getElementById('qrCodePreview');
        const deleteQrCode = document.getElementById('deleteQrCode');
        if (!qrCodePreview || !deleteQrCode) return;
        qrCodePreview.innerHTML = `<img src="${imageUrl}" alt="提现二维码">`;
        deleteQrCode.style.display = 'flex';
    }

    // 重置二维码预览
    function resetQrCodePreview() {
        const qrCodePreview = document.getElementById('qrCodePreview');
        const deleteQrCode = document.getElementById('deleteQrCode');
        if (!qrCodePreview || !deleteQrCode) return;
        qrCodePreview.innerHTML = `
            <div class="qr-code-placeholder">
                <i class="fas fa-qrcode"></i>
                <p>暂无二维码</p>
            </div>
        `;
        deleteQrCode.style.display = 'none';
    }

    // 加载用户二维码（优先后端）
    function loadUserQrCode() {
        // 先查本地url
        const localUrl = localStorage.getItem('withdrawQrCodeUrl');
        if (localUrl) {
            displayQrCode(localUrl);
            return;
        }
        // 再查用户信息
        let userInfo = window.authData && window.authData.userInfo;
        if (userInfo && userInfo.qrcode) {
            displayQrCode(userInfo.qrcode);
            localStorage.setItem('withdrawQrCodeUrl', userInfo.qrcode);
        } else {
            // 可选：尝试请求后端最新用户信息
            const token = sessionStorage.getItem('access_token');
            if (token) {
                fetch(window.BASE_URL + '/api/user_info', {
                    method: 'GET',
                    headers: { 'Token': token }
                })
                .then(res => res.json())
                .then(data => {
                    if (data.code === 200 && data.data && data.data.qrcode) {
                        displayQrCode(data.data.qrcode);
                        localStorage.setItem('withdrawQrCodeUrl', data.data.qrcode);
                    } else {
                        resetQrCodePreview();
                    }
                })
                .catch(() => resetQrCodePreview());
            } else {
                resetQrCodePreview();
            }
        }
    }
    
    // 在账号后台模态框初始化时调用
    function initAccountDashboardModal() {
        // ... existing code ...
        
        // 初始化二维码上传功能
        initQrCodeUpload();
        
        // ... existing code ...
    }

// 在模块和所有其他代码之前定义全局函数
    // 这些函数将立即在全局作用域中可用
    window.showDeveloperModal = function() {
        var modal = document.getElementById('developerModal');
        if (!modal) {
            alert("错误：未找到模态框元素");
            return;
        }
        
        modal.style.display = "flex";
        
        // 初始化标签页和表单
        if (window.initModalTabs) initModalTabs();
        if (window.initModalForms) initModalForms();
    };
    
    window.hideDeveloperModal = function() {
        var modal = document.getElementById('developerModal');
        if (modal) {
            modal.style.display = "none";
        }
    };
    
    // 验证邮箱格式
    function validateEmail(email) {
        var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }
    
    // 验证QQ号格式
    function validateQQ(qq) {
        var re = /^\d{5,12}$/;
        return re.test(String(qq));
    }
    
    // 验证密码复杂度
    function validatePasswordComplexity(password) {
        // 检查是否包含数字
        var hasNumber = /\d/.test(password);
        // 检查是否包含字母
        var hasLetter = /[a-zA-Z]/.test(password);
        // 检查是否包含特殊字符
        var hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
        
        // 至少包含两种字符类型
        return (hasNumber && hasLetter) || (hasNumber && hasSpecial) || (hasLetter && hasSpecial);
    }
    
    // 显示密码强度
    function showPasswordStrength(password) {
        var strength = 0;
        var comment = '';
        
        // 检查长度
        if (password.length >= 8) strength += 1;
        if (password.length >= 12) strength += 1;
        
        // 检查复杂度
        if (/\d/.test(password)) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) strength += 1;
        
        // 根据强度返回评价
        if (strength < 3) {
            return { level: 'weak', comment: '弱' };
        } else if (strength < 5) {
            return { level: 'medium', comment: '中等' };
        } else {
            return { level: 'strong', comment: '强' };
        }
    }
    
    // 初始化密码强度指示器
    function initPasswordStrengthIndicator() {
        const passwordInput = document.getElementById('registerPassword');
        const confirmInput = document.getElementById('registerPasswordConfirm');
        
        if (!passwordInput) return;
        
        // 创建密码强度指示器（如果不存在）
        let strengthIndicator = document.getElementById('passwordStrengthIndicator');
        if (!strengthIndicator) {
            strengthIndicator = document.createElement('div');
            strengthIndicator.id = 'passwordStrengthIndicator';
            strengthIndicator.className = 'password-strength-indicator';
            passwordInput.parentNode.appendChild(strengthIndicator);
            
            // 添加强度条
            strengthIndicator.innerHTML = `
                <div class="strength-bars">
                    <div class="strength-bar"></div>
                    <div class="strength-bar"></div>
                    <div class="strength-bar"></div>
                </div>
                <div class="strength-text"></div>
            `;
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .password-strength-indicator {
                    margin-top: 8px;
                    font-size: 12px;
                    height: 25px;
                    transition: all 0.3s;
                }
                .strength-bars {
                    display: flex;
                    gap: 5px;
                    margin-bottom: 5px;
                }
                .strength-bar {
                    height: 4px;
                    flex: 1;
                    background-color: #e0e0e0;
                    border-radius: 2px;
                    transition: all 0.3s;
                }
                .strength-text {
                    font-size: 12px;
                    color: #777;
                }
                .password-match-indicator {
                    margin-top: 5px;
                    font-size: 12px;
                    color: #777;
                    transition: all 0.3s;
                }
                .password-match-indicator.match {
                    color: #28a745;
                }
                .password-match-indicator.mismatch {
                    color: #dc3545;
                }
                .password-weak .strength-bar:nth-child(1) {
                    background-color: #dc3545;
                }
                .password-medium .strength-bar:nth-child(1),
                .password-medium .strength-bar:nth-child(2) {
                    background-color: #ffc107;
                }
                .password-strong .strength-bar {
                    background-color: #28a745;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 创建密码匹配指示器（如果不存在）
        let matchIndicator = document.getElementById('passwordMatchIndicator');
        if (!matchIndicator && confirmInput) {
            matchIndicator = document.createElement('div');
            matchIndicator.id = 'passwordMatchIndicator';
            matchIndicator.className = 'password-match-indicator';
            confirmInput.parentNode.appendChild(matchIndicator);
        }
        
        // 监听密码输入变化
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strengthResult = showPasswordStrength(password);
            
            if (password) {
                // 更新强度条
                strengthIndicator.className = 'password-strength-indicator password-' + strengthResult.level;
                strengthIndicator.querySelector('.strength-text').textContent = '密码强度: ' + strengthResult.comment;
                
                // 如果有确认密码，检查匹配
                if (confirmInput && confirmInput.value) {
                    updatePasswordMatch(password, confirmInput.value);
                }
            } else {
                // 密码为空，重置显示
                strengthIndicator.className = 'password-strength-indicator';
                strengthIndicator.querySelector('.strength-text').textContent = '';
            }
        });
        
        // 监听确认密码输入变化
        if (confirmInput) {
            confirmInput.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirm = this.value;
                
                if (confirm) {
                    updatePasswordMatch(password, confirm);
                } else {
                    matchIndicator.textContent = '';
                    matchIndicator.className = 'password-match-indicator';
                }
            });
        }
        
        // 更新密码匹配状态
        function updatePasswordMatch(password, confirm) {
            if (!matchIndicator) return;
            
            if (password === confirm) {
                matchIndicator.textContent = '密码匹配 ✓';
                matchIndicator.className = 'password-match-indicator match';
            } else {
                matchIndicator.textContent = '密码不匹配 ✗';
                matchIndicator.className = 'password-match-indicator mismatch';
            }
        }
    }
    
    // 检查用户是否已登录
    function checkLoginStatus() {
        const token = sessionStorage.getItem('access_token');
        const refreshToken = localStorage.getItem('refresh_token');
        
        if (token) {
            // 检查令牌是否即将过期，如果是则刷新
            const timestamp = sessionStorage.getItem('timestamp');
            if (timestamp) {
                const tokenTime = parseInt(timestamp);
                const now = Math.floor(Date.now() / 1000);
                const expiresIn = tokenTime + 3600 - now; // 令牌有效期1小时
                
                if (expiresIn < 300) { // 如果令牌将在5分钟内过期，刷新它
                    refreshToken().catch(err => {
                        // 如果刷新失败，清除所有令牌
                        clearTokens();
                    });
                }
            }
            return true;
        } else if (refreshToken) {
            // 有刷新令牌但无访问令牌，尝试刷新
            refreshToken()
                .then(() => {
                })
                .catch(err => {
                    clearTokens();
                });
            return 'refreshing';
        }
        
        return false;
    }
    
    // 清除所有令牌
    function clearTokens() {
        sessionStorage.removeItem('access_token');
        sessionStorage.removeItem('timestamp');
        sessionStorage.removeItem('key');
        localStorage.removeItem('refresh_token');
    }
    
    // 用户登出
    function logoutUser() {
        // 清除所有令牌和用户数据
        clearTokens();
        localStorage.removeItem('userId');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('rememberedEmail');
        
        // 显示登出成功消息
        showToast('您已退出登录', 'info');
    }
    
    // 处理API错误响应
    function handleApiError(data) {
        if (!data || !data.msg) {
            return '未知错误，请稍后重试';
        }
        
        // 根据错误消息提供友好提示
        switch (data.msg) {
            case '该邮箱已被注册':
                return '该邮箱已被注册，请使用其他邮箱或找回密码';
                
            case '该QQ已被关联到其他账户':
                return 'QQ号已被使用，请使用其他QQ号';
                
            case '邮箱或密码错误':
                return '邮箱或密码错误，请检查后重试';
                
            case '账户已被锁定':
                return '账户已被锁定，请30分钟后再试或联系客服';
                
            case '登录尝试次数过多':
                return '尝试次数过多，请稍后再试';
                
            case '注册频率过高，请稍后再试':
                return '注册频率过高，请稍后再试';
                
            default:
                return data.msg;
        }
    }
    
    // 用户登录函数
    function loginUser(email, password) {
        showLoading();
        
        // 记住用户状态 - 用于登录表单自动填充，不保存密码
        try {
            localStorage.setItem('rememberedEmail', email);
        } catch (e) {
        }
        
        fetch(window.BASE_URL + '/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        })
        .then(response => {
            // 保存响应头中的Token和其他信息
            const token = response.headers.get('Token');
            const timestamp = response.headers.get('timestamp');
            const key = response.headers.get('key');
            
            if (token) {
                sessionStorage.setItem('access_token', token);
                sessionStorage.setItem('timestamp', timestamp);
                sessionStorage.setItem('key', key);
            }
            
            return response.json();
        })
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                // 保存刷新令牌和用户信息
                localStorage.setItem('refresh_token', data.data.refresh_token);
                localStorage.setItem('userId', data.data.id);
                localStorage.setItem('userEmail', data.data.email);
                
                showToast('登录成功', 'success');
                hideDeveloperModal();
                
                // 更新页面显示的用户信息
                updateUserInterface(data.data);
                
            } else {
                const errorMsg = handleApiError(data);
                showToast(errorMsg, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('登录请求失败: ' + error.message, 'error');
        });
    }
    
    // 用户注册函数
    function registerUser(email, qq, password) {
        showLoading();
        
        fetch(window.BASE_URL + '/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                qq: qq,
                password: password
            })
        })
        .then(response => {
            // 保存响应头中的Token和其他信息
            const token = response.headers.get('Token');
            const timestamp = response.headers.get('timestamp');
            const key = response.headers.get('key');
            
            if (token) {
                sessionStorage.setItem('access_token', token);
                sessionStorage.setItem('timestamp', timestamp);
                sessionStorage.setItem('key', key);
            }
            
            return response.json();
        })
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                // 保存刷新令牌和用户信息
                localStorage.setItem('refresh_token', data.data.refresh_token);
                localStorage.setItem('userId', data.data.id);
                localStorage.setItem('userEmail', data.data.email);
                
                showToast('注册成功', 'success');
                hideDeveloperModal();
                
                // 更新页面显示的用户信息
                updateUserInterface(data.data);
                
            } else {
                const errorMsg = handleApiError(data);
                showToast(errorMsg, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('注册请求失败: ' + error.message, 'error');
        });
    }
    
    // 刷新令牌函数
    function refreshToken() {
        return new Promise((resolve, reject) => {
            const refreshToken = localStorage.getItem('refresh_token');
            
            if (!refreshToken) {
                reject(new Error('无刷新令牌'));
                return;
            }
            
            fetch(window.BASE_URL + '/api/refresh_token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: refreshToken
                })
            })
            .then(response => {
                // 保存新的令牌
                const token = response.headers.get('Token');
                const timestamp = response.headers.get('timestamp');
                const key = response.headers.get('key');
                
                if (token) {
                    sessionStorage.setItem('access_token', token);
                    sessionStorage.setItem('timestamp', timestamp);
                    sessionStorage.setItem('key', key);
                }
                
                return response.json();
            })
            .then(data => {
                if (data.code === 200) {
                    // 保存新的刷新令牌
                    localStorage.setItem('refresh_token', data.data.refresh_token);
                    resolve(true);
                } else {
                    reject(new Error(data.msg || '刷新令牌失败'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    }
    
    // 更新用户界面
    function updateUserInterface(userData) {
        if (!userData) {
            return;
        }
        
        
        // 获取开发者按钮
        const developerBtn = document.getElementById('developerBtn');
        
        if (developerBtn) {
            // 如果用户已登录，改变按钮显示
            developerBtn.innerHTML = `
                <i class="fas fa-user-circle"></i> ${userData.email.split('@')[0]}
                <span class="btn-sparkle"></span>
            `;
            
            // 修改点击事件为显示用户菜单
            developerBtn.onclick = function() {
                showUserMenu(userData);
                return false;
            };
        } else {
            // 尝试设置观察器等待按钮加载
            setupButtonObserver();
        }
    }
    
    // 显示用户菜单
    function showUserMenu(userData) {
        // 检查是否已存在用户菜单
        let userMenu = document.getElementById('userMenu');
        
        if (!userMenu) {
            // 创建用户菜单
            userMenu = document.createElement('div');
            userMenu.id = 'userMenu';
            userMenu.className = 'user-menu';

            // 菜单样式
            const style = document.createElement('style');
            style.textContent = `
                .user-menu {
                    position: absolute;
                    top: 60px;
                    right: 20px;
                    background-color: white;
                    border-radius: 15px;
                    box-shadow: 0 5px 25px rgba(255, 126, 185, 0.25);
                    width: 200px;
                    z-index: 1000;
                    overflow: hidden;
                    border: 2px solid #ffe6f2;
                    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                    transform-origin: top right;
                    transform: scale(0.8);
                    opacity: 0;
                    visibility: hidden;
                }
                
                .user-menu.show {
                    transform: scale(1);
                    opacity: 1;
                    visibility: visible;
                }
                
                .user-menu-header {
                    padding: 15px;
                    background: linear-gradient(135deg, #ff7eb9, #ff5ca8);
                    color: white;
                    text-align: center;
                    border-bottom: 2px solid #ffe6f2;
                }
                
                /* 用户菜单中的头像样式 */
                .user-menu .user-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background-color: white;
                    margin: 0 auto 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    color: #ff7eb9;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                }
                
                .user-name {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                
                .user-email {
                    font-size: 12px;
                    opacity: 0.8;
                }
                
                .user-menu-body {
                    padding: 15px;
                }
                
                .user-balance {
                    background: linear-gradient(90deg, #ffb8d9 0%, #ff7eb9 100%);
                    padding: 14px 10px 12px 10px;
                    border-radius: 12px;
                    text-align: center;
                    margin-bottom: 15px;
                    color: #fff;
                    box-shadow: 0 2px 8px rgba(255,126,185,0.10);
                }
                
                .balance-amount {
                    font-size: 22px;
                    font-weight: bold;
                    color: #fff;
                    text-shadow: 0 2px 8px rgba(255,126,185,0.25), 0 1px 0 #c06;
                    letter-spacing: 1px;
                }
                
                .user-menu-items {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                }
                
                .user-menu-item {
                    padding: 10px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    cursor: pointer;
                    transition: all 0.2s;
                    color: #555;
                }
                
                .user-menu-item:hover {
                    background-color: #fff9fc;
                    color: #ff7eb9;
                }
                
                .user-menu-item i {
                    width: 20px;
                    text-align: center;
                }
                
                .logout-item {
                    border-top: 1px solid #f0f0f0;
                    margin-top: 10px;
                    padding-top: 10px;
                    color: #dc3545;
                }
                
                .logout-item:hover {
                    background-color: #fff5f5;
                    color: #dc3545;
                }
            `;
            document.head.appendChild(style);
            
            // 获取菜单内容
            const email = userData.email;
            const displayName = email.split('@')[0];
            const balance = userData.balance || 0;
            
            // 构建菜单HTML
            userMenu.innerHTML = `
                <div class="user-menu-header">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-name">${displayName}</div>
                    <div class="user-email">${email}</div>
                </div>
                <div class="user-menu-body">
                    <div class="user-balance">
                        余额: <span class="balance-amount">¥${balance.toFixed(2)}</span>
                    </div>
                    <div class="user-menu-items">
                        <div class="user-menu-item" id="accountDashboardBtn">
                            <i class="fas fa-user-cog"></i>
                            <span>账号后台</span>
                        </div>
                        <div class="user-menu-item logout-item" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(userMenu);
            
            // 添加账号后台按钮点击事件
            document.getElementById('accountDashboardBtn').addEventListener('click', function() {
                hideUserMenu(); // 先隐藏用户菜单
                showAccountDashboard(); // 不传递userData参数，让函数自己去获取用户信息
            });
            
            // 添加登出事件
            document.getElementById('logoutBtn').addEventListener('click', function() {
                logoutUser();
                hideUserMenu();
                
                // 恢复开发者按钮原始状态
                const developerBtn = document.getElementById('developerBtn');
                if (developerBtn) {
                    developerBtn.innerHTML = `
                        <i class="fas fa-code"></i> 开发者后台
                        <span class="btn-sparkle"></span>
                    `;
                    developerBtn.onclick = function() {
                        emergencyShowModal();
                        return false;
                    };
                }
            });
            
            // 点击页面其他地方关闭菜单
            document.addEventListener('click', function(event) {
                if (event.target.closest('#userMenu') || event.target.closest('#developerBtn')) {
                    return;
                }
                hideUserMenu();
            });
        }
        
        // 显示菜单
        setTimeout(() => {
            userMenu.classList.add('show');
        }, 10);
    }
    
    // 隐藏用户菜单
    function hideUserMenu() {
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            userMenu.classList.remove('show');
        }
    }
    
    // 加载用户信息（如果已登录）
    function loadUserInfo() {
        const token = sessionStorage.getItem('access_token');
        const userId = localStorage.getItem('userId');
        
        if (token && userId) {
            fetch(window.BASE_URL + '/api/user_info', {
                method: 'GET',
                headers: {
                    'Token': token
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 更新用户界面
                    updateUserInterface(data.data);
                } else if (data.code === -1 && data.msg === 'Token已过期') {
                    // 令牌过期，尝试刷新
                    refreshToken()
                        .then(() => {
                            // 刷新成功，重新加载用户信息
                            loadUserInfo();
                        })
                        .catch(err => {
                            // 刷新失败，清除令牌
                            clearTokens();
                        });
                }
            })
            .catch(error => {
            });
        }
    }
    
    // 在页面加载时检查是否已登录
    document.addEventListener('DOMContentLoaded', function() {
        
        // 确保开发者按钮已经正确设置
        const developerBtn = document.getElementById('developerBtn');
        if (developerBtn) {
            // 如果用户已登录但按钮仍然显示"开发者后台"，则更新它
            if ((window.tokenStatus === 'valid' || window.authData.userInfo) && developerBtn.innerText.includes('开发者后台')) {
                if (window.authData.userInfo) {
                    developerBtn.innerHTML = `
                        <i class="fas fa-user-circle"></i> ${window.authData.userInfo.email.split('@')[0]}
                        <span class="btn-sparkle"></span>
                    `;
                    
                    // 修改点击事件为显示用户菜单
                    developerBtn.onclick = function() {
                        showUserMenu(window.authData.userInfo);
                        return false;
                    };
                } else {
                    // 尝试加载用户信息
                    loadUserInfo();
                }
            }
        } else {
        }
        
        // 根据之前检测的令牌状态采取行动
        switch (window.tokenStatus) {
            case 'valid':
                // 令牌有效，直接加载用户信息
                loadUserInfo();
                break;
                
            case 'expiring':
                // 令牌即将过期，刷新令牌
                refreshToken()
                    .then(() => {
                        loadUserInfo();
                    })
                    .catch(err => {
                        // 如果刷新失败但仍有用户信息，尝试显示
                        if (window.authData.userInfo) {
                            updateUserInterface(window.authData.userInfo);
                        }
                    });
                break;
                
            case 'expired':
            case 'refresh_only':
                // 令牌已过期或只有刷新令牌，尝试刷新
                refreshToken()
                    .then(() => {
                        loadUserInfo();
                    })
                    .catch(err => {
                        clearTokens();
                        // 不自动显示登录框，等用户点击登录按钮
                    });
                break;
                
            case 'no_token':
            default:
                // 无令牌，用户未登录，不做任何操作
                break;
        }
        
        // 初始化密码强度指示器
        initPasswordStrengthIndicator();
        
        // 自动填充上次登录的邮箱（如果有）
        const rememberedEmail = localStorage.getItem('rememberedEmail');
        const loginEmailInput = document.getElementById('loginEmail');
        
        if (rememberedEmail && loginEmailInput) {
            loginEmailInput.value = rememberedEmail;
        }
    });

    // 全局模拟数据
    var mockPlugins = [
        {
            id: "1",
            name: "1111",
            author: "11111",
            description: "简介",
            icon: null, // 使用默认图标
            installed: true,
            updateAvailable: true,
            price: 0, // 免费
            priceText: "免费"
        },
        {
            id: "2",
            name: "222",
            author: "222",
            description: "简介",
            icon: null, // 使用默认图标
            installed: true,
            updateAvailable: false,
            price: 29.9, // 付费
            priceText: "¥29.9"
        },
        {
            id: "3",
            name: "333",
            author: "333",
            description: "简介",
            icon: null, // 使用默认图标
            installed: false,
            updateAvailable: false,
            price: 39.9, // 付费
            priceText: "¥39.9"
        },
        {
            id: "4",
            name: "444",
            author: "444",
            description: "简介",
            icon: null, // 使用默认图标
            installed: false,
            updateAvailable: false,
            price: 99.9, // 付费
            priceText: "¥99.9"
        },
        {
            id: "5",
            name: "555",
            author: "555",
            description: "简介",
            icon: null, // 使用默认图标
            installed: true,
            updateAvailable: false,
            price: 0, // 免费
            priceText: "免费"
        },
        {
            id: "6",
            name: "666",
            author: "666",
            description: "简介",
            icon: null, // 使用默认图标
            installed: false,
            updateAvailable: false,
            price: 0, // 免费
            priceText: "免费"
        }
    ];

    // API基础URL - 使用window属性而非const声明，避免重复声明错误
    window.BASE_URL = window.BASE_URL || "http://127.0.0.1:8070";

    // 立即执行插件渲染 - 确保页面加载时执行一次
    (function() {
        renderPluginData();
    })();

    // DOM加载完成后执行初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 加载插件数据
        if (document.querySelector('.plugin-grid').children.length === 0) {
            renderPluginData();
        }
        
        // 初始化确认对话框
        initConfirmDialog();
    });

    // 页面完全加载后的最终检查
    window.onload = function() {
        // 再检查一次是否需要渲染插件数据
        if (document.querySelector('.plugin-grid').children.length === 0) {
            renderPluginData();
        }
    };

    // 核心渲染函数 - 负责将插件数据显示到页面上
    function renderPluginData() {
        try {
            const pluginGrid = document.querySelector('.plugin-grid');
            const emptyState = document.querySelector('.empty-state');
            
            if (!pluginGrid) {
                return;
            }
            
            // 清空现有内容
            pluginGrid.innerHTML = '';
            
            // 检查是否有数据
            if (!mockPlugins || mockPlugins.length === 0) {
                if (emptyState) emptyState.style.display = 'block';
                return;
            }
            
            // 隐藏空状态
            if (emptyState) emptyState.style.display = 'none';
            
            
            // 渲染插件卡片
            mockPlugins.forEach(function(plugin) {
                try {
                    const card = createPluginCard(plugin);
                    pluginGrid.appendChild(card);
                } catch(err) {
                }
            });
            
            // 绑定事件
            try {
                bindPluginEvents();
            } catch(err) {
            }
            
        } catch (err) {
        }
    }

    // 创建单个插件卡片
    function createPluginCard(plugin) {
        if (!plugin) return document.createElement('div');
        
        const card = document.createElement('div');
        card.className = 'plugin-card';
        card.setAttribute('data-id', plugin.id);
        
        // 准备图标HTML
        let iconHtml = '';
        if (plugin.icon) {
            iconHtml = `<img src="${plugin.icon}" alt="${plugin.name}">`;
        } else {
            // 使用Font Awesome图标，根据插件类型选择不同图标
            let iconClass = 'puzzle-piece'; // 默认图标
            
            if (plugin.price > 0) {
                iconClass = 'coins'; // 付费插件使用coins图标
            } else if (plugin.updateAvailable) {
                iconClass = 'sync-alt'; // 可更新插件使用sync-alt图标
            }
            
            iconHtml = `<i class="fas fa-${iconClass}"></i>`;
        }
        
        // 准备按钮HTML
        let buttonsHtml = '';
        
        if (plugin.installed) {
            if (plugin.disabled) {
                // 已安装但禁用的插件显示启动按钮
                buttonsHtml = `
                    <button class="plugin-btn btn-download">
                        <i class="fas fa-play"></i> 启动
                    </button>
                `;
            } else {
                // 已安装且启用的插件显示停用、更新和设置按钮
                buttonsHtml = `
                    <button class="plugin-btn btn-disable">
                        <i class="fas fa-power-off"></i> 停用
                    </button>
                `;
                
                if (plugin.updateAvailable) {
                    buttonsHtml += `
                        <button class="plugin-btn btn-update">
                            <i class="fas fa-sync-alt"></i> 更新
                        </button>
                    `;
                }
                
                buttonsHtml += `
                    <button class="plugin-btn btn-settings">
                        <i class="fas fa-cog"></i> 设置
                    </button>
                `;
            }
        } else {
            // 未安装的插件显示下载按钮
            buttonsHtml = `
                <button class="plugin-btn btn-download">
                    <i class="fas fa-download"></i> 下载
                </button>
            `;
        }
        
        // 准备状态标签HTML
        let statusHtml = '';
        if (plugin.installed) {
            if (plugin.disabled) {
                statusHtml = '<span class="plugin-status disabled">已禁用</span>';
            } else {
                statusHtml = '<span class="plugin-status installed">已安装</span>';
            }
        }
        
        // 准备价格标签HTML
        let priceTagHtml = '';
        if (plugin.price === 0) {
            priceTagHtml = `<span class="plugin-price free">免费</span>`;
        } else {
            priceTagHtml = `<span class="plugin-price paid">${plugin.priceText}</span>`;
        }
        
        // 构建卡片HTML
        card.innerHTML = `
            <div class="plugin-left">
                <div class="plugin-icon">
                    ${iconHtml}
                </div>
                ${statusHtml}
            </div>
            <div class="plugin-content">
                <div class="plugin-header">
                    <div class="plugin-title">
                        <h3 class="plugin-name">${plugin.name}</h3>
                        <div class="plugin-author">作者: ${plugin.author}</div>
                    </div>
                    <div class="plugin-badges">
                        ${plugin.updateAvailable ? '<span class="plugin-badge">可更新</span>' : ''}
                        ${priceTagHtml}
                    </div>
                </div>
                <div class="plugin-description">
                    ${plugin.description}
                </div>
                <div class="plugin-actions">
                    ${buttonsHtml}
                </div>
            </div>
        `;
        
        return card;
    }

    // 绑定插件卡片上的所有事件
    function bindPluginEvents() {
        // 搜索框事件
        const searchInput = document.getElementById('pluginSearch');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput && searchBtn) {
            // 清除所有旧事件
            const newSearchInput = searchInput.cloneNode(true);
            searchInput.replaceWith(newSearchInput);
            
            const newSearchBtn = searchBtn.cloneNode(true);
            searchBtn.replaceWith(newSearchBtn);
            
            // 重新获取元素
            const updatedSearchInput = document.getElementById('pluginSearch');
            const updatedSearchBtn = document.getElementById('searchBtn');
            
            // 添加新的事件处理程序
            updatedSearchBtn.addEventListener('click', handleSearch);
            updatedSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') handleSearch();
            });
        }
        
        // 筛选标签事件 - 使用事件委托来避免重复绑定
        const filterTagsContainer = document.querySelector('.filter-tags');
        if (filterTagsContainer) {
            // 清除现有事件
            const newFilterTagsContainer = filterTagsContainer.cloneNode(true);
            filterTagsContainer.replaceWith(newFilterTagsContainer);
            
            // 获取新元素
            const updatedFilterTagsContainer = document.querySelector('.filter-tags');
            
            // 使用事件委托绑定点击事件
            updatedFilterTagsContainer.addEventListener('click', function(e) {
                const filterTag = e.target.closest('.filter-tag');
                if (filterTag) {
                    // 移除所有标签的激活状态
                    document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                    // 激活当前标签
                    filterTag.classList.add('active');
                    // 获取并应用筛选
                    const filterType = filterTag.getAttribute('data-filter');
                    applyFilter(filterType);
                }
            });
        }
        
        // 使用事件委托处理所有插件卡片按钮的点击
        const pluginGrid = document.querySelector('.plugin-grid');
        if (pluginGrid) {
            // 移除旧事件
            const newPluginGrid = pluginGrid.cloneNode(true);
            pluginGrid.replaceWith(newPluginGrid);
            
            // 获取更新后的网格
            const updatedPluginGrid = document.querySelector('.plugin-grid');
            
            // 使用事件委托绑定所有按钮点击
            updatedPluginGrid.addEventListener('click', function(e) {
                // 下载或启动按钮
                if (e.target.closest('.btn-download')) {
                e.stopPropagation();
                    const button = e.target.closest('.btn-download');
                    const card = button.closest('.plugin-card');
                const pluginId = card.getAttribute('data-id');
                
                    // 根据按钮文本判断操作类型
                    if (button.textContent.trim().includes('启动')) {
                    enablePlugin(pluginId);
                } else {
                    installPlugin(pluginId);
                }
                }
                
                // 停用按钮
                else if (e.target.closest('.btn-disable')) {
                e.stopPropagation();
                    const button = e.target.closest('.btn-disable');
                    const card = button.closest('.plugin-card');
                const pluginId = card.getAttribute('data-id');
                const pluginName = card.querySelector('.plugin-name').textContent;
                showConfirmDialog('停用', pluginId, pluginName);
                }
                
                // 更新按钮
                else if (e.target.closest('.btn-update')) {
                e.stopPropagation();
                    const button = e.target.closest('.btn-update');
                    const card = button.closest('.plugin-card');
                    const pluginId = card.getAttribute('data-id');
                updatePlugin(pluginId);
                }
                
                // 设置按钮
                else if (e.target.closest('.btn-settings')) {
                e.stopPropagation();
                    const button = e.target.closest('.btn-settings');
                    const card = button.closest('.plugin-card');
                    const pluginId = card.getAttribute('data-id');
                openPluginSettings(pluginId);
                }
            });
        }
        
        // 初始化确认对话框
        initConfirmDialog();
    }

    // 搜索处理函数
    function handleSearch() {
        const keyword = document.getElementById('pluginSearch').value.trim();
        if (!keyword) {
            renderPluginData(); // 如果搜索为空，显示所有插件
            return;
        }
        
        // 显示加载中
        showLoading();
        
        // 模拟延迟，真实情况下可能是API请求
        setTimeout(function() {
            hideLoading();
            
            // 搜索逻辑
            const results = mockPlugins.filter(plugin => 
                plugin.name.toLowerCase().includes(keyword.toLowerCase()) || 
                plugin.description.toLowerCase().includes(keyword.toLowerCase()) ||
                plugin.author.toLowerCase().includes(keyword.toLowerCase())
            );
            
            // 显示结果
            renderSearchResults(results, keyword);
        }, 300);
    }

    // 渲染搜索结果
    function renderSearchResults(results, keyword) {
        const pluginGrid = document.querySelector('.plugin-grid');
        const emptyState = document.querySelector('.empty-state');
        
        if (!pluginGrid) return;
        
        // 清空网格
        pluginGrid.innerHTML = '';
        
        // 如果没有结果
        if (results.length === 0) {
            if (emptyState) {
                emptyState.style.display = 'block';
                const emptyText = emptyState.querySelector('.empty-state-text');
                if (emptyText) {
                    emptyText.textContent = `未找到匹配"${keyword}"的插件`;
                }
            }
            return;
        }
        
        // 隐藏空状态
        if (emptyState) emptyState.style.display = 'none';
        
        // 渲染结果
        results.forEach(function(plugin) {
            const card = createPluginCard(plugin);
            pluginGrid.appendChild(card);
        });
        
        // 重新绑定事件
        bindPluginEvents();
        
        // 显示消息
        showToast(`找到 ${results.length} 个匹配"${keyword}"的插件`, 'success');
    }

    // 应用筛选
    function applyFilter(filterType) {
        showLoading();
        
        setTimeout(function() {
            hideLoading();
            
            let filteredPlugins = [];
            
            // 根据筛选类型过滤插件
            if (filterType === 'all') {
                filteredPlugins = mockPlugins;
            } else if (filterType === 'installed') {
                filteredPlugins = mockPlugins.filter(plugin => plugin.installed);
            } else if (filterType === 'notInstalled') {
                filteredPlugins = mockPlugins.filter(plugin => !plugin.installed);
            } else if (filterType === 'free') {
                filteredPlugins = mockPlugins.filter(plugin => plugin.price === 0);
            } else if (filterType === 'paid') {
                filteredPlugins = mockPlugins.filter(plugin => plugin.price > 0);
            }
            
            // 显示筛选结果
            renderFilterResults(filteredPlugins, filterType);
        }, 300);
    }

    // 渲染筛选结果
    function renderFilterResults(results, filterType) {
        const pluginGrid = document.querySelector('.plugin-grid');
        const emptyState = document.querySelector('.empty-state');
        
        if (!pluginGrid) return;
        
        // 清空网格
        pluginGrid.innerHTML = '';
        
        // 如果没有结果
        if (results.length === 0) {
            if (emptyState) {
                emptyState.style.display = 'block';
                const emptyText = emptyState.querySelector('.empty-state-text');
                if (emptyText) {
                    if (filterType === 'installed') {
                        emptyText.textContent = '暂无已安装的插件';
                    } else if (filterType === 'notInstalled') {
                        emptyText.textContent = '暂无可安装的插件';
                    } else if (filterType === 'free') {
                        emptyText.textContent = '暂无免费插件';
                    } else if (filterType === 'paid') {
                        emptyText.textContent = '暂无付费插件';
                    } else {
                        emptyText.textContent = '插件市场为空';
                    }
                }
            }
            return;
        }
        
        // 隐藏空状态
        if (emptyState) emptyState.style.display = 'none';
        
        // 渲染结果
        results.forEach(function(plugin) {
            const card = createPluginCard(plugin);
            pluginGrid.appendChild(card);
        });
        
        // 重新绑定事件
        bindPluginEvents();
        
        // 显示消息
        if (filterType !== 'all') {
            let filterName = '';
            if (filterType === 'installed') {
                filterName = '已安装';
            } else if (filterType === 'notInstalled') {
                filterName = '未安装';
            } else if (filterType === 'free') {
                filterName = '免费';
            } else if (filterType === 'paid') {
                filterName = '付费';
            }
            showToast(`已筛选 ${results.length} 个${filterName}插件`, 'success');
        }
    }

    // 安装插件
    function installPlugin(pluginId) {
        showLoading();
        
        // 模拟延迟
        setTimeout(() => {
            hideLoading();
            
            // 在模拟数据中找到对应的插件
            const plugin = mockPlugins.find(p => p.id === pluginId);
            
            if (plugin) {
                // 更新插件状态
                plugin.installed = true;
                
                // 重新渲染页面
                renderPluginData();
                
                // 显示成功消息
                showToast(`插件"${plugin.name}"安装成功`, 'success');
            }
        }, 800);
    }
    
    // 停用插件
    function disablePlugin(pluginId) {
        showLoading();
        
        // 模拟延迟
        setTimeout(() => {
            hideLoading();
            
            // 在模拟数据中找到对应的插件
            const plugin = mockPlugins.find(p => p.id === pluginId);
            
            if (plugin) {
                plugin.disabled = true;
                plugin.updateAvailable = false;
                
                // 重新渲染页面
                renderPluginData();
                
                // 显示成功消息
                showToast(`插件"${plugin.name}"已停用`, 'success');
            }
        }, 800);
    }
    
    // 更新插件
    function updatePlugin(pluginId) {
        showLoading();

        setTimeout(() => {
            hideLoading();

            const plugin = mockPlugins.find(p => p.id === pluginId);
            
            if (plugin) {
                // 更新插件状态
                plugin.updateAvailable = false;
                
                // 重新渲染页面
                renderPluginData();
                
                // 显示成功消息
                showToast(`插件"${plugin.name}"更新成功`, 'success');
            }
        }, 800);
    }
    
    // 打开插件设置
    function openPluginSettings(pluginId) {
        // 在模拟数据中找到对应的插件
        const plugin = mockPlugins.find(p => p.id === pluginId);
        
        if (plugin) {
            showToast(`正在打开"${plugin.name}"的设置页面`, 'info');
        }
    }
    
    // 显示确认对话框
    function showConfirmDialog(action, pluginId, pluginName) {
        const confirmDialog = document.getElementById('confirmDialog');
        const confirmMessage = document.getElementById('confirmMessage');
        
        if (!confirmDialog || !confirmMessage) return;

        // 设置确认消息
        confirmMessage.textContent = `您确定要${action}插件"${pluginName}"吗？`;
        
        // 设置确认回调
        window.confirmAction = function() {
            if (action === '停用') {
                disablePlugin(pluginId);
            }
        };
        
        // 显示对话框
        confirmDialog.classList.add('show');
    }
    
    // 隐藏确认对话框
    function hideConfirmDialog() {
        const confirmDialog = document.getElementById('confirmDialog');
        if (confirmDialog) {
            confirmDialog.classList.remove('show');
        }
    }
    
    // 初始化确认对话框
    function initConfirmDialog() {
        const confirmDialog = document.getElementById('confirmDialog');
        if (!confirmDialog) return;
        
        // 获取按钮元素
        const confirmActionBtn = document.getElementById('confirmActionBtn');
        const cancelActionBtn = document.getElementById('cancelActionBtn');
        const closeBtn = document.querySelector('.confirm-close-btn');
        
        // 背景点击事件 - 关闭对话框
        confirmDialog.addEventListener('click', function(e) {
            if (e.target === confirmDialog) {
                hideConfirmDialog();
            }
        });
        
        // 确认按钮事件
        if (confirmActionBtn) {
            confirmActionBtn.addEventListener('click', function() {
                // confirmAction会在showConfirmDialog中设置
                if (window.confirmAction && typeof window.confirmAction === 'function') {
                    window.confirmAction();
                }
                hideConfirmDialog();
            });
        }
        
        // 取消按钮事件
        if (cancelActionBtn) {
            cancelActionBtn.addEventListener('click', function() {
                hideConfirmDialog();
            });
        }
        
        // 关闭按钮事件
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                hideConfirmDialog();
            });
        }
    }
    
    // 显示加载状态
    function showLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        
        if (loadingIndicator) {
            loadingIndicator.style.display = 'block';
            // 确保元素可见
            loadingIndicator.style.opacity = '1';
            loadingIndicator.style.zIndex = '10000';
        } else {
            // 如果找不到元素，创建一个
            const newIndicator = document.createElement('div');
            newIndicator.id = 'loading-indicator';
            newIndicator.className = 'loading-indicator';
            newIndicator.innerHTML = `
                <div class="spinner"></div>
                <div class="loading-text">加载中...</div>
            `;
            newIndicator.style.display = 'block';
            newIndicator.style.position = 'fixed';
            newIndicator.style.top = '0';
            newIndicator.style.left = '0';
            newIndicator.style.width = '100%';
            newIndicator.style.height = '100%';
            newIndicator.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
            newIndicator.style.zIndex = '10000';
            newIndicator.style.display = 'flex';
            newIndicator.style.justifyContent = 'center';
            newIndicator.style.alignItems = 'center';
            document.body.appendChild(newIndicator);
        }
    }
    
    // 隐藏加载状态
    function hideLoading() {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        } else {
        }
    }
    
    // 显示通知消息
    function showToast(message, type = 'info') {
        // 检查toast容器是否存在，不存在则创建
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
        
        // 设置容器样式确保它在右上角
        toastContainer.style.position = 'fixed';
        toastContainer.style.top = '20px';
        toastContainer.style.right = '20px';
        toastContainer.style.left = 'auto';
        toastContainer.style.bottom = 'auto';
        toastContainer.style.zIndex = '99999';
        toastContainer.style.display = 'flex';
        toastContainer.style.flexDirection = 'column';
        toastContainer.style.alignItems = 'flex-end';
        
        // 防止显示重复通知
        const existingToasts = toastContainer.querySelectorAll('.toast-content');
        for (let i = 0; i < existingToasts.length; i++) {
            if (existingToasts[i].textContent === message) {
                return; // 如果发现相同内容的通知，则不再显示
            }
        }
        
        // 创建通知元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // 设置图标
        let icon = '';
        if (type === 'success') {
            icon = 'fa-check-circle';
        } else if (type === 'error') {
            icon = 'fa-exclamation-circle';
        } else if (type === 'warning') {
            icon = 'fa-exclamation-triangle';
        } else {
            icon = 'fa-info-circle';
        }
        
        // 构建通知内容
        toast.innerHTML = `
            <div class="toast-icon"><i class="fas ${icon}"></i></div>
            <div class="toast-content">${message}</div>
            <div class="toast-close"><i class="fas fa-times"></i></div>
        `;
        
        // 添加到容器
        toastContainer.appendChild(toast);

        toast.style.transform = 'translateX(120%)';
        toast.style.opacity = '0';
        toast.offsetHeight;

        requestAnimationFrame(() => {
            toast.style.transition = 'all 0.3s ease';
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        });
        
        const closeBtn = toast.querySelector('.toast-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                closeToast(toast);
            });
        }
        
        setTimeout(() => {
            closeToast(toast);
        }, 3000);
    }

    // 关闭通知的辅助函数
    function closeToast(toast) {
        if (!toast || !toast.parentNode) return;
        

        toast.style.transition = 'all 0.3s ease';
        toast.style.transform = 'translateX(120%)';
        toast.style.opacity = '0';

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    // 启动插件
    function enablePlugin(pluginId) {
        showLoading();
        setTimeout(() => {
            hideLoading();

            const plugin = mockPlugins.find(p => p.id === pluginId);
            
            if (plugin) {
                plugin.disabled = false;
                renderPluginData();
                showToast(`插件"${plugin.name}"已启动`, 'success');
            }
        }, 800);
    }

    // 全局令牌和用户信息对象
    if (!window.authData) {
        window.authData = {
            token: null,
            refreshToken: null,
            timestamp: null,
            key: null,
            userId: null,
            userEmail: null,
            userInfo: null
        };
    }

    // 保存令牌到全局和本地存储
    function saveTokens(token, refreshToken, timestamp, key) {
        // 保存到全局对象
        window.authData.token = token;
        window.authData.refreshToken = refreshToken;
        window.authData.timestamp = timestamp;
        window.authData.key = key;
        
        // 同时保存到存储中作为备份
        if (token) sessionStorage.setItem('access_token', token);
        if (refreshToken) localStorage.setItem('refresh_token', refreshToken);
        if (timestamp) sessionStorage.setItem('timestamp', timestamp);
        if (key) sessionStorage.setItem('key', key);
        
        // 如果在主框架中，更新框架的用户ID
        if (window.parent && window.parent !== window) {
            try {
                window.parent.currentUserId = window.authData.userId;
            } catch (e) {
            }
        } else if (window.currentUserId != window.authData.userId) {
            window.currentUserId = window.authData.userId;
        }
    }

    // 保存用户信息到全局和本地存储
    function saveUserInfo(userData) {
        if (!userData) return;
        
        // 保存用户ID和邮箱到全局
        window.authData.userId = userData.id;
        window.authData.userEmail = userData.email;
        window.authData.userInfo = userData;
        
        // 同时保存到本地存储
        localStorage.setItem('userId', userData.id);
        localStorage.setItem('userEmail', userData.email);
        
        // 如果支持，保存完整用户信息
        try {
            localStorage.setItem('userInfo', JSON.stringify(userData));
        } catch (e) {
        }
    }

    // 清除所有令牌
    function clearTokens() {
        // 清除全局对象中的数据
        window.authData.token = null;
        window.authData.refreshToken = null;
        window.authData.timestamp = null;
        window.authData.key = null;
        window.authData.userId = null;
        window.authData.userEmail = null;
        window.authData.userInfo = null;
        
        // 清除存储
        sessionStorage.removeItem('access_token');
        sessionStorage.removeItem('timestamp');
        sessionStorage.removeItem('key');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('userId');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userInfo');
        
        // 清除window.currentUserId
        window.currentUserId = null;
    }

    // 用户登出
    function logoutUser() {
        // 清除所有令牌和用户数据
        clearTokens();
        localStorage.removeItem('rememberedEmail');
        
        // 显示登出成功消息
        showToast('您已退出登录', 'info');
        
        // 如果在框架中，通知框架更新状态
        try {
            if (window.parent && window.parent !== window && window.parent.checkLoginStatus) {
                window.parent.checkLoginStatus();
            }
        } catch (e) {
        }
    }

    // 从本地存储和全局对象恢复令牌
    function restoreTokens() {
        // 先检查全局对象
        let token = window.authData.token;
        let refreshToken = window.authData.refreshToken;
        let timestamp = window.authData.timestamp;
        let key = window.authData.key;
        
        // 如果全局对象中没有，则尝试从存储中恢复
        if (!token) token = sessionStorage.getItem('access_token');
        if (!refreshToken) refreshToken = localStorage.getItem('refresh_token');
        if (!timestamp) timestamp = sessionStorage.getItem('timestamp');
        if (!key) key = sessionStorage.getItem('key');
        
        // 恢复到全局对象
        window.authData.token = token;
        window.authData.refreshToken = refreshToken;
        window.authData.timestamp = timestamp;
        window.authData.key = key;
        
        // 恢复用户信息
        if (!window.authData.userId) {
            window.authData.userId = localStorage.getItem('userId');
        }
        
        if (!window.authData.userEmail) {
            window.authData.userEmail = localStorage.getItem('userEmail');
        }
        
        // 尝试恢复完整用户信息
        if (!window.authData.userInfo) {
            try {
                const userInfoStr = localStorage.getItem('userInfo');
                if (userInfoStr) {
                    window.authData.userInfo = JSON.parse(userInfoStr);
                }
            } catch (e) {
            }
        }
        
        return {
            token: token,
            refreshToken: refreshToken,
            timestamp: timestamp,
            key: key
        };
    }

    // 检查用户是否已登录
    function checkLoginStatus() {
        // 恢复令牌数据
        const tokens = restoreTokens();
        const token = tokens.token;
        const refreshToken = tokens.refreshToken;
        
        if (token) {
            // 检查令牌是否即将过期，如果是则刷新
            const timestamp = tokens.timestamp;
            if (timestamp) {
                const tokenTime = parseInt(timestamp);
                const now = Math.floor(Date.now() / 1000);
                const expiresIn = tokenTime + 3600 - now; // 令牌有效期1小时
                
                if (expiresIn < 300) { // 如果令牌将在5分钟内过期，刷新它
                    refreshToken().catch(err => {
                        // 如果刷新失败，清除所有令牌
                        clearTokens();
                    });
                }
            }
            return true;
        } else if (refreshToken) {
            // 有刷新令牌但无访问令牌，尝试刷新
            refreshToken()
                .then(() => {
                })
                .catch(err => {
                    clearTokens();
                });
            return 'refreshing';
        }
        
        return false;
    }

    // 用户登录函数
    function loginUser(email, password) {
        showLoading();
        
        // 记住用户状态 - 用于登录表单自动填充，不保存密码
        try {
            localStorage.setItem('rememberedEmail', email);
        } catch (e) {
        }
        
        fetch(window.BASE_URL + '/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        })
        .then(response => {
            // 保存响应头中的Token和其他信息
            const token = response.headers.get('Token');
            const timestamp = response.headers.get('timestamp');
            const key = response.headers.get('key');
            
            // 暂存令牌信息
            if (token) {
                sessionStorage.setItem('access_token', token);
                sessionStorage.setItem('timestamp', timestamp);
                sessionStorage.setItem('key', key);
            }
            
            return response.json();
        })
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                // 保存刷新令牌和用户信息
                localStorage.setItem('refresh_token', data.data.refresh_token);
                localStorage.setItem('userId', data.data.id);
                localStorage.setItem('userEmail', data.data.email);
                
                showToast('登录成功', 'success');
                hideDeveloperModal();
                
                // 更新页面显示的用户信息
                updateUserInterface(data.data);
                
            } else {
                const errorMsg = handleApiError(data);
                showToast(errorMsg, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('登录请求失败: ' + error.message, 'error');
        });
    }

    // 用户注册函数
    function registerUser(email, qq, password) {
        showLoading();
        
        fetch(window.BASE_URL + '/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: email,
                qq: qq,
                password: password
            })
        })
        .then(response => {
            // 保存响应头中的Token和其他信息
            const token = response.headers.get('Token');
            const timestamp = response.headers.get('timestamp');
            const key = response.headers.get('key');
            
            // 暂存令牌信息
            if (token) {
                sessionStorage.setItem('access_token', token);
                sessionStorage.setItem('timestamp', timestamp);
                sessionStorage.setItem('key', key);
            }
            
            return response.json();
        })
        .then(data => {
            hideLoading();
            
            if (data.code === 200) {
                // 保存刷新令牌和用户信息到全局和本地存储
                const token = sessionStorage.getItem('access_token');
                const timestamp = sessionStorage.getItem('timestamp');
                const key = sessionStorage.getItem('key');
                
                saveTokens(token, data.data.refresh_token, timestamp, key);
                saveUserInfo(data.data);
                
                showToast('注册成功', 'success');
                hideDeveloperModal();
                
                // 更新页面显示的用户信息
                updateUserInterface(data.data);
                
                
                // 通知父窗口（如果在iframe中）
                if (window.parent && window.parent !== window) {
                    try {
                        if (window.parent.onUserLogin) {
                            window.parent.onUserLogin(data.data);
                        }
                    } catch (e) {
                    }
                }
            } else {
                const errorMsg = handleApiError(data);
                showToast(errorMsg, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('注册请求失败: ' + error.message, 'error');
        });
    }

    // 刷新令牌函数
    function refreshToken() {
        return new Promise((resolve, reject) => {
            // 尝试从全局和本地存储获取刷新令牌
            const refreshToken = window.authData.refreshToken || localStorage.getItem('refresh_token');
            
            if (!refreshToken) {
                reject(new Error('无刷新令牌'));
                return;
            }
            
            fetch(window.BASE_URL + '/api/refresh_token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: refreshToken
                })
            })
            .then(response => {
                // 保存新的令牌
                const token = response.headers.get('Token');
                const timestamp = response.headers.get('timestamp');
                const key = response.headers.get('key');
                
                if (token) {
                    sessionStorage.setItem('access_token', token);
                    sessionStorage.setItem('timestamp', timestamp);
                    sessionStorage.setItem('key', key);
                }
                
                return response.json();
            })
            .then(data => {
                if (data.code === 200) {
                    // 更新全局和本地存储中的令牌
                    const token = sessionStorage.getItem('access_token');
                    const timestamp = sessionStorage.getItem('timestamp');
                    const key = sessionStorage.getItem('key');
                    
                    saveTokens(token, data.data.refresh_token, timestamp, key);
                    
                    resolve(true);
                } else {
                    reject(new Error(data.msg || '刷新令牌失败'));
                }
            })
            .catch(error => {
                reject(error);
            });
        });
    }

    // 在页面加载时检查是否已登录并恢复状态
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化全局令牌对象
        restoreTokens();
        
        // 检查登录状态
        const loginStatus = checkLoginStatus();
        
        if (loginStatus === true) {
            // 已登录，加载用户信息
            loadUserInfo();
        } else if (loginStatus === 'refreshing') {
            // 正在刷新令牌，等待完成
        }
        
        // 初始化密码强度指示器
        initPasswordStrengthIndicator();
        
        // 自动填充上次登录的邮箱（如果有）
        const rememberedEmail = localStorage.getItem('rememberedEmail');
        const loginEmailInput = document.getElementById('loginEmail');
        
        if (rememberedEmail && loginEmailInput) {
            loginEmailInput.value = rememberedEmail;
        }
    });
    
    // 加载用户信息函数增强版
    function loadUserInfo() {
        // 首先检查全局对象中是否已有完整用户信息
        if (window.authData.userInfo) {
            // 直接使用全局对象中的用户信息
            updateUserInterface(window.authData.userInfo);
            return;
        }
        
        // 如果没有完整信息，但有用户ID和令牌，则从服务器获取
        const token = window.authData.token || sessionStorage.getItem('access_token');
        const userId = window.authData.userId || localStorage.getItem('userId');
        
        if (token && userId) {
            // 显示加载状态
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) loadingIndicator.style.display = 'block';
            
            fetch(window.BASE_URL + '/api/user_info', {
                method: 'GET',
                headers: {
                    'Token': token
                }
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载状态
                if (loadingIndicator) loadingIndicator.style.display = 'none';
                
                if (data.code === 200) {
                    // 保存用户信息到全局对象
                    saveUserInfo(data.data);
                    
                    // 更新用户界面
                    updateUserInterface(data.data);
                } else if (data.code === -1 && (data.msg === 'Token已过期' || data.msg === '无效的Token')) {
                    // 令牌无效，尝试刷新
                    refreshToken()
                        .then(() => {
                            // 刷新成功，重新加载用户信息
                            loadUserInfo();
                        })
                        .catch(err => {
                            // 刷新失败，清除令牌
                            clearTokens();
                        });
                }
            })
            .catch(error => {
                // 隐藏加载状态
                if (loadingIndicator) loadingIndicator.style.display = 'none';
            });
        } else if (userId) {
            // 有用户ID但无令牌，尝试使用有限信息更新界面
            const userEmail = window.authData.userEmail || localStorage.getItem('userEmail');
            if (userEmail) {
                updateUserInterface({id: userId, email: userEmail});
            }
        }
    }

    // 隐藏用户菜单
    function hideUserMenu() {
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            userMenu.classList.remove('show');
        }
    }
    
    // 显示账号后台模态框
    function showAccountDashboard(userData) {
        
        // 如果没有传入用户数据，尝试获取用户信息
        if (!userData) {
            // 尝试获取用户信息
            const token = sessionStorage.getItem('access_token');
            
            if (token) {
                // 显示加载状态
                try {
                    showLoading();
                } catch (e) {
                }
                
                // 输出API请求URL
                
                // 使用验证逻辑.txt中定义的API路径请求用户信息
                fetch(window.BASE_URL + '/api/user_info', {
                    method: 'GET',
                    headers: {
                        'Token': token
                    }
                })
                .then(response => {
                    return response.json();
                })
                .then(data => {
                    hideLoading();
                    
                    if (data.code === 200) {
                        // 保存用户信息
                        saveUserInfo(data.data);
                        
                        // 递归调用，这次传入获取到的用户数据
                        showAccountDashboard(data.data);
                        
                        // 更新用户界面
                        updateUserInterface(data.data);
                        
                    } else if (data.code === -1 && (data.msg === 'Token已过期' || data.msg === '无效的Token')) {
                        // 令牌无效，尝试刷新
                        refreshToken()
                            .then(() => {
                                // 刷新成功后重新获取用户信息
                                const newToken = sessionStorage.getItem('access_token');
                                if (newToken) {
                                    // 重新请求用户信息
                                    fetch(window.BASE_URL + '/api/user_info', {
                                        method: 'GET',
                                        headers: {
                                            'Token': newToken
                                        }
                                    })
                                    .then(response => response.json())
                                    .then(userData => {
                                        if (userData.code === 200) {
                                            // 保存用户信息
                                            saveUserInfo(userData.data);
                                            
                                            // 递归调用，这次传入获取到的用户数据
                                            showAccountDashboard(userData.data);
                                            
                                            // 更新用户界面
                                            updateUserInterface(userData.data);
                                        } else {
                                            showToast('获取用户信息失败: ' + userData.msg, 'error');
                                        }
                                    })
                                    .catch(err => {
                                        hideLoading();
                                        showToast('获取用户信息失败', 'error');
                                    });
                                } else {
                                    hideLoading();
                                    showToast('无法获取令牌', 'error');
                                }
                            })
                            .catch(err => {
                                clearTokens();
                                hideLoading();
                                showToast('会话已过期，请重新登录', 'error');
                            });
                    } else {
                        // 其他错误
                        showToast('获取用户信息失败: ' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showToast('网络请求失败，请稍后再试', 'error');
                });
                
                // 暂时返回，等待异步请求完成后会递归调用
                return;
            } else {
                showToast('请先登录', 'error');
                // 尝试打开登录模态框
                window.emergencyShowModal();
                return;
            }
        }
        
        // 获取模态框元素
        const modal = document.getElementById('accountDashboardModal');
        if (!modal) return;
        
        // 填充用户信息
        const userEmailInput = document.getElementById('userEmail');
        const userQQInput = document.getElementById('userQQ');
        
        if (userEmailInput && userData && userData.email) {
            userEmailInput.value = userData.email;
        }
        
        if (userQQInput && userData && userData.qq) {
            userQQInput.value = userData.qq;
        }
        
        // 设置标签指示器位置
        setupTabIndicator();
        
        // 显示模态框
        modal.classList.add('show');
        
        // 初始化模态框功能
        initAccountDashboardModal();
    }
    
    // 隐藏账号后台模态框
    function hideAccountDashboard() {
        const modal = document.getElementById('accountDashboardModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }
    
    // 初始化账号后台模态框
    function initAccountDashboardModal() {
        // 关闭按钮
        const closeBtn = document.querySelector('.account-modal-close-btn');
        if (closeBtn) {
            closeBtn.onclick = hideAccountDashboard;
        }
        
        // 背景点击关闭
        const modal = document.getElementById('accountDashboardModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideAccountDashboard();
                }
            });
        }
        
        // 标签切换
        const tabItems = document.querySelectorAll('.account-tab-item');
        tabItems.forEach(function(tab) {
            tab.addEventListener('click', function() {
                // 移除所有标签的激活状态
                tabItems.forEach(t => t.classList.remove('active'));
                
                // 激活当前标签
                this.classList.add('active');
                
                // 获取标签对应的内容ID
                const tabName = this.getAttribute('data-tab');
                const paneId = tabName + '-pane';
                
                // 激活对应的内容区域
                const tabPanes = document.querySelectorAll('.tab-pane');
                tabPanes.forEach(function(pane) {
                    pane.classList.remove('active');
                });
                
                const targetPane = document.getElementById(paneId);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
                
                // 更新标签指示器位置
                setupTabIndicator();
            });
        });
        
        // 初始化密码显示/隐藏切换
        const togglePasswordBtns = document.querySelectorAll('.toggle-password-btn');
        togglePasswordBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const input = this.parentNode.querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // 保存密码按钮事件
        const savePasswordBtn = document.querySelector('.save-password-btn');
        if (savePasswordBtn) {
            savePasswordBtn.addEventListener('click', function() {
                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                // 验证输入
                if (!currentPassword) {
                    showToast('请输入当前密码', 'error');
                    return;
                }
                
                if (!newPassword) {
                    showToast('请输入新密码', 'error');
                    return;
                }
                
                if (newPassword.length < 8) {
                    showToast('新密码长度不能少于8位', 'error');
                    return;
                }
                
                if (!validatePasswordComplexity(newPassword)) {
                    showToast('密码必须包含数字、字母和特殊字符中的至少两种', 'error');
                    return;
                }
                
                if (newPassword === currentPassword) {
                    showToast('新密码不能与当前密码相同', 'error');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    showToast('两次输入的新密码不一致', 'error');
                    return;
                }
                
                // 获取访问令牌
                const token = sessionStorage.getItem('access_token');
                if (!token) {
                    showToast('未授权，请重新登录', 'error');
                    return;
                }
                
                // 显示加载状态
                showLoading();
                
                // 发送修改密码请求
                fetch(window.BASE_URL + '/api/change_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': token
                    },
                    body: JSON.stringify({
                        current_password: currentPassword,
                        new_password: newPassword,
                        confirm_password: confirmPassword
                    })
                })
                .then(response => {
                    // 保存新的令牌和时间戳
                    const newToken = response.headers.get('Token');
                    const newTimestamp = response.headers.get('timestamp');
                    const newKey = response.headers.get('key');
                    
                    if (newToken) {
                        sessionStorage.setItem('access_token', newToken);
                        sessionStorage.setItem('timestamp', newTimestamp);
                        sessionStorage.setItem('key', newKey);
                    }
                    
                    return response.json();
                })
                .then(data => {
                    hideLoading();
                    
                    if (data.code === 200) {
                        // 密码修改成功
                        localStorage.setItem('refresh_token', data.data.refresh_token);
                        
                        // 显示成功消息
                        showToast('密码修改成功，请重新登录', 'success');
                        
                        // 关闭账号后台模态框
                        hideAccountDashboard();
                        
                        // 清空输入框
                        document.getElementById('currentPassword').value = '';
                        document.getElementById('newPassword').value = '';
                        document.getElementById('confirmPassword').value = '';
                        
                        // 主动退出登录状态
                        setTimeout(() => {
                            logoutUser();
                            
                            // 恢复开发者按钮原始状态
                            const developerBtn = document.getElementById('developerBtn');
                            if (developerBtn) {
                                developerBtn.innerHTML = `
                                    <i class="fas fa-code"></i> 开发者后台
                                    <span class="btn-sparkle"></span>
                                `;
                                developerBtn.onclick = function() {
                                    emergencyShowModal();
                                    return false;
                                };
                            }
                            
                            // 提示用户重新登录
                            setTimeout(() => {
                                showToast('请使用新密码重新登录', 'info');
                                emergencyShowModal(); // 自动打开登录框
                            }, 1000);
                        }, 1500);
                    } else {
                        // 显示错误消息
                        const errorMsg = data.msg || '密码修改失败，请稍后再试';
                        showToast(errorMsg, 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showToast('请求失败: ' + error.message, 'error');
                });
            });
        }
        
        // 初始化二维码上传功能
        initQrCodeUpload();
    }
    
    // 设置标签指示器位置
    function setupTabIndicator() {
        const activeTab = document.querySelector('.account-tab-item.active');
        const indicator = document.querySelector('.account-modal-tabs .tab-indicator');
        
        if (activeTab && indicator) {
            const tabWidth = activeTab.offsetWidth;
            const tabLeft = activeTab.offsetLeft;
            
            indicator.style.width = tabWidth + 'px';
            indicator.style.left = tabLeft + 'px';
        }
    }
    
    // 添加账号后台按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        // 延迟一点以确保先有DOM元素
        setTimeout(function() {
            const accountBtn = document.getElementById('accountDashboardBtn');
            if (accountBtn) {
                accountBtn.addEventListener('click', function() {
                    hideUserMenu(); // 隐藏用户菜单
                    
                    // 显示账号后台模态框
                    showAccountDashboard();
                });
            }
            
            // 初始化模态框关闭按钮
            const closeBtn = document.querySelector('.account-modal-close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', hideAccountDashboard);
            }
            
            // 初始化标签切换
            const tabItems = document.querySelectorAll('.account-tab-item');
            tabItems.forEach(function(tab) {
                tab.addEventListener('click', function() {
                    const tabItems = document.querySelectorAll('.account-tab-item');
                    tabItems.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    const tabName = this.getAttribute('data-tab');
                    const paneId = tabName + '-pane';
                    
                    const tabPanes = document.querySelectorAll('.tab-pane');
                    tabPanes.forEach(function(pane) {
                        pane.classList.remove('active');
                    });
                    
                    const targetPane = document.getElementById(paneId);
                    if (targetPane) {
                        targetPane.classList.add('active');
                    }
                    
                    setupTabIndicator();
                });
            });
        }, 100);
    });
    
    // 加载用户信息（如果已登录）