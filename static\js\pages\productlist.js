
(function () {
    'use strict';

    let productList = [];
    let categoriesList = [];
    let priceTemplateList = [];
    let cardLibraryList = [];
    let currentProductId = '';
    let isEditing = false;

    // 分页相关变量
    let currentPage = 1;
    let pageSize = 20;
    let totalRecords = 0;
    let totalPages = 0;

    // 将分页变量添加到全局作用域
    window.currentPage = currentPage;
    window.pageSize = pageSize;
    window.totalRecords = totalRecords;
    window.totalPages = totalPages;

    // -------- 先定义所有基础功能函数 --------

    // 移动端检测函数
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // 移动端兼容的显示/隐藏方法
    function hideTypeCard(selector) {
        if (isMobile()) {
            $(selector).addClass('mobile-hidden');
        } else {
            $(selector).hide();
        }
    }

    function showTypeCard(selector) {
        if (isMobile()) {
            $(selector).removeClass('mobile-hidden');
        } else {
            $(selector).show();
        }
    }

    // 显示通知
    function showNotification(message, type) {
        const notification = $('#notification');
        const notificationMessage = $('#notificationMessage');
        const notificationIcon = notification.find('.notification-icon');

        // 设置通知类型和图标
        notification.removeClass('success error').addClass(type);

        if (type === 'success') {
            notificationIcon.attr('class', 'notification-icon fas fa-check-circle');
        } else if (type === 'error') {
            notificationIcon.attr('class', 'notification-icon fas fa-exclamation-circle');
        }

        // 设置通知消息
        notificationMessage.text(message);

        // 强制确保通知在所有元素之上
        notification.css('z-index', '200000');

        // 添加/清除其他类来触发动画
        notification.addClass('show');

        // 添加抖动效果
        if (type === 'error') {
            notification.css('animation', 'shake 0.5s ease');
            setTimeout(() => {
                notification.css('animation', '');
            }, 500);
        }

        // 3秒后自动隐藏
        setTimeout(function () {
            notification.removeClass('show');
        }, 3000);
    }

    // 将showNotification函数添加到全局作用域
    window.showNotification = showNotification;


    // 初始化页面的函数，避免重复执行
    let initialized = false;
    function initPage() {
        if (initialized) return;
        initialized = true;

        // 初始化页面

        try {
            // 基础环境初始化
            if (!window.jQuery && !window.$) {
                console.error('jQuery未加载，页面初始化失败');
                showNotification('页面依赖库加载失败，请刷新页面重试', 'error');
                return;
            }

            if (!window.bootstrap) {
                console.warn('Bootstrap JavaScript未加载，部分功能可能受影响');
            }

            // 立即绑定核心事件
            $(document).on('click', '#refreshBtn', function(event) {
                event.preventDefault();
                // 获取当前搜索条件并刷新
                const searchParams = getCurrentSearchParams ? getCurrentSearchParams() : {};
                getGoodsList(1, pageSize, searchParams);
            });
            $(document).on('click', '#addProductBtn', openAddProductModal);

            // 绑定分页事件
            bindPaginationEvents();

            // 关键数据初始化
            fetchCategoryList();
            fetchPriceTemplateList();
            fetchCardLibraryList();

            // 验证DOM就绪状态

            // 初始化搜索功能 - 直接调用，不使用延迟
            initializeSearchFeatures();

            // 启动搜索功能健康检查
            setTimeout(function () {
                if (typeof window.startSearchHealthCheck === 'function') {
                    window.startSearchHealthCheck();
                }
            }, 2000);

            // 确保关键函数和变量在全局作用域中可用
            window.getGoodsList = getGoodsList;
            window.showNotification = showNotification;
            window.currentPage = currentPage;
            window.pageSize = pageSize;
            window.totalRecords = totalRecords;
            window.totalPages = totalPages;

            // 添加初始化完成标记
        } catch (e) {
            showNotification("页面初始化失败，请刷新", "error");
        }
    }

    // 新增函数：绑定表格操作按钮事件
    function bindTableActions() {

        // 等待DOM完全准备好
        if (document.readyState !== 'complete') {
            setTimeout(bindTableActions, 100);
            return;
        }

        // 绑定刷新按钮 - 添加元素存在检查
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn && typeof refreshBtn.addEventListener === 'function') {
            // 移除可能存在的旧事件监听器
            refreshBtn.removeEventListener('click', getGoodsList);
            refreshBtn.addEventListener('click', function (event) {
                // 阻止默认行为
                event.preventDefault();
                // 获取当前搜索条件并刷新
                const searchParams = getCurrentSearchParams ? getCurrentSearchParams() : {};
                getGoodsList(1, pageSize, searchParams);
            });
        } else {
            console.warn('刷新按钮元素未找到或不支持事件监听');
        }

        // 绑定添加商品按钮 - 添加元素存在检查
        const addProductBtn = document.getElementById('addProductBtn');
        if (addProductBtn && typeof addProductBtn.addEventListener === 'function') {
            // 移除可能存在的旧事件监听器
            addProductBtn.removeEventListener('click', openAddProductModal);
            addProductBtn.addEventListener('click', function () {
                openAddProductModal();
            });
        } else {
        }

        // 使用事件委托机制绑定编辑按钮事件
        $(document).off('click', '.edit-btn').on('click', '.edit-btn', function () {
            const id = $(this).data('id') || $(this).closest('tr').data('id');
            if (id) {
                editProduct(id);
            }
        });

        // 使用事件委托机制绑定删除按钮事件
        $(document).off('click', '.delete-btn').on('click', '.delete-btn', function () {
            const id = $(this).data('id') || $(this).closest('tr').data('id');
            const name = $(this).data('name') || $(this).closest('tr').data('name');
            if (id) {
                showDeleteConfirmation(id, name);
            }
        });

    }

    // 渲染商品列表
    function renderProductList() {
        if (!productList || productList.length === 0) {
            $('#productListTable').html(`
                    <tr>
                        <td colspan="10">
                            <div class="empty-state">
                                <i class="fas fa-box-open"></i>
                                <h3>暂无商品数据</h3>
                                <p>点击"添加商品"按钮创建您的第一个商品</p>
                            </div>
                        </td>
                    </tr>
                `);
            return;
        }

        let html = '';

        productList.forEach(product => {
            // 获取分类名称
            const category = getCategoryName(product.category_id);

            // 商品状态显示
            const statusClass = product.status === '1' ? 'success' : 'warning';
            const statusText = product.status === '1' ? '上架' : '下架';

            // 商品类型显示
            let typeText, typeIcon, typeClass = '';

            if (product.type === '1') {
                typeText = '卡密商品';
                typeIcon = '<i class="fas fa-key" style="margin-right: 5px;"></i>';
            } else if (product.type === '2') {
                typeText = '虚拟商品';
                typeIcon = '<i class="fas fa-cloud-download-alt" style="margin-right: 5px;"></i>';
            } else if (product.type === '3') {
                typeText = '对接商品';
                typeIcon = '<i class="fas fa-sync-alt" style="margin-right: 5px;"></i>';
                typeClass = 'docking-product-type';
            } else {
                typeText = '未知商品';
                typeIcon = '<i class="fas fa-question-circle" style="margin-right: 5px;"></i>';
            }

            html += `
                <tr data-id="${product.id}" data-name="${product.name}">
                    <td><span style="color: var(--primary-color); font-weight: 500;">${product.id}</span></td>
                    <td title="${product.name}" style="overflow: hidden; text-overflow: ellipsis;">${product.name}</td>
                    <td>
                        <img src="${product.image || '/static/images/product-placeholder.svg'}" alt="${product.name}" class="product-image">
                    </td>
                    <td>${category}</td>
                    <td class="price">¥${parseFloat(product.price).toFixed(2)}</td>
                    <td class="text-center"><span style="color: var(--primary-color); font-weight: 500;">${product.sales_count || 0}</span></td>
                    <td class="text-center"><span style="color: var(--text-secondary); font-weight: 500;">${product.stock || 0}</span></td>
                    <td><span class="${typeClass}">${typeIcon}${typeText}</span></td>
                    <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn edit-btn" data-id="${product.id}" title="编辑商品">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-btn" data-id="${product.id}" data-name="${product.name}" title="删除商品">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                    
                    <!-- 价格模板信息悬浮提示 -->
                    <div class="tooltip-card">
                        <h6 class="mb-2" style="color: var(--primary-color); font-weight: 600;">价格模板信息</h6>
                        <div class="mb-2">
                            <small class="text-muted">基础价格：</small>
                            <span class="price">¥${parseFloat(product.price).toFixed(2)}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">模板名称：</small>
                            <span>${getPriceTemplateName(product.PriceTemplate)}</span>
                        </div>
                        <hr class="my-2" style="border-color: var(--border-color);">
                        <div class="mb-0">
                            ${renderMemberPrices(product.price, product.PriceTemplate)}
                        </div>
                    </div>
                </tr>
                `;
        });

        $('#productListTable').html(html);

        // 表格渲染完成后绑定事件
        bindTableActions();
    }

    // 从API获取商品分类列表并填充下拉框
    function fetchCategoryList() {
        // 调用API获取商品分类列表
        $.ajax({
            url: '/api/get_category_list',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.code === 200) {
                    const categories = response.data || [];
                    categoriesList = [];

                    // 清空一级分类下拉框
                    $('#categoryParent').empty().append('<option value="">请选择一级分类</option>');

                    // 遍历并处理分类数据
                    categories.forEach(parent => {
                        // 添加一级分类到下拉框
                        $('#categoryParent').append(`<option value="${parent.id}">${parent.name}</option>`);

                        // 添加一级分类到分类列表数组
                        categoriesList.push({
                            id: parent.id,
                            name: parent.name,
                            level: parent.level,
                            parent_id: parent.parent_id || ''
                        });

                        // 处理二级分类
                        if (parent.children && parent.children.length > 0) {
                            parent.children.forEach(child => {
                                categoriesList.push({
                                    id: child.id,
                                    name: child.name,
                                    level: child.level,
                                    parent_id: child.parent_id || parent.id
                                });
                            });
                        }
                    });


                    // 初始化二级分类下拉框
                    $('#categoryChild').empty().append('<option value="">请选择二级分类</option>');

                    // 绑定一级分类变更事件
                    $('#categoryParent').off('change').on('change', function () {
                        updateChildCategories($(this).val());
                    });
                } else {
                    showNotification(response.msg || '获取分类列表失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 从API获取价格模板列表并填充下拉框
    function fetchPriceTemplateList() {
        // 调用API获取价格模板列表
        $.ajax({
            url: '/api/GetPriceTemplateList',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.code === 200) {
                    priceTemplateList = response.data || [];

                    // 填充价格模板下拉框
                    $('#priceTemplate').empty().append('<option value="">请选择价格模板</option>');

                    priceTemplateList.forEach(template => {
                        const typeText = template.type === '1' ? '[固定金额]' : '[百分比]';
                        $('#priceTemplate').append(`<option value="${template.id}">${template.name} ${typeText}</option>`);
                    });


                    // 绑定价格模板和基础价格变更事件
                    $('#priceTemplate, #basePrice').off('change').on('change', function () {
                        updatePriceCalculation();
                    });
                } else {
                    showNotification(response.msg || '获取价格模板列表失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 从API获取卡库列表并填充下拉框
    function fetchCardLibraryList() {
        // 调用API获取卡库列表
        $.ajax({
            url: '/api/GetCardLibraryList',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.code === 200) {
                    cardLibraryList = response.data || [];

                    // 填充卡库下拉框
                    $('#cardLibrary').empty().append('<option value="">请选择卡库</option>');

                    cardLibraryList.forEach(library => {
                        $('#cardLibrary').append(`<option value="${library.id}">${library.name} (${library.count}张卡)</option>`);
                    });

                } else {
                    showNotification(response.msg || '获取卡库列表失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 获取商品列表
    function getGoodsList(page = currentPage, size = pageSize, searchParams = {}) {

        // 更新当前分页参数
        currentPage = page;
        pageSize = size;

        // 同步更新全局引用
        window.currentPage = currentPage;
        window.pageSize = pageSize;

        // 显示加载中遮罩
        $('#loading-overlay').css('display', 'flex');

        // 构建API URL，包含分页参数和搜索参数
        const params = new URLSearchParams({
            page: page,
            page_size: size,
            _: new Date().getTime()
        });

        // 添加搜索筛选参数
        if (searchParams.keyword) {
            params.append('keyword', searchParams.keyword);
        }
        if (searchParams.primary_category) {
            params.append('primary_category', searchParams.primary_category);
        }
        if (searchParams.secondary_category) {
            params.append('secondary_category', searchParams.secondary_category);
        }
        if (searchParams.product_type) {
            params.append('product_type', searchParams.product_type);
        }
        if (searchParams.product_status) {
            params.append('product_status', searchParams.product_status);
        }

        const apiUrl = `/api/GetGoodsList?${params.toString()}`;

        // 调用API获取商品列表
        $.ajax({
            url: apiUrl,
            type: 'GET',
            dataType: 'json',
            cache: false,
            headers: {
                'X-Debug-Init': 'true'
            },
            success: function (response) {
                // 隐藏加载中遮罩
                $('#loading-overlay').css('display', 'none');


                if (response.code === 200) {
                    // 处理新的分页响应格式
                    if (response.data && response.data.items) {
                        // 新的分页格式
                        productList = response.data.items || [];
                        const pagination = response.data.pagination || {};

                        // 更新分页信息
                        totalRecords = pagination.total || 0;
                        totalPages = pagination.total_pages || 0;
                        currentPage = pagination.current_page || 1;
                        pageSize = pagination.page_size || 20;

                        // 同步更新全局引用
                        window.totalRecords = totalRecords;
                        window.totalPages = totalPages;
                        window.currentPage = currentPage;
                        window.pageSize = pageSize;

                        // 分页信息更新完成

                        // 更新分页UI
                        updatePaginationUI();
                    } else {
                        // 兼容旧格式
                        productList = response.data || [];
                        totalRecords = productList.length;
                        totalPages = 1;
                    }


                    // 检查是否有对接商品
                    const dockingProducts = productList.filter(p => p.type === '3');
                    if (dockingProducts.length > 0) {
                    }

                    renderProductList();
                } else {
                    $('#productListTable').html(`<tr><td colspan="10" class="text-center py-5 text-danger"><i class="fas fa-exclamation-circle me-2"></i>${response.msg || '获取商品列表失败'}</td></tr>`);
                    // 重置分页信息
                    totalRecords = 0;
                    totalPages = 0;
                    updatePaginationUI();
                }
            },
            error: function (xhr, status, error) {
                // API请求失败处理
                // 隐藏加载中遮罩
                $('#loading-overlay').css('display', 'none');

                $('#productListTable').html('<tr><td colspan="10" class="text-center py-5 text-danger"><i class="fas fa-exclamation-circle me-2"></i>网络错误，请稍后再试</td></tr>');
            }
        });
    }

    // 将getGoodsList函数添加到全局作用域
    window.getGoodsList = getGoodsList;

    // 更新分页UI
    function updatePaginationUI() {
        // 更新分页信息文本
        const start = totalRecords === 0 ? 0 : (currentPage - 1) * pageSize + 1;
        const end = Math.min(currentPage * pageSize, totalRecords);
        $('#paginationInfo').text(`显示第 ${start}-${end} 条，共 ${totalRecords} 条记录`);

        // 更新页码选择器
        $('#pageSizeSelect').val(pageSize);

        // 更新上一页/下一页按钮状态
        $('#prevPageBtn').prop('disabled', currentPage <= 1);
        $('#nextPageBtn').prop('disabled', currentPage >= totalPages);

        // 生成页码按钮
        generatePageNumbers();
    }

    // 生成页码按钮
    function generatePageNumbers() {
        const pageNumbers = $('#pageNumbers');
        pageNumbers.empty();

        if (totalPages <= 1) {
            return;
        }

        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        // 确保显示至少5个页码（如果总页数足够）
        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else if (endPage === totalPages) {
                startPage = Math.max(1, endPage - 4);
            }
        }

        // 添加第一页和省略号
        if (startPage > 1) {
            pageNumbers.append(`<span class="page-number" data-page="1">1</span>`);
            if (startPage > 2) {
                pageNumbers.append(`<span class="page-ellipsis">...</span>`);
            }
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            pageNumbers.append(`<span class="page-number ${activeClass}" data-page="${i}">${i}</span>`);
        }

        // 添加省略号和最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pageNumbers.append(`<span class="page-ellipsis">...</span>`);
            }
            pageNumbers.append(`<span class="page-number" data-page="${totalPages}">${totalPages}</span>`);
        }
    }

    // 绑定分页事件
    function bindPaginationEvents() {
        // 上一页按钮
        $(document).on('click', '#prevPageBtn', function () {
            if (currentPage > 1) {
                getGoodsList(currentPage - 1, pageSize);
            }
        });

        // 下一页按钮
        $(document).on('click', '#nextPageBtn', function () {
            if (currentPage < totalPages) {
                getGoodsList(currentPage + 1, pageSize);
            }
        });

        // 页码按钮
        $(document).on('click', '.page-number', function () {
            const page = parseInt($(this).data('page'));
            if (page && page !== currentPage) {
                getGoodsList(page, pageSize);
            }
        });

        // 每页条数选择
        $(document).on('change', '#pageSizeSelect', function () {
            const newPageSize = parseInt($(this).val());
            if (newPageSize !== pageSize) {
                // 重置到第一页
                getGoodsList(1, newPageSize);
            }
        });
    }

    // 获取分类名称
    function getCategoryName(categoryId) {
        if (!categoryId) return '未分类';

        // 直接在分类列表中查找对应的分类
        const category = categoriesList.find(cat => cat.id === categoryId);

        if (category) {
            if (category.level === 2) {
                // 查找父分类
                const parentCategory = categoriesList.find(cat => cat.id === category.parent_id);
                if (parentCategory) {
                    return `${parentCategory.name} > ${category.name}`;
                }
            }
            return category.name;
        }

        return '未知分类';
    }

    // 获取价格模板名称
    function getPriceTemplateName(templateId) {
        if (!templateId) return '无模板';

        const template = priceTemplateList.find(t => t.id === templateId);
        return template ? template.name : '未知模板';
    }

    // 渲染会员价格
    function renderMemberPrices(basePrice, template) {
        basePrice = parseFloat(basePrice) || 0;

        if (!template || !template.data) {
            return '<div class="text-muted">未选择价格模板</div>';
        }

        let html = '';

        // 根据模板类型计算不同会员等级的价格
        if (template.type === '1') { // 固定金额加价
            Object.keys(template.data).forEach(level => {
                const addition = parseFloat(template.data[level]) || 0;
                const finalPrice = basePrice + addition;

                html += `
                    <div class="mb-2">
                        <span class="text-muted">${getLevelName(level)}：</span>
                        <span class="price">¥${finalPrice.toFixed(2)}</span>
                        <small class="text-muted">(+${addition.toFixed(2)})</small>
                    </div>`;
            });
        } else { // 百分比加价
            Object.keys(template.data).forEach(level => {
                const percentage = parseFloat(template.data[level]) || 0;
                const addition = basePrice * (percentage / 100);
                const finalPrice = basePrice + addition;

                html += `
                    <div class="mb-2">
                        <span class="text-muted">${getLevelName(level)}：</span>
                        <span class="price">¥${finalPrice.toFixed(2)}</span>
                        <small class="text-muted">(+${percentage}%)</small>
                    </div>`;
            });
        }

        return html;
    }

    // 获取会员等级名称
    function getLevelName(level) {
        // 根据level键名获取对应的用户友好名称
        const levelMap = {
            'NormalUser': '普通用户',
            'VIPUser': 'VIP会员',
            'SVIPUser': '超级会员'
        };

        return levelMap[level] || level;
    }

    // 打开添加商品模态框函数
    function openAddProductModal() {
        isEditing = false;
        currentProductId = '';
        $('#modalTitle').text('添加商品');
        resetForm();

        hideTypeCard(`.type-card[data-type="3"]`);
        showTypeCard(`.type-card[data-type="1"], .type-card[data-type="2"]`);

        try {
            $('#loading-overlay').css('display', 'none');

            $('#productModal').css({
                'display': 'block',
                'background-color': 'rgba(0, 0, 0, 0.5)',
                'z-index': '100000'
            });

            // 重新绑定模态框内的事件
            bindModalEvents();

            // 获取卡库列表、商品分类列表和价格模板列表
            fetchCardLibraryList();
            fetchCategoryList();
            fetchPriceTemplateList();

        } catch (e) {
            alert("打开添加商品对话框时出错，请刷新页面后重试");
        }
    }

    // 重置表单
    function resetForm() {
        $('#productId').val('');
        $('#productName').val('');
        $('#basePrice').val('');
        $('#productInfo').val('');

        // 重置类型选择，但不显示或隐藏对接商品类型（由调用函数决定）
        $('.type-card').removeClass('selected locked').css({
            'opacity': '1',
            'cursor': 'pointer',
            'pointer-events': 'auto',
            'border': ''
        });
        $('.type-card[data-type="1"]').addClass('selected');
        $('#cardLibraryContainer').show();

        // 重置下拉选择
        $('#cardLibrary').val('');
        $('#categoryParent').val('');
        $('#categoryChild').val('');
        $('#priceTemplate').val('');

        // 重置图片预览
        $('#imagePreview').html('');

        // 重置参数区域
        $('#paramContainer').html(`
                <div class="parameter-row">
                    <button type="button" class="btn btn-light remove-param">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                    <div class="row">
                        <div class="form-group">
                            <label class="form-label">参数名称</label>
                            <input type="text" class="form-control param-name" placeholder="如：QQ号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">参数提示</label>
                            <input type="text" class="form-control param-tip" placeholder="如：请填写QQ号">
                        </div>
                    </div>
                </div>
            `);
    }

    // 显示删除确认
    function showDeleteConfirmation(id, name) {
        const confirmMessage = $('.confirm-message');
        confirmMessage.text(`确定要删除"${name}"商品吗？此操作无法撤销。`);

        // 设置删除按钮事件
        const confirmDeleteBtn = $('#confirmDelete');
        confirmDeleteBtn.off('click').on('click', function () {
            deleteProduct(id);
            $('#confirmDialog').removeClass('show');
        });

        // 设置取消按钮事件
        const cancelDeleteBtn = $('#cancelDelete');
        cancelDeleteBtn.off('click').on('click', function () {
            $('#confirmDialog').removeClass('show');
        });

        $('#confirmDialog').addClass('show');
    }

    // 编辑商品
    function editProduct(id) {
        isEditing = true;
        currentProductId = id;

        // 设置模态框标题
        $('#modalTitle').text('编辑商品');

        // 显示加载中状态
        $('#loading-overlay').css('display', 'flex');
        resetForm();

        // 默认情况下先隐藏对接商品选项，显示虚拟商品和卡密商品选项，等加载数据后再决定是否显示
        hideTypeCard(`.type-card[data-type="3"]`);
        showTypeCard(`.type-card[data-type="1"], .type-card[data-type="2"]`);

        // 使用jQuery直接显示模态框
        $('#productModal').css({
            'display': 'block',
            'background-color': 'rgba(0, 0, 0, 0.5)',
            'z-index': '100000'
        });

        // 重新绑定模态框内的事件
        bindModalEvents();

        // 构建请求数据
        const data = {
            id: id
        };

        // 添加签名
        data.sign = generateSign(data);

        // 获取商品详情
        $.ajax({
            url: '/api/GetGoodsDetail',
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (response) {
                // 隐藏加载中状态
                $('#loading-overlay').css('display', 'none');

                if (response.code === 200) {
                    // fillProductForm函数会根据商品类型自动处理显示逻辑
                    // 这里不需要预先设置，让fillProductForm统一处理

                    fillProductForm(response.data);
                } else {
                    // 关闭模态框
                    $('#productModal').css('display', 'none');
                    showNotification(response.msg || '获取商品详情失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                // 隐藏加载中状态
                $('#loading-overlay').css('display', 'none');

                // 关闭模态框
                $('#productModal').css('display', 'none');
                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 填充商品表单（用于编辑模式）
    function fillProductForm(data) {
        $('#productId').val(data.id);
        $('#productName').val(data.name);
        $('#basePrice').val(data.price);
        $('#productInfo').val(data.info);

        // 设置商品类型
        $('.type-card').removeClass('selected');

        if (data.type === '3') {
            // 对接商品：显示对接商品选项，隐藏虚拟商品和卡密商品选项
            showTypeCard(`.type-card[data-type="3"]`);
            $(`.type-card[data-type="3"]`).addClass('selected');
            hideTypeCard(`.type-card[data-type="1"], .type-card[data-type="2"]`);

            // 给选中的卡片添加特殊样式，表示不可更改
            $(`.type-card[data-type="3"]`).addClass('locked').css({
                'opacity': '0.9',
                'cursor': 'not-allowed',
                'pointer-events': 'none',
                'border': '2px solid #ff7eb9'
            });

            // 隐藏卡库选择
            $('#cardLibraryContainer').hide();
        } else {
            // 虚拟商品或卡密商品：显示虚拟商品和卡密商品选项，隐藏对接商品选项
            showTypeCard(`.type-card[data-type="1"], .type-card[data-type="2"]`);
            $(`.type-card[data-type="1"], .type-card[data-type="2"]`).removeClass('locked').css({
                'opacity': '1',
                'cursor': 'pointer',
                'pointer-events': 'auto',
                'border': ''
            });
            hideTypeCard(`.type-card[data-type="3"]`);

            $(`.type-card[data-type="${data.type}"]`).addClass('selected');

            if (data.type === '1') {
                $('#cardLibraryContainer').show();
                $('#cardLibrary').val(data.card_library);
            } else {
                $('#cardLibraryContainer').hide();
            }
        }

        // 设置分类
        if (data.category_id) {
            // 根据category对象的结构查找分类ID
            const category = categoriesList.find(cat => cat.id === data.category_id);

            if (category) {
                if (category.level === 2) {
                    // 二级分类
                    $('#categoryParent').val(category.parent_id);
                    updateChildCategories(category.parent_id);
                    setTimeout(() => {
                        $('#categoryChild').val(category.id);
                    }, 300);
                } else {
                    // 一级分类
                    $('#categoryParent').val(category.id);
                    updateChildCategories(category.id);
                }
            }
        }

        // 设置价格模板
        $('#priceTemplate').val(data.PriceTemplate);
        setTimeout(() => {
            updatePriceCalculation();
        }, 300);

        // 设置图片预览
        if (data.image) {
            $('#imagePreview').html(`<img src="${data.image}" alt="商品图片" style="max-width: 100%; max-height: 200px; border-radius: 5px; margin-top: 10px;">`);
        }

        // 设置参数
        $('#paramContainer').empty();
        if (data.attach && data.attach.length > 0) {
            data.attach.forEach(param => {
                $('#paramContainer').append(`
                        <div class="parameter-row">
                            <button type="button" class="btn btn-light remove-param">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <div class="row">
                                <div class="form-group">
                                    <label class="form-label">参数名称</label>
                                    <input type="text" class="form-control param-name" placeholder="如：规格、颜色" value="${param.name || ''}">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">参数提示</label>
                                    <input type="text" class="form-control param-tip" placeholder="如：请选择规格" value="${param.tip || ''}">
                                </div>
                            </div>
                        </div>
                    `);
            });
        } else {
            // 添加一个默认参数行
            addParamRow();
        }

        // 填充库存字段
        if (data.stock !== undefined) {
            if (data.stock === 99999) {
                // 无限库存
                $('#unlimitedStock').prop('checked', true);
                $('#productStock').val('').prop('disabled', true).css('background-color', '#f5f5f5');
            } else {
                // 有限库存
                $('#unlimitedStock').prop('checked', false);
                $('#productStock').val(data.stock).prop('disabled', false).css('background-color', '');
            }
        }

        // 填充弹窗公告字段
        if (data.notice !== undefined) {
            $('#productPopupTip').val(data.notice);
        }

        // 填充后置操作字段
        if (data.operation_data !== undefined && data.operation_data) {
            const operationData = data.operation_data;

            if (operationData.type === 0) {
                // 无后置操作
                $('#postActionType').val('none');
                $('#urlConfigSection').hide();
            } else if (operationData.type === 1) {
                // 请求指定URL
                $('#postActionType').val('url');
                $('#urlConfigSection').show();

                const configData = operationData.data || {};

                // 填充URL
                if (configData.url) {
                    $('#requestUrl').text(configData.url);
                    highlightVariables(document.getElementById('requestUrl'));
                }

                // 填充请求方式
                const requestMethod = configData.requestMethod === 1 ? 'POST' : 'GET';

                // 更新按钮状态
                $('.method-btn').removeClass('active').css({
                    'background-color': 'white',
                    'color': 'var(--text-secondary)',
                    'border-color': 'var(--border-color)'
                });

                $(`.method-btn[data-method="${requestMethod}"]`).addClass('active').css({
                    'background-color': 'var(--primary-color)',
                    'color': 'white',
                    'border-color': 'var(--primary-color)'
                });

                $('#postActionMethod').val(requestMethod);

                // 根据请求方式显示/隐藏请求体区域
                const $requestBodySection = $('#requestBodySection');
                if (requestMethod === 'GET') {
                    $requestBodySection.hide();
                } else if (requestMethod === 'POST') {
                    $requestBodySection.show();
                }

                // 填充请求头
                $('#headersContainer').empty();
                if (configData.header && typeof configData.header === 'object') {
                    Object.entries(configData.header).forEach(([key, value]) => {
                        addHeaderRow(key, value);
                    });
                }

                // 填充请求体
                if (configData.body) {
                    $('#requestBody').text(configData.body);
                    highlightVariables(document.getElementById('requestBody'));
                }

                // 填充签名配置
                const signConfig = configData.sign || {};
                if (signConfig.body) {
                    $('#signContent').text(signConfig.body);
                    highlightVariables(document.getElementById('signContent'));
                }
                if (signConfig.encryptMethod) {
                    $('#encryptMethod').val(signConfig.encryptMethod);
                }
                if (signConfig.outputMethod) {
                    $('#outputMethod').val(signConfig.outputMethod);
                }
            }
        } else {
            // 默认无后置操作
            $('#postActionType').val('none');
            $('#urlConfigSection').hide();
        }
    }

    // 删除商品
    function deleteProduct(id) {
        // 显示加载中遮罩
        $('#loading-overlay').css('display', 'flex');

        // 构建请求数据
        const data = {
            id: id
        };

        // 添加签名
        data.sign = generateSign(data);

        $.ajax({
            url: '/api/deleteGood',
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (response) {
                // 隐藏加载中遮罩
                $('#loading-overlay').css('display', 'none');

                if (response.code === 200) {
                    showNotification('删除成功', 'success');
                    getGoodsList(currentPage, pageSize);
                } else {
                    showNotification(response.msg || '删除失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                // 隐藏加载中遮罩
                $('#loading-overlay').css('display', 'none');

                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 上传图片到服务器
    function uploadImageToServer(base64Data) {
        try {
            console.log('开始上传图片，数据长度:', base64Data.length);

            // 准备上传数据
            const uploadData = {
                image: base64Data
            };

            // 添加签名，包含错误处理
            try {
                uploadData.sign = generateSign(uploadData);
                console.log('签名生成成功');
            } catch (signError) {
                console.error('签名生成失败:', signError);

                // 显示签名生成失败的错误信息
                $('#imagePreview').html(`
                            <div style="text-align: center; padding: 20px; color: #f44336;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                <div style="margin-top: 10px;">签名生成失败，请重试</div>
                                <div style="margin-top: 5px; font-size: 12px; color: #999;">错误详情: ${signError.message}</div>
                            </div>
                        `);
                showNotification('签名生成失败，请重试上传图片', 'error');

                // 重置文件输入框
                $('#productImage').val('');
                return;
            }

            // 发送上传请求
            $.ajax({
                url: '/api/upload_product_image',
                type: 'POST',
                dataType: 'json',
                data: JSON.stringify(uploadData),
                contentType: 'application/json',
                timeout: 30000, // 30秒超时
                success: function (response) {
                    console.log('上传响应:', response);

                    if (response.code === 200) {
                        // 上传成功，显示预览图片
                        const imageUrl = response.data.image_url;
                        $('#imagePreview').html(`
                                    <div style="position:relative; display:inline-block;">
                                        <img src="${imageUrl}" alt="预览图片" style="max-width: 100%; max-height: 200px; border-radius: 10px; border: 2px solid var(--border-color); box-shadow: 0 4px 12px rgba(255, 158, 210, 0.2);">
                                        <button type="button" id="removeImage" style="position:absolute; top:-10px; right:-10px; width:25px; height:25px; border-radius:50%; background-color:var(--primary-color); color:white; border:none; display:flex; align-items:center; justify-content:center; cursor:pointer; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                                            <i class="fas fa-times" style="font-size:12px;"></i>
                                        </button>
                                    </div>
                                `);

                        // 将图片URL存储到隐藏字段中，供后续使用
                        $('#imagePreview').data('image-url', imageUrl);

                        // 绑定移除图片按钮事件
                        $('#removeImage').on('click', function () {
                            $('#productImage').val('');
                            $('#imagePreview').empty().removeData('image-url');
                        });

                        showNotification('图片上传成功', 'success');
                    } else {
                        // 上传失败
                        $('#imagePreview').html(`
                                    <div style="text-align: center; padding: 20px; color: #f44336;">
                                        <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                        <div style="margin-top: 10px;">图片上传失败</div>
                                        <div style="margin-top: 5px; font-size: 12px; color: #999;">${response.msg || '未知错误'}</div>
                                    </div>
                                `);
                        showNotification(response.msg || '图片上传失败', 'error');

                        // 重置文件输入框
                        $('#productImage').val('');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('上传请求失败:', { xhr, status, error });

                    let errorMessage = '网络错误，上传失败';
                    if (status === 'timeout') {
                        errorMessage = '上传超时，请检查网络连接';
                    } else if (xhr.status === 413) {
                        errorMessage = '图片文件过大，请选择较小的图片';
                    } else if (xhr.status >= 500) {
                        errorMessage = '服务器错误，请稍后重试';
                    }

                    // 上传失败
                    $('#imagePreview').html(`
                                <div style="text-align: center; padding: 20px; color: #f44336;">
                                    <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                    <div style="margin-top: 10px;">${errorMessage}</div>
                                    <div style="margin-top: 5px; font-size: 12px; color: #999;">状态码: ${xhr.status || 'N/A'}</div>
                                </div>
                            `);
                    showNotification(errorMessage, 'error');

                    // 重置文件输入框
                    $('#productImage').val('');
                }
            });
        } catch (error) {
            console.error('uploadImageToServer: 意外错误:', error);

            // 显示意外错误信息
            $('#imagePreview').html(`
                        <div style="text-align: center; padding: 20px; color: #f44336;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                            <div style="margin-top: 10px;">上传过程中发生意外错误</div>
                            <div style="margin-top: 5px; font-size: 12px; color: #999;">${error.message}</div>
                        </div>
                    `);
            showNotification('上传过程中发生意外错误，请重试', 'error');

            // 重置文件输入框
            $('#productImage').val('');
        }
    }

    // 生成签名
    // 递归排序对象键，确保与后端json.dumps(sort_keys=True)完全一致
    function sortObjectKeys(obj, visited = new WeakSet(), depth = 0, maxDepth = 50) {
        // 防止无限递归：检查递归深度
        if (depth > maxDepth) {
            console.warn('sortObjectKeys: 达到最大递归深度，停止处理');
            return obj;
        }

        // 基本类型直接返回
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        // 防止循环引用：检查对象是否已被访问
        if (visited.has(obj)) {
            console.warn('sortObjectKeys: 检测到循环引用，返回空对象');
            return {};
        }

        // 将当前对象添加到已访问集合
        visited.add(obj);

        try {
            if (Array.isArray(obj)) {
                const result = obj.map(item => sortObjectKeys(item, visited, depth + 1, maxDepth));
                visited.delete(obj); // 处理完成后从已访问集合中移除
                return result;
            }

            const sortedKeys = Object.keys(obj).sort();
            const sortedObj = {};
            sortedKeys.forEach(key => {
                try {
                    sortedObj[key] = sortObjectKeys(obj[key], visited, depth + 1, maxDepth);
                } catch (error) {
                    console.warn(`sortObjectKeys: 处理键 "${key}" 时出错:`, error);
                    sortedObj[key] = obj[key]; // 出错时使用原值
                }
            });

            visited.delete(obj); // 处理完成后从已访问集合中移除
            return sortedObj;
        } catch (error) {
            console.error('sortObjectKeys: 处理对象时出错:', error);
            visited.delete(obj); // 确保在错误情况下也移除对象
            return obj; // 出错时返回原对象
        }
    }

    function generateSign(data) {
        try {

            // 创建数据副本并移除sign字段
            const dataForSign = { ...data };
            delete dataForSign.sign;
            // console.log('排除sign字段后的数据:', dataForSign);

            // 递归排序所有对象的键，确保与后端json.dumps(sort_keys=True)完全一致
            const sortedData = sortObjectKeys(dataForSign);
            // console.log('递归排序后的数据:', sortedData);

            // 使用与后端相同的JSON序列化参数：无空格分隔符
            const jsonStr = JSON.stringify(sortedData);
            // console.log('JSON字符串长度:', jsonStr.length);

            // 直接使用UTF-8编码，与后端json_str.encode('utf-8')保持一致
            const utf8Bytes = new TextEncoder().encode(jsonStr);
            // console.log('UTF-8编码后的字节数组长度:', utf8Bytes.length);

            // 安全地将字节数组转换为字符串用于Base64编码
            // 使用分块处理避免String.fromCharCode.apply的参数数量限制
            let utf8Str = '';
            const chunkSize = 8192; // 8KB chunks to avoid stack overflow

            for (let i = 0; i < utf8Bytes.length; i += chunkSize) {
                const chunk = utf8Bytes.slice(i, i + chunkSize);
                utf8Str += String.fromCharCode.apply(null, chunk);
            }
            // console.log('UTF-8字符串长度:', utf8Str.length);

            // 使用btoa进行Base64编码，添加错误处理
            let base64Str;
            try {
                base64Str = btoa(utf8Str);
                // console.log('Base64编码后的字符串长度:', base64Str.length);
            } catch (base64Error) {
                console.error('Base64编码失败:', base64Error);
                // throw new Error('Base64编码失败: ' + base64Error.message);
            }

            const md5Result = md5(base64Str);
            // console.log('MD5签名结果:', md5Result);

            return md5Result;
        } catch (error) {
            // console.error('generateSign: 签名生成失败:', error);
            // console.error('generateSign: 错误堆栈:', error.stack);

            // 返回一个默认签名或抛出更有意义的错误
            throw new Error('签名生成失败: ' + error.message);
        }
    }

    // MD5哈希函数
    function md5(string) {
        function md5_RotateLeft(lValue, iShiftBits) {
            return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
        }

        function md5_AddUnsigned(lX, lY) {
            var lX4, lY4, lX8, lY8, lResult;
            lX8 = (lX & 0x80000000);
            lY8 = (lY & 0x80000000);
            lX4 = (lX & 0x40000000);
            lY4 = (lY & 0x40000000);
            lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
            if (lX4 & lY4) {
                return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
            }
            if (lX4 | lY4) {
                if (lResult & 0x40000000) {
                    return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                } else {
                    return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                }
            } else {
                return (lResult ^ lX8 ^ lY8);
            }
        }

        function md5_F(x, y, z) {
            return (x & y) | ((~x) & z);
        }

        function md5_G(x, y, z) {
            return (x & z) | (y & (~z));
        }

        function md5_H(x, y, z) {
            return (x ^ y ^ z);
        }

        function md5_I(x, y, z) {
            return (y ^ (x | (~z)));
        }

        function md5_FF(a, b, c, d, x, s, ac) {
            a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
            return md5_AddUnsigned(md5_RotateLeft(a, s), b);
        }

        function md5_GG(a, b, c, d, x, s, ac) {
            a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
            return md5_AddUnsigned(md5_RotateLeft(a, s), b);
        }

        function md5_HH(a, b, c, d, x, s, ac) {
            a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
            return md5_AddUnsigned(md5_RotateLeft(a, s), b);
        }

        function md5_II(a, b, c, d, x, s, ac) {
            a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
            return md5_AddUnsigned(md5_RotateLeft(a, s), b);
        }

        function md5_ConvertToWordArray(string) {
            var lWordCount;
            var lMessageLength = string.length;
            var lNumberOfWords_temp1 = lMessageLength + 8;
            var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
            var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
            var lWordArray = Array(lNumberOfWords - 1);
            var lBytePosition = 0;
            var lByteCount = 0;
            while (lByteCount < lMessageLength) {
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition));
                lByteCount++;
            }
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
            lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
            lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
            return lWordArray;
        }

        function md5_WordToHex(lValue) {
            var WordToHexValue = "", WordToHexValue_temp = "", lByte, lCount;
            for (lCount = 0; lCount <= 3; lCount++) {
                lByte = (lValue >>> (lCount * 8)) & 255;
                WordToHexValue_temp = "0" + lByte.toString(16);
                WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
            }
            return WordToHexValue;
        }

        function md5_Utf8Encode(string) {
            string = string.replace(/\r\n/g, "\n");
            var utftext = "";
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                } else if ((c > 127) && (c < 2048)) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                } else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        }

        var x = Array();
        var k, AA, BB, CC, DD, a, b, c, d;
        var S11 = 7, S12 = 12, S13 = 17, S14 = 22;
        var S21 = 5, S22 = 9, S23 = 14, S24 = 20;
        var S31 = 4, S32 = 11, S33 = 16, S34 = 23;
        var S41 = 6, S42 = 10, S43 = 15, S44 = 21;
        string = md5_Utf8Encode(string);
        x = md5_ConvertToWordArray(string);
        a = 0x67452301;
        b = 0xEFCDAB89;
        c = 0x98BADCFE;
        d = 0x10325476;

        for (k = 0; k < x.length; k += 16) {
            AA = a;
            BB = b;
            CC = c;
            DD = d;
            a = md5_FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
            d = md5_FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
            c = md5_FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
            b = md5_FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
            a = md5_FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
            d = md5_FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
            c = md5_FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
            b = md5_FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
            a = md5_FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
            d = md5_FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
            c = md5_FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
            b = md5_FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
            a = md5_FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
            d = md5_FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
            c = md5_FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
            b = md5_FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
            a = md5_GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
            d = md5_GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
            c = md5_GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
            b = md5_GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
            a = md5_GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
            d = md5_GG(d, a, b, c, x[k + 10], S22, 0x2441453);
            c = md5_GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
            b = md5_GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
            a = md5_GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
            d = md5_GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
            c = md5_GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
            b = md5_GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
            a = md5_GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
            d = md5_GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
            c = md5_GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
            b = md5_GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
            a = md5_HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
            d = md5_HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
            c = md5_HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
            b = md5_HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
            a = md5_HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
            d = md5_HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
            c = md5_HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
            b = md5_HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
            a = md5_HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
            d = md5_HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
            c = md5_HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
            b = md5_HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
            a = md5_HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
            d = md5_HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
            c = md5_HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
            b = md5_HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
            a = md5_II(a, b, c, d, x[k + 0], S41, 0xF4292244);
            d = md5_II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
            c = md5_II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
            b = md5_II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
            a = md5_II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
            d = md5_II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
            c = md5_II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
            b = md5_II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
            a = md5_II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
            d = md5_II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
            c = md5_II(c, d, a, b, x[k + 6], S43, 0xA3014314);
            b = md5_II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
            a = md5_II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
            d = md5_II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
            c = md5_II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
            b = md5_II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
            a = md5_AddUnsigned(a, AA);
            b = md5_AddUnsigned(b, BB);
            c = md5_AddUnsigned(c, CC);
            d = md5_AddUnsigned(d, DD);
        }
        var temp = md5_WordToHex(a) + md5_WordToHex(b) + md5_WordToHex(c) + md5_WordToHex(d);
        return temp.toLowerCase();
    }

    // 更新二级分类
    function updateChildCategories(parentId) {
        $('#categoryChild').empty().append('<option value="">请选择二级分类</option>');

        if (!parentId) {
            return;
        }

        // 查找与选中的一级分类相关的二级分类
        const childCategories = categoriesList.filter(cat => cat.parent_id === parentId);

        if (childCategories.length > 0) {
            childCategories.forEach(cat => {
                $('#categoryChild').append(`<option value="${cat.id}">${cat.name}</option>`);
            });
        } else {
            $('#categoryChild').append('<option value="" disabled>暂无二级分类</option>');
        }
    }

    // 更新价格计算
    function updatePriceCalculation() {
        const basePrice = parseFloat($('#basePrice').val()) || 0;
        const templateId = $('#priceTemplate').val();

        if (!basePrice || !templateId) {
            $('#priceCalculation').addClass('d-none');
            return;
        }

        // 获取价格模板详情
        const template = priceTemplateList.find(t => t.id === templateId);

        if (!template) {
            $('#priceCalculation').addClass('d-none');
            return;
        }

        // 显示计算区域
        $('#priceCalculation').removeClass('d-none');

        // 设置基础价格
        $('#previewBasePrice').text(`¥${basePrice.toFixed(2)}`);

        // 设置模板类型
        const typeText = template.type === '1' ? '固定金额加价' : '百分比加价';
        $('#templateType').text(typeText);

        // 渲染会员价格
        $('#memberPrices').html(renderMemberPrices(basePrice, template));
    }

    // 添加参数行
    function addParamRow() {
        $('#paramContainer').append(`
                <div class="parameter-row">
                    <button type="button" class="btn btn-light remove-param">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                    <div class="row">
                        <div class="form-group">
                            <label class="form-label">参数名称</label>
                            <input type="text" class="form-control param-name" placeholder="如：规格、颜色" style="border-color: var(--border-color);">
                        </div>
                        <div class="form-group">
                            <label class="form-label">参数提示</label>
                            <input type="text" class="form-control param-tip" placeholder="如：请选择规格" style="border-color: var(--border-color);">
                        </div>
                    </div>
                </div>
            `);

        // 绑定删除按钮事件
        $('.remove-param').off('click').on('click', function () {
            $(this).closest('.parameter-row').fadeOut(300, function () {
                $(this).remove();
            });
        });
    }

    // 关闭模态框并重置状态
    function closeProductModal() {
        // 隐藏模态框
        $('#productModal').css('display', 'none');

        // 重置商品类型显示状态，确保下次打开时能正确显示
        $('.type-card').css('display', '').removeClass('mobile-hidden');
    }

    // 绑定模态框事件
    function bindModalEvents() {
        // 绑定模态框关闭按钮
        $('.btn-close').off('click').on('click', function () {
            closeProductModal();
        });



        // 绑定模态框背景点击关闭
        $('#productModal').off('click').on('click', function (e) {
            if ($(e.target).is('#productModal')) {
                closeProductModal();
            }
        });

        // 绑定ESC键关闭模态框
        $(document).off('keydown.modal').on('keydown.modal', function (e) {
            if (e.key === 'Escape' && $('#productModal').css('display') === 'block') {
                closeProductModal();
            }
        });

        // 绑定取消按钮
        $('.modal-footer .btn-light').off('click').on('click', function () {
            closeProductModal();
        });


        // 标签页切换
        $('.nav-link').off('click').on('click', function (e) {
            e.preventDefault();

            $('.nav-link').removeClass('active');
            $(this).addClass('active');

            const target = $(this).data('bs-target');
            $('.tab-pane').removeClass('show active');
            $(target).addClass('show active');
        });

        // 商品类型选择事件
        $('.type-card').off('click').on('click', function () {
            const $this = $(this);
            const productType = $this.data('type');

            // 如果是对接商品且已经有locked类，则不允许更改
            if (productType === 3 && $this.hasClass('locked')) {
                // 如果是对接商品且已被锁定，则不允许更改
                showNotification('对接商品类型不可更改', 'error');
                return;
            }

            // 移除所有卡片的选中状态
            $('.type-card').removeClass('selected');
            // 添加当前卡片的选中状态
            $this.addClass('selected');

            // 根据类型显示/隐藏卡库选择
            if (productType === 1) {
                $('#cardLibraryContainer').slideDown(300);
            } else {
                $('#cardLibraryContainer').slideUp(300);
            }
        });

        // 绑定添加参数按钮事件
        $('#addParamBtn').off('click').on('click', function () {
            addParamRow();
        });

        // 文件上传预览和自动上传
        $('#productImage').off('change').on('change', function (e) {
            const file = e.target.files[0];
            if (file) {
                // 显示上传中状态
                $('#imagePreview').html(`
                            <div style="text-align: center; padding: 20px;">
                                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--primary-color);"></i>
                                <div style="margin-top: 10px; color: var(--text-secondary);">正在上传图片...</div>
                            </div>
                        `);

                const reader = new FileReader();
                reader.onload = function (e) {
                    // 立即上传图片到服务器
                    uploadImageToServer(e.target.result);
                };
                reader.readAsDataURL(file);
            }
        });

        // 拖拽上传
        const fileUpload = $('.custom-file-upload')[0];
        if (fileUpload) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                fileUpload.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                fileUpload.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                fileUpload.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                fileUpload.style.borderColor = 'var(--primary-color)';
                fileUpload.style.backgroundColor = '#fff5f9';
                fileUpload.style.boxShadow = '0 0 10px rgba(255, 126, 185, 0.3)';
            }

            function unhighlight() {
                fileUpload.style.borderColor = 'var(--border-color)';
                fileUpload.style.backgroundColor = '#fff9fc';
                fileUpload.style.boxShadow = 'none';
            }

            fileUpload.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length) {
                    const file = files[0];
                    // 检查文件类型
                    if (file.type.startsWith('image/')) {
                        $('#productImage')[0].files = files;
                        $('#productImage').trigger('change');
                    } else {
                        showNotification('请选择图片文件', 'error');
                    }
                }
            }
        }

        // 绑定保存按钮事件
        $('#saveProductBtn').off('click').on('click', function () {
            saveProduct();
        });

        // 绑定一级分类变化事件
        $('#categoryParent').off('change').on('change', function () {
            const parentId = $(this).val();
            updateChildCategories(parentId);
        });

        // 绑定后置操作类型变化事件
        $('#postActionType').off('change').on('change', function () {
            const actionType = $(this).val();
            if (actionType === 'url') {
                $('#urlConfigSection').slideDown(300);
            } else {
                $('#urlConfigSection').slideUp(300);
            }
        });

        // 绑定无限库存复选框事件
        $('#unlimitedStock').off('change').on('change', function () {
            const isChecked = $(this).is(':checked');
            const $stockInput = $('#productStock');

            if (isChecked) {
                $stockInput.prop('disabled', true);
                $stockInput.css('background-color', '#f5f5f5');
                $stockInput.css('color', '#999');
                $stockInput.val('');
            } else {
                $stockInput.prop('disabled', false);
                $stockInput.css('background-color', '');
                $stockInput.css('color', '');
            }
        });

        // 绑定请求方式按钮事件
        $('.method-btn').off('click').on('click', function () {
            const $this = $(this);
            const method = $this.data('method');

            // 移除所有按钮的active状态
            $('.method-btn').removeClass('active').css({
                'background-color': 'white',
                'color': 'var(--text-secondary)',
                'border-color': 'var(--border-color)'
            });

            // 添加当前按钮的active状态
            $this.addClass('active').css({
                'background-color': 'var(--primary-color)',
                'color': 'white',
                'border-color': 'var(--primary-color)'
            });

            // 更新隐藏字段的值
            $('#postActionMethod').val(method);

            // 根据请求方式显示/隐藏请求体区域
            const $requestBodySection = $('#requestBodySection');
            if (method === 'GET') {
                // GET请求不需要请求体，隐藏请求体区域
                $requestBodySection.slideUp(300);
            } else if (method === 'POST') {
                // POST请求需要请求体，显示请求体区域
                $requestBodySection.slideDown(300);
            }
        });

        // 绑定添加请求头按钮事件
        $('#addHeaderBtn').off('click').on('click', function () {
            addHeaderRow();
        });

        // 绑定变量信息展示切换事件
        $('.variable-info-toggle').off('click').on('click', function () {
            const $content = $('#variableInfoContent');
            const $icon = $('#variableToggleIcon');

            if ($content.is(':visible')) {
                $content.slideUp(300);
                $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            } else {
                $content.slideDown(300);
                $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            }
        });

        // 优化的变量按钮事件绑定 - 支持contenteditable元素
        $('.variable-btn').off('click.variable').on('click.variable', function () {
            const variable = $(this).data('variable');
            const variableFormat = `<${variable}>`;
            insertTextAtCursorContentEditable('#postActionBody', variableFormat);
        });

        // 优化的签名内容变量按钮事件绑定 - 支持contenteditable元素
        $('.signature-variable-btn').off('click.variable').on('click.variable', function () {
            const variable = $(this).data('variable');
            const variableFormat = `<${variable}>`;
            insertTextAtCursorContentEditable('#signatureContent', variableFormat);
        });

        // 优化的变量高亮事件绑定 - 使用防抖处理
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 创建防抖的高亮函数
        const debouncedHighlight = debounce(function (element) {
            highlightVariables(element);
        }, 300);

        // 绑定变量高亮显示事件 - 支持contenteditable元素
        $('#postActionUrl, #postActionBody, #signatureContent').off('input.highlight').on('input.highlight', function () {
            debouncedHighlight(this);
        });

        // 为动态添加的请求头输入框绑定变量高亮事件
        $(document).off('input.highlight', '.header-key, .header-value').on('input.highlight', '.header-key, .header-value', function () {
            debouncedHighlight(this);
        });

        // 优化的contenteditable元素初始化
        $('.editable-content').each(function () {
            const $this = $(this);
            const placeholder = $this.data('placeholder');

            // 清理初始内容，移除旧的placeholder span
            const initialContent = $this.html().replace(/<span class="placeholder"[^>]*>.*?<\/span>/g, '').trim();
            if (initialContent === '' || initialContent === '<br>' || initialContent === '<div><br></div>') {
                $this.empty();
            } else {
                $this.html(initialContent);
                // 如果有内容，立即进行变量高亮
                highlightVariables(this);
            }
        });
    }

    // 优化的变量高亮显示函数 - 支持contenteditable元素
    function highlightVariables(element) {
        try {
            // 保存当前光标位置
            const selection = window.getSelection();
            let cursorPosition = 0;
            let shouldRestoreCursor = false;

            if (selection.rangeCount > 0 && selection.focusNode) {
                const range = selection.getRangeAt(0);
                if (element.contains(selection.focusNode)) {
                    cursorPosition = getCursorPosition(element);
                    shouldRestoreCursor = true;
                }
            }

            // 获取纯文本内容
            const text = element.textContent || element.innerText || '';

            // 如果没有内容，直接返回
            if (!text.trim()) {
                return;
            }

            const variableRegex = /<([^>]+)>/g;

            // 检查是否包含变量
            if (!variableRegex.test(text)) {
                return;
            }

            // 重置正则表达式
            variableRegex.lastIndex = 0;

            // 构建新的HTML内容
            let newHtml = '';
            let lastIndex = 0;
            let match;

            while ((match = variableRegex.exec(text)) !== null) {
                // 添加变量前的普通文本
                if (match.index > lastIndex) {
                    newHtml += escapeHtml(text.substring(lastIndex, match.index));
                }

                // 添加高亮的变量
                newHtml += `<span class="variable-highlight">&lt;${escapeHtml(match[1])}&gt;</span>`;

                lastIndex = match.index + match[0].length;
            }

            // 添加剩余的普通文本
            if (lastIndex < text.length) {
                newHtml += escapeHtml(text.substring(lastIndex));
            }

            // 更新元素内容
            element.innerHTML = newHtml;

            // 恢复光标位置
            if (shouldRestoreCursor) {
                setCursorPosition(element, cursorPosition);
            }

        } catch (e) {
        }
    }

    // 获取光标在contenteditable元素中的位置
    function getCursorPosition(element) {
        const selection = window.getSelection();
        if (selection.rangeCount === 0) return 0;

        const range = selection.getRangeAt(0);
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(element);
        preCaretRange.setEnd(range.endContainer, range.endOffset);

        return preCaretRange.toString().length;
    }

    // 设置光标在contenteditable元素中的位置
    function setCursorPosition(element, position) {
        try {
            const selection = window.getSelection();
            const range = document.createRange();

            let currentPos = 0;
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            let node;
            while (node = walker.nextNode()) {
                const nodeLength = node.textContent.length;
                if (currentPos + nodeLength >= position) {
                    range.setStart(node, position - currentPos);
                    range.collapse(true);
                    break;
                }
                currentPos += nodeLength;
            }

            // 如果没有找到合适的位置，设置到末尾
            if (!range.startContainer) {
                range.selectNodeContents(element);
                range.collapse(false);
            }

            selection.removeAllRanges();
            selection.addRange(range);
        } catch (e) {
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 移除旧的光标位置恢复函数，已被新的实现替代

    // 更新变量提示函数
    function updateVariableHints(element, text) {
        const variableRegex = /<([^>]+)>/g;
        const matches = [...text.matchAll(variableRegex)];
        const elementId = $(element).attr('id');

        // 移除现有的提示
        $(`#${elementId}-hints`).remove();

        if (matches.length > 0) {
            const variables = matches.map(match => match[1]);
            const uniqueVariables = [...new Set(variables)];

            const hintsHtml = `
                    <div id="${elementId}-hints" class="variable-hints" style="margin-top: 5px; padding: 8px; background-color: rgba(255, 126, 185, 0.1); border-radius: 4px; font-size: 12px; color: var(--primary-dark);">
                        <i class="fas fa-info-circle me-1"></i>
                        检测到变量: ${uniqueVariables.map(v => `<span class="variable-tag">${v}</span>`).join(' ')}
                    </div>
                `;

            $(element).after(hintsHtml);
        }
    }

    // 辅助函数：设置元素覆盖层
    function setElementOverlay($element, overlayHtml) {
        if ($element.parent().css('position') !== 'relative') {
            $element.parent().css('position', 'relative');
        }

        // 添加覆盖层
        $element.after(overlayHtml);

        // 确保输入框在覆盖层之上
        $element.css({
            'position': 'relative',
            'z-index': '2',
            'background': 'transparent'
        });
    }

    // 在光标位置插入文本的函数 - 用于textarea
    function insertTextAtCursor(selector, text) {
        const element = $(selector)[0];
        if (element.selectionStart || element.selectionStart === 0) {
            const startPos = element.selectionStart;
            const endPos = element.selectionEnd;
            element.value = element.value.substring(0, startPos) + text + element.value.substring(endPos, element.value.length);
            element.selectionStart = startPos + text.length;
            element.selectionEnd = startPos + text.length;
            element.focus();
        } else {
            element.value += text;
            element.focus();
        }
    }

    // 优化的在contenteditable元素的光标位置插入文本
    function insertTextAtCursorContentEditable(selector, text) {
        try {
            const element = $(selector)[0];
            if (!element) return;

            element.focus();

            // 获取选择和范围
            const selection = window.getSelection();
            if (selection.getRangeAt && selection.rangeCount) {
                const range = selection.getRangeAt(0);
                range.deleteContents();

                // 创建文本节点并插入
                const textNode = document.createTextNode(text);
                range.insertNode(textNode);

                // 移动光标到插入文本的末尾
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);

                // 延迟触发高亮处理，避免干扰用户输入
                setTimeout(() => {
                    highlightVariables(element);
                }, 100);
            } else {
                // 如果没有选择，则追加到末尾
                const currentText = element.textContent || '';
                element.textContent = currentText + text;

                // 设置光标到末尾
                const range = document.createRange();
                range.selectNodeContents(element);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);

                // 延迟触发高亮处理
                setTimeout(() => {
                    highlightVariables(element);
                }, 100);
            }
        } catch (e) {
        }
    }

    // 优化的添加请求头行函数 - 使用新的placeholder机制
    function addHeaderRow(key = '', value = '') {
        const headerRowHtml = `
                <div class="header-row mb-2" style="display: flex; gap: 10px; align-items: center; animation: slideIn 0.3s ease-out;">
                    <div style="flex: 0 0 30%;">
                        <div class="editable-content header-key" contenteditable="true" data-placeholder="请求头名称" style="min-height: 38px;">${key}</div>
                    </div>
                    <div style="flex: 1;">
                        <div class="editable-content header-value" contenteditable="true" data-placeholder="请求头值" style="min-height: 38px;">${value}</div>
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-header-btn" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

        $('#headersContainer').append(headerRowHtml);

        // 为新添加的contenteditable元素绑定变量高亮事件
        const $newRow = $('#headersContainer .header-row:last');
        $newRow.find('.editable-content').each(function () {
            const $this = $(this);

            // 如果有初始内容，进行变量高亮处理
            if ($this.text().trim()) {
                highlightVariables(this);
            }

            // 绑定变量高亮事件
            $this.off('input.highlight').on('input.highlight', function () {
                debouncedHighlight(this);
            });
        });

        // 绑定删除按钮事件
        $('.remove-header-btn').off('click').on('click', function () {
            $(this).closest('.header-row').fadeOut(200, function () {
                $(this).remove();
            });
        });
    }



    // 绑定保存按钮事件并修复加载状态问题
    function bindSaveButtonEvent() {
        // 此函数已被合并到bindModalEvents中
    }

    // 保存商品
    function saveProduct() {
        const $btn = $('#saveProductBtn');
        const $spinner = $('#saveSpinner');
        const $text = $('#saveButtonText');

        // 验证表单
        if (!validateProductForm()) {
            return false;
        }

        // 显示加载状态
        $spinner.show();
        $text.text('保存中...');
        $btn.prop('disabled', true);
        $btn.css('opacity', '0.7');

        // 获取表单数据
        let productData;
        try {
            productData = collectFormData();
            console.log('收集到的表单数据:', productData);
        } catch (error) {
            console.error('收集表单数据时出错:', error);
            // 恢复按钮状态
            $spinner.hide();
            $text.text('保存');
            $btn.prop('disabled', false);
            $btn.css('opacity', '1');
            showNotification('表单数据收集失败，请检查输入内容', 'error');
            return;
        }

        // 构建请求数据
        let data;
        try {
            data = {
                ...productData,
                sign: generateSign(productData)
            };
            console.log('构建的请求数据:', data);
        } catch (error) {
            console.error('构建请求数据时出错:', error);
            // 恢复按钮状态
            $spinner.hide();
            $text.text('保存');
            $btn.prop('disabled', false);
            $btn.css('opacity', '1');
            showNotification('数据处理失败，请重试', 'error');
            return;
        }

        // 发送保存请求
        $.ajax({
            url: isEditing ? '/api/UpdateGoods' : '/api/AddGoods',
            type: 'POST',
            dataType: 'json',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (response) {
                // 恢复按钮状态
                $spinner.hide();
                $text.text('保存');
                $btn.prop('disabled', false);
                $btn.css('opacity', '1');

                if (response.code === 200) {
                    // 显示成功通知
                    showNotification(isEditing ? '商品修改成功' : '商品添加成功', 'success');

                    // 关闭模态框并重置状态
                    closeProductModal();

                    // 刷新商品列表
                    getGoodsList(currentPage, pageSize);
                } else {
                    showNotification(response.msg || '保存失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                // 恢复按钮状态
                $spinner.hide();
                $text.text('保存');
                $btn.prop('disabled', false);
                $btn.css('opacity', '1');

                showNotification('网络错误，请稍后再试', 'error');
            }
        });
    }

    // 收集表单数据
    function collectFormData() {
        const productType = $('.type-card.selected').data('type');

        const data = {
            name: $('#productName').val().trim(),
            price: $('#basePrice').val().trim(),
            image: $('#imagePreview').data('image-url') || '',
            PriceTemplate: $('#priceTemplate').val(),
            type: productType.toString(),
            info: $('#productInfo').val().trim()
        };

        // 如果是编辑模式，添加ID
        if (isEditing && currentProductId) {
            data.id = currentProductId;
        }

        // 根据商品类型添加特定字段
        if (productType === 1) {
            // 卡密商品，添加卡库ID
            data.card_library = $('#cardLibrary').val();
        } else if (productType === 3) {
            // 对接商品，保留对接信息
            // 注意：对接商品的信息由后端填充和管理
        }

        // 收集参数
        const attach = [];
        $('.parameter-row').each(function () {
            const paramName = $(this).find('.param-name').val().trim();
            const paramTip = $(this).find('.param-tip').val().trim();

            if (paramName && paramTip) {
                attach.push({
                    name: paramName,
                    tip: paramTip
                });
            }
        });

        data.attach = attach;

        // 设置分类ID (使用二级分类的ID)
        data.category_id = $('#categoryChild').val() || $('#categoryParent').val();

        // 处理库存字段
        const isUnlimitedStock = $('#unlimitedStock').is(':checked');
        data.stock = isUnlimitedStock ? 99999 : parseInt($('#productStock').val()) || 0;

        // 处理弹窗公告字段
        data.notice = $('#productPopupTip').val().trim();

        // 处理后置操作字段
        const postActionType = $('#postActionType').val();
        if (postActionType === 'none') {
            data.order_operation = {
                type: 0
            };
        } else if (postActionType === 'url') {
            // 收集请求头数据
            const headers = {};
            $('.header-row').each(function () {
                const key = $(this).find('.header-key').text().trim();
                const value = $(this).find('.header-value').text().trim();
                if (key && value) {
                    headers[key] = value;
                }
            });

            // 获取请求方式
            const requestMethod = $('input[name="requestMethod"]:checked').val() === 'POST' ? 1 : 0;

            // 获取签名配置
            const signBody = $('#signContent').text().trim();
            const encryptMethod = parseInt($('#encryptMethod').val()) || 1;
            const outputMethod = parseInt($('#outputMethod').val()) || 1;

            data.order_operation = {
                type: 1,
                data: {
                    url: $('#postActionUrl').text().trim(),
                    requestMethod: requestMethod,
                    header: headers,
                    body: $('#postActionBody').text().trim(),
                    sign: {
                        body: $('#signatureContent').text().trim(),
                        encryptMethod: encryptMethod,
                        outputMethod: outputMethod
                    }
                }
            };
        } else {
            // 默认无后置操作
            data.order_operation = {
                type: 0
            };
        }

        return data;
    }

    // 表单验证函数
    function validateProductForm() {
        let isValid = true;

        // 重置所有验证状态
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        // 获取商品类型
        const productType = $('.type-card.selected').data('type');

        // 验证商品名称
        const productName = $('#productName').val().trim();
        if (!productName) {
            isValid = false;
            $('#productName').addClass('is-invalid');
            $('#productName').after('<div class="invalid-feedback show">请输入商品名称</div>');
        }

        // 根据商品类型进行特定验证
        if (productType === 1) {
            // 卡密商品：验证卡库选择
            const cardLibrary = $('#cardLibrary').val();
            if (!cardLibrary) {
                isValid = false;
                $('#cardLibrary').addClass('is-invalid');
                $('#cardLibrary').after('<div class="invalid-feedback show">请选择卡库</div>');
            }
        } else if (productType === 3) {
            // 对接商品：特殊处理，不需要验证卡库
        }

        // 验证商品分类
        const categoryParent = $('#categoryParent').val();
        if (!categoryParent) {
            isValid = false;
            $('#categoryParent').addClass('is-invalid');
            $('#categoryParent').after('<div class="invalid-feedback show">请选择一级分类</div>');
        }

        // 验证二级分类
        const categoryChild = $('#categoryChild').val();
        if (!categoryChild) {
            isValid = false;
            $('#categoryChild').addClass('is-invalid');
            $('#categoryChild').after('<div class="invalid-feedback show">请选择二级分类</div>');
        }

        // 验证基础价格
        const basePrice = $('#basePrice').val();
        if (!basePrice || isNaN(parseFloat(basePrice)) || parseFloat(basePrice) <= 0) {
            isValid = false;
            $('#basePrice').addClass('is-invalid');
            $('#basePrice').after('<div class="invalid-feedback show">请输入有效的基础价格</div>');
        }

        // 验证价格模板
        const priceTemplate = $('#priceTemplate').val();
        if (!priceTemplate) {
            isValid = false;
            $('#priceTemplate').addClass('is-invalid');
            $('#priceTemplate').after('<div class="invalid-feedback show">请选择价格模板</div>');
        }

        // 验证参数行
        $('.parameter-row').each(function (index) {
            const $row = $(this);
            const paramName = $row.find('.param-name').val().trim();
            const paramTip = $row.find('.param-tip').val().trim();

            if (!paramName) {
                isValid = false;
                $row.find('.param-name').addClass('is-invalid');
                $row.find('.param-name').after('<div class="invalid-feedback show">请输入参数名称</div>');
            }

            if (!paramTip) {
                isValid = false;
                $row.find('.param-tip').addClass('is-invalid');
                $row.find('.param-tip').after('<div class="invalid-feedback show">请输入参数提示</div>');
            }
        });

        // 如果表单无效，显示错误通知
        if (!isValid) {
            showNotification('请完善必填信息', 'error');

            // 自动滚动到第一个错误字段
            const $firstError = $('.is-invalid').first();
            if ($firstError.length) {
                const tabId = $firstError.closest('.tab-pane').attr('id');
                if (tabId) {
                    $(`a[href="#${tabId}"]`).tab('show');
                }

                // 滚动到错误字段
                const modalBody = document.querySelector('.modal-body');
                const errorTop = $firstError.offset().top - $(modalBody).offset().top + modalBody.scrollTop;
                modalBody.scrollTo({
                    top: errorTop - 100,
                    behavior: 'smooth'
                });
            }
        }

        return isValid;
    }

    // -------- 辅助函数和依赖检查 --------

    // 检查并加载依赖
    (function loadDependencies() {

        // 检查jQuery是否已加载
        if (typeof jQuery === 'undefined') {
            var jqueryScript = document.createElement('script');
            jqueryScript.src = "https://g.alicdn.com/code/lib/jquery/3.6.0/jquery.min.js";
            jqueryScript.onload = function () {
                // jQuery加载完成后检查Bootstrap
                if (typeof bootstrap === 'undefined') {
                    var bootstrapScript = document.createElement('script');
                    bootstrapScript.src = "https://g.alicdn.com/code/lib/bootstrap/5.3.0/js/bootstrap.bundle.min.js";
                    bootstrapScript.onload = function () {
                        // 所有依赖加载完成，初始化页面
                        initializeAfterDependencies();
                    };
                    document.body.appendChild(bootstrapScript);
                } else {
                    initializeAfterDependencies();
                }
            };
            document.body.appendChild(jqueryScript);
        } else {
            // 检查Bootstrap是否已加载
            if (typeof bootstrap === 'undefined') {
                var bootstrapScript = document.createElement('script');
                bootstrapScript.src = "https://g.alicdn.com/code/lib/bootstrap/5.3.0/js/bootstrap.bundle.min.js";
                bootstrapScript.onload = function () {
                    initializeAfterDependencies();
                };
                document.body.appendChild(bootstrapScript);
            } else {
                initializeAfterDependencies();
            }
        }

        // 等待所有依赖加载完成后初始化页面
        function initializeAfterDependencies() {
            // 在单页面应用中，直接初始化，不依赖DOMContentLoaded
            if (typeof initializeApp === 'function') {
                initializeApp();
            } else {
            }
        }
    })();

    // 增强版依赖检查
    function checkCoreDependencies() {
        const errors = [];
        if (typeof jQuery === 'undefined') {
            errors.push('jQuery');
        }
        if (typeof bootstrap === 'undefined') {
            errors.push('Bootstrap');
        }
        return errors;
    }

    // 基础依赖检查
    function checkDependencies() {
        const errors = [];

        // 检查jQuery（同时检查jQuery和$符号）
        if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
            errors.push('jQuery');
        }

        // 检查Bootstrap
        if (typeof bootstrap === 'undefined') {
            errors.push('Bootstrap');
        }

        // 如果有缺失的依赖，显示详细错误信息
        if (errors.length > 0) {
            console.error('商品列表页面缺少必要依赖库：', errors);
            console.error('请确保在admin-framework.html中正确加载了以下库：');
            errors.forEach(dep => {
                console.error(`- ${dep}`);
            });
        }

        return errors;
    }

    // 处理库加载错误
    function handleLibraryError() {
        // 显示友好的错误消息
        const errorMsg = document.createElement('div');
        errorMsg.style.padding = '20px';
        errorMsg.style.color = 'red';
        errorMsg.style.textAlign = 'center';
        errorMsg.style.fontSize = '16px';
        errorMsg.innerHTML = `
                <h3>依赖库加载失败</h3>
                <p>页面加载所需的依赖库(jQuery/Bootstrap)失败。</p>
                <p>可能的解决方案:</p>
                <ul style="list-style-type:none;padding:0">
                    <li>1. 检查网络连接</li>
                    <li>2. 刷新页面重试</li>
                    <li>3. 清除浏览器缓存</li>
                </ul>
                <button onclick="location.reload()" style="margin-top:10px;padding:8px 15px;background:#ff7eb9;color:white;border:none;border-radius:5px;cursor:pointer">刷新页面</button>
            `;

        // 清空页面内容并显示错误
        const container = document.querySelector('#product-container') || document.body;
        container.innerHTML = '';
        container.appendChild(errorMsg);
    }

    // 重构初始化流程
    async function initializeApp() {

        // 检查基础依赖
        const missingDeps = checkDependencies();
        if (missingDeps.length > 0) {
            console.error('初始化失败：缺少必要依赖库', missingDeps);

            // 显示用户友好的错误信息
            const container = document.getElementById('product-container');
            if (container) {
                container.innerHTML = `
                    <div style="padding: 40px; text-align: center; color: #e74c3c; background: #fff;">
                        <h3><i class="fas fa-exclamation-triangle"></i> 页面加载失败</h3>
                        <p>缺少必要的依赖库：<strong>${missingDeps.join(', ')}</strong></p>
                        <p>请联系管理员检查系统配置</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; background: #ff6b9d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            重新加载页面
                        </button>
                    </div>
                `;
            }
            return;
        }

        // 添加全局动画CSS
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                    @keyframes rowFadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                    .products-table tbody tr {
                        animation: rowFadeIn 0.5s ease forwards;
                    }
                    .products-table tbody tr:nth-child(1) { animation-delay: 0.05s; }
                    .products-table tbody tr:nth-child(2) { animation-delay: 0.1s; }
                    .products-table tbody tr:nth-child(3) { animation-delay: 0.15s; }
                    .products-table tbody tr:nth-child(4) { animation-delay: 0.2s; }
                    .products-table tbody tr:nth-child(5) { animation-delay: 0.25s; }
                    .products-table tbody tr:nth-child(6) { animation-delay: 0.3s; }
                    .products-table tbody tr:nth-child(7) { animation-delay: 0.35s; }
                    .products-table tbody tr:nth-child(8) { animation-delay: 0.4s; }
                    .products-table tbody tr:nth-child(9) { animation-delay: 0.45s; }
                    .products-table tbody tr:nth-child(10) { animation-delay: 0.5s; }
                `)
            .appendTo('head');

        // 初始化页面组件
        try {
            // 加载中动画
            $('#loading-overlay').fadeIn(300);

            await initPage();

            await Promise.all([
                fetchCategoryList(),
                fetchPriceTemplateList(),
                fetchCardLibraryList()
            ]);

            await getGoodsList(1, pageSize || 20);

            // 初始化表格操作按钮事件
            bindTableActions();

            // 确保按钮的加载状态正确
            $('#saveSpinner').hide();

            // 隐藏加载中动画
            $('#loading-overlay').fadeOut(300);

            // 添加欢迎提示
            setTimeout(() => {
                showNotification("商品管理系统加载完成", "success");
            }, 500);


            // 为table添加鼠标悬停效果
            $('.products-table tbody').on('mouseenter', 'tr', function () {
                $(this).css('transform', 'translateY(-2px)');
                $(this).css('box-shadow', '0 4px 8px rgba(255, 158, 210, 0.15)');
                $(this).css('transition', 'all 0.3s ease');
            }).on('mouseleave', 'tr', function () {
                $(this).css('transform', '');
                $(this).css('box-shadow', '');
            });

        } catch (e) {
            $('#loading-overlay').fadeOut(300);
            showNotification("系统初始化失败，请刷新页面", "error");
        }
    }

    // 刷新按钮旋转动画
    $(document).on('click', '#refreshBtn', function (event) {
        const icon = $(this).find('i');
        icon.css({
            'transition': 'transform 0.5s ease',
            'transform': 'rotate(360deg)'
        });

        setTimeout(() => {
            icon.css('transform', '');
        }, 500);
    });

    // 自定义函数 - 渲染会员价格优化版
    function renderMemberPrices(basePrice, template) {
        basePrice = parseFloat(basePrice) || 0;

        if (!template || !template.data) {
            return '<div class="text-muted" style="font-style: italic;">未选择价格模板</div>';
        }

        let html = '';

        // 根据模板类型计算不同会员等级的价格
        if (template.type === '1') { // 固定金额加价
            Object.keys(template.data).forEach(level => {
                const addition = parseFloat(template.data[level]) || 0;
                const finalPrice = basePrice + addition;

                html += `
                    <div class="mb-2" style="padding: 5px; border-radius: 8px; transition: all 0.3s ease;">
                        <span class="text-muted">${getLevelName(level)}：</span>
                        <span class="price">¥${finalPrice.toFixed(2)}</span>
                        <small class="text-muted" style="font-size: 0.8rem;">(+${addition.toFixed(2)})</small>
                    </div>`;
            });
        } else { // 百分比加价
            Object.keys(template.data).forEach(level => {
                const percentage = parseFloat(template.data[level]) || 0;
                const addition = basePrice * (percentage / 100);
                const finalPrice = basePrice + addition;

                html += `
                    <div class="mb-2" style="padding: 5px; border-radius: 8px; transition: all 0.3s ease;">
                        <span class="text-muted">${getLevelName(level)}：</span>
                        <span class="price">¥${finalPrice.toFixed(2)}</span>
                        <small class="text-muted" style="font-size: 0.8rem;">(+${percentage}%)</small>
                    </div>`;
            });
        }

        return html;
    }

    // 不再直接在DOMContentLoaded事件中调用初始化函数
    // 而是由loadDependencies函数在依赖加载完成后调用

    // 确保全局可访问
    window.initializeApp = initializeApp;

})(); // IIFE结束

// ========== 搜索筛选功能 ==========

// 获取Cookie值的辅助函数
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 统一的搜索分类数据加载函数
function loadSearchCategories() {

    // 检查是否已有分类数据，如果有则直接使用
    if (window.categoriesList && window.categoriesList.length > 0) {
        populateSearchCategoryFilters();
        return;
    }

    // 调用API获取分类列表
    $.ajax({
        url: '/api/get_category_list',
        type: 'GET',
        dataType: 'json',
        success: function (response) {

            if (response.code === 200) {
                const categories = response.data || [];

                // 使用与模态框相同的数据结构
                if (!window.categoriesList) {
                    window.categoriesList = [];
                }

                // 清空现有数据
                window.categoriesList = [];

                // 遍历并处理分类数据
                categories.forEach(parent => {
                    // 添加一级分类到分类列表数组
                    window.categoriesList.push({
                        id: parent.id,
                        name: parent.name,
                        level: parent.level,
                        parent_id: parent.parent_id || ''
                    });

                    // 处理二级分类
                    if (parent.children && parent.children.length > 0) {
                        parent.children.forEach(child => {
                            window.categoriesList.push({
                                id: child.id,
                                name: child.name,
                                level: child.level,
                                parent_id: child.parent_id || parent.id
                            });
                        });
                    }
                });


                // 填充搜索筛选下拉框
                populateSearchCategoryFilters();

            } else {
            }
        },
        error: function (xhr, status, error) {
        }
    });
}

// 填充搜索筛选区域的分类下拉框
function populateSearchCategoryFilters() {

    // 填充一级分类下拉框
    const primarySelect = document.getElementById('primaryCategoryFilter');
    if (primarySelect && window.categoriesList) {
        primarySelect.innerHTML = '<option value="">全部分类</option>';

        // 获取所有一级分类
        const primaryCategories = window.categoriesList.filter(cat => cat.level === 1);
        primaryCategories.forEach(cat => {
            primarySelect.innerHTML += `<option value="${cat.id}">${cat.name}</option>`;
        });

    }

    // 初始化二级分类下拉框
    const secondarySelect = document.getElementById('secondaryCategoryFilter');
    if (secondarySelect) {
        secondarySelect.innerHTML = '<option value="">全部分类</option>';
    }
}

// 更新搜索筛选区域的二级分类下拉框
function updateSecondaryCategories(primaryCategoryId) {

    const secondarySelect = document.getElementById('secondaryCategoryFilter');
    if (!secondarySelect) {
        return;
    }

    // 清空二级分类选项
    secondarySelect.innerHTML = '<option value="">全部分类</option>';

    if (primaryCategoryId && window.categoriesList) {
        // 查找与选中的一级分类相关的二级分类
        const secondaryCategories = window.categoriesList.filter(
            category => category.level === 2 && category.parent_id == primaryCategoryId
        );


        if (secondaryCategories.length > 0) {
            secondaryCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                secondarySelect.appendChild(option);
            });
        } else {
            // 如果没有二级分类，添加提示选项
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '暂无二级分类';
            option.disabled = true;
            secondarySelect.appendChild(option);
        }
    } else {
    }
}

// 获取当前搜索参数
function getCurrentSearchParams() {
    const keywordEl = document.getElementById('searchKeyword');
    const primaryCategoryEl = document.getElementById('primaryCategoryFilter');
    const secondaryCategoryEl = document.getElementById('secondaryCategoryFilter');
    const productTypeEl = document.getElementById('productTypeFilter');
    const productStatusEl = document.getElementById('productStatusFilter');

    return {
        keyword: keywordEl ? keywordEl.value.trim() : '',
        primary_category: primaryCategoryEl ? primaryCategoryEl.value : '',
        secondary_category: secondaryCategoryEl ? secondaryCategoryEl.value : '',
        product_type: productTypeEl ? productTypeEl.value : '',
        product_status: productStatusEl ? productStatusEl.value : ''
    };
}

// 执行搜索
function performSearch() {

    try {
        const searchParams = getCurrentSearchParams();

        // 验证搜索参数
        if (!searchParams) {
            if (typeof window.showNotification === 'function') {
                window.showNotification('获取搜索参数失败', 'error');
            }
            return;
        }

        // 检查getGoodsList函数是否存在（优先使用全局引用）
        const getGoodsListFunc = window.getGoodsList || getGoodsList;
        if (typeof getGoodsListFunc === 'function') {
            // 获取分页大小（优先使用全局引用）
            const currentPageSize = window.pageSize || pageSize || 20;

            // 重置到第一页并执行搜索
            if (typeof window.currentPage !== 'undefined') {
                window.currentPage = 1;
            } else if (typeof currentPage !== 'undefined') {
                currentPage = 1;
            }
            getGoodsListFunc(1, currentPageSize, searchParams);

        } else {
            if (typeof window.showNotification === 'function') {
                window.showNotification('搜索功能初始化失败，请刷新页面', 'error');
            }
        }
    } catch (error) {
        if (typeof window.showNotification === 'function') {
            window.showNotification('搜索执行失败: ' + error.message, 'error');
        }
    }
}

// 重置搜索条件
function resetSearchFilters() {
    try {
        const keywordEl = document.getElementById('searchKeyword');
        const primaryCategoryEl = document.getElementById('primaryCategoryFilter');
        const secondaryCategoryEl = document.getElementById('secondaryCategoryFilter');
        const productTypeEl = document.getElementById('productTypeFilter');
        const productStatusEl = document.getElementById('productStatusFilter');

        // 重置所有搜索条件
        if (keywordEl) keywordEl.value = '';
        if (primaryCategoryEl) primaryCategoryEl.value = '';
        if (secondaryCategoryEl) secondaryCategoryEl.value = '';
        if (productTypeEl) productTypeEl.value = '';
        if (productStatusEl) productStatusEl.value = '';

        // 重新加载商品列表
        const getGoodsListFunc = window.getGoodsList || getGoodsList;
        if (typeof getGoodsListFunc === 'function') {
            const currentPageSize = window.pageSize || pageSize || 20;
            getGoodsListFunc(1, currentPageSize, {});
        }

        // 显示重置成功提示
        if (typeof showNotification === 'function') {
            showNotification('搜索条件已重置', 'success');
        }

    } catch (error) {
        console.error('重置搜索条件失败:', error);
        if (typeof showNotification === 'function') {
            showNotification('重置失败: ' + error.message, 'error');
        }
    }
}

// 重置搜索条件（保留原函数用于兼容性）
function resetSearch() {

    try {
        const keywordEl = document.getElementById('searchKeyword');
        const primaryCategoryEl = document.getElementById('primaryCategoryFilter');
        const secondaryCategoryEl = document.getElementById('secondaryCategoryFilter');
        const productTypeEl = document.getElementById('productTypeFilter');
        const productStatusEl = document.getElementById('productStatusFilter');

        // 重置所有搜索条件
        if (keywordEl) keywordEl.value = '';
        if (primaryCategoryEl) primaryCategoryEl.value = '';
        if (secondaryCategoryEl) secondaryCategoryEl.value = '';
        if (productTypeEl) productTypeEl.value = '';
        if (productStatusEl) productStatusEl.value = '';

        // 重置二级分类
        updateSecondaryCategories('');


        // 重新加载商品列表
        const getGoodsListFunc = window.getGoodsList || getGoodsList;
        if (typeof getGoodsListFunc === 'function') {
            // 获取分页大小（优先使用全局引用）
            const currentPageSize = window.pageSize || pageSize || 20;

            // 重置到第一页
            if (typeof window.currentPage !== 'undefined') {
                window.currentPage = 1;
            } else if (typeof currentPage !== 'undefined') {
                currentPage = 1;
            }

            getGoodsListFunc(1, currentPageSize, {});
        } else {
            if (typeof window.showNotification === 'function') {
                window.showNotification('重置功能异常，请刷新页面', 'error');
            }
        }
    } catch (error) {
        if (typeof window.showNotification === 'function') {
            window.showNotification('重置失败: ' + error.message, 'error');
        }
    }
}

// 初始化搜索功能
function initializeSearchFeatures() {

    // 防止重复初始化
    if (window.searchFeaturesInitialized) {
        return;
    }

    // 获取搜索筛选区域的HTML元素
    const searchKeyword = document.getElementById('searchKeyword');
    const primaryCategoryFilter = document.getElementById('primaryCategoryFilter');
    const secondaryCategoryFilter = document.getElementById('secondaryCategoryFilter');
    const productTypeFilter = document.getElementById('productTypeFilter');
    const productStatusFilter = document.getElementById('productStatusFilter');
    const searchBtn = document.getElementById('searchBtn');
    const resetBtn = document.getElementById('resetBtn');


    // 如果关键元素不存在，尝试延迟初始化一次
    if (!searchKeyword || !primaryCategoryFilter || !searchBtn) {
        setTimeout(function () {
            if (!window.searchFeaturesInitialized) {
                initializeSearchFeatures();
            }
        }, 500);
        return;
    }

    // 加载分类数据 - 使用统一的分类数据管理
    loadSearchCategories();

    // 绑定搜索按钮事件
    if (searchBtn) {
        searchBtn.addEventListener('click', function () {
            performSearch();
        });
    } else {
    }

    // 绑定重置按钮事件
    if (resetBtn) {
        resetBtn.addEventListener('click', function () {
            resetSearch();
        });
    } else {
    }

    // 绑定一级分类变化事件
    if (primaryCategoryFilter) {
        primaryCategoryFilter.addEventListener('change', function () {
            updateSecondaryCategories(this.value);
        });
    } else {
    }

    // 绑定回车键搜索
    if (searchKeyword) {
        searchKeyword.addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    } else {
    }

    // 标记初始化完成
    window.searchFeaturesInitialized = true;
}

// 优化的搜索功能初始化 - 确保在页面完全就绪后执行
function ensureSearchInitialization() {
    // 检查jQuery和必要的DOM元素是否就绪
    if ((typeof $ === 'undefined' && typeof jQuery === 'undefined') || !document.getElementById('searchBtn')) {
        setTimeout(ensureSearchInitialization, 500);
        return;
    }

    // 执行实际的搜索功能初始化
    performSearchInitialization();
}

// 执行实际的搜索功能初始化
function performSearchInitialization() {
    if (window.searchFeaturesInitialized) {
        return;
    }

    try {
        // 加载搜索分类数据
        loadSearchCategories();

        // 绑定搜索按钮事件
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', function(event) {
                event.preventDefault();
                performSearch();
            });
        }

        // 绑定重置按钮事件
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', function(event) {
                event.preventDefault();
                resetSearchFilters();
            });
        }

        // 绑定一级分类变更事件
        const primaryCategoryFilter = document.getElementById('primaryCategoryFilter');
        if (primaryCategoryFilter) {
            primaryCategoryFilter.addEventListener('change', function() {
                updateSecondaryCategories(this.value);
            });
        }

        // 绑定回车键搜索
        const searchKeyword = document.getElementById('searchKeyword');
        if (searchKeyword) {
            searchKeyword.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    performSearch();
                }
            });
        }

        // 标记初始化完成
        window.searchFeaturesInitialized = true;
        console.log('搜索功能初始化完成');

    } catch (error) {
        console.error('搜索功能初始化失败:', error);
    }
}

// 立即初始化搜索功能，不依赖DOMContentLoaded事件
function initializeSearchFeatures() {
    if (window.searchFeaturesInitialized) {
        return;
    }

    // 检查jQuery和必要的DOM元素是否就绪
    if ((typeof $ === 'undefined' && typeof jQuery === 'undefined') || !document.getElementById('searchBtn')) {
        console.warn('搜索功能初始化失败：jQuery未加载或搜索按钮不存在');
        return;
    }

    // 执行实际的搜索功能初始化
    performSearchInitialization();
}

// 提供全局搜索初始化函数
window.initializeProductListSearch = function () {
    initializeSearchFeatures();
};

// 也在window.onload时初始化，确保万无一失
window.addEventListener('load', function () {
    if (!window.searchFeaturesInitialized) {
        console.log('页面加载完成，尝试初始化搜索功能');
        initializeSearchFeatures();
    }
});

// 搜索功能状态检测和自动修复
function checkAndFixSearchFeatures() {

    const issues = [];

    // 检查DOM元素
    const searchKeyword = document.getElementById('searchKeyword');
    const primaryCategoryFilter = document.getElementById('primaryCategoryFilter');
    const searchBtn = document.getElementById('searchBtn');

    if (!searchKeyword) issues.push('搜索关键字输入框未找到');
    if (!primaryCategoryFilter) issues.push('一级分类下拉框未找到');
    if (!searchBtn) issues.push('搜索按钮未找到');

    // 检查分类数据
    if (!window.categoriesList || window.categoriesList.length === 0) {
        issues.push('分类数据未加载');
    }

    // 检查初始化状态
    if (!window.searchFeaturesInitialized) {
        issues.push('搜索功能未初始化');
    }

    // 检查函数是否存在
    if (typeof performSearch !== 'function') {
        issues.push('performSearch函数不存在');
    }

    if (issues.length > 0) {

        // 尝试自动修复
        if (!window.searchFeaturesInitialized && searchKeyword && primaryCategoryFilter && searchBtn) {
            initializeSearchFeatures();
        }

        return false;
    } else {
        return true;
    }
}

// 添加测试函数到全局作用域，便于调试
window.testSearchFeatures = function () {

    // 先检测状态
    const isHealthy = checkAndFixSearchFeatures();


    // 测试搜索功能
    if (typeof performSearch === 'function') {
        performSearch();
    } else {
    }
};

// 定期检查搜索功能状态
window.startSearchHealthCheck = function () {
    setInterval(function () {
        if (!window.searchFeaturesInitialized) {
            checkAndFixSearchFeatures();
        }
    }, 5000); // 每5秒检查一次
};

// 测试表格居中对齐效果
window.testTableAlignment = function () {

    const table = document.querySelector('.products-table');
    if (!table) {
        return false;
    }

    // 检查表头对齐
    const headers = table.querySelectorAll('th');
    headers.forEach((th, index) => {
        const style = window.getComputedStyle(th);
    });

    // 检查表格单元格对齐
    const cells = table.querySelectorAll('tbody td');
    if (cells.length > 0) {
        const firstRowCells = table.querySelectorAll('tbody tr:first-child td');
        firstRowCells.forEach((td, index) => {
            const style = window.getComputedStyle(td);
        });
    } else {
    }

    return true;
};

// 综合搜索功能测试
window.runSearchFunctionTest = function () {

    // 1. 检查基础状态
    const isHealthy = checkAndFixSearchFeatures();
    if (!isHealthy) {
        return false;
    }

    // 2. 测试分类数据加载
    if (!window.categoriesList || window.categoriesList.length === 0) {
        return false;
    }

    // 3. 测试一级分类下拉框
    const primarySelect = document.getElementById('primaryCategoryFilter');
    if (!primarySelect || primarySelect.options.length <= 1) {
        return false;
    }

    // 4. 测试搜索参数获取
    try {
        const searchParams = getCurrentSearchParams();
    } catch (error) {
        return false;
    }

    // 5. 测试搜索功能
    if (typeof performSearch !== 'function') {
        return false;
    }

    // 6. 测试API调用
    const getGoodsListFunc = window.getGoodsList || getGoodsList;
    if (typeof getGoodsListFunc !== 'function') {
        return false;
    }

    // 7. 测试通知函数
    const showNotificationFunc = window.showNotification || showNotification;
    if (typeof showNotificationFunc !== 'function') {
    }

    return true;
};

// 专门测试作用域问题修复的函数
window.testScopeFix = function () {

    // 测试全局函数引用

    // 测试全局变量引用

    // 测试搜索功能调用
    try {
        const searchParams = getCurrentSearchParams();

        // 模拟搜索调用（不实际执行）
        const getGoodsListFunc = window.getGoodsList || getGoodsList;
        if (typeof getGoodsListFunc === 'function') {
        } else {
        }

        const showNotificationFunc = window.showNotification || showNotification;
        if (typeof showNotificationFunc === 'function') {
        } else {
        }

        // 测试分页变量访问
        const currentPageSize = window.pageSize || pageSize || 20;

        return true;

    } catch (error) {
        return false;
    }
};

// 创建一个完全独立的初始化系统
(function () {
    // 核心初始化函数 - 直接在这里定义，不依赖外部函数
    function coreInitialization() {

        // 直接执行关键的初始化代码
        try {
            // 初始化商品管理功能
            if (typeof bindModalEvents === 'function') {
                bindModalEvents();
            }

            // 初始化搜索功能
            if (!window.searchFeaturesInitialized && typeof initializeSearchFeatures === 'function') {
                initializeSearchFeatures();
            }

            // 初始化分页功能
            if (typeof initializePagination === 'function') {
                initializePagination();
            }

            // 加载商品数据
            if (typeof loadProducts === 'function') {
                loadProducts();
            }

            return true;
        } catch (error) {
            return false;
        }
    }

    // 立即定义全局初始化函数
    window.initializeProductListPage = function () {

        if (window.productListInitialized) {
            return true;
        }

        // 检查jQuery依赖
        if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
            console.error('jQuery未加载，无法初始化商品列表页面');
            return false;
        }

        // 执行核心初始化
        const success = coreInitialization();
        if (success) {
            window.productListInitialized = true;
        }
        return success;
    };

    window.initializeProductListSearch = function () {
        return true;
    };
})();

// 最终解决方案：为单页面应用创建健壮的初始化系统
(function () {

    // 创建一个完整的初始化函数
    function performCompleteInitialization() {

        if (window.productListInitialized) {
            return true;
        }

        try {
            // 第一步：绑定基本的DOM事件（使用原生JavaScript）

            const addBtn = document.getElementById('addProductBtn');
            if (addBtn) {
                addBtn.onclick = function (e) {
                    e.preventDefault();
                    const modal = document.getElementById('productModal');
                    if (modal) {
                        modal.classList.add('active');
                    }
                };
            } else {
            }

            // 绑定模态框关闭按钮
            const closeButtons = document.querySelectorAll('.modal-close, #cancelBtn');
            closeButtons.forEach(btn => {
                btn.onclick = function (e) {
                    e.preventDefault();
                    const modal = document.getElementById('productModal');
                    if (modal) {
                        modal.classList.remove('active');
                    }
                };
            });

            // 绑定保存按钮
            const saveBtn = document.getElementById('saveProductBtn');
            if (saveBtn) {
                saveBtn.onclick = function (e) {
                    e.preventDefault();
                    // 这里可以添加保存逻辑
                };
            }

            // 第二步：如果jQuery可用，执行jQuery相关的初始化
            if (typeof jQuery !== 'undefined' || typeof $ !== 'undefined') {

                // 延迟执行jQuery相关的初始化，确保DOM完全准备好
                setTimeout(() => {
                    try {
                        // 尝试调用原有的初始化函数
                        if (typeof initializeApp === 'function') {
                            initializeApp();
                        } else {
                        }

                        // 尝试初始化搜索功能
                        if (!window.searchFeaturesInitialized && typeof initializeSearchFeatures === 'function') {
                            initializeSearchFeatures();
                        }

                        // 尝试绑定模态框事件
                        if (typeof bindModalEvents === 'function') {
                            bindModalEvents();
                        } else {
                        }

                    } catch (error) {
                    }
                }, 200); // 延迟200ms确保DOM完全准备好
            } else {
            }

            // 标记初始化完成
            window.productListInitialized = true;
            return true;

        } catch (error) {
            return false;
        }
    }

    // 创建全局初始化函数供主页面调用
    window.initializeProductListPage = function () {
        return performCompleteInitialization();
    };

    // 创建一个智能的DOM检测和初始化系统
    function smartInitialization() {
        let attempts = 0;
        const maxAttempts = 100; // 最多尝试100次（10秒）

        function checkAndInitialize() {
            attempts++;

            // 检查关键DOM元素是否存在
            const container = document.getElementById('product-container');
            const addBtn = document.getElementById('addProductBtn');
            const modal = document.getElementById('productModal');

            // 检查页面内容容器（单页面应用的内容容器）
            const pageContent = document.getElementById('page-content');

            if (container || addBtn || modal) {
                setTimeout(performCompleteInitialization, 100);
                return;
            }

            // 如果页面内容容器存在，检查其内容
            if (pageContent && pageContent.innerHTML.includes('product-container')) {
                setTimeout(performCompleteInitialization, 100);
                return;
            }

            // 如果还没有找到元素，继续尝试
            if (attempts < maxAttempts) {
                setTimeout(checkAndInitialize, 100);
            } else {
                performCompleteInitialization();
            }
        }

        // 立即开始检查
        checkAndInitialize();
    }

    // 使用MutationObserver监听DOM变化（如果支持）
    if (typeof MutationObserver !== 'undefined') {

        const observer = new MutationObserver(function (mutations) {
            let shouldInitialize = false;

            mutations.forEach(function (mutation) {
                if (mutation.type === 'childList') {
                    const addedNodes = Array.from(mutation.addedNodes);

                    // 检查是否有商品列表相关的元素被添加
                    const hasProductElements = addedNodes.some(node => {
                        if (node.nodeType === 1) { // Element node
                            const hasContainer = node.id === 'product-container' ||
                                (node.querySelector && node.querySelector('#product-container'));
                            const hasButton = node.id === 'addProductBtn' ||
                                (node.querySelector && node.querySelector('#addProductBtn'));
                            const hasModal = node.id === 'productModal' ||
                                (node.querySelector && node.querySelector('#productModal'));

                            return hasContainer || hasButton || hasModal;
                        }
                        return false;
                    });

                    if (hasProductElements) {
                        shouldInitialize = true;
                    }
                }
            });

            if (shouldInitialize) {
                observer.disconnect(); // 停止观察
                setTimeout(performCompleteInitialization, 150);
            }
        });

        // 监听整个document的变化
        observer.observe(document, {
            childList: true,
            subtree: true
        });

    }

    // 启动智能初始化系统
    smartInitialization();

})();