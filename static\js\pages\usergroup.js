// 立即执行的函数
    (function() {
        
        // 获取DOM元素
        const addBtn = document.getElementById('add-level-btn');
        const modal = document.getElementById('add-level-modal');
        const closeBtn = document.getElementById('close-modal-btn');
        const cancelBtn = document.getElementById('cancel-add-btn');
        const submitBtn = document.getElementById('submit-add-btn');
        const levelNameInput = document.getElementById('level-name');
        const addSpinner = document.getElementById('add-spinner');
        const confirmDeleteModal = document.getElementById('confirm-delete-modal');
        
        // MD5函数实现
        function md5(str) {
            // 一个标准的MD5哈希实现
            var xl;
            var rotateLeft = function(lValue, iShiftBits) {
                return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
            };
            var addUnsigned = function(lX, lY) {
                var lX4, lY4, lX8, lY8, lResult;
                lX8 = (lX & 0x80000000);
                lY8 = (lY & 0x80000000);
                lX4 = (lX & 0x40000000);
                lY4 = (lY & 0x40000000);
                lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
                if (lX4 & lY4) {
                    return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
                }
                if (lX4 | lY4) {
                    if (lResult & 0x40000000) {
                        return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                    } else {
                        return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                    }
                } else {
                    return (lResult ^ lX8 ^ lY8);
                }
            };
            var _F = function(x, y, z) {
                return (x & y) | ((~x) & z);
            };
            var _G = function(x, y, z) {
                return (x & z) | (y & (~z));
            };
            var _H = function(x, y, z) {
                return (x ^ y ^ z);
            };
            var _I = function(x, y, z) {
                return (y ^ (x | (~z)));
            };
            var _FF = function(a, b, c, d, x, s, ac) {
                a = addUnsigned(a, addUnsigned(addUnsigned(_F(b, c, d), x), ac));
                return addUnsigned(rotateLeft(a, s), b);
            };
            var _GG = function(a, b, c, d, x, s, ac) {
                a = addUnsigned(a, addUnsigned(addUnsigned(_G(b, c, d), x), ac));
                return addUnsigned(rotateLeft(a, s), b);
            };
            var _HH = function(a, b, c, d, x, s, ac) {
                a = addUnsigned(a, addUnsigned(addUnsigned(_H(b, c, d), x), ac));
                return addUnsigned(rotateLeft(a, s), b);
            };
            var _II = function(a, b, c, d, x, s, ac) {
                a = addUnsigned(a, addUnsigned(addUnsigned(_I(b, c, d), x), ac));
                return addUnsigned(rotateLeft(a, s), b);
            };
            var convertToWordArray = function(str) {
                var lWordCount;
                var lMessageLength = str.length;
                var lNumberOfWords_temp1 = lMessageLength + 8;
                var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
                var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
                var lWordArray = Array(lNumberOfWords - 1);
                var lBytePosition = 0;
                var lByteCount = 0;
                while (lByteCount < lMessageLength) {
                    lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                    lBytePosition = (lByteCount % 4) * 8;
                    lWordArray[lWordCount] = (lWordArray[lWordCount] | (str.charCodeAt(lByteCount) << lBytePosition));
                    lByteCount++;
                }
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
                lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
                lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
                return lWordArray;
            };
            var wordToHex = function(lValue) {
                var wordToHexValue = "", wordToHexValue_temp = "", lByte, lCount;
                for (lCount = 0; lCount <= 3; lCount++) {
                    lByte = (lValue >>> (lCount * 8)) & 255;
                    wordToHexValue_temp = "0" + lByte.toString(16);
                    wordToHexValue = wordToHexValue + wordToHexValue_temp.substr(wordToHexValue_temp.length - 2, 2);
                }
                return wordToHexValue;
            };
            var x = [], k, AA, BB, CC, DD, a, b, c, d, S11 = 7, S12 = 12, S13 = 17, S14 = 22, S21 = 5, S22 = 9, S23 = 14, S24 = 20, S31 = 4, S32 = 11, S33 = 16, S34 = 23, S41 = 6, S42 = 10, S43 = 15, S44 = 21;
            var utf8_encode = function(string) {
                string = string.replace(/\r\n/g, "\n");
                var utftext = "";
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    } else if ((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    } else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                }
                return utftext;
            };
            str = utf8_encode(str);
            x = convertToWordArray(str);
            a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
            xl = x.length;
            for (k = 0; k < xl; k += 16) {
                AA = a; BB = b; CC = c; DD = d;
                a = _FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
                d = _FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
                c = _FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
                b = _FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
                a = _FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
                d = _FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
                c = _FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
                b = _FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
                a = _FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
                d = _FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
                c = _FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
                b = _FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
                a = _FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
                d = _FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
                c = _FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
                b = _FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
                a = _GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
                d = _GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
                c = _GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
                b = _GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
                a = _GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
                d = _GG(d, a, b, c, x[k + 10], S22, 0x2441453);
                c = _GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
                b = _GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
                a = _GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
                d = _GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
                c = _GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
                b = _GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
                a = _GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
                d = _GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
                c = _GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
                b = _GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
                a = _HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
                d = _HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
                c = _HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
                b = _HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
                a = _HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
                d = _HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
                c = _HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
                b = _HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
                a = _HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
                d = _HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
                c = _HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
                b = _HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
                a = _HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
                d = _HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
                c = _HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
                b = _HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
                a = _II(a, b, c, d, x[k + 0], S41, 0xF4292244);
                d = _II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
                c = _II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
                b = _II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
                a = _II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
                d = _II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
                c = _II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
                b = _II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
                a = _II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
                d = _II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
                c = _II(c, d, a, b, x[k + 6], S43, 0xA3014314);
                b = _II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
                a = _II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
                d = _II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
                c = _II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
                b = _II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
                a = addUnsigned(a, AA);
                b = addUnsigned(b, BB);
                c = addUnsigned(c, CC);
                d = addUnsigned(d, DD);
            }
            var temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
            return temp.toLowerCase();
        }
        
        // Base64编码函数
        function Base64Encode(str) {
            return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
                return String.fromCharCode(parseInt(p1, 16));
            }));
        }
        
        // 显示通知
        function showToast(message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) return;
            
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                </div>
                <div class="toast-message">${message}</div>
            `;
            
            toastContainer.appendChild(toast);
            
            // 强制重绘
            toast.offsetHeight;
            
            // 显示通知
            requestAnimationFrame(() => {
                toast.classList.add('show');
            });
            
            // 自动隐藏通知
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
        
        // 直接绑定点击事件，不使用命名函数
        if (addBtn) {
            addBtn.onclick = function() {
                if (modal) {
                    modal.classList.add('show');
                    if (levelNameInput) {
                        levelNameInput.value = '';
                        levelNameInput.focus();
                    }
                }
            };
        }
        
        // 关闭和取消按钮
        if (closeBtn) {
            closeBtn.onclick = function() {
                if (modal) modal.classList.remove('show');
            };
        }
        
        if (cancelBtn) {
            cancelBtn.onclick = function() {
                if (modal) modal.classList.remove('show');
            };
        }
        
        // 递归排序对象键的函数
        function sortObjectKeys(obj) {
            // 如果不是对象或是数组，直接返回
            if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
                return obj;
            }
            
            // 创建新的有序对象
            const sortedObj = {};
            // 获取所有键并排序
            const keys = Object.keys(obj).sort();
            
            // 填充排序后的对象
            for (const key of keys) {
                sortedObj[key] = sortObjectKeys(obj[key]);
            }
            
            return sortedObj;
        }
        
        // 提交按钮
        if (submitBtn) {
            submitBtn.onclick = function() {
                try {
                    const nameInput = document.getElementById('level-name');
                    if (!nameInput || !nameInput.value.trim()) {
                        showToast('请输入会员等级名称', 'error');
                        return;
                    }
                    
                    const levelName = nameInput.value.trim();
                    
                    // 显示加载状态
                    submitBtn.disabled = true;
                    if (addSpinner) addSpinner.style.display = 'inline-block';
                    
                    // 1. 准备请求数据对象（不含sign字段）
                    const requestData = {
                        name: levelName
                    };
                    
                    // 确保对象键按ASCII码排序
                    const sortedData = sortObjectKeys(requestData);
                    
                    // 2. 序列化为JSON字符串
                    const jsonStr = JSON.stringify(sortedData);
                    
                    // 3. Base64编码
                    const base64Str = Base64Encode(jsonStr);
                    
                    // 4. 计算MD5
                    const sign = md5(base64Str);
                    
                    // 5. 添加签名到请求数据
                    requestData.sign = sign;
                    
                    // 发送请求
                    fetch('/api/AddMemberLevel', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => response.json())
                    .then(result => {
                        
                        if (result.code === 200) {
                            showToast('会员等级添加成功', 'success');
                            if (modal) modal.classList.remove('show');
                            // 刷新页面显示新数据
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showToast('添加失败：' + (result.msg || '未知错误'), 'error');
                        }
                    })
                    .catch(error => {
                        showToast('添加会员等级出错', 'error');
                    })
                    .finally(() => {
                        // 恢复按钮状态
                        submitBtn.disabled = false;
                        if (addSpinner) addSpinner.style.display = 'none';
                    });
                } catch (error) {
                    showToast('处理请求时出错', 'error');
                    submitBtn.disabled = false;
                    if (addSpinner) addSpinner.style.display = 'none';
                }
            };
        }
        
        // 加载会员等级列表
        function loadMembershipLevels() {
            const membershipListBody = document.getElementById('membership-list-body');
            const emptyState = document.getElementById('empty-state');
            
            if (!membershipListBody) return;
            
            try {
                fetch('/api/GetMemberLevelList', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        const levels = result.data || [];
                        
                        if (levels.length === 0) {
                            membershipListBody.innerHTML = '';
                            if (emptyState) emptyState.style.display = 'block';
                            return;
                        }
                        
                        if (emptyState) emptyState.style.display = 'none';
                        membershipListBody.innerHTML = '';
                        
                        levels.forEach(level => {
                            const row = document.createElement('tr');
                            row.className = 'fade-in';
                            
                            row.innerHTML = `
                                <td>${level.id}</td>
                                <td>${level.name}</td>
                                <td>
                                    <div class="level-actions">
                                        <button class="action-btn delete-btn" data-id="${level.id}" data-name="${level.name}" title="删除">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            `;
                            
                            membershipListBody.appendChild(row);
                            
                            // 为删除按钮添加事件
                            const deleteBtn = row.querySelector('.delete-btn');
                            if (deleteBtn) {
                                deleteBtn.onclick = function() {
                                    const levelId = this.getAttribute('data-id');
                                    const levelName = this.getAttribute('data-name');
                                    showDeleteConfirm(levelId, levelName);
                                };
                            }
                        });
                    } else {
                        showToast('获取会员等级列表失败：' + (result.msg || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    showToast('获取会员等级列表时出错', 'error');
                });
            } catch (error) {
                showToast('加载会员等级列表出错', 'error');
            }
        }
        
        // 显示删除确认模态框
        function showDeleteConfirm(levelId, levelName) {
            const deleteModal = document.getElementById('confirm-delete-modal');
            const deleteLevelName = document.getElementById('delete-level-name');
            const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
            
            if (deleteLevelName) deleteLevelName.textContent = levelName || '';
            if (deleteModal) deleteModal.classList.add('show');
            
            // 设置确认删除按钮的点击事件
            if (confirmDeleteBtn) {
                // 移除之前的事件处理程序
                const newBtn = confirmDeleteBtn.cloneNode(true);
                confirmDeleteBtn.parentNode.replaceChild(newBtn, confirmDeleteBtn);
                
                // 添加新的事件处理程序
                newBtn.onclick = function() {
                    deleteLevel(levelId, levelName);
                };
            }
        }
        
        // 删除会员等级
        function deleteLevel(levelId, levelName) {
            const deleteModal = document.getElementById('confirm-delete-modal');
            const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
            const deleteSpinner = document.getElementById('delete-spinner');
            
            if (deleteSpinner) deleteSpinner.style.display = 'inline-block';
            if (confirmDeleteBtn) confirmDeleteBtn.disabled = true;
            
            try {
                // 1. 准备请求数据对象（不含sign字段）
                const requestData = {
                    id: levelId
                };
                
                // 确保对象键按ASCII码排序
                const sortedData = sortObjectKeys(requestData);
                
                // 2. 序列化为JSON字符串
                const jsonStr = JSON.stringify(sortedData);
                
                // 3. Base64编码
                const base64Str = Base64Encode(jsonStr);
                
                // 4. 计算MD5
                const sign = md5(base64Str);
                
                // 5. 添加签名到请求数据
                requestData.sign = sign;
                
                // 发送请求
                fetch('/api/DelMemberLevel', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(result => {
                    
                    if (result.code === 200) {
                        showToast(`会员等级"${levelName}"已删除`, 'success');
                        if (deleteModal) deleteModal.classList.remove('show');
                        // 刷新页面显示新数据
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showToast('删除失败：' + (result.msg || '未知错误'), 'error');
                    }
                })
                .catch(error => {
                    showToast('删除会员等级出错', 'error');
                })
                .finally(() => {
                    // 恢复按钮状态
                    if (confirmDeleteBtn) confirmDeleteBtn.disabled = false;
                    if (deleteSpinner) deleteSpinner.style.display = 'none';
                });
            } catch (error) {
                showToast('处理删除请求时出错', 'error');
                if (confirmDeleteBtn) confirmDeleteBtn.disabled = false;
                if (deleteSpinner) deleteSpinner.style.display = 'none';
            }
        }
        
        // 绑定关闭删除模态框按钮
        const closeConfirmBtn = document.getElementById('close-confirm-modal-btn');
        const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
        
        if (closeConfirmBtn) {
            closeConfirmBtn.onclick = function() {
                if (confirmDeleteModal) confirmDeleteModal.classList.remove('show');
            };
        }
        
        if (cancelDeleteBtn) {
            cancelDeleteBtn.onclick = function() {
                if (confirmDeleteModal) confirmDeleteModal.classList.remove('show');
            };
        }
        
        // 初始化加载
        loadMembershipLevels();
        
        // 暴露调试功能到全局
        window.debugUtils = {
            showModal: function() {
                if (modal) {
                    modal.classList.add('show');
                } else {
                }
            },
            testMD5: function(text) {
                // 测试会员等级添加计算
                const requestData = {name: text};
                
                // 确保对象键按ASCII码排序
                const sortedData = sortObjectKeys(requestData);
                
                // 序列化为JSON字符串
                const jsonStr = JSON.stringify(sortedData);
                
                // Base64编码 - 使用正确的编码函数
                const base64Str = Base64Encode(jsonStr);
                
                // 计算MD5
                const sign = md5(base64Str);
                
                return sign;
            }
        };
        
    })();