// MD5哈希函数实现
        function MD5(string) {
            function RotateLeft(lValue, iShiftBits) {
                return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
            }
            
            function AddUnsigned(lX, lY) {
                var lX4, lY4, lX8, lY8, lResult;
                lX8 = (lX & 0x80000000);
                lY8 = (lY & 0x80000000);
                lX4 = (lX & 0x40000000);
                lY4 = (lY & 0x40000000);
                lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
                if (lX4 & lY4) {
                    return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
                }
                if (lX4 | lY4) {
                    if (lResult & 0x40000000) {
                        return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                    } else {
                        return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                    }
                } else {
                    return (lResult ^ lX8 ^ lY8);
                }
            }
            
            function F(x, y, z) { return (x & y) | ((~x) & z); }
            function G(x, y, z) { return (x & z) | (y & (~z)); }
            function H(x, y, z) { return (x ^ y ^ z); }
            function I(x, y, z) { return (y ^ (x | (~z))); }
            
            function FF(a, b, c, d, x, s, ac) {
                a = AddUnsigned(a, AddUnsigned(AddUnsigned(F(b, c, d), x), ac));
                return AddUnsigned(RotateLeft(a, s), b);
            }
            
            function GG(a, b, c, d, x, s, ac) {
                a = AddUnsigned(a, AddUnsigned(AddUnsigned(G(b, c, d), x), ac));
                return AddUnsigned(RotateLeft(a, s), b);
            }
            
            function HH(a, b, c, d, x, s, ac) {
                a = AddUnsigned(a, AddUnsigned(AddUnsigned(H(b, c, d), x), ac));
                return AddUnsigned(RotateLeft(a, s), b);
            }
            
            function II(a, b, c, d, x, s, ac) {
                a = AddUnsigned(a, AddUnsigned(AddUnsigned(I(b, c, d), x), ac));
                return AddUnsigned(RotateLeft(a, s), b);
            }
            
            function ConvertToWordArray(string) {
                var lWordCount;
                var lMessageLength = string.length;
                var lNumberOfWords_temp1 = lMessageLength + 8;
                var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
                var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
                var lWordArray = Array(lNumberOfWords - 1);
                var lBytePosition = 0;
                var lByteCount = 0;
                while (lByteCount < lMessageLength) {
                    lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                    lBytePosition = (lByteCount % 4) * 8;
                    lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition));
                    lByteCount++;
                }
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
                lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
                lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
                return lWordArray;
            }
            
            function WordToHex(lValue) {
                var WordToHexValue = "", WordToHexValue_temp = "", lByte, lCount;
                for (lCount = 0; lCount <= 3; lCount++) {
                    lByte = (lValue >>> (lCount * 8)) & 255;
                    WordToHexValue_temp = "0" + lByte.toString(16);
                    WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
                }
                return WordToHexValue;
            }
            
            function Utf8Encode(string) {
                string = string.replace(/\r\n/g, "\n");
                var utftext = "";
                
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    } else if ((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    } else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                }
                
                return utftext;
            }
            
            var x = Array();
            var k, AA, BB, CC, DD, a, b, c, d;
            var S11 = 7, S12 = 12, S13 = 17, S14 = 22;
            var S21 = 5, S22 = 9, S23 = 14, S24 = 20;
            var S31 = 4, S32 = 11, S33 = 16, S34 = 23;
            var S41 = 6, S42 = 10, S43 = 15, S44 = 21;
            
            string = Utf8Encode(string);
            
            x = ConvertToWordArray(string);
            
            a = 0x67452301;
            b = 0xEFCDAB89;
            c = 0x98BADCFE;
            d = 0x10325476;
            
            for (k = 0; k < x.length; k += 16) {
                AA = a;
                BB = b;
                CC = c;
                DD = d;
                a = FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
                d = FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
                c = FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
                b = FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
                a = FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
                d = FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
                c = FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
                b = FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
                a = FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
                d = FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
                c = FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
                b = FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
                a = FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
                d = FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
                c = FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
                b = FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
                a = GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
                d = GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
                c = GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
                b = GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
                a = GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
                d = GG(d, a, b, c, x[k + 10], S22, 0x2441453);
                c = GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
                b = GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
                a = GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
                d = GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
                c = GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
                b = GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
                a = GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
                d = GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
                c = GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
                b = GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
                a = HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
                d = HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
                c = HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
                b = HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
                a = HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
                d = HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
                c = HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
                b = HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
                a = HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
                d = HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
                c = HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
                b = HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
                a = HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
                d = HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
                c = HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
                b = HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
                a = II(a, b, c, d, x[k + 0], S41, 0xF4292244);
                d = II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
                c = II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
                b = II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
                a = II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
                d = II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
                c = II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
                b = II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
                a = II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
                d = II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
                c = II(c, d, a, b, x[k + 6], S43, 0xA3014314);
                b = II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
                a = II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
                d = II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
                c = II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
                b = II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
                a = AddUnsigned(a, AA);
                b = AddUnsigned(b, BB);
                c = AddUnsigned(c, CC);
                d = AddUnsigned(d, DD);
            }
            
            var temp = WordToHex(a) + WordToHex(b) + WordToHex(c) + WordToHex(d);
            
            return temp.toLowerCase();
        }
        
        // 定义全局API URL，确保在所有函数中可访问
        const API_URL = {
            categories: '/user/api/categories/',
            getUser: '/user/api/GetUser/'
        };
        
        // 打印API URL以确认定义
        
        // 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');
        
        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // 切换菜单图标
            const menuIcon = this.querySelector('.fa-solid');
            if (menuIcon.classList.contains('fa-bars')) {
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-xmark');
            } else {
                menuIcon.classList.remove('fa-xmark');
                menuIcon.classList.add('fa-bars');
            }
        });
        
        // 点击导航链接时关闭移动菜单
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 768) {
                    navMenu.classList.remove('active');
                    const menuIcon = mobileMenuBtn.querySelector('.fa-solid');
                    menuIcon.classList.remove('fa-xmark');
                    menuIcon.classList.add('fa-bars');
                }
            });
        });
        
        // 点击页面其他区域关闭菜单
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !e.target.closest('.nav-menu') && 
                !e.target.closest('.mobile-menu-btn') && 
                navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                const menuIcon = mobileMenuBtn.querySelector('.fa-solid');
                menuIcon.classList.remove('fa-xmark');
                menuIcon.classList.add('fa-bars');
            }
        });
        
        // 分类数据加载和处理
        document.addEventListener('DOMContentLoaded', function() {
            
            // DOM元素引用
            const primaryCategoryList = document.getElementById('primaryCategoryList');
            const primaryLoadingState = document.getElementById('primaryLoadingState');
            const secondaryCategoryContainer = document.getElementById('secondaryCategoryContainer');
            const secondaryEmptyState = document.getElementById('secondaryEmptyState');
            const secondaryLoadingState = document.getElementById('secondaryLoadingState');
            const primaryCategoryDropdownBtn = document.getElementById('primaryCategoryDropdownBtn');
            const primaryCategoryWrapper = document.getElementById('primaryCategoryWrapper');
            const primaryCategoryInlineGrid = document.getElementById('primaryCategoryInlineGrid');
            
            // 验证关键DOM元素是否存在
            const criticalElements = {
                primaryCategoryList,
                primaryLoadingState,
                secondaryCategoryContainer,
                secondaryEmptyState,
                primaryCategoryWrapper,
                primaryCategoryInlineGrid
            };
            
            // 检查关键DOM元素
            let missingElements = [];
            for (const [name, element] of Object.entries(criticalElements)) {
                if (!element) {
                    missingElements.push(name);
                }
            }
            
            if (missingElements.length > 0) {
                alert(`页面加载错误：缺少关键元素 (${missingElements.join(', ')})，请刷新页面重试。`);
                return; // 阻止脚本继续执行
            } else {
            }
            
            // 存储分类数据的变量
            let categoriesData = [];
            let activeCategoryId = null;
            
            // 用户认证相关函数
            // 从localStorage获取Token
            function getTokenFromStorage() {
                return localStorage.getItem('token');
            }
            
            // 从Cookie获取Token
            function getTokenFromCookie() {
                const name = "token=";
                const decodedCookie = decodeURIComponent(document.cookie);
                const cookieArray = decodedCookie.split(';');
                for(let i = 0; i < cookieArray.length; i++) {
                    let cookie = cookieArray[i].trim();
                    if (cookie.indexOf(name) === 0) {
                        return cookie.substring(name.length, cookie.length);
                    }
                }
                return null;
            }
            
            // 保存Token到localStorage
            function saveTokenToStorage(token) {
                if (token) {
                    localStorage.setItem('token', token);
                }
            }
            
            // 保存用户信息到localStorage
            function saveUserInfo(userInfo) {
                if (userInfo) {
                    localStorage.setItem('userId', userInfo.id);
                    localStorage.setItem('userKey', userInfo.user_key);
                    // 可以根据需要保存其他用户信息
                }
            }
            
            // 检查用户是否已登录
            function checkUserLogin() {
                // 先检查Cookie中是否有token
                const cookieToken = getTokenFromCookie();
                if (cookieToken) {
                    return true;
                }
                
                // 如果Cookie中没有，再检查localStorage
                const token = getTokenFromStorage();
                if (!token) {
                    return false;
                }
                return true;
            }
            
            // 处理需要认证的链接点击
            function handleAuthenticatedLink(e, targetUrl) {
                e.preventDefault();
                
                if (checkUserLogin()) {
                    // 已登录，跳转到目标页面
                    window.location.href = targetUrl;
                } else {
                    // 未登录，跳转到登录页面
                    window.location.href = '/login/';
                }
            }
            
            // 获取用户信息
            function getUserInfo() {
                // 准备请求配置
                const requestOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                // 先检查Cookie中是否有token
                const cookieToken = getTokenFromCookie();
                if (cookieToken) {
                    requestOptions.headers['Token'] = cookieToken;
                } else {
                    // 如果Cookie中没有，再检查localStorage
                    const token = getTokenFromStorage();
                    if (token) {
                        requestOptions.headers['Token'] = token;
                        
                        // 将localStorage中的token同步到cookie，以确保一致性
                        document.cookie = `token=${token}; path=/`;
                    }
                }
                
                // 发送请求到正确的API路径
                fetch('/user/api/GetUser/', requestOptions)
                    .then(response => {
                        // 检查响应状态
                        if (!response.ok) {
                            throw new Error(`HTTP错误! 状态码: ${response.status}`);
                        }
                        
                        // 检查内容类型，确保是JSON
                        const contentType = response.headers.get('content-type');
                        if (!contentType || !contentType.includes('application/json')) {
                            throw new Error('返回的数据不是JSON格式!');
                        }
                        
                        // 保存新的Token（如果存在）
                        const newToken = response.headers.get('Token');
                        if (newToken) {
                            saveTokenToStorage(newToken);
                        }
                        
                        return response.json();
                    })
                    .then(data => {
                        if (data.code === 200 && data.user) {
                            // 保存用户信息
                            saveUserInfo(data.user);
                        } else {
                        }
                    })
                    .catch(error => {
                    });
            }
            
            // 页面加载时执行用户认证
            // getUserInfo();
            
            // 调用用户信息和分类加载函数
            // 使用setTimeout确保DOM完全加载和渲染
            setTimeout(function() {
                getUserInfoAndLoadCategories();
            }, 100);
            
            // 处理个人中心链接点击
            const profileLink = document.getElementById('profileLink');
            if (profileLink) {
                profileLink.addEventListener('click', function(e) {
                    handleAuthenticatedLink(e, '/profile/');
                });
            }
            
            // 存储展开状态
            let isExpanded = false;

            // 点击分类下拉按钮
            if (primaryCategoryDropdownBtn) {
                primaryCategoryDropdownBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    toggleCategoryView();
                });
            }

            // 点击页面其他区域关闭展开状态
            document.addEventListener('click', function(e) {
                if (isExpanded &&
                    !e.target.closest('.primary-category-wrapper') &&
                    !e.target.closest('.primary-category-dropdown-btn')) {
                    collapseCategoryView();
                }
            });
            
            // 切换分类视图（展开/收起）
            function toggleCategoryView() {
                if (isExpanded) {
                    collapseCategoryView();
                } else {
                    expandCategoryView();
                }
            }

            // 展开分类视图
            function expandCategoryView() {
                if (!primaryCategoryWrapper || !primaryCategoryInlineGrid) {
                    return;
                }

                isExpanded = true;

                // 添加按钮展开状态和动画
                primaryCategoryDropdownBtn.classList.add('expanded');

                // 触发容器展开动画
                primaryCategoryWrapper.style.animation = 'expandContainer 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards';
                primaryCategoryWrapper.classList.add('expanded');

                // 延迟渲染网格内容，让容器动画先开始
                setTimeout(() => {
                    renderInlineGrid(categoriesData);
                }, 100);
            }

            // 收起分类视图
            function collapseCategoryView() {
                if (!primaryCategoryWrapper) {
                    return;
                }

                isExpanded = false;

                // 移除按钮展开状态
                primaryCategoryDropdownBtn.classList.remove('expanded');

                // 触发容器收起动画
                primaryCategoryWrapper.style.animation = 'collapseContainer 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards';

                // 延迟移除expanded类，让动画完成
                setTimeout(() => {
                    primaryCategoryWrapper.classList.remove('expanded');
                    primaryCategoryWrapper.style.animation = '';
                }, 500);
            }

            // 渲染内嵌网格
            function renderInlineGrid(categories) {
                if (!primaryCategoryInlineGrid) {
                    return;
                }

                // 清空网格
                primaryCategoryInlineGrid.innerHTML = '';

                // 添加所有分类项
                if (categories && categories.length > 0) {
                    categories.forEach((category, index) => {
                        const gridItem = document.createElement('div');
                        gridItem.className = 'primary-category-inline-grid-item';
                        if (category.id === activeCategoryId) {
                            gridItem.classList.add('active');
                        }
                        gridItem.setAttribute('data-category-id', category.id);

                        // 创建图片容器
                        const imageContainer = document.createElement('div');
                        imageContainer.className = 'primary-category-inline-image';

                        // 创建图片元素
                        if (category.image && category.image.trim() !== '') {
                            const img = document.createElement('img');
                            img.src = category.image;
                            img.alt = category.name;
                            img.loading = 'lazy'; // 懒加载

                            // 图片加载错误处理
                            img.onerror = function() {
                                imageContainer.classList.add('no-image');
                                imageContainer.innerHTML = '<i class="fa-solid fa-image"></i>';
                            };

                            imageContainer.appendChild(img);
                        } else {
                            // 没有图片时显示默认图标
                            imageContainer.classList.add('no-image');
                            imageContainer.innerHTML = '<i class="fa-solid fa-image"></i>';
                        }

                        // 创建文本容器
                        const textContainer = document.createElement('div');
                        textContainer.className = 'primary-category-inline-text';
                        textContainer.textContent = category.name;

                        // 将图片和文本添加到网格项
                        gridItem.appendChild(imageContainer);
                        gridItem.appendChild(textContainer);

                        // 创建波浪式级联动画延迟
                        const row = Math.floor(index / 3); // 假设3列布局
                        const col = index % 3;
                        const delay = (row * 100) + (col * 50) + 150; // 行延迟更大，列延迟较小
                        gridItem.style.animationDelay = `${delay}ms`;

                        // 点击事件
                        gridItem.addEventListener('click', () => {
                            selectPrimaryCategory(category.id);
                            collapseCategoryView(); // 选择后自动收起
                        });

                        primaryCategoryInlineGrid.appendChild(gridItem);
                    });
                }
            }
            

            
            // 获取用户信息并加载分类
            function getUserInfoAndLoadCategories() {
                
                // 检查DOM元素是否已加载
                if (!primaryCategoryList || !primaryLoadingState) {
                    return;
                }
                
                // 准备请求配置
                const requestOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                // 添加Token到请求头
                const token = localStorage.getItem('token') || getTokenFromCookie();
                if (token) {
                    requestOptions.headers['Token'] = token;
                } else {
                }
                
                
                // 发送请求获取用户信息
                fetch(API_URL.getUser, requestOptions)
                    .then(response => {
                        
                        // 保存新的Token（如果存在）
                        const newToken = response.headers.get('Token');
                        if (newToken) {
                            saveTokenToStorage(newToken);
                            document.cookie = `token=${newToken}; path=/`;
                        }
                        return response.json();
                    })
                    .then(data => {
                        
                        if (data.code === 200 && data.user) {
                            // 保存用户信息
                            saveUserInfo(data.user);
                            // 用户信息获取成功后加载分类
                            loadPrimaryCategories();
                        } else {
                            showError(primaryCategoryList, '用户认证失败，请重新登录');
                        }
                    })
                    .catch(error => {
                        showError(primaryCategoryList, '用户认证失败，请重新登录');
                    });
            }
            

            
            // 渲染一级分类列表
            function renderPrimaryCategories(categories) {
                // 隐藏加载状态
                primaryLoadingState.style.display = 'none';
                
                // 清空列表
                const currentItems = primaryCategoryList.querySelectorAll('.primary-category-item');
                currentItems.forEach(item => item.remove());
                
                // 添加所有分类项
                if (categories && categories.length > 0) {
                    categories.forEach((category, index) => {
                        const categoryItem = document.createElement('div');
                        categoryItem.className = 'primary-category-item';
                        categoryItem.setAttribute('data-category-id', category.id);
                        categoryItem.textContent = category.name;
                        
                        // 添加延迟进入动画
                        categoryItem.style.opacity = '0';
                        categoryItem.style.transform = 'translateX(-10px)';
                        
                        // 点击事件：加载二级分类
                        categoryItem.addEventListener('click', () => {
                            selectPrimaryCategory(category.id);
                        });
                        
                        primaryCategoryList.appendChild(categoryItem);
                        
                        // 应用动画
                        setTimeout(() => {
                            categoryItem.style.transition = 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)';
                            categoryItem.style.opacity = '1';
                            categoryItem.style.transform = 'translateX(0)';
                        }, index * 50);
                        
                        // 默认选中第一个分类
                        if (index === 0) {
                            setTimeout(() => {
                                selectPrimaryCategory(category.id, categoryItem);
                            }, categories.length * 50 + 100);
                        }
                    });
                } else {
                    showError(primaryCategoryList, '没有可用的分类');
                }
            }
            
            // 选择一级分类
            function selectPrimaryCategory(categoryId, categoryItem = null) {
                // 如果没有传入分类项元素，则查找
                if (!categoryItem) {
                    categoryItem = primaryCategoryList.querySelector(`[data-category-id="${categoryId}"]`);
                }
                
                // 如果是小屏幕，将选中的分类滚动到可视区域
                if (window.innerWidth <= 576 && categoryItem) {
                    const containerRect = primaryCategoryList.getBoundingClientRect();
                    const itemRect = categoryItem.getBoundingClientRect();
                    
                    if (itemRect.left < containerRect.left || itemRect.right > containerRect.right) {
                        primaryCategoryList.scrollTo({
                            left: categoryItem.offsetLeft - containerRect.width / 2 + itemRect.width / 2,
                            behavior: 'smooth'
                        });
                    }
                }
                
                // 移除所有激活状态
                const allItems = primaryCategoryList.querySelectorAll('.primary-category-item');
                allItems.forEach(item => item.classList.remove('active'));
                
                // 添加当前激活状态
                if (categoryItem) {
                    categoryItem.classList.add('active');
                }
                
                // 更新当前激活的分类ID
                activeCategoryId = categoryId;
                
                // 加载对应的二级分类
                loadSecondaryCategories(categoryId);
            }
            
            // 加载二级分类
            function loadSecondaryCategories(categoryId) {
                // 显示加载状态，隐藏空状态
                secondaryEmptyState.style.display = 'none';
                secondaryLoadingState.style.display = 'flex';
                
                // 清空之前的内容
                const currentSections = secondaryCategoryContainer.querySelectorAll('.secondary-category-section');
                currentSections.forEach(section => section.remove());
                
                // 从已加载的分类数据中查找当前分类
                const selectedCategory = categoriesData.find(cat => cat.id === categoryId);
                
                // 如果找到当前分类，渲染其子分类
                if (selectedCategory) {
                    setTimeout(() => {
                        renderSecondaryCategories(categoryId, selectedCategory.name, selectedCategory.children || []);
                    }, 300); // 保留短暂延迟以展示加载效果
                } else {
                    // 如果未找到，显示错误
                    secondaryLoadingState.style.display = 'none';
                    showError(secondaryCategoryContainer, '未找到分类数据');
                }
            }
            
            // 渲染二级分类
            function renderSecondaryCategories(categoryId, categoryName, subcategories) {
                // 隐藏加载状态
                secondaryLoadingState.style.display = 'none';
                
                if (!subcategories || subcategories.length === 0) {
                    secondaryEmptyState.style.display = 'flex';
                    return;
                }
                
                // 创建分类区域
                const sectionElement = document.createElement('div');
                sectionElement.className = 'secondary-category-section';
                sectionElement.id = `category-${categoryId}`;
                
                // 添加标题
                const titleElement = document.createElement('h3');
                titleElement.className = 'secondary-category-title';
                titleElement.textContent = categoryName;
                sectionElement.appendChild(titleElement);
                
                // 创建网格容器
                const gridElement = document.createElement('div');
                gridElement.className = 'secondary-category-grid';
                
                // 添加所有子分类项
                subcategories.forEach((subcategory, index) => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'secondary-category-item';
                    itemElement.setAttribute('data-subcategory-id', subcategory.id);
                    
                    // 设置动画延迟
                    itemElement.style.animationDelay = `${index * 50}ms`;
                    
                    // 图标
                    const iconElement = document.createElement('img');
                    iconElement.className = 'secondary-category-icon';
                    iconElement.src = subcategory.image || 'https://via.placeholder.com/100';
                    iconElement.alt = subcategory.name;
                    iconElement.loading = 'lazy'; // 图片延迟加载
                    itemElement.appendChild(iconElement);
                    
                    // 名称
                    const nameElement = document.createElement('div');
                    nameElement.className = 'secondary-category-name';
                    nameElement.textContent = subcategory.name;
                    itemElement.appendChild(nameElement);
                    
                    // 点击事件（可以导航到商品列表页或其他操作）
                    itemElement.addEventListener('click', () => {
                        // 添加点击反馈动画
                        itemElement.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            itemElement.style.transform = '';
                            navigateToSubcategory(subcategory.id, subcategory.name);
                        }, 150);
                    });
                    
                    gridElement.appendChild(itemElement);
                });
                
                sectionElement.appendChild(gridElement);
                secondaryCategoryContainer.appendChild(sectionElement);
                
                // 平滑滚动到顶部
                secondaryCategoryContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
            
            // 导航到子分类页面
            function navigateToSubcategory(subcategoryId, subcategoryName) {

                // 跳转到商品列表页面，传递分类ID参数
                window.location.href = `/productlist?id=${subcategoryId}`;
            }
            
            // 初始化：加载所有分类
            // loadPrimaryCategories();
            
            // 窗口大小改变时调整布局
            window.addEventListener('resize', () => {
                if (activeCategoryId) {
                    const activeItem = primaryCategoryList.querySelector('.primary-category-item.active');
                    if (activeItem && window.innerWidth <= 576) {
                        const containerRect = primaryCategoryList.getBoundingClientRect();
                        const itemRect = activeItem.getBoundingClientRect();
                        
                        if (itemRect.left < containerRect.left || itemRect.right > containerRect.right) {
                            primaryCategoryList.scrollTo({
                                left: activeItem.offsetLeft - containerRect.width / 2 + itemRect.width / 2,
                                behavior: 'smooth'
                            });
                        }
                    }
                }
            });
            
            // 加载所有一级分类
            function loadPrimaryCategories() {
                
                // 显示加载状态
                primaryLoadingState.style.display = 'flex';
                
                // 获取用户ID和密钥
                const userId = localStorage.getItem('userId');
                const userKey = localStorage.getItem('userKey');
                
                // 检查是否有用户信息
                if (!userId || !userKey) {
                    showError(primaryCategoryList, '用户认证失败，请重新登录');
                    return;
                }
                
                // 生成MD5签名
                try {
                    const sign = MD5(userId + userKey);
                    
                    // 准备请求数据
                    const formData = new FormData();
                    formData.append('userId', userId);
                    formData.append('sign', sign);
                    
                    // 准备请求配置
                    const requestOptions = {
                        method: 'POST',
                        body: formData,
                        headers: {}
                    };
                    
                    // 添加Token到请求头
                    const token = localStorage.getItem('token') || getTokenFromCookie();
                    if (token) {
                        requestOptions.headers['Token'] = token;
                    } else {
                    }
                    
                    
                    // 发送API请求
                    fetch(API_URL.categories, requestOptions)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP请求错误! 状态: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            
                            if (data.code === 200 && data.data) {
                                // 保存分类数据
                                categoriesData = data.data;
                                
                                // 渲染分类
                                renderPrimaryCategories(data.data);
                            } else {
                                showError(primaryCategoryList, data.message || '加载分类失败');
                            }
                        })
                        .catch(error => {
                            showError(primaryCategoryList, '加载分类失败，请重试');
                        });
                } catch (error) {
                    showError(primaryCategoryList, '请求准备失败，请重试');
                }
            }
            
            // 显示错误信息
            function showError(container, message) {
                // 清空容器内容
                const existingError = container.querySelector('.error-state');
                if (existingError) {
                    existingError.remove();
                }
                
                const errorElement = document.createElement('div');
                errorElement.className = 'error-state';
                errorElement.innerHTML = `
                    <i class="fa-solid fa-triangle-exclamation error-icon"></i>
                    <div class="error-text">${message}</div>
                    <button class="retry-button">重试</button>
                `;
                
                const retryButton = errorElement.querySelector('.retry-button');
                retryButton.addEventListener('click', () => {
                    errorElement.remove();
                    getUserInfoAndLoadCategories();
                });
                
                container.appendChild(errorElement);
            }
        });