// 卡密显示/隐藏
        const revealBtn = document.getElementById('reveal-btn');
        const cdkeyValue = document.getElementById('cdkey');
        const countdownEl = document.getElementById('countdown');
        let countdownInterval;
        let secondsLeft = 30;
        
        revealBtn.addEventListener('click', function() {
            if (cdkeyValue.classList.contains('revealed')) {
                cdkeyValue.classList.remove('revealed');
                this.innerHTML = '<span class="material-icons">visibility</span> 显示卡密';
                // 清除倒计时
                clearInterval(countdownInterval);
                countdownEl.textContent = '30';
                secondsLeft = 30;
            } else {
                cdkeyValue.classList.add('revealed');
                this.innerHTML = '<span class="material-icons">visibility_off</span> 隐藏卡密';
                
                // 真实卡密内容
                {% if card_content %}
                cdkeyValue.textContent = '{{ card_content|escapejs }}';
                {% else %}
                cdkeyValue.textContent = '暂无卡密信息';
                {% endif %}
                
                // 倒计时
                secondsLeft = 30;
                countdownEl.textContent = secondsLeft;
                
                countdownInterval = setInterval(() => {
                    secondsLeft--;
                    countdownEl.textContent = secondsLeft;
                    
                    if (secondsLeft <= 0) {
                        cdkeyValue.classList.remove('revealed');
                        revealBtn.innerHTML = '<span class="material-icons">visibility</span> 显示卡密';
                        clearInterval(countdownInterval);
                        secondsLeft = 30;
                        countdownEl.textContent = secondsLeft;
                    }
                }, 1000);
            }
        });
        
        // 复制卡密
        const copyBtn = document.getElementById('copy-btn');
        
        copyBtn.addEventListener('click', function() {
            if (!cdkeyValue.classList.contains('revealed')) {
                alert('请先点击"显示卡密"按钮查看卡密');
                return;
            }
            
            // 创建临时textarea元素
            const textarea = document.createElement('textarea');
            textarea.value = cdkeyValue.textContent;
            document.body.appendChild(textarea);
            
            // 选择并复制
            textarea.select();
            document.execCommand('copy');
            
            // 移除临时元素
            document.body.removeChild(textarea);
            
            // 提示已复制
            this.innerHTML = '<span class="material-icons">check</span> 已复制';
            setTimeout(() => {
                this.innerHTML = '<span class="material-icons">content_copy</span> 复制卡密';
            }, 2000);
        });
        
        // 防截图措施 - 监听鼠标移动
        const cdkeyContainer = document.querySelector('.cdkey-container');
        
        document.addEventListener('mousemove', function(e) {
            // 计算鼠标与卡密容器的距离
            const rect = cdkeyContainer.getBoundingClientRect();
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            const isNear = mouseX >= rect.left - 100 && 
                          mouseX <= rect.right + 100 && 
                          mouseY >= rect.top - 100 && 
                          mouseY <= rect.bottom + 100;
            
            // 如果鼠标接近卡密容器且卡密是显示状态，添加额外的保护
            if (isNear && cdkeyValue.classList.contains('revealed')) {
                cdkeyContainer.style.background = 'repeating-linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.9) 10px, rgba(64,169,255,0.1) 10px, rgba(64,169,255,0.1) 20px)';
            } else {
                cdkeyContainer.style.background = '#f9f9f9';
            }
        });