// 全局变量
        let currentUser = null;
        let currentProductId = null;
        let currentProductData = null;
        let currentQuantity = 1; // 固定数量为1
        let currentCouponDiscount = 0;
        let basePrice = 0;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL参数中的商品ID
            const urlParams = new URLSearchParams(window.location.search);
            currentProductId = urlParams.get('id');

            if (!currentProductId) {
                alert('商品ID不能为空');
                window.location.href = '/';
                return;
            }

            // 初始化用户信息和商品数据
            initializeUserAndProduct();

            // 绑定事件监听器
            bindEventListeners();
        });

        // 初始化用户信息和商品数据
        async function initializeUserAndProduct() {
            try {
                // 显示加载状态
                showLoadingState();

                // 获取用户信息
                const userResponse = await fetch('/user/api/GetUser/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!userResponse.ok) {
                    throw new Error('获取用户信息失败');
                }

                const userData = await userResponse.json();
                if (userData.code !== 200) {
                    throw new Error(userData.message || '用户验证失败');
                }

                currentUser = userData.user;

                // 设置Token到请求头
                const token = userResponse.headers.get('Token');
                if (token) {
                    sessionStorage.setItem('authToken', token);
                }

                // 获取商品详情
                await loadProductInfo();

                // 获取支付方式列表
                await loadPaymentMethods();

                // 隐藏加载状态
                hideLoadingState();

                // 处理预填充数据（支付取消返回时使用）
                await handlePrefillData();

            } catch (error) {
                showErrorState('页面加载失败: ' + error.message);
            }
        }

        // 加载商品详情
        async function loadProductInfo() {
            try {
                if (!currentUser || !currentProductId) {
                    throw new Error('用户信息或商品ID缺失');
                }

                // 生成签名
                const signData = currentProductId + currentUser.id + currentUser.user_key;
                const sign = await generateMD5(signData);

                // 准备请求数据
                const formData = new FormData();
                formData.append('id', currentProductId);
                formData.append('userId', currentUser.id);
                formData.append('sign', sign);

                // 获取Token
                const token = sessionStorage.getItem('authToken');

                // 发送请求
                const response = await fetch('/user/api/productInfo/', {
                    method: 'POST',
                    headers: {
                        'Token': token || ''
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();

                if (result.code !== 200) {
                    throw new Error(result.msg || '获取商品信息失败');
                }

                if (!result.data || result.data.length === 0) {
                    throw new Error('商品数据为空');
                }

                currentProductData = result.data[0];
                basePrice = parseFloat(currentProductData.price);

                // 渲染商品信息
                renderProductInfo(currentProductData);

                // 更新价格计算
                updatePriceCalculation();

            } catch (error) {
                showErrorState('加载商品信息失败: ' + error.message);
            }
        }

        // 加载支付方式列表
        async function loadPaymentMethods() {
            try {
                if (!currentUser) {
                    throw new Error('用户信息缺失');
                }


                // 生成签名：userId + userKey
                const signData = currentUser.id + currentUser.user_key;
                const sign = await generateMD5(signData);


                // 准备请求数据
                const formData = new FormData();
                formData.append('userId', currentUser.id);
                formData.append('sign', sign);

                // 获取Token
                const token = sessionStorage.getItem('authToken');


                // 发送请求
                const response = await fetch('/user/api/payment_methods/', {
                    method: 'POST',
                    headers: {
                        'Token': token || ''
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();


                if (result.code !== 200) {
                    return;
                }

                if (result.data && Array.isArray(result.data)) {

                    result.data.forEach((method, index) => {
                    });

                    // 存储支付方式数据
                    storePaymentMethods(result.data);

                    // 渲染支付方式界面
                    renderPaymentMethods(result.data);
                } else {
                    showEmptyPaymentState();
                }

            } catch (error) {
                showEmptyPaymentState();
                // 不抛出错误，避免影响页面其他功能
            }
        }

        // 渲染支付方式界面
        function renderPaymentMethods(paymentMethods) {
            try {
                const paymentNav = document.getElementById('paymentNav');
                const paymentLoading = document.getElementById('paymentLoading');
                const paymentEmpty = document.getElementById('paymentEmpty');
                const paymentOptions = document.getElementById('paymentOptions');

                if (!paymentMethods || paymentMethods.length === 0) {
                    showEmptyPaymentState();
                    return;
                }

                // 存储支付方式数据到全局变量，供自动选择功能使用
                window.currentPaymentMethods = paymentMethods;

                // 隐藏加载状态
                if (paymentLoading) paymentLoading.style.display = 'none';
                if (paymentEmpty) paymentEmpty.style.display = 'none';

                // 生成导航栏
                generatePaymentNav(paymentMethods);

                // 显示导航栏
                if (paymentNav) paymentNav.style.display = 'flex';

                // 默认显示第一个支付通道
                if (paymentMethods.length > 0) {
                    showPaymentChannel(paymentMethods[0], 0);
                }

            } catch (error) {
                showEmptyPaymentState();
            }
        }

        // 生成支付通道导航栏
        function generatePaymentNav(paymentMethods) {
            const paymentNav = document.getElementById('paymentNav');
            if (!paymentNav) return;

            let navHTML = '';
            paymentMethods.forEach((method, index) => {
                const activeClass = index === 0 ? 'active' : '';
                navHTML += `
                    <button class="payment-nav-item ${activeClass}"
                            data-channel-index="${index}"
                            onclick="switchPaymentChannel(${index})">
                        ${method.name}
                        <span style="font-size: 0.8em; color: var(--text-light); margin-left: 5px;">
                            (${method.data ? method.data.length : 0}种方式)
                        </span>
                    </button>
                `;
            });

            paymentNav.innerHTML = navHTML;
        }

        // 切换支付通道
        function switchPaymentChannel(channelIndex) {
            try {
                // 更新导航栏状态
                const navItems = document.querySelectorAll('.payment-nav-item');
                navItems.forEach((item, index) => {
                    if (index === channelIndex) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });

                // 获取支付方式数据（从控制台日志中获取的数据）
                // 这里需要存储支付方式数据到全局变量
                if (window.currentPaymentMethods && window.currentPaymentMethods[channelIndex]) {
                    showPaymentChannel(window.currentPaymentMethods[channelIndex], channelIndex);
                }

            } catch (error) {
            }
        }

        // 显示指定支付通道的支付方式
        function showPaymentChannel(channelData, channelIndex) {
            try {
                const paymentOptions = document.getElementById('paymentOptions');
                if (!paymentOptions) return;

                // 存储当前选中的通道数据
                window.currentSelectedChannel = channelData;

                if (!channelData.data || !Array.isArray(channelData.data) || channelData.data.length === 0) {
                    // 该通道没有支付方式
                    paymentOptions.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <i class="fa-solid fa-info-circle" style="font-size: 24px; margin-bottom: 15px;"></i>
                            <p>该支付通道暂无可用的支付方式</p>
                        </div>
                    `;
                    return;
                }

                // 生成支付方式选项
                let optionsHTML = '';
                channelData.data.forEach((paymentType, index) => {
                    const paymentId = `payment_${channelIndex}_${index}`;
                    const iconClass = getPaymentIconClass(paymentType);
                    const paymentName = getPaymentDisplayName(paymentType);
                    const iconSymbol = getPaymentIconSymbol(paymentType);

                    optionsHTML += `
                        <div class="payment-option payment-card" data-way="${paymentType}">
                            <input type="radio"
                                   class="payment-input"
                                   id="${paymentId}"
                                   name="payment_method"
                                   value="${channelData.id}"
                                   data-channel-id="${channelData.id}"
                                   data-payment-type="${paymentType}"
                                   data-fee="${channelData.fee}">
                            <label class="payment-label" for="${paymentId}">
                                <div class="payment-icon ${iconClass}">
                                    ${iconSymbol}
                                </div>
                                <div class="payment-details">
                                    <div class="payment-name">${paymentName}</div>
                                    <div style="font-size: 0.85em; color: var(--text-light);">
                                        手续费: ${channelData.fee}%
                                    </div>
                                </div>
                            </label>
                        </div>
                    `;
                });

                paymentOptions.innerHTML = optionsHTML;

            } catch (error) {
            }
        }

        // 获取支付方式图标类名
        function getPaymentIconClass(paymentType) {
            const iconMap = {
                'wxpay': 'wxpay',
                'alipay': 'alipay',
                'qqpay': 'qqpay'
            };
            return iconMap[paymentType] || 'default';
        }

        // 获取支付方式显示名称
        function getPaymentDisplayName(paymentType) {
            const nameMap = {
                'wxpay': '微信支付',
                'alipay': '支付宝',
                'qqpay': 'QQ钱包'
            };
            return nameMap[paymentType] || paymentType;
        }

        // 获取支付方式图标符号
        function getPaymentIconSymbol(paymentType) {
            const symbolMap = {
                'wxpay': '<i class="fa-brands fa-weixin"></i>',
                'alipay': '<span style="font-weight: bold; font-size: 12px; color: white; line-height: 1;">支付宝</span>',
                'qqpay': '<i class="fa-brands fa-qq"></i>'
            };
            return symbolMap[paymentType] || '<i class="fa-solid fa-credit-card"></i>';
        }

        // 显示空支付状态
        function showEmptyPaymentState() {
            const paymentNav = document.getElementById('paymentNav');
            const paymentLoading = document.getElementById('paymentLoading');
            const paymentEmpty = document.getElementById('paymentEmpty');

            if (paymentNav) paymentNav.style.display = 'none';
            if (paymentLoading) paymentLoading.style.display = 'none';
            if (paymentEmpty) paymentEmpty.style.display = 'block';
        }

        // 存储支付方式数据到全局变量
        function storePaymentMethods(paymentMethods) {
            window.currentPaymentMethods = paymentMethods;
        }

        // 渲染商品信息
        function renderProductInfo(productData) {
            try {
                // 更新商品图片
                const imageElement = document.getElementById('productImage');
                const summaryImageElement = document.getElementById('summaryItemImg');
                if (imageElement && productData.image) {
                    imageElement.src = productData.image;
                    imageElement.alt = productData.name;
                    summaryImageElement.src = productData.image;
                    summaryImageElement.alt = productData.name;

                    // 图片加载失败时使用占位图
                    imageElement.onerror = function() {
                        this.src = '/static/images/product-placeholder.svg';
                    };
                    summaryImageElement.onerror = function() {
                        this.src = '/static/images/product-placeholder.svg';
                    };
                }

                // 更新商品名称
                const nameElement = document.getElementById('productName');
                const summaryNameElement = document.getElementById('summaryItemName');
                if (nameElement) {
                    nameElement.textContent = productData.name;
                    summaryNameElement.textContent = productData.name;
                }

                // 更新页面标题
                document.title = productData.name + ' - 下单确认 - 无名SUP';

                // 更新商品元信息
                const metaElement = document.getElementById('productMeta');
                if (metaElement) {
                    metaElement.innerHTML = `
                        <span>商品ID: ${productData.id}</span>
                        <span>销量: ${productData.sales_count}</span>
                    `;
                }

                // 更新商品价格
                const priceElement = document.getElementById('productPrice');
                if (priceElement) {
                    priceElement.textContent = '¥' + parseFloat(productData.price).toFixed(2);
                }

                // 渲染商品参数
                renderProductParams(productData);

                // 更新面包屑导航
                updateBreadcrumb(productData);

            } catch (error) {
                showErrorState('显示商品信息失败');
            }
        }

        // 渲染商品参数
        function renderProductParams(productData) {
            const paramsSection = document.getElementById('paramsSection');
            const paramsContainer = document.getElementById('paramsContainer');

            if (!paramsSection || !paramsContainer) return;

            // 检查是否有attach数组
            if (!productData.attach || !Array.isArray(productData.attach) || productData.attach.length === 0) {
                paramsSection.style.display = 'none';
                return;
            }

            // 显示参数区域
            paramsSection.style.display = 'block';

            // 生成参数输入字段HTML
            // 注意：这里直接使用商品attach数组中的name属性作为输入框的name属性
            // 这样在订单提交时，后端接收到的attach字段格式为：[{"充值账号": "值1", "其他参数": "值2"}]
            let paramsHTML = '';
            productData.attach.forEach((param, index) => {
                if (param.name) {
                    // 为了确保name属性的唯一性，如果有重复的name，添加索引后缀
                    let inputName = param.name;
                    let inputId = `param_${index}`;

                    paramsHTML += `
                        <div class="param-group">
                            <label class="param-label" for="${inputId}">${param.name}</label>
                            <input type="text" class="param-input" id="${inputId}" name="${inputName}"
                                   placeholder="请输入${param.name}" required
                                   data-param-index="${index}">
                        </div>
                    `;
                }
            });

            paramsContainer.innerHTML = paramsHTML;
        }

        // 更新面包屑导航
        function updateBreadcrumb(productData) {
            const breadcrumbElement = document.querySelector('.breadcrumb');
            const productListLink = document.getElementById('productListLink');
            const productNameBreadcrumb = document.getElementById('productNameBreadcrumb');

            if (productListLink) {
                // 移除href设置，避免跳转到无参数的productlist页面
                productListLink.textContent = '商品列表';
                // 添加样式使其看起来像普通文本
                productListLink.style.color = '#666';
                productListLink.style.cursor = 'default';
                productListLink.style.textDecoration = 'none';
                // 阻止点击事件
                productListLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    return false;
                });
            }

            if (productNameBreadcrumb) {
                productNameBreadcrumb.textContent = productData.name + ' - 下单确认';
            }
        }

        // 绑定事件监听器
        function bindEventListeners() {

            // 优惠券应用按钮
            const applyCouponBtn = document.getElementById('applyCouponBtn');
            if (applyCouponBtn) {
                applyCouponBtn.addEventListener('click', applyCoupon);
            }

            // 优惠券重置按钮
            const resetCouponBtn = document.getElementById('resetCouponBtn');
            if (resetCouponBtn) {
                resetCouponBtn.addEventListener('click', resetCoupon);
            }

            // 下单按钮
            const submitOrderBtn = document.getElementById('submitOrderBtn');
            const mobileSubmitOrderBtn = document.getElementById('mobileSubmitOrderBtn');

            if (submitOrderBtn) {
                submitOrderBtn.addEventListener('click', submitOrder);
            }

            if (mobileSubmitOrderBtn) {
                mobileSubmitOrderBtn.addEventListener('click', submitOrder);
            }

            // 移动端菜单切换
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navMenu = document.querySelector('.nav-menu');

            if (mobileMenuBtn && navMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                });
            }

            // 返回商品页按钮
            const backToProductBtn = document.getElementById('backToProductBtn');
            if (backToProductBtn) {
                backToProductBtn.addEventListener('click', function() {
                    if (currentProductId) {
                        window.location.href = `/product-detail/?id=${currentProductId}`;
                    } else {
                        alert('商品信息加载中，请稍后再试');
                    }
                });
            }

            // 批量下单按钮（预留功能）
            const batchOrderBtn = document.getElementById('batchOrderBtn');
            if (batchOrderBtn) {
                batchOrderBtn.addEventListener('click', function() {
                    alert('批量下单功能即将上线，敬请期待！');
                    // TODO: 实现批量下单功能
                });
            }
        }

        // 更新价格计算
        function updatePriceCalculation() {
            if (!basePrice || !currentQuantity) return;

            const subtotal = basePrice * currentQuantity;
            const discount = currentCouponDiscount;
            const service = 0; // 服务费
            const total = subtotal - discount + service;

            // 更新各项金额显示
            const subtotalElement = document.getElementById('subtotalAmount');
            const discountElement = document.getElementById('discountAmount');
            const serviceElement = document.getElementById('serviceAmount');
            const totalElement = document.getElementById('totalAmount');
            const summaryItemPriceElement = document.getElementById('summaryItemPrice');
            const mobileTotalElement = document.getElementById('mobileTotalAmount');

            if (subtotalElement) subtotalElement.textContent = '¥' + subtotal.toFixed(2);
            if (discountElement) discountElement.textContent = '-¥' + discount.toFixed(2);
            if (serviceElement) serviceElement.textContent = '¥' + service.toFixed(2);
            if (totalElement) totalElement.textContent = '¥' + total.toFixed(2);
            if (summaryItemPriceElement) summaryItemPriceElement.textContent = '¥' + subtotal.toFixed(2);
            if (mobileTotalElement) mobileTotalElement.textContent = '¥' + total.toFixed(2);
        }

        // 应用优惠券
        async function applyCoupon() {
            const couponInput = document.getElementById('couponInput');
            const couponMessage = document.getElementById('couponMessage');
            const applyCouponBtn = document.getElementById('applyCouponBtn');

            if (!couponInput || !couponMessage) return;

            const couponCode = couponInput.value.trim();
            if (!couponCode) {
                showCouponMessage('请输入优惠券代码', 'error');
                return;
            }

            // 检查用户信息是否可用
            if (!currentUser) {
                showCouponMessage('用户信息加载中，请稍后重试', 'error');
                return;
            }

            // 显示加载状态
            applyCouponBtn.disabled = true;
            applyCouponBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 验证中...';

            try {
                // 生成签名：coupon + userId + user_key
                const signData = couponCode + currentUser.id + currentUser.user_key;
                const sign = await generateMD5(signData);

                // 获取Token
                const token = sessionStorage.getItem('authToken');


                // 调用卡券详情API
                const response = await fetch(`/user/api/couponsDetail/?coupon=${encodeURIComponent(couponCode)}&userId=${encodeURIComponent(currentUser.id)}&sign=${encodeURIComponent(sign)}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': token || ''
                    }
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();


                if (result.code === 200) {
                    // 卡券验证成功，计算折扣
                    const couponData = result.data;
                    const subtotal = basePrice * currentQuantity;
                    let discount = 0;

                    // 检查最低消费金额
                    const minOrderAmount = parseFloat(couponData.minOrderAmount);
                    if (subtotal < minOrderAmount) {
                        showCouponMessage(`订单金额不足，最低需要消费¥${minOrderAmount.toFixed(2)}`, 'error');
                        return;
                    }

                    // 根据优惠类型计算折扣
                    if (couponData.discountType === 'fixed') {
                        // 固定金额折扣
                        discount = Math.min(parseFloat(couponData.discountValue), subtotal);
                    } else if (couponData.discountType === 'percentage') {
                        // 百分比折扣
                        const percentage = parseFloat(couponData.discountValue);
                        discount = subtotal * (percentage / 100);
                    }

                    // 应用折扣
                    currentCouponDiscount = discount;
                    updatePriceCalculation();

                    // 显示成功消息
                    const discountText = couponData.discountType === 'fixed'
                        ? `¥${couponData.discountValue}`
                        : `${couponData.discountValue}%`;
                    showCouponMessage(`优惠券应用成功！享受${discountText}优惠，节省¥${discount.toFixed(2)}`, 'success');

                    // 禁用输入框和按钮，防止重复应用
                    couponInput.disabled = true;
                    applyCouponBtn.innerHTML = '<i class="fa-solid fa-check"></i> 已应用';
                    applyCouponBtn.style.background = '#4CAF50';
                    applyCouponBtn.style.display = 'none';

                    // 显示重置按钮
                    const resetCouponBtn = document.getElementById('resetCouponBtn');
                    if (resetCouponBtn) {
                        resetCouponBtn.style.display = 'flex';
                    }

                } else {
                    // 卡券验证失败
                    showCouponMessage(result.msg || '优惠券验证失败', 'error');
                }

            } catch (error) {
                showCouponMessage('优惠券验证失败，请稍后重试', 'error');
            } finally {
                // 恢复按钮状态（仅在失败时）
                if (!applyCouponBtn.innerHTML.includes('已应用')) {
                    applyCouponBtn.disabled = false;
                    applyCouponBtn.innerHTML = '<i class="fa-solid fa-check"></i> 应用';
                }
            }
        }

        // 重置优惠券
        function resetCoupon() {
            const couponInput = document.getElementById('couponInput');
            const couponMessage = document.getElementById('couponMessage');
            const applyCouponBtn = document.getElementById('applyCouponBtn');
            const resetCouponBtn = document.getElementById('resetCouponBtn');

            // 重置优惠券折扣
            currentCouponDiscount = 0;
            updatePriceCalculation();

            // 重置UI状态
            if (couponInput) {
                couponInput.value = '';
                couponInput.disabled = false;
            }

            if (applyCouponBtn) {
                applyCouponBtn.innerHTML = '<i class="fa-solid fa-check"></i> 应用';
                applyCouponBtn.style.background = '';
                applyCouponBtn.style.display = 'flex';
                applyCouponBtn.disabled = false;
            }

            if (resetCouponBtn) {
                resetCouponBtn.style.display = 'none';
            }

            // 清除消息
            showCouponMessage('', '');
        }

        // 显示优惠券消息
        function showCouponMessage(message, type) {
            const couponMessage = document.getElementById('couponMessage');
            if (!couponMessage) return;

            couponMessage.textContent = message;
            couponMessage.style.color = type === 'success' ? '#4CAF50' : '#F44336';
            couponMessage.style.fontWeight = '500';
        }

        // 提交订单
        async function submitOrder() {
            try {
                // 验证表单
                if (!validateOrderForm()) {
                    return;
                }

                // 显示加载状态
                const submitBtn = document.getElementById('submitOrderBtn');
                const mobileSubmitBtn = document.getElementById('mobileSubmitOrderBtn');

                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 处理中...';
                }

                if (mobileSubmitBtn) {
                    mobileSubmitBtn.disabled = true;
                    mobileSubmitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 处理中...';
                }

                // 收集订单数据
                const orderData = collectOrderData();

                // 生成签名
                const sign = await generateOrderSign(orderData);
                orderData.sign = sign;


                // 获取Token
                const token = sessionStorage.getItem('authToken');
                const timestamp = Date.now().toString();

                // 调用订单提交API
                const response = await fetch('/user/api/submit_order/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': token || '',
                        'time': timestamp
                    },
                    body: JSON.stringify(orderData)
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();

                if (result.code === 200) {
                    alert('订单提交成功！正在跳转到支付页面...');

                    // 跳转到支付页面
                    if (result.data && result.data.payment_url) {
                        window.location.href = result.data.payment_url;
                    } else {
                        window.location.href = `/payment/?order_id=${result.data.order_id}`;
                    }
                } else {
                    throw new Error(result.msg || '订单提交失败');
                }

            } catch (error) {
                alert('订单提交失败: ' + error.message);

                // 恢复按钮状态
                const submitBtn = document.getElementById('submitOrderBtn');
                const mobileSubmitBtn = document.getElementById('mobileSubmitOrderBtn');

                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fa-solid fa-shopping-cart"></i> 确认下单';
                }

                if (mobileSubmitBtn) {
                    mobileSubmitBtn.disabled = false;
                    mobileSubmitBtn.innerHTML = '<i class="fa-solid fa-shopping-cart"></i> 确认下单';
                }
            }
        }

        // 验证订单表单
        function validateOrderForm() {
            // 检查邮箱
            const emailInput = document.getElementById('userEmail');
            if (!emailInput || !emailInput.value.trim()) {
                alert('请填写接收订单信息的邮箱地址');
                if (emailInput) emailInput.focus();
                return false;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailInput.value.trim())) {
                alert('请输入有效的邮箱地址');
                emailInput.focus();
                return false;
            }

            // 检查支付方式
            const selectedPayment = document.querySelector('.payment-input:checked');
            if (!selectedPayment) {
                alert('请选择支付方式');
                return false;
            }

            // 检查商品参数
            const paramsContainer = document.getElementById('paramsContainer');
            if (paramsContainer && paramsContainer.style.display !== 'none') {
                const paramInputs = paramsContainer.querySelectorAll('.param-input[required]');
                for (let input of paramInputs) {
                    if (!input.value.trim()) {
                        alert(`请填写${input.previousElementSibling.textContent}`);
                        input.focus();
                        return false;
                    }
                }
            }

            // 检查数量
            if (currentQuantity < 1) {
                alert('购买数量不能少于1');
                return false;
            }

            return true;
        }

        // 收集订单数据（按照API文档格式）
        function collectOrderData() {
            // 获取选中的支付方式
            const selectedPayment = document.querySelector('.payment-input:checked');
            if (!selectedPayment) {
                throw new Error('请选择支付方式');
            }

            // 获取支付方式信息
            const paymentCard = selectedPayment.closest('.payment-card');
            const paymentId = selectedPayment.value;

            // 优先从input的data-payment-type获取，然后从card的data-way获取
            let paymentWay = selectedPayment.dataset.paymentType;
            if (!paymentWay && paymentCard) {
                paymentWay = paymentCard.dataset.way;
            }
            if (!paymentWay) {
                paymentWay = 'wxpay'; // 默认微信支付
            }


            // 收集商品参数
            // 这里将用户输入的参数值收集为原始参数名称格式，如{"充值账号": "值1", "其他参数": "值2"}
            const attachParams = {};
            const paramsContainer = document.getElementById('paramsContainer');


            if (paramsContainer && paramsContainer.style.display !== 'none') {
                const paramInputs = paramsContainer.querySelectorAll('.param-input');

                paramInputs.forEach((input, index) => {
                    const paramName = input.name; // 现在直接使用原始参数名称
                    const paramIndex = input.getAttribute('data-param-index') || index;


                    // 只收集有值的参数，直接使用原始参数名称作为键名
                    if (input.value.trim()) {
                        attachParams[paramName] = input.value.trim();
                    }
                });
            }


            // 构建attach数组（支持批量下单）
            // 注意：这里的格式必须是数组，每个元素是一个对象，包含原始参数名称作为键值对
            const attach = [];
            if (Object.keys(attachParams).length > 0) {
                attach.push(attachParams);
            } else {
                // 如果没有参数，添加空对象以保持与后端一致的数据结构
                attach.push({});
            }

            // 获取邮箱
            const mailInput = document.getElementById('userEmail');
            const mail = mailInput ? mailInput.value.trim() : '';

            // 生成时间戳
            const timestamp = Date.now().toString();

            // 构建订单数据
            const orderData = {
                id: currentProductId,
                coupon: document.getElementById('couponInput').value.trim(),
                mail: mail,
                attach: attach,
                payment: {
                    id: paymentId,
                    way: paymentWay
                },
                time: timestamp
            };

            return orderData;
        }

        // 显示加载状态
        function showLoadingState() {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.opacity = '0.5';
                mainContent.style.pointerEvents = 'none';
            }
        }

        // 隐藏加载状态
        function hideLoadingState() {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.opacity = '1';
                mainContent.style.pointerEvents = 'auto';
            }
        }

        // 显示错误状态
        function showErrorState(message) {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #666;">
                        <i class="fa-solid fa-exclamation-triangle" style="font-size: 48px; color: #ff6b6b; margin-bottom: 20px;"></i>
                        <h3>加载失败</h3>
                        <p>${message}</p>
                        <button onclick="window.location.reload()" style="
                            background: var(--primary-color);
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin-top: 20px;
                        ">重新加载</button>
                    </div>
                `;
            }
        }

        // 生成MD5签名
        function generateMD5(text) {
            return md5(text);
        }

        // 深度排序对象的所有键（递归处理嵌套对象）
        function deepSortKeys(obj) {
            if (obj === null || typeof obj !== 'object') {
                return obj;
            }

            if (Array.isArray(obj)) {
                return obj.map(item => deepSortKeys(item));
            }

            const sortedObj = {};
            const sortedKeys = Object.keys(obj).sort();

            for (const key of sortedKeys) {
                sortedObj[key] = deepSortKeys(obj[key]);
            }

            return sortedObj;
        }

        // 生成订单签名
        async function generateOrderSign(orderData) {
            try {
                if (!currentUser || !currentUser.user_key) {
                    throw new Error('用户信息缺失');
                }


                // 创建签名数据（排除sign字段）
                const signData = {};
                for (const key in orderData) {
                    if (key !== 'sign') {
                        signData[key] = orderData[key];
                    }
                }


                // 深度排序所有键（包括嵌套对象）
                const sortedSignData = deepSortKeys(signData);


                // 将数据转换为JSON字符串，使用与后端相同的格式
                // 后端使用: json.dumps(sign_data, separators=(',', ':'), sort_keys=True, ensure_ascii=False)
                const jsonStr = JSON.stringify(sortedSignData, null, 0);

                // 添加用户key
                const signText = jsonStr + currentUser.user_key;


                // 计算MD5签名
                const sign = generateMD5(signText);


                return sign;
            } catch (error) {
                throw error;
            }
        }

        // 处理预填充数据（支付取消返回时使用）
        async function handlePrefillData() {
            try {

                // 从Django模板获取预填充数据
                const prefillDataStr = '{{ prefill_data_json|escapejs }}';

                const prefillData = JSON.parse(prefillDataStr);

                // 检查是否有预填充数据
                if (!prefillData || Object.keys(prefillData).length === 0) {
                    // 即使没有预填充数据，也要清理URL参数
                    cleanUrlParameters();
                    return;
                }

                // 检查每个字段是否有值

                // 自动填充邮箱
                if (prefillData.notice_mail) {
                    const emailInput = document.getElementById('userEmail');
                    if (emailInput) {
                        emailInput.value = prefillData.notice_mail;
                    } else {
                    }
                }

                // 自动填充优惠券
                if (prefillData.coupon) {
                    const couponInput = document.getElementById('couponInput');
                    if (couponInput) {
                        couponInput.value = prefillData.coupon;

                        // 自动应用优惠券
                        await applyCoupon();
                    } else {
                    }
                }

                // 自动填充商品参数
                if (prefillData.attach) {
                    try {
                        const attachData = JSON.parse(prefillData.attach);

                        if (Array.isArray(attachData) && attachData.length > 0) {
                            // 遍历每个参数对象
                            attachData.forEach((paramObj, index) => {

                                // 检查参数对象是否有name和value属性
                                if (paramObj.name && paramObj.value !== undefined) {
                                    const paramName = paramObj.name;
                                    const paramValue = paramObj.value;


                                    // 使用参数名称作为name属性选择器查找对应的输入框
                                    const paramInput = document.querySelector(`input[name="${paramName}"]`);
                                    if (paramInput) {
                                        paramInput.value = paramValue;
                                    } else {

                                        // 调试：列出所有可用的参数输入框
                                        const allParamInputs = document.querySelectorAll('input[name]');
                                        allParamInputs.forEach(input => {
                                        });
                                    }
                                } else {
                                }
                            });
                        }
                    } catch (e) {
                    }
                }

                // 自动选择支付方式
                if (prefillData.payment_id && prefillData.payment_way) {
                    // 等待支付方式加载完成后再选择
                    setTimeout(() => {
                        selectPaymentMethod(prefillData.payment_id, prefillData.payment_way);
                    }, 500);
                } else {
                }


                // 清理URL参数，只保留id参数
                cleanUrlParameters();

            } catch (error) {
            }
        }

        // 清理URL参数，只保留id参数
        function cleanUrlParameters() {
            try {

                // 获取当前URL参数
                const currentUrl = new URL(window.location.href);
                const urlParams = new URLSearchParams(currentUrl.search);


                // 获取商品ID参数
                const productId = urlParams.get('id');

                if (!productId) {
                    return;
                }

                // 构建新的URL，只保留id参数
                const newUrl = new URL(window.location.origin + window.location.pathname);
                newUrl.searchParams.set('id', productId);


                // 使用History API更新URL，不刷新页面
                window.history.replaceState(
                    { cleanedUrl: true }, // 状态对象，标记这是清理后的URL
                    '', // 标题（通常被忽略）
                    newUrl.href // 新的URL
                );


            } catch (error) {
            }
        }

        // 自动选择支付方式
        function selectPaymentMethod(paymentId, paymentWay) {
            try {

                // 查找包含指定paymentId的支付渠道
                if (window.currentPaymentMethods) {
                    let targetChannelIndex = -1;

                    // 遍历所有支付渠道，查找指定paymentId的渠道
                    window.currentPaymentMethods.forEach((method, channelIndex) => {

                        // 直接比较渠道ID（不是在data数组中查找）
                        if (method.id === paymentId) {
                            targetChannelIndex = channelIndex;
                        }
                    });

                    if (targetChannelIndex >= 0) {

                        // 切换到目标支付渠道
                        switchPaymentChannel(targetChannelIndex);

                        // 等待支付方式加载后选择具体的支付方式
                        setTimeout(() => {
                            const paymentOptions = document.querySelectorAll('.payment-option');

                            let foundMatch = false;
                            paymentOptions.forEach((option, index) => {
                                const optionWay = option.dataset.way;
                                const optionInput = option.querySelector('input[type="radio"]');
                                const optionChannelId = optionInput ? optionInput.dataset.channelId : null;


                                if (optionChannelId === paymentId && optionWay === paymentWay) {
                                    if (optionInput) {
                                        optionInput.checked = true;
                                        foundMatch = true;
                                    }
                                }
                            });

                            if (!foundMatch) {
                            }
                        }, 300);
                    } else {
                    }
                } else {
                }
            } catch (error) {
            }
        }

        // MD5算法实现（简化版，实际项目中建议使用专业的加密库）
        function md5(string) {
            function md5_RotateLeft(lValue, iShiftBits) {
                return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
            }
            function md5_AddUnsigned(lX,lY) {
                var lX4,lY4,lX8,lY8,lResult;
                lX8 = (lX & 0x80000000);
                lY8 = (lY & 0x80000000);
                lX4 = (lX & 0x40000000);
                lY4 = (lY & 0x40000000);
                lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
                if (lX4 & lY4) {
                    return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
                }
                if (lX4 | lY4) {
                    if (lResult & 0x40000000) {
                        return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                    } else {
                        return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                    }
                } else {
                    return (lResult ^ lX8 ^ lY8);
                }
            }
            function md5_F(x,y,z) { return (x & y) | ((~x) & z); }
            function md5_G(x,y,z) { return (x & z) | (y & (~z)); }
            function md5_H(x,y,z) { return (x ^ y ^ z); }
            function md5_I(x,y,z) { return (y ^ (x | (~z))); }
            function md5_FF(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_GG(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_HH(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_II(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_ConvertToWordArray(string) {
                var lWordCount;
                var lMessageLength = string.length;
                var lNumberOfWords_temp1=lMessageLength + 8;
                var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
                var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
                var lWordArray=Array(lNumberOfWords-1);
                var lBytePosition = 0;
                var lByteCount = 0;
                while ( lByteCount < lMessageLength ) {
                    lWordCount = (lByteCount-(lByteCount % 4))/4;
                    lBytePosition = (lByteCount % 4)*8;
                    lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
                    lByteCount++;
                }
                lWordCount = (lByteCount-(lByteCount % 4))/4;
                lBytePosition = (lByteCount % 4)*8;
                lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
                lWordArray[lNumberOfWords-2] = lMessageLength<<3;
                lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
                return lWordArray;
            };
            function md5_WordToHex(lValue) {
                var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
                for (lCount = 0;lCount<=3;lCount++) {
                    lByte = (lValue>>>(lCount*8)) & 255;
                    WordToHexValue_temp = "0" + lByte.toString(16);
                    WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length-2,2);
                }
                return WordToHexValue;
            };
            function md5_Utf8Encode(string) {
                string = string.replace(/\r\n/g,"\n");
                var utftext = "";
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    }
                    else if((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                    else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                }
                return utftext;
            };
            var x=Array();
            var k,AA,BB,CC,DD,a,b,c,d;
            var S11=7, S12=12, S13=17, S14=22;
            var S21=5, S22=9 , S23=14, S24=20;
            var S31=4, S32=11, S33=16, S34=23;
            var S41=6, S42=10, S43=15, S44=21;
            string = md5_Utf8Encode(string);
            x = md5_ConvertToWordArray(string);
            a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
            for (k=0;k<x.length;k+=16) {
                AA=a; BB=b; CC=c; DD=d;
                a=md5_FF(a,b,c,d,x[k+0], S11,0xD76AA478);
                d=md5_FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
                c=md5_FF(c,d,a,b,x[k+2], S13,0x242070DB);
                b=md5_FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
                a=md5_FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
                d=md5_FF(d,a,b,c,x[k+5], S12,0x4787C62A);
                c=md5_FF(c,d,a,b,x[k+6], S13,0xA8304613);
                b=md5_FF(b,c,d,a,x[k+7], S14,0xFD469501);
                a=md5_FF(a,b,c,d,x[k+8], S11,0x698098D8);
                d=md5_FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
                c=md5_FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
                b=md5_FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
                a=md5_FF(a,b,c,d,x[k+12],S11,0x6B901122);
                d=md5_FF(d,a,b,c,x[k+13],S12,0xFD987193);
                c=md5_FF(c,d,a,b,x[k+14],S13,0xA679438E);
                b=md5_FF(b,c,d,a,x[k+15],S14,0x49B40821);
                a=md5_GG(a,b,c,d,x[k+1], S21,0xF61E2562);
                d=md5_GG(d,a,b,c,x[k+6], S22,0xC040B340);
                c=md5_GG(c,d,a,b,x[k+11],S23,0x265E5A51);
                b=md5_GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
                a=md5_GG(a,b,c,d,x[k+5], S21,0xD62F105D);
                d=md5_GG(d,a,b,c,x[k+10],S22,0x2441453);
                c=md5_GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
                b=md5_GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
                a=md5_GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
                d=md5_GG(d,a,b,c,x[k+14],S22,0xC33707D6);
                c=md5_GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
                b=md5_GG(b,c,d,a,x[k+8], S24,0x455A14ED);
                a=md5_GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
                d=md5_GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
                c=md5_GG(c,d,a,b,x[k+7], S23,0x676F02D9);
                b=md5_GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
                a=md5_HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
                d=md5_HH(d,a,b,c,x[k+8], S32,0x8771F681);
                c=md5_HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
                b=md5_HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
                a=md5_HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
                d=md5_HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
                c=md5_HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
                b=md5_HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
                a=md5_HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
                d=md5_HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
                c=md5_HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
                b=md5_HH(b,c,d,a,x[k+6], S34,0x4881D05);
                a=md5_HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
                d=md5_HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
                c=md5_HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
                b=md5_HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
                a=md5_II(a,b,c,d,x[k+0], S41,0xF4292244);
                d=md5_II(d,a,b,c,x[k+7], S42,0x432AFF97);
                c=md5_II(c,d,a,b,x[k+14],S43,0xAB9423A7);
                b=md5_II(b,c,d,a,x[k+5], S44,0xFC93A039);
                a=md5_II(a,b,c,d,x[k+12],S41,0x655B59C3);
                d=md5_II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
                c=md5_II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
                b=md5_II(b,c,d,a,x[k+1], S44,0x85845DD1);
                a=md5_II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
                d=md5_II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
                c=md5_II(c,d,a,b,x[k+6], S43,0xA3014314);
                b=md5_II(b,c,d,a,x[k+13],S44,0x4E0811A1);
                a=md5_II(a,b,c,d,x[k+4], S41,0xF7537E82);
                d=md5_II(d,a,b,c,x[k+11],S42,0xBD3AF235);
                c=md5_II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
                b=md5_II(b,c,d,a,x[k+9], S44,0xEB86D391);
                a=md5_AddUnsigned(a,AA);
                b=md5_AddUnsigned(b,BB);
                c=md5_AddUnsigned(c,CC);
                d=md5_AddUnsigned(d,DD);
            }
            return (md5_WordToHex(a)+md5_WordToHex(b)+md5_WordToHex(c)+md5_WordToHex(d)).toLowerCase();
        }