// 全局变量
        let orderId = '{{ order_id }}';
        let checkInterval = null;
        let checkCount = 0;
        const maxCheckCount = 120; // 最多检查2分钟（每秒检查一次）

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 开始支付状态检查
            startPaymentStatusCheck();



            // 添加页面可见性检测
            document.addEventListener('visibilitychange', handleVisibilityChange);

            // 添加ID复制功能
            addCopyFunctionality();
        });

        // 开始支付状态检查
        function startPaymentStatusCheck() {
            
            checkInterval = setInterval(function() {
                checkCount++;
                
                if (checkCount > maxCheckCount) {
                    // 超时停止检查
                    clearInterval(checkInterval);
                    updateStatusIndicator('timeout');
                    return;
                }

                // 检查支付状态
                checkPaymentStatus();
            }, 1000); // 每秒检查一次
        }

        // 检查支付状态
        async function checkPaymentStatus() {
            try {
                const response = await fetch('/user/api/check_payment_status/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify({
                        order_id: orderId
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.code === 200) {
                        if (result.data.status === 'paid') {
                            // 支付成功
                            clearInterval(checkInterval);
                            updateStatusIndicator('success');
                            showPaymentSuccess();
                        } else if (result.data.status === 'failed') {
                            // 支付失败
                            clearInterval(checkInterval);
                            updateStatusIndicator('failed');
                            showPaymentFailed();
                        }
                        // 其他状态继续检查
                    }
                }
            } catch (error) {
            }
        }

        // 更新状态指示器
        function updateStatusIndicator(status) {
            const indicator = document.getElementById('statusIndicator');
            const icon = indicator.querySelector('.fa-solid');
            
            switch (status) {
                case 'success':
                    icon.className = 'fa-solid fa-check';
                    indicator.style.background = '#52c41a';
                    break;
                case 'failed':
                    icon.className = 'fa-solid fa-times';
                    indicator.style.background = '#ff4d4f';
                    break;
                case 'timeout':
                    icon.className = 'fa-solid fa-exclamation';
                    indicator.style.background = '#faad14';
                    break;
                default:
                    icon.className = 'fa-solid fa-clock';
                    indicator.style.background = 'white';
            }
        }

        // 显示支付成功
        function showPaymentSuccess() {
            // 直接跳转到支付成功页面
            window.location.href = '/payment/success/?order=' + orderId;
        }

        // 显示支付失败
        function showPaymentFailed() {
            alert('支付失败，请重新尝试或联系客服。');
        }



        // 处理页面可见性变化
        function handleVisibilityChange() {
            if (document.hidden) {
                // 页面隐藏时暂停检查
                if (checkInterval) {
                    clearInterval(checkInterval);
                }
            } else {
                // 页面显示时恢复检查
                if (checkCount < maxCheckCount) {
                    startPaymentStatusCheck();
                }
            }
        }

        // 获取CSRF Token
        function getCsrfToken() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        }

        // 添加ID复制功能
        function addCopyFunctionality() {
            const orderIdElement = document.querySelector('.info-value.order-id');
            const productIdElement = document.querySelector('.product-id-subtitle');

            if (orderIdElement) {
                orderIdElement.addEventListener('click', function() {
                    copyToClipboard(this.textContent, '订单号');
                });
                orderIdElement.title = '点击复制订单号';
            }

            if (productIdElement) {
                productIdElement.addEventListener('click', function() {
                    copyToClipboard(this.textContent, '商品ID');
                });
                productIdElement.title = '点击复制商品ID';
            }
        }

        // 复制到剪贴板
        function copyToClipboard(text, type) {
            if (navigator.clipboard && window.isSecureContext) {
                // 使用现代API
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess(type);
                }).catch(function(err) {
                    fallbackCopyTextToClipboard(text, type);
                });
            } else {
                // 降级方案
                fallbackCopyTextToClipboard(text, type);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text, type) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess(type);
                } else {
                }
            } catch (err) {
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess(type) {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.textContent = `${type}已复制到剪贴板`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--kawaii-gradient);
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                font-weight: 600;
                box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
                z-index: 10000;
                animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s forwards;
            `;

            document.body.appendChild(toast);

            // 3秒后移除提示
            setTimeout(function() {
                if (toast.parentNode) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (checkInterval) {
                clearInterval(checkInterval);
            }
        });