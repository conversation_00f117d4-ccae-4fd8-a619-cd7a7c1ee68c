// 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加订单号复制功能
            const orderIdElement = document.querySelector('.product-order-id-summary');
            if (orderIdElement) {
                orderIdElement.addEventListener('click', function() {
                    copyToClipboard(this.textContent, '订单号');
                });
                orderIdElement.title = '点击复制订单号';
            }

            // 自动跳转倒计时（可选）
            // setTimeout(function() {
            //     window.location.href = '/';
            // }, 10000); // 10秒后自动跳转到首页
        });

        // 复制到剪贴板
        function copyToClipboard(text, type) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess(type);
                }).catch(function(err) {
                    fallbackCopyTextToClipboard(text, type);
                });
            } else {
                fallbackCopyTextToClipboard(text, type);
            }
        }

        // 降级复制方案
        function fallbackCopyTextToClipboard(text, type) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess(type);
                } else {
                }
            } catch (err) {
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess(type) {
            const toast = document.createElement('div');
            toast.textContent = `${type}已复制到剪贴板`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--kawaii-gradient);
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                font-size: 0.9rem;
                font-weight: 600;
                box-shadow: 0 4px 15px rgba(64, 169, 255, 0.3);
                z-index: 10000;
                animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s forwards;
            `;

            document.body.appendChild(toast);

            setTimeout(function() {
                if (toast.parentNode) {
                    document.body.removeChild(toast);
                }
            }, 3000);
        }