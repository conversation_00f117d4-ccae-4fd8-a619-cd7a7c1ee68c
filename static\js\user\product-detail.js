// 全局变量
        let currentUser = null;
        let currentProductId = null;
        let currentProductData = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL参数中的商品ID
            const urlParams = new URLSearchParams(window.location.search);
            currentProductId = urlParams.get('id');

            if (!currentProductId) {
                alert('商品ID不能为空');
                window.location.href = '/';
                return;
            }

            // 检查商品公告并显示弹窗
            const noticeContent = window.productNotice || '';
            if (noticeContent && noticeContent.trim() !== '') {
                showProductNoticeModal(noticeContent);
            }

            // 初始化用户信息和商品数据
            initializeUserAndProduct();
        });

        // 初始化用户信息和商品数据
        async function initializeUserAndProduct() {
            try {
                // 显示加载状态
                showLoadingState();

                // 获取用户信息
                const userResponse = await fetch('/user/api/GetUser/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!userResponse.ok) {
                    throw new Error('获取用户信息失败');
                }

                const userData = await userResponse.json();
                if (userData.code !== 200) {
                    throw new Error(userData.message || '用户验证失败');
                }

                currentUser = userData.user;

                // 设置Token到请求头
                const token = userResponse.headers.get('Token');
                if (token) {
                    // 存储Token供后续请求使用
                    sessionStorage.setItem('authToken', token);
                }

                // 获取商品详情
                await loadProductInfo();

            } catch (error) {
                showErrorState('页面加载失败: ' + error.message);
            }
        }

        // 加载商品详情
        async function loadProductInfo() {
            try {
                if (!currentUser || !currentProductId) {
                    throw new Error('用户信息或商品ID缺失');
                }

                // 生成签名
                const signData = currentProductId + currentUser.id + currentUser.user_key;
                const sign = await generateMD5(signData);

                // 准备请求数据
                const formData = new FormData();
                formData.append('id', currentProductId);
                formData.append('userId', currentUser.id);
                formData.append('sign', sign);

                // 获取Token
                const token = sessionStorage.getItem('authToken');

                // 发送请求
                const response = await fetch('/user/api/productInfo/', {
                    method: 'POST',
                    headers: {
                        'Token': token || ''
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();

                if (result.code !== 200) {
                    throw new Error(result.msg || '获取商品信息失败');
                }

                if (!result.data || result.data.length === 0) {
                    throw new Error('商品数据为空');
                }

                currentProductData = result.data[0];

                // 渲染商品信息
                renderProductInfo(currentProductData);

                // 隐藏加载状态
                hideLoadingState();

            } catch (error) {
                showErrorState('加载商品信息失败: ' + error.message);
            }
        }

        // 渲染商品信息
        function renderProductInfo(productData) {
            try {
                // 更新商品标题
                const titleElement = document.querySelector('.product-title');
                if (titleElement) {
                    titleElement.textContent = productData.name;
                }

                // 更新页面标题
                document.title = productData.name + ' - 商品详情';

                // 更新商品元信息
                const metaElement = document.querySelector('.product-meta');
                if (metaElement) {
                    metaElement.innerHTML = `
                        <span>商品ID: ${productData.id}</span>
                        <span>销量: ${productData.sales_count}</span>
                    `;
                }

                // 更新商品价格
                const priceElement = document.querySelector('.product-price');
                const mobilePriceElement = document.querySelector('.mobile-price');
                if (priceElement) {
                    priceElement.textContent = '¥' + parseFloat(productData.price).toFixed(2);
                }
                if (mobilePriceElement) {
                    mobilePriceElement.textContent = '¥' + parseFloat(productData.price).toFixed(2);
                }

                // 更新商品图片
                const imageElement = document.querySelector('.product-main-image');
                if (imageElement && productData.image) {
                    imageElement.src = productData.image;
                    imageElement.alt = productData.name;

                    // 图片加载失败时使用占位图
                    imageElement.onerror = function() {
                        this.src = '/static/images/product-placeholder.svg';
                    };
                }

                // 更新库存状态
                updateStockStatus(productData);

                // 渲染商品参数
                renderProductParams(productData);

                // 更新商品详情
                const infoElement = document.querySelector('#description');
                if (infoElement) {
                    if (productData.info && productData.info.trim() !== '') {
                        // 如果有商品详情信息，直接显示API返回的HTML内容，无需重复标题
                        infoElement.innerHTML = `
                            <div class="product-info-content">${productData.info}</div>
                        `;
                    } else {
                        // 如果没有详情信息，显示默认内容
                        infoElement.innerHTML = `
                            <div class="product-info-content">
                                <p>暂无详细信息</p>
                            </div>
                        `;
                    }
                }

                // 更新面包屑导航
                updateBreadcrumb(productData);

            } catch (error) {
                showErrorState('显示商品信息失败');
            }
        }

        // 更新库存状态
        function updateStockStatus(productData) {
            const stockElement = document.querySelector('.product-stock');
            if (!stockElement) return;

            let stockText = '';
            let stockClass = '';
            let iconClass = 'fa-check-circle';

            if (productData.status === '3') { // 售罄
                stockText = '售空';
                stockClass = 'out';
                iconClass = 'fa-times-circle';
            } else if (productData.status === '2') { // 下架
                stockText = '已下架';
                stockClass = 'out';
                iconClass = 'fa-times-circle';
            } else {
                stockText = '库存充足';
                stockClass = '';
                iconClass = 'fa-check-circle';
            }

            stockElement.className = `product-stock ${stockClass}`;
            stockElement.innerHTML = `
                <i class="fa-solid ${iconClass}"></i>
                ${stockText}
            `;
        }

        // 渲染商品参数
        function renderProductParams(productData) {
            const paramsContainer = document.getElementById('product-params');
            if (!paramsContainer) return;

            // 检查是否有attach数组
            if (!productData.attach || !Array.isArray(productData.attach) || productData.attach.length === 0) {
                paramsContainer.innerHTML = '';
                return;
            }

            // 生成参数标签HTML
            let tagsHTML = '';
            productData.attach.forEach((param) => {
                if (param.name) {
                    tagsHTML += `
                        <span class="param-tag">
                            ${param.name}
                        </span>
                    `;
                }
            });

            // 如果有参数，显示参数区域
            if (tagsHTML) {
                paramsContainer.innerHTML = `
                    <div class="params-title">所需参数</div>
                    <div class="params-tags">
                        ${tagsHTML}
                    </div>
                `;
            } else {
                paramsContainer.innerHTML = '';
            }
        }

        // 更新面包屑导航
        function updateBreadcrumb(productData) {
            const breadcrumbElement = document.querySelector('.breadcrumb');
            if (breadcrumbElement) {
                breadcrumbElement.innerHTML = `
                    <div class="breadcrumb-item"><a href="/">首页</a></div>
                    <div class="breadcrumb-item"><span>商品列表</span></div>
                    <div class="breadcrumb-item">${productData.name}</div>
                `;
            }
        }

        // 显示加载状态
        function showLoadingState() {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.opacity = '0.5';
                mainContent.style.pointerEvents = 'none';
            }
        }

        // 隐藏加载状态
        function hideLoadingState() {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.opacity = '1';
                mainContent.style.pointerEvents = 'auto';
            }
        }

        // 显示错误状态
        function showErrorState(message) {
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #666;">
                        <i class="fa-solid fa-exclamation-triangle" style="font-size: 48px; color: #ff6b6b; margin-bottom: 20px;"></i>
                        <h3>加载失败</h3>
                        <p>${message}</p>
                        <button onclick="window.location.reload()" style="
                            background: var(--primary-color);
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin-top: 20px;
                        ">重新加载</button>
                    </div>
                `;
            }
        }

        // 生成MD5签名
        function generateMD5(text) {
            return md5(text);
        }

        // MD5算法实现
        function md5(string) {
            function md5_RotateLeft(lValue, iShiftBits) {
                return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
            }
            function md5_AddUnsigned(lX,lY) {
                var lX4,lY4,lX8,lY8,lResult;
                lX8 = (lX & 0x80000000);
                lY8 = (lY & 0x80000000);
                lX4 = (lX & 0x40000000);
                lY4 = (lY & 0x40000000);
                lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
                if (lX4 & lY4) {
                    return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
                }
                if (lX4 | lY4) {
                    if (lResult & 0x40000000) {
                        return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                    } else {
                        return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                    }
                } else {
                    return (lResult ^ lX8 ^ lY8);
                }
            }
            function md5_F(x,y,z) { return (x & y) | ((~x) & z); }
            function md5_G(x,y,z) { return (x & z) | (y & (~z)); }
            function md5_H(x,y,z) { return (x ^ y ^ z); }
            function md5_I(x,y,z) { return (y ^ (x | (~z))); }
            function md5_FF(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_GG(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_HH(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_II(a,b,c,d,x,s,ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_ConvertToWordArray(string) {
                var lWordCount;
                var lMessageLength = string.length;
                var lNumberOfWords_temp1=lMessageLength + 8;
                var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
                var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
                var lWordArray=Array(lNumberOfWords-1);
                var lBytePosition = 0;
                var lByteCount = 0;
                while ( lByteCount < lMessageLength ) {
                    lWordCount = (lByteCount-(lByteCount % 4))/4;
                    lBytePosition = (lByteCount % 4)*8;
                    lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
                    lByteCount++;
                }
                lWordCount = (lByteCount-(lByteCount % 4))/4;
                lBytePosition = (lByteCount % 4)*8;
                lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
                lWordArray[lNumberOfWords-2] = lMessageLength<<3;
                lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
                return lWordArray;
            };
            function md5_WordToHex(lValue) {
                var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
                for (lCount = 0;lCount<=3;lCount++) {
                    lByte = (lValue>>>(lCount*8)) & 255;
                    WordToHexValue_temp = "0" + lByte.toString(16);
                    WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length-2,2);
                }
                return WordToHexValue;
            };
            function md5_Utf8Encode(string) {
                string = string.replace(/\r\n/g,"\n");
                var utftext = "";
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    }
                    else if((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                    else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                }
                return utftext;
            };
            var x=Array();
            var k,AA,BB,CC,DD,a,b,c,d;
            var S11=7, S12=12, S13=17, S14=22;
            var S21=5, S22=9 , S23=14, S24=20;
            var S31=4, S32=11, S33=16, S34=23;
            var S41=6, S42=10, S43=15, S44=21;
            string = md5_Utf8Encode(string);
            x = md5_ConvertToWordArray(string);
            a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
            for (k=0;k<x.length;k+=16) {
                AA=a; BB=b; CC=c; DD=d;
                a=md5_FF(a,b,c,d,x[k+0], S11,0xD76AA478);
                d=md5_FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
                c=md5_FF(c,d,a,b,x[k+2], S13,0x242070DB);
                b=md5_FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
                a=md5_FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
                d=md5_FF(d,a,b,c,x[k+5], S12,0x4787C62A);
                c=md5_FF(c,d,a,b,x[k+6], S13,0xA8304613);
                b=md5_FF(b,c,d,a,x[k+7], S14,0xFD469501);
                a=md5_FF(a,b,c,d,x[k+8], S11,0x698098D8);
                d=md5_FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
                c=md5_FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
                b=md5_FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
                a=md5_FF(a,b,c,d,x[k+12],S11,0x6B901122);
                d=md5_FF(d,a,b,c,x[k+13],S12,0xFD987193);
                c=md5_FF(c,d,a,b,x[k+14],S13,0xA679438E);
                b=md5_FF(b,c,d,a,x[k+15],S14,0x49B40821);
                a=md5_GG(a,b,c,d,x[k+1], S21,0xF61E2562);
                d=md5_GG(d,a,b,c,x[k+6], S22,0xC040B340);
                c=md5_GG(c,d,a,b,x[k+11],S23,0x265E5A51);
                b=md5_GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
                a=md5_GG(a,b,c,d,x[k+5], S21,0xD62F105D);
                d=md5_GG(d,a,b,c,x[k+10],S22,0x2441453);
                c=md5_GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
                b=md5_GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
                a=md5_GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
                d=md5_GG(d,a,b,c,x[k+14],S22,0xC33707D6);
                c=md5_GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
                b=md5_GG(b,c,d,a,x[k+8], S24,0x455A14ED);
                a=md5_GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
                d=md5_GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
                c=md5_GG(c,d,a,b,x[k+7], S23,0x676F02D9);
                b=md5_GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
                a=md5_HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
                d=md5_HH(d,a,b,c,x[k+8], S32,0x8771F681);
                c=md5_HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
                b=md5_HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
                a=md5_HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
                d=md5_HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
                c=md5_HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
                b=md5_HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
                a=md5_HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
                d=md5_HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
                c=md5_HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
                b=md5_HH(b,c,d,a,x[k+6], S34,0x4881D05);
                a=md5_HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
                d=md5_HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
                c=md5_HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
                b=md5_HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
                a=md5_II(a,b,c,d,x[k+0], S41,0xF4292244);
                d=md5_II(d,a,b,c,x[k+7], S42,0x432AFF97);
                c=md5_II(c,d,a,b,x[k+14],S43,0xAB9423A7);
                b=md5_II(b,c,d,a,x[k+5], S44,0xFC93A039);
                a=md5_II(a,b,c,d,x[k+12],S41,0x655B59C3);
                d=md5_II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
                c=md5_II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
                b=md5_II(b,c,d,a,x[k+1], S44,0x85845DD1);
                a=md5_II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
                d=md5_II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
                c=md5_II(c,d,a,b,x[k+6], S43,0xA3014314);
                b=md5_II(b,c,d,a,x[k+13],S44,0x4E0811A1);
                a=md5_II(a,b,c,d,x[k+4], S41,0xF7537E82);
                d=md5_II(d,a,b,c,x[k+11],S42,0xBD3AF235);
                c=md5_II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
                b=md5_II(b,c,d,a,x[k+9], S44,0xEB86D391);
                a=md5_AddUnsigned(a,AA);
                b=md5_AddUnsigned(b,BB);
                c=md5_AddUnsigned(c,CC);
                d=md5_AddUnsigned(d,DD);
            }
            return (md5_WordToHex(a)+md5_WordToHex(b)+md5_WordToHex(c)+md5_WordToHex(d)).toLowerCase();
        }

        // 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');
        
        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');

            // 切换菜单图标
            const menuIcon = this.querySelector('.fa-solid');
            if (menuIcon.classList.contains('fa-bars')) {
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-times');
            } else {
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
            }
        });

        // 标签页切换 (现在只有商品详情一个标签，保留代码以备将来扩展)
        const tabItems = document.querySelectorAll('.tab-item');
        const tabContents = document.querySelectorAll('.tab-content');

        tabItems.forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有active类
                tabItems.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // 添加active类到当前点击的标签和对应内容
                this.classList.add('active');
                const targetTab = document.getElementById(this.dataset.tab);
                if (targetTab) {
                    targetTab.classList.add('active');
                }
            });
        });
        
        // 移动端底部购买按钮
        const mobileBuyBtn = document.querySelector('.mobile-buy-btn');

        mobileBuyBtn.addEventListener('click', function() {
            // 跳转到订单确认页面，传递商品ID
            if (currentProductId) {
                window.location.href = `/confirmOrder/?id=${currentProductId}`;
            } else {
                alert('商品信息加载失败，请刷新页面重试');
            }
        });

        // 桌面端购买按钮
        const buyNowBtn = document.querySelector('.buy-now-btn');

        buyNowBtn.addEventListener('click', function() {
            // 跳转到订单确认页面，传递商品ID
            if (currentProductId) {
                window.location.href = `/confirmOrder/?id=${currentProductId}`;
            } else {
                alert('商品信息加载失败，请刷新页面重试');
            }
        });

        // 显示商品公告模态框
        function showProductNoticeModal(content) {
            // 检查是否已存在模态框，如果存在则先移除
            const existingModal = document.getElementById('noticeModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // 创建模态框元素
            const modalHTML = `
                <div class="modal fade" id="noticeModal" tabindex="-1" aria-labelledby="noticeModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content notice-modal-content">
                            <div class="modal-header notice-modal-header">
                                <div class="notice-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <h5 class="modal-title notice-modal-title" id="noticeModalLabel">商品公告</h5>
                                <button type="button" class="btn-close notice-btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body notice-modal-body">
                                <div id="notice-content" class="notice-content"></div>
                            </div>
                            <div class="modal-footer notice-modal-footer">
                                <button type="button" class="btn notice-btn-primary" data-bs-dismiss="modal">
                                    <i class="fas fa-check-circle"></i>
                                    我知道了
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 将模态框添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            
            // 设置公告内容
            document.getElementById('notice-content').innerHTML = content;
            
            // 显示模态框 - 添加延时确保DOM已插入
            setTimeout(() => {
                try {
                    // 检查Bootstrap是否可用
                    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                        const noticeModal = new bootstrap.Modal(document.getElementById('noticeModal'));
                        noticeModal.show();
                    } else {
                        // 如果Bootstrap不可用，使用原生方式显示
                        const modal = document.getElementById('noticeModal');
                        modal.style.display = 'block';
                        modal.classList.add('show');
                        document.body.classList.add('modal-open');
                        
                        // 添加关闭事件
                        const closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"]');
                        closeButtons.forEach(btn => {
                            btn.addEventListener('click', () => {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                document.body.classList.remove('modal-open');
                                modal.remove();
                            });
                        });
                        
                        // 点击背景关闭
                        modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                document.body.classList.remove('modal-open');
                                modal.remove();
                            }
                        });
                    }
                } catch (error) {
                    console.error('显示商品公告模态框时出错:', error);
                    // 降级处理：使用alert显示公告内容
                    alert('商品公告：\n' + content.replace(/<[^>]*>/g, ''));
                }
            }, 100);
        }