// 移动端菜单切换
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');
        
        if (mobileMenuBtn && navMenu) {
            mobileMenuBtn.addEventListener('click', function() {
                navMenu.classList.toggle('active');
            });
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const errorContainer = document.querySelector('.error-container');
            if (errorContainer) {
                errorContainer.style.opacity = '0';
                errorContainer.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    errorContainer.style.transition = 'all 0.6s ease';
                    errorContainer.style.opacity = '1';
                    errorContainer.style.transform = 'translateY(0)';
                }, 100);
            }
        });