// MD5计算函数
        function md5(string) {
            function md5_RotateLeft(lValue, lShiftBits) {
                return (lValue << lShiftBits) | (lValue >>> (32 - lShiftBits));
            }
            function md5_AddUnsigned(lX, lY) {
                var lX4, lY4, lX8, lY8, lResult;
                lX8 = (lX & 0x80000000);
                lY8 = (lY & 0x80000000);
                lX4 = (lX & 0x40000000);
                lY4 = (lY & 0x40000000);
                lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
                if (lX4 & lY4) {
                    return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
                }
                if (lX4 | lY4) {
                    if (lResult & 0x40000000) {
                        return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
                    } else {
                        return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
                    }
                } else {
                    return (lResult ^ lX8 ^ lY8);
                }
            }
            function md5_F(x, y, z) { return (x & y) | ((~x) & z); }
            function md5_G(x, y, z) { return (x & z) | (y & (~z)); }
            function md5_H(x, y, z) { return (x ^ y ^ z); }
            function md5_I(x, y, z) { return (y ^ (x | (~z))); }
            function md5_FF(a, b, c, d, x, s, ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_GG(a, b, c, d, x, s, ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_HH(a, b, c, d, x, s, ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_II(a, b, c, d, x, s, ac) {
                a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
                return md5_AddUnsigned(md5_RotateLeft(a, s), b);
            };
            function md5_ConvertToWordArray(string) {
                var lWordCount;
                var lMessageLength = string.length;
                var lNumberOfWords_temp1 = lMessageLength + 8;
                var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
                var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
                var lWordArray = Array(lNumberOfWords - 1);
                var lBytePosition = 0;
                var lByteCount = 0;
                while (lByteCount < lMessageLength) {
                    lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                    lBytePosition = (lByteCount % 4) * 8;
                    lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition));
                    lByteCount++;
                }
                lWordCount = (lByteCount - (lByteCount % 4)) / 4;
                lBytePosition = (lByteCount % 4) * 8;
                lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
                lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
                lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
                return lWordArray;
            };
            function md5_WordToHex(lValue) {
                var WordToHexValue = "", WordToHexValue_temp = "", lByte, lCount;
                for (lCount = 0; lCount <= 3; lCount++) {
                    lByte = (lValue >>> (lCount * 8)) & 255;
                    WordToHexValue_temp = "0" + lByte.toString(16);
                    WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
                }
                return WordToHexValue;
            };
            function md5_Utf8Encode(string) {
                string = string.replace(/\r\n/g, "\n");
                var utftext = "";
                for (var n = 0; n < string.length; n++) {
                    var c = string.charCodeAt(n);
                    if (c < 128) {
                        utftext += String.fromCharCode(c);
                    }
                    else if ((c > 127) && (c < 2048)) {
                        utftext += String.fromCharCode((c >> 6) | 192);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                    else {
                        utftext += String.fromCharCode((c >> 12) | 224);
                        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                        utftext += String.fromCharCode((c & 63) | 128);
                    }
                }
                return utftext;
            };
            var x = Array();
            var k, AA, BB, CC, DD, a, b, c, d;
            var S11 = 7, S12 = 12, S13 = 17, S14 = 22;
            var S21 = 5, S22 = 9, S23 = 14, S24 = 20;
            var S31 = 4, S32 = 11, S33 = 16, S34 = 23;
            var S41 = 6, S42 = 10, S43 = 15, S44 = 21;
            string = md5_Utf8Encode(string);
            x = md5_ConvertToWordArray(string);
            a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
            for (k = 0; k < x.length; k += 16) {
                AA = a; BB = b; CC = c; DD = d;
                a = md5_FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
                d = md5_FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
                c = md5_FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
                b = md5_FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
                a = md5_FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
                d = md5_FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
                c = md5_FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
                b = md5_FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
                a = md5_FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
                d = md5_FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
                c = md5_FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
                b = md5_FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
                a = md5_FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
                d = md5_FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
                c = md5_FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
                b = md5_FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
                a = md5_GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
                d = md5_GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
                c = md5_GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
                b = md5_GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
                a = md5_GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
                d = md5_GG(d, a, b, c, x[k + 10], S22, 0x2441453);
                c = md5_GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
                b = md5_GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
                a = md5_GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
                d = md5_GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
                c = md5_GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
                b = md5_GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
                a = md5_GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
                d = md5_GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
                c = md5_GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
                b = md5_GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
                a = md5_HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
                d = md5_HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
                c = md5_HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
                b = md5_HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
                a = md5_HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
                d = md5_HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
                c = md5_HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
                b = md5_HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
                a = md5_HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
                d = md5_HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
                c = md5_HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
                b = md5_HH(b, c, d, a, x[k + 6], S34, 0x4881D05);
                a = md5_HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
                d = md5_HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
                c = md5_HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
                b = md5_HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
                a = md5_II(a, b, c, d, x[k + 0], S41, 0xF4292244);
                d = md5_II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
                c = md5_II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
                b = md5_II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
                a = md5_II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
                d = md5_II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
                c = md5_II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
                b = md5_II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
                a = md5_II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
                d = md5_II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
                c = md5_II(c, d, a, b, x[k + 6], S43, 0xA3014314);
                b = md5_II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
                a = md5_II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
                d = md5_II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
                c = md5_II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
                b = md5_II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
                a = md5_AddUnsigned(a, AA);
                b = md5_AddUnsigned(b, BB);
                c = md5_AddUnsigned(c, CC);
                d = md5_AddUnsigned(d, DD);
            }
            return (md5_WordToHex(a) + md5_WordToHex(b) + md5_WordToHex(c) + md5_WordToHex(d)).toLowerCase();
        }

        // 用户认证相关函数
        // 从Cookie获取Token
        function getTokenFromCookie() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'token') {
                    return value;
                }
            }
            return null;
        }

        // 从localStorage获取Token
        function getTokenFromStorage() {
            return localStorage.getItem('token');
        }

        // 保存用户信息到localStorage
        function saveUserInfo(userInfo) {
            if (userInfo) {
                localStorage.setItem('userId', userInfo.id);
                localStorage.setItem('userKey', userInfo.user_key);
                // 可以根据需要保存其他用户信息
            }
        }

        // 检查用户是否已登录
        function checkUserLogin() {
            // 先检查Cookie中是否有token
            const cookieToken = getTokenFromCookie();
            if (cookieToken) {
                return true;
            }

            // 再检查localStorage中是否有token
            const storageToken = getTokenFromStorage();
            if (!storageToken) {
                return false;
            }
            return true;
        }

        // 处理需要认证的链接点击
        function handleAuthenticatedLink(e, targetUrl) {
            e.preventDefault();

            if (checkUserLogin()) {
                // 已登录，跳转到目标页面
                window.location.href = targetUrl;
            } else {
                // 未登录，跳转到登录页面
                window.location.href = '/login/';
            }
        }

        // 全局变量存储用户信息
        let currentUser = null;

        // 获取用户信息
        async function getCurrentUser() {
            if (currentUser) {
                return currentUser;
            }

            try {
                const response = await fetch('/user/api/GetUser/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': localStorage.getItem('token') || ''
                    }
                });

                const data = await response.json();
                if (data.code === 200) {
                    currentUser = data.user;
                    // 更新Token
                    const newToken = response.headers.get('Token');
                    if (newToken) {
                        localStorage.setItem('token', newToken);
                    }
                    return currentUser;
                } else {
                    return null;
                }
            } catch (error) {
                return null;
            }
        }

        // 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');

        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');

            // 切换菜单图标
            const menuIcon = this.querySelector('.material-icons');
            if (menuIcon.textContent === 'menu') {
                menuIcon.textContent = 'close';
            } else {
                menuIcon.textContent = 'menu';
            }
        });

        // 导航栏跳转逻辑
        document.addEventListener('DOMContentLoaded', function() {
            // 处理个人中心链接点击
            const profileLink = document.getElementById('profileLink');
            if (profileLink) {
                profileLink.addEventListener('click', function(e) {
                    handleAuthenticatedLink(e, '/profile/');
                });
            }

            // 点击导航链接时关闭移动菜单
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 768) {
                        navMenu.classList.remove('active');
                        const menuIcon = mobileMenuBtn.querySelector('.material-icons');
                        menuIcon.textContent = 'menu';
                    }
                });
            });
        });
        
        // 分类面板功能
        document.addEventListener('DOMContentLoaded', function() {

            // 添加浏览器前进后退事件监听（History API支持）
            window.addEventListener('popstate', function(event) {

                // 获取当前URL中的分类ID
                const currentCategoryId = getUrlParameter('id');

                if (currentCategoryId) {
                    // 如果有分类ID，加载对应分类的商品
                    const categoryName = event.state ? event.state.categoryName : '';
                    loadProductsByCategoryId(currentCategoryId, categoryName);
                } else {
                    // 如果没有分类ID，加载所有商品
                    loadProducts(1, currentSort);
                }

                // 更新分类面板中的活跃状态
                updateCategoryActiveState(currentCategoryId);
            });
            // 筛选面板元素
            const filterToggle = document.getElementById('filterToggle');
            const advancedFilterPanel = document.getElementById('advancedFilterPanel');
            const closeFilter = document.querySelector('.close-filter');
            const filterBackdrop = document.getElementById('filterBackdrop');
            const categoryTree = document.getElementById('categoryTree');

            // 确保面板初始状态正确（隐藏）
            advancedFilterPanel.classList.remove('open');
            filterBackdrop.classList.remove('visible');
            document.body.style.overflow = '';

            // 强制设置面板初始位置和层级
            advancedFilterPanel.style.right = '-480px'; // 根据新宽度调整隐藏位置
            advancedFilterPanel.style.position = 'fixed';
            advancedFilterPanel.style.zIndex = '1300'; // 确保在遮罩层之上
            advancedFilterPanel.style.height = '100vh'; // 确保完全适应屏幕高度
            advancedFilterPanel.style.width = '460px'; // 设置新的宽度
            advancedFilterPanel.style.top = '0';
            advancedFilterPanel.style.bottom = '0';

            // 调试：检查面板的实际位置
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(advancedFilterPanel);
            }, 100);
            
            // 打开筛选面板
            filterToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();


                // 显示面板
                advancedFilterPanel.classList.add('open');
                filterBackdrop.classList.add('visible');
                document.body.style.overflow = 'hidden'; // 防止背景滚动


                // 如果分类还没有加载，则加载分类
                if (!categoryTree.querySelector('.category-item')) {
                    loadCategories();
                }
            });

            // 关闭筛选面板
            function closeFilterPanel() {
                // 调试日志
                advancedFilterPanel.classList.remove('open');
                filterBackdrop.classList.remove('visible');
                document.body.style.overflow = ''; // 恢复滚动
            }
            
            closeFilter.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeFilterPanel();
            });

            filterBackdrop.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeFilterPanel();
            });
            
            // 从API加载分类数据
            async function loadCategoriesFromAPI() {
                try {

                    // 获取用户信息
                    const user = await getCurrentUser();
                    if (!user) {
                        showCategoryError('无法获取用户信息，请刷新页面重试');
                        return;
                    }


                    // 生成签名
                    const signData = user.id + user.user_key;
                    const sign = md5(signData);


                    // 构建请求数据
                    const formData = new FormData();
                    formData.append('userId', user.id);
                    formData.append('sign', sign);

                    // 发送API请求
                    const response = await fetch('/user/api/categories/', {
                        method: 'POST',
                        headers: {
                            'Token': localStorage.getItem('token') || ''
                        },
                        body: formData
                    });


                    const data = await response.json();

                    if (data.code === 200) {
                        // 成功获取分类数据
                        if (data.data && data.data.length > 0) {
                            // 转换数据格式以匹配现有的渲染函数
                            const formattedCategories = data.data.map(category => ({
                                id: category.id,
                                name: category.name,
                                icon: category.image || '/static/images/product-placeholder.svg',
                                subcategories: category.children.map(child => ({
                                    id: child.id,
                                    name: child.name,
                                    icon: child.image || '/static/images/product-placeholder.svg'
                                }))
                            }));

                            renderCategories(formattedCategories);
                        } else {
                            showCategoryEmpty();
                        }
                    } else {
                        // API返回错误
                        showCategoryError(data.message || '获取分类数据失败');
                    }

                } catch (error) {
                    showCategoryError('网络错误，请检查网络连接后重试');
                }
            }

            // 加载分类数据
            function loadCategories() {

                // 显示加载状态
                categoryTree.innerHTML = `
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">加载分类中...</div>
                    </div>
                `;


                // 调用真实API获取分类数据
                loadCategoriesFromAPI();
            }

            // 显示分类加载错误
            function showCategoryError(message) {
                categoryTree.innerHTML = `
                    <div class="error-state" style="padding: 30px; text-align: center;">
                        <div class="error-icon" style="font-size: 48px; margin-bottom: 15px; color: #ff6b6b;">⚠️</div>
                        <div class="error-text" style="font-size: 16px; color: #666; margin-bottom: 20px;">${message}</div>
                        <button class="retry-button" style="
                            padding: 10px 20px;
                            background: var(--primary-color);
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='var(--primary-dark)'"
                           onmouseout="this.style.background='var(--primary-color)'">重新加载</button>
                    </div>
                `;

                // 添加重试按钮事件
                const retryButton = categoryTree.querySelector('.retry-button');
                if (retryButton) {
                    retryButton.addEventListener('click', function() {
                        loadCategories();
                    });
                }
            }

            // 显示分类为空状态
            function showCategoryEmpty() {
                categoryTree.innerHTML = `
                    <div class="empty-state" style="padding: 30px; text-align: center;">
                        <div class="empty-state-icon" style="font-size: 48px; margin-bottom: 15px; color: #ddd;">📂</div>
                        <div class="empty-state-text" style="font-size: 16px; color: #999;">暂无分类数据</div>
                    </div>
                `;
            }

            // 渲染分类树
            function renderCategories(categories) {
                categoryTree.innerHTML = '';

                categories.forEach(category => {
                    // 创建分类项
                    const categoryItem = document.createElement('div');
                    categoryItem.className = 'category-item';
                    categoryItem.dataset.categoryId = category.id;
                    
                    // 创建分类头部
                    const categoryHeader = document.createElement('div');
                    categoryHeader.className = 'category-header';
                    
                    // 添加分类图标
                    const categoryIcon = document.createElement('img');
                    categoryIcon.className = 'category-icon';
                    categoryIcon.src = category.icon;
                    categoryIcon.alt = category.name;
                    // 添加图片加载失败的回退方案
                    categoryIcon.onerror = function() {
                        this.src = '/static/images/product-placeholder.svg';
                    };
                    categoryHeader.appendChild(categoryIcon);
                    
                    // 添加分类名称
                    const categoryName = document.createElement('div');
                    categoryName.className = 'category-name';
                    categoryName.textContent = category.name;
                    categoryHeader.appendChild(categoryName);
                    
                    // 添加展开/收起图标
                    const categoryToggle = document.createElement('span');
                    categoryToggle.className = 'material-icons category-toggle';
                    categoryToggle.textContent = 'keyboard_arrow_down';
                    categoryHeader.appendChild(categoryToggle);
                    
                    categoryItem.appendChild(categoryHeader);
                    
                    // 创建子分类列表
                    if (category.subcategories && category.subcategories.length > 0) {
                        const subcategoryList = document.createElement('div');
                        subcategoryList.className = 'subcategory-list';
                        
                        // 添加入场动画的延迟
                        let delay = 0;
                        
                        category.subcategories.forEach(subcategory => {
                            // 创建子分类项
                            const subcategoryItem = document.createElement('div');
                            subcategoryItem.className = 'subcategory-item';
                            subcategoryItem.dataset.categoryId = subcategory.id;
                            // 添加延迟动画样式
                            subcategoryItem.style.opacity = '0';
                            subcategoryItem.style.transform = 'translateX(10px)';
                            subcategoryItem.style.transition = `all 0.3s ease ${delay}s`;
                            delay += 0.05; // 每个项目增加50毫秒延迟
                            
                            // 添加子分类图标
                            const subcategoryIcon = document.createElement('img');
                            subcategoryIcon.className = 'subcategory-icon';
                            subcategoryIcon.src = subcategory.icon;
                            subcategoryIcon.alt = subcategory.name;
                            // 添加图片加载失败的回退方案
                            subcategoryIcon.onerror = function() {
                                this.src = '/static/images/product-placeholder.svg';
                            };
                            subcategoryItem.appendChild(subcategoryIcon);
                            
                            // 添加子分类名称
                            const subcategoryName = document.createElement('div');
                            subcategoryName.className = 'subcategory-name';
                            subcategoryName.textContent = subcategory.name;
                            subcategoryItem.appendChild(subcategoryName);
                            
                            // 添加箭头图标
                            const subcategoryArrow = document.createElement('span');
                            subcategoryArrow.className = 'material-icons subcategory-arrow';
                            subcategoryArrow.textContent = 'arrow_forward_ios';
                            subcategoryItem.appendChild(subcategoryArrow);
                            
                            // 添加点击波纹效果
                            subcategoryItem.addEventListener('mousedown', function(e) {
                                const ripple = document.createElement('div');
                                ripple.className = 'ripple';
                                this.appendChild(ripple);
                                
                                const rect = this.getBoundingClientRect();
                                const size = Math.max(rect.width, rect.height);
                                ripple.style.width = ripple.style.height = `${size}px`;
                                
                                const x = e.clientX - rect.left - size / 2;
                                const y = e.clientY - rect.top - size / 2;
                                ripple.style.left = `${x}px`;
                                ripple.style.top = `${y}px`;
                                
                                setTimeout(() => {
                                    ripple.remove();
                                }, 600);
                            });
                            
                            // 点击子分类使用History API更新URL并加载商品
                            subcategoryItem.addEventListener('click', function() {
                                // 添加选中动画
                                this.classList.add('clicked');

                                // 延迟执行，让动画有时间显示
                                setTimeout(() => {
                                    // 关闭分类面板
                                    closeFilterPanel();

                                    // 使用History API更新URL，不刷新页面
                                    const newUrl = '/productlist/?id=' + subcategory.id;
                                    history.pushState(
                                        { categoryId: subcategory.id, categoryName: subcategory.name },
                                        '',
                                        newUrl
                                    );

                                    // 加载该分类的商品（使用现有的加载机制）
                                    loadProductsByCategoryId(subcategory.id, subcategory.name);

                                    // 移除所有活跃状态
                                    document.querySelectorAll('.subcategory-item.active').forEach(el => {
                                        el.classList.remove('active');
                                    });

                                    // 添加活跃状态
                                    this.classList.add('active');

                                    // 延迟后移除点击动画类
                                    setTimeout(() => {
                                        this.classList.remove('clicked');
                                    }, 300);
                                }, 150);
                            });
                            
                            subcategoryList.appendChild(subcategoryItem);
                        });
                        
                        categoryItem.appendChild(subcategoryList);
                        
                        // 点击分类头部展开/收起子分类
                        categoryHeader.addEventListener('click', function(e) {
                            // 阻止冒泡
                            e.stopPropagation();
                            
                            // 展开/收起子分类
                            categoryItem.classList.toggle('expanded');
                            categoryHeader.classList.toggle('expanded');
                            
                            // 如果展开，添加动画
                            if (categoryItem.classList.contains('expanded')) {
                                // 设置子分类项动画
                                setTimeout(() => {
                                    const subcategoryItems = categoryItem.querySelectorAll('.subcategory-item');
                                    subcategoryItems.forEach(item => {
                                        item.style.opacity = '1';
                                        item.style.transform = 'translateX(0)';
                                    });
                                }, 50);
                                
                                // 移除所有其他展开的分类
                                document.querySelectorAll('.category-item.expanded').forEach(item => {
                                    if (item !== categoryItem) {
                                        item.classList.remove('expanded');
                                        item.querySelector('.category-header').classList.remove('expanded');
                                        
                                        // 重置其子分类的动画状态
                                        const hiddenItems = item.querySelectorAll('.subcategory-item');
                                        hiddenItems.forEach(hiddenItem => {
                                            hiddenItem.style.opacity = '0';
                                            hiddenItem.style.transform = 'translateX(10px)';
                                        });
                                    }
                                });
                            } else {
                                // 如果收起，重置子分类的动画状态
                                const subcategoryItems = categoryItem.querySelectorAll('.subcategory-item');
                                subcategoryItems.forEach(item => {
                                    item.style.opacity = '0';
                                    item.style.transform = 'translateX(10px)';
                                });
                            }
                        });
                    }
                    
                    categoryTree.appendChild(categoryItem);
                });
                
                // 默认展开第一个分类
                setTimeout(() => {
                    const firstCategory = categoryTree.querySelector('.category-item');
                    if (firstCategory) {
                        const header = firstCategory.querySelector('.category-header');
                        header.click();
                    }
                }, 500);
            }
            
            // 按分类ID加载商品（History API方案专用）
            function loadProductsByCategoryId(categoryId, categoryName) {

                // 使用现有的loadProducts函数，传递分类ID作为筛选条件
                loadProducts(1, currentSort, { categoryId: categoryId });

                // 更新页面标题（可选）
                if (categoryName) {
                    document.title = `${categoryName} - 商品列表 - 无名SUP`;
                }
            }

            // 更新分类面板中的活跃状态
            function updateCategoryActiveState(categoryId) {
                // 移除所有活跃状态
                document.querySelectorAll('.subcategory-item.active').forEach(el => {
                    el.classList.remove('active');
                });

                // 如果有分类ID，添加对应的活跃状态
                if (categoryId) {
                    const targetItem = document.querySelector(`.subcategory-item[data-category-id="${categoryId}"]`);
                    if (targetItem) {
                        targetItem.classList.add('active');
                    }
                }
            }

            // 获取URL参数的辅助函数
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }
            
            // 商品列表相关功能
            // DOM元素引用
            const productsGrid = document.getElementById('productsGrid');
            const loadingState = document.getElementById('loadingState');
            const emptyState = document.getElementById('emptyState');
            const pagination = document.getElementById('pagination');
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');
            const sortOptions = document.querySelectorAll('.sort-option');
            
            // 存储商品数据和分页信息的变量
            let productsData = [];
            let currentPage = 1;
            let totalPages = 1;
            let currentSort = 'default';
            let pageSize = 10;
            
            // 接口URL（在实际项目中应替换为正确的API路径）
            const API_URL = {
                products: '/api/products'
            };
            
            // 从API加载商品列表
            async function loadProductsFromAPI(page = 1, sort = 'default', filters = null) {
                try {

                    // 获取用户信息
                    const user = await getCurrentUser();
                    if (!user) {
                        showError('无法获取用户信息，请刷新页面重试');
                        return;
                    }


                    // 构建请求参数
                    let categoryId = null;

                    // 如果有筛选条件中的分类ID，使用它
                    if (filters && filters.categoryId) {
                        categoryId = filters.categoryId;
                    }

                    // 如果没有分类ID，尝试从URL参数获取
                    if (!categoryId) {
                        categoryId = getUrlParameter('id');
                    }

                    // 如果还是没有分类ID，获取所有商品（传空字符串或不传）
                    if (!categoryId) {
                        categoryId = '';
                    }

                    // 生成签名
                    const signData = categoryId + user.id + user.user_key;
                    const sign = md5(signData);


                    // 构建请求数据
                    const formData = new FormData();
                    formData.append('categoryId', categoryId);
                    formData.append('userId', user.id);
                    formData.append('sign', sign);

                    // 添加分页参数
                    formData.append('page', page);
                    formData.append('page_size', pageSize);

                    // 添加排序参数（如果后端支持）
                    if (sort && sort !== 'default') {
                        formData.append('sort', sort);
                    }

                    // 发送API请求
                    const response = await fetch('/user/api/productlist/', {
                        method: 'POST',
                        headers: {
                            'Token': localStorage.getItem('token') || ''
                        },
                        body: formData
                    });


                    const data = await response.json();

                    if (data.code === 200) {
                        // 处理新的分页响应格式
                        let products = [];
                        let paginationData = null;

                        if (data.data && data.data.items) {
                            // 新的分页格式
                            products = data.data.items || [];
                            paginationData = data.data.pagination || {};

                            // 更新分页信息
                            totalPages = paginationData.total_pages || 1;
                            currentPage = paginationData.current_page || page;
                            const totalRecords = paginationData.total || 0;

                        } else if (data.data && Array.isArray(data.data)) {
                            // 兼容旧格式
                            products = data.data;
                            totalPages = 1;
                            currentPage = 1;
                        }

                        // 成功获取商品数据
                        if (products && products.length > 0) {

                            // 应用客户端排序（如果后端不支持排序）
                            let sortedProducts = [...products];
                            if (sort && sort !== 'default') {
                                sortedProducts = applySorting(sortedProducts, sort);
                            }

                            renderRealProducts(sortedProducts);

                            // 更新分页控制器
                            if (totalPages > 1) {
                                updatePagination(totalPages, currentPage);
                            } else {
                                pagination.style.display = 'none';
                            }
                        } else {
                            showEmpty();
                        }
                    } else {
                        // API返回错误
                        showError(data.message || '获取商品列表失败');
                    }

                } catch (error) {
                    showError('网络错误，请检查网络连接后重试');
                }
            }

            // 应用客户端排序
            function applySorting(products, sort) {
                switch (sort) {
                    case 'price-asc':
                        return products.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
                    case 'price-desc':
                        return products.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
                    case 'sales':
                        return products.sort((a, b) => (b.sales_count || 0) - (a.sales_count || 0));
                    default:
                        return products;
                }
            }

            // 加载商品列表
            function loadProducts(page = 1, sort = 'default', filters = null) {
                // 显示加载状态
                loadingState.style.display = 'flex';
                emptyState.style.display = 'none';

                // 清空现有的商品卡片
                const productCards = productsGrid.querySelectorAll('.product-card');
                productCards.forEach(card => card.remove());

                // 调用真实API加载商品
                loadProductsFromAPI(page, sort, filters);
            }
            


            // 渲染真实商品数据
            function renderRealProducts(products) {
                // 隐藏加载状态
                loadingState.style.display = 'none';

                if (!products || products.length === 0) {
                    showEmpty();
                    return;
                }

                // 添加所有商品卡片
                products.forEach(product => {
                    const productCard = createRealProductCard(product);
                    productsGrid.appendChild(productCard);
                });

                // 隐藏分页控制（真实数据暂时不分页）
                pagination.style.display = 'none';
            }
            


            // 创建真实商品卡片
            function createRealProductCard(product) {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.setAttribute('data-product-id', product.id);

                // 商品图片
                const imageDiv = document.createElement('div');
                imageDiv.className = 'product-image';

                const image = document.createElement('img');
                // 根据image_type处理图片URL
                if (product.image_type === '1') {
                    // 本地图片
                    image.src = product.image || '/static/images/product-placeholder.svg';
                } else {
                    // 链接图片
                    image.src = product.image || '/static/images/product-placeholder.svg';
                }
                image.alt = product.name;
                image.onerror = function() {
                    this.src = '/static/images/product-placeholder.svg';
                };

                imageDiv.appendChild(image);

                // 商品信息区域
                const infoDiv = document.createElement('div');
                infoDiv.className = 'product-info';

                // 商品名称
                const nameDiv = document.createElement('h3');
                nameDiv.className = 'product-name';
                nameDiv.textContent = product.name;
                infoDiv.appendChild(nameDiv);

                // 商品ID
                const idDiv = document.createElement('p');
                idDiv.className = 'product-id';
                idDiv.textContent = `编号: ${product.id}`;
                infoDiv.appendChild(idDiv);

                // 商品标签
                const tagsDiv = document.createElement('div');
                tagsDiv.className = 'product-tags';

                // 商品类型标签
                const typeTag = document.createElement('span');
                typeTag.className = 'product-tag tag-type';
                typeTag.textContent = getProductTypeText(product.type);
                tagsDiv.appendChild(typeTag);

                // 商品状态标签
                const statusTag = document.createElement('span');
                statusTag.className = 'product-tag tag-status';
                statusTag.textContent = getProductStatusText(product.status);
                tagsDiv.appendChild(statusTag);

                // 销量标签
                if (product.sales_count !== undefined && product.sales_count !== null) {
                    const salesTag = document.createElement('span');
                    salesTag.className = 'product-tag tag-sales';
                    salesTag.textContent = `销量: ${product.sales_count}`;
                    tagsDiv.appendChild(salesTag);
                }

                // 热销标签（基于销量）
                if (product.sales_count > 50) {
                    const hotTag = document.createElement('span');
                    hotTag.className = 'product-tag tag-hot';
                    hotTag.textContent = '热销';
                    tagsDiv.appendChild(hotTag);
                }

                infoDiv.appendChild(tagsDiv);

                // 价格和按钮区域
                const priceSection = document.createElement('div');
                priceSection.className = 'product-price-section';

                // 价格
                const priceDiv = document.createElement('div');
                priceDiv.className = 'product-price';

                const currencySpan = document.createElement('span');
                currencySpan.className = 'price-currency';
                currencySpan.textContent = '¥';

                const price = parseFloat(product.price) || 0;
                const priceMain = document.createElement('span');
                priceMain.textContent = price.toFixed(2);

                priceDiv.appendChild(currencySpan);
                priceDiv.appendChild(priceMain);

                // 原价（模拟）
                const originalPrice = (price * 1.2).toFixed(2);
                const originalPriceDiv = document.createElement('div');
                originalPriceDiv.className = 'original-price';
                originalPriceDiv.textContent = `¥${originalPrice}`;

                // 查看详情按钮
                const detailButton = document.createElement('button');
                detailButton.className = 'view-detail-btn';
                detailButton.textContent = '查看详情';

                detailButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    navigateToRealProductDetail(product);
                });

                priceSection.appendChild(priceDiv);
                priceSection.appendChild(originalPriceDiv);
                priceSection.appendChild(detailButton);

                // 组装卡片
                card.appendChild(imageDiv);
                card.appendChild(infoDiv);
                card.appendChild(priceSection);

                // 点击卡片进入商品详情
                card.addEventListener('click', function() {
                    navigateToRealProductDetail(product);
                });

                return card;
            }
            
            // 更新分页控制器
            function updatePagination(total, current) {
                const pageItems = pagination.querySelectorAll('.page-item');
                pageItems.forEach(item => item.remove());
                
                // 添加页码
                const maxPageDisplay = 5; // 最多显示5个页码
                let startPage = Math.max(1, current - Math.floor(maxPageDisplay / 2));
                let endPage = Math.min(total, startPage + maxPageDisplay - 1);
                
                // 调整startPage，确保始终显示maxPageDisplay个页码（如果有足够的页码）
                if (endPage - startPage + 1 < maxPageDisplay && total >= maxPageDisplay) {
                    startPage = Math.max(1, endPage - maxPageDisplay + 1);
                }
                
                // 在上一页按钮后插入页码
                for (let i = startPage; i <= endPage; i++) {
                    const pageItem = document.createElement('div');
                    pageItem.className = 'page-item' + (i === current ? ' active' : '');
                    pageItem.textContent = i;
                    
                    pageItem.addEventListener('click', function() {
                        if (i !== current) {
                            loadProducts(i, currentSort);
                        }
                    });
                    
                    prevPage.parentNode.insertBefore(pageItem, nextPage);
                }
                
                // 更新上一页/下一页按钮状态
                prevPage.classList.toggle('disabled', current === 1);
                nextPage.classList.toggle('disabled', current === total);
                
                // 显示分页控制器
                pagination.style.display = 'flex';
            }
            
            // 显示商品为空状态
            function showEmpty() {
                loadingState.style.display = 'none';
                emptyState.style.display = 'flex';
                pagination.style.display = 'none';
            }
            
            // 显示错误信息
            function showError(message) {
                // 隐藏加载状态
                loadingState.style.display = 'none';
                
                // 创建错误状态
                const errorElement = document.createElement('div');
                errorElement.className = 'error-state';
                errorElement.innerHTML = `
                    <div class="error-icon">⚠️</div>
                    <div class="error-text">${message}</div>
                    <button class="retry-button">重试</button>
                `;
                
                const retryButton = errorElement.querySelector('.retry-button');
                retryButton.addEventListener('click', function() {
                    errorElement.remove();
                    loadProducts(currentPage, currentSort);
                });
                
                productsGrid.appendChild(errorElement);
            }
            


            // 处理真实商品购买
            function handleBuyRealProduct(product) {

                // 显示商品详细信息
                let attachInfo = '';
                if (product.attach && product.attach.length > 0) {
                    attachInfo = '\n\n商品参数：\n';
                    product.attach.forEach(param => {
                        attachInfo += `${param.name}: ${param.tip}\n`;
                    });
                }

                const message = `商品名称: ${product.name}\n价格: ¥${product.price}\n销量: ${product.sales_count}\n商品类型: ${getProductTypeText(product.type)}\n状态: ${getProductStatusText(product.status)}${attachInfo}\n\n确定要购买此商品吗？`;

                if (confirm(message)) {
                    alert('购买功能开发中，敬请期待！');
                }
            }

            // 跳转到真实商品详情页
            function navigateToRealProductDetail(product) {

                // 检查商品ID是否存在
                if (!product || !product.id) {
                    alert('商品信息错误，无法查看详情');
                    return;
                }

                // 构建商品详情页面URL
                const detailUrl = `/product-detail/?id=${encodeURIComponent(product.id)}`;


                // 跳转到商品详情页面
                try {
                    window.location.href = detailUrl;
                } catch (error) {
                    alert('页面跳转失败，请稍后重试');
                }
            }

            // 获取商品类型文本
            function getProductTypeText(type) {
                switch(type) {
                    case '1': return '卡密商品';
                    case '2': return '虚拟商品';
                    case '3': return '对接商品';
                    default: return '未知类型';
                }
            }

            // 获取商品状态文本
            function getProductStatusText(status) {
                switch(status) {
                    case '1': return '上架';
                    case '2': return '下架';
                    case '3': return '售罄';
                    default: return '未知状态';
                }
            }
            
            // 添加排序选项点击事件
            sortOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const sortBy = this.getAttribute('data-sort');
                    
                    // 更新激活状态
                    sortOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 如果选择了不同的排序方式，重新加载商品
                    if (sortBy !== currentSort) {
                        currentSort = sortBy;
                        loadProducts(1, currentSort);
                    }
                });
            });
            
            // 添加上一页/下一页点击事件
            prevPage.addEventListener('click', function() {
                if (!this.classList.contains('disabled') && currentPage > 1) {
                    loadProducts(currentPage - 1, currentSort);
                }
            });

            nextPage.addEventListener('click', function() {
                if (!this.classList.contains('disabled') && currentPage < totalPages) {
                    loadProducts(currentPage + 1, currentSort);
                }
            });
            



            // 初始化：检查是否有分类ID参数
            const categoryId = getUrlParameter('id');

            if (categoryId) {
                // 如果有分类ID，按分类加载商品数据

                // 使用统一的API调用方式，传递分类ID作为筛选条件
                loadProducts(currentPage, currentSort, { categoryId: categoryId });

                // 更新页面标题（如果后端传递了分类名称）
                const categoryName = '{{ category_name|default:"" }}';
                if (categoryName) {
                    document.title = `依思SUP - 商品列表`;

                    // 更新页面标题显示
                    const pageTitle = document.querySelector('.page-title');
                    if (pageTitle) {
                        pageTitle.textContent = `${categoryName} - 商品列表`;
                    }
                }
            } else {
                // 没有分类ID，加载所有商品
                loadProducts(currentPage, currentSort);
            }
        });