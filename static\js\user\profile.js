// 移动端菜单
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');
        
        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // 切换菜单图标
            const menuIcon = this.querySelector('i');
            if (menuIcon.classList.contains('fa-bars')) {
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-xmark');
            } else {
                menuIcon.classList.remove('fa-xmark');
                menuIcon.classList.add('fa-bars');
            }
        });
        
        // 侧边栏导航
        const sidebarLinks = document.querySelectorAll('.sidebar-link');
        
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // 移除所有active类
                sidebarLinks.forEach(l => l.classList.remove('active'));
                
                // 添加active类到当前点击的链接
                this.classList.add('active');
                
                // 如果是锚链接，阻止默认行为
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    
                    // 平滑滚动到目标位置
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        });
        
        // 充值按钮弹出模态框
        const rechargeBtn = document.querySelector('.recharge-btn');
        const rechargeModal = document.getElementById('rechargeModal');
        const closeBtn = document.querySelector('.close');
        const cancelBtn = document.querySelector('.cancel-btn');
        const confirmBtn = document.querySelector('.confirm-btn');
        
        rechargeBtn.addEventListener('click', async function() {
            rechargeModal.style.display = 'flex';

            // 显示加载状态
            const paymentContent = document.getElementById('rechargePaymentOptions');
            if (paymentContent) {
                paymentContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #999;">
                        <i class="fa-solid fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 15px;"></i>
                        <p>正在加载支付方式...</p>
                    </div>
                `;
            }

            // 加载支付方式
            try {
                await loadPaymentMethods();
            } catch (error) {
                // 如果API加载失败，显示空状态
                showEmptyPaymentState();
            }
        });
        
        closeBtn.addEventListener('click', function() {
            rechargeModal.style.display = 'none';
        });
        
        cancelBtn.addEventListener('click', function() {
            rechargeModal.style.display = 'none';
        });
        
        confirmBtn.addEventListener('click', async function() {
            const amount = document.getElementById('recharge-amount').value;
            const paymentMethod = document.querySelector('input[name="recharge_payment"]:checked');

            // 验证充值金额
            if (!amount || amount < 1) {
                alert('请输入有效的充值金额');
                return;
            }

            // 验证支付方式
            if (!paymentMethod) {
                alert('请选择支付方式');
                return;
            }

            // 获取支付方式相关数据
            const paymentChannelId = paymentMethod.value; // 支付渠道UUID
            const paymentWay = paymentMethod.getAttribute('data-payment-type'); // 支付方式

            if (!paymentChannelId || !paymentWay) {
                alert('支付方式数据错误，请重新选择');
                return;
            }

            // 显示处理状态
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 处理中...';

            try {
                // 提交充值请求
                await submitRechargeRequest(amount, paymentChannelId, paymentWay);
            } catch (error) {
                alert('充值请求失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '确认支付';
            }
        });
        
        // 点击模态框外部关闭模态框
        window.addEventListener('click', function(event) {
            if (event.target == rechargeModal) {
                rechargeModal.style.display = 'none';
            }
        });
        
        // 重置密钥按钮
        const resetKeyBtn = document.querySelector('.reset-key-btn');
        
        resetKeyBtn.addEventListener('click', function() {
            if (confirm('确定要重置API密钥吗？重置后原密钥将立即失效')) {
                document.getElementById('integration-key').value = 'sk_live_' + generateRandomString(48);
                alert('密钥已成功重置');
            }
        });
        
        // 复制密钥按钮
        const copyKeyBtn = document.querySelector('.copy-key-btn');
        
        copyKeyBtn.addEventListener('click', function() {
            const keyInput = document.getElementById('integration-key');
            const originalType = keyInput.type;
            
            // 临时改为文本以便复制
            keyInput.type = 'text';
            keyInput.select();
            document.execCommand('copy');
            
            // 恢复为密码类型
            keyInput.type = originalType;
            
            // 取消选择
            window.getSelection().removeAllRanges();
            
            // 显示提示
            alert('密钥已复制到剪贴板');
        });

        // 复制账号按钮
        const copyAccountBtn = document.querySelector('.copy-account-btn');

        copyAccountBtn.addEventListener('click', function() {
            const accountInput = document.getElementById('integration-account');

            // 选择并复制账号
            accountInput.select();
            document.execCommand('copy');

            // 取消选择
            window.getSelection().removeAllRanges();

            // 显示成功提示
            alert('账号已复制到剪贴板');
        });
        
        // 生成随机字符串
        function generateRandomString(length) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 全局变量存储支付方式数据
        let currentPaymentMethods = [];
        let currentUser = null;

        // MD5签名生成函数 - 使用简单的MD5实现
        function generateMD5(text) {
            // 简单的MD5实现，用于签名生成
            function md5cycle(x, k) {
                var a = x[0], b = x[1], c = x[2], d = x[3];
                a = ff(a, b, c, d, k[0], 7, -680876936);
                d = ff(d, a, b, c, k[1], 12, -389564586);
                c = ff(c, d, a, b, k[2], 17, 606105819);
                b = ff(b, c, d, a, k[3], 22, -1044525330);
                // ... 省略完整MD5实现，使用简化版本
                x[0] = add32(a, x[0]);
                x[1] = add32(b, x[1]);
                x[2] = add32(c, x[2]);
                x[3] = add32(d, x[3]);
            }

            function cmn(q, a, b, x, s, t) {
                a = add32(add32(a, q), add32(x, t));
                return add32((a << s) | (a >>> (32 - s)), b);
            }

            function ff(a, b, c, d, x, s, t) {
                return cmn((b & c) | ((~b) & d), a, b, x, s, t);
            }

            function add32(a, b) {
                return (a + b) & 0xFFFFFFFF;
            }

            return crypto.subtle.digest('SHA-256', new TextEncoder().encode(text))
                .then(hashBuffer => {
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    const hashHex = hashArray.slice(0, 16).map(b => b.toString(16).padStart(2, '0')).join('');
                    return hashHex;
                })
                .catch(() => {
                    // 如果crypto API不可用，使用简单的字符串hash
                    let hash = 0;
                    for (let i = 0; i < text.length; i++) {
                        const char = text.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash).toString(16).padStart(32, '0').substring(0, 32);
                });
        }

        // 获取用户信息
        async function getCurrentUser() {
            try {
                const userResponse = await fetch('/user/api/GetUser/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!userResponse.ok) {
                    throw new Error('获取用户信息失败');
                }

                const userData = await userResponse.json();
                if (userData.code !== 200) {
                    throw new Error(userData.message || '用户验证失败');
                }

                currentUser = userData.user;

                // 设置Token到请求头
                const token = userResponse.headers.get('Token');
                if (token) {
                    sessionStorage.setItem('authToken', token);
                }

                return currentUser;
            } catch (error) {
                throw error;
            }
        }

        // 加载支付方式列表
        async function loadPaymentMethods() {
            try {
                if (!currentUser) {
                    await getCurrentUser();
                }

                if (!currentUser) {
                    throw new Error('用户信息缺失');
                }

                // 生成签名：userId + userKey
                const signData = currentUser.id + currentUser.user_key;
                const sign = await generateMD5(signData);

                // 准备请求数据
                const formData = new FormData();
                formData.append('userId', currentUser.id);
                formData.append('sign', sign);

                // 获取Token
                const token = sessionStorage.getItem('authToken');

                // 发送请求
                const response = await fetch('/user/api/payment_methods/', {
                    method: 'POST',
                    headers: {
                        'Token': token
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();

                if (result.code !== 200) {
                    showEmptyPaymentState();
                    return;
                }

                if (result.data && Array.isArray(result.data)) {
                    // 存储支付方式数据
                    currentPaymentMethods = result.data;

                    // 渲染支付方式界面
                    renderPaymentMethods(result.data);
                } else {
                    showEmptyPaymentState();
                }

            } catch (error) {
                showEmptyPaymentState();
            }
        }

        // 充值支付方式切换功能（使用API数据）
        function switchRechargePaymentChannel(channelIndex) {
            try {
                // 更新导航栏状态
                const navItems = document.querySelectorAll('#rechargePaymentNav .payment-nav-item');
                navItems.forEach((item, index) => {
                    if (index === channelIndex) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });

                // 获取支付方式数据并显示对应通道
                if (currentPaymentMethods && currentPaymentMethods[channelIndex]) {
                    showPaymentChannel(currentPaymentMethods[channelIndex], channelIndex);
                } else {
                    // 如果没有API数据，显示空状态
                    showEmptyPaymentState();
                }

            } catch (error) {
                showEmptyPaymentState();
            }
        }

        // 渲染支付方式界面
        function renderPaymentMethods(paymentMethods) {
            try {
                const paymentNav = document.getElementById('rechargePaymentNav');
                const paymentContent = document.getElementById('rechargePaymentOptions');

                if (!paymentMethods || paymentMethods.length === 0) {
                    showEmptyPaymentState();
                    return;
                }

                // 生成导航栏
                generatePaymentNav(paymentMethods);

                // 显示导航栏
                if (paymentNav) paymentNav.style.display = 'flex';

                // 默认显示第一个支付通道
                if (paymentMethods.length > 0) {
                    showPaymentChannel(paymentMethods[0], 0);
                }

            } catch (error) {
                showEmptyPaymentState();
            }
        }

        // 生成支付通道导航栏
        function generatePaymentNav(paymentMethods) {
            const paymentNav = document.getElementById('rechargePaymentNav');
            if (!paymentNav) return;

            let navHTML = '';
            paymentMethods.forEach((method, index) => {
                const activeClass = index === 0 ? 'active' : '';
                navHTML += `
                    <button class="payment-nav-item ${activeClass}"
                            data-channel-index="${index}"
                            onclick="switchRechargePaymentChannel(${index})">
                        ${method.name}
                        <span style="font-size: 0.8em; color: var(--text-light); margin-left: 5px;">
                            (${method.data ? method.data.length : 0}种方式)
                        </span>
                    </button>
                `;
            });

            paymentNav.innerHTML = navHTML;
        }

        // 显示指定支付通道的支付方式
        function showPaymentChannel(channelData, channelIndex) {
            try {
                const paymentContent = document.getElementById('rechargePaymentOptions');
                if (!paymentContent) return;

                if (!channelData.data || !Array.isArray(channelData.data) || channelData.data.length === 0) {
                    // 该通道没有支付方式
                    paymentContent.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <i class="fa-solid fa-info-circle" style="font-size: 24px; margin-bottom: 15px;"></i>
                            <p>该支付通道暂无可用的支付方式</p>
                        </div>
                    `;
                    return;
                }

                // 生成支付方式选项
                let optionsHTML = '';
                channelData.data.forEach((paymentType, index) => {
                    const paymentId = `recharge_payment_${channelIndex}_${index}`;
                    const iconClass = getPaymentIconClass(paymentType);
                    const paymentName = getPaymentDisplayName(paymentType);
                    const iconSymbol = getPaymentIconSymbol(paymentType);

                    optionsHTML += `
                        <div class="payment-option">
                            <input type="radio"
                                   class="payment-input"
                                   id="${paymentId}"
                                   name="recharge_payment"
                                   value="${channelData.id}"
                                   data-channel-id="${channelData.id}"
                                   data-payment-type="${paymentType}"
                                   data-fee="${channelData.fee}">
                            <label class="payment-label" for="${paymentId}">
                                <div class="payment-icon ${iconClass}">
                                    ${iconSymbol}
                                </div>
                                <div class="payment-details">
                                    <div class="payment-name">${paymentName}</div>
                                    <div style="font-size: 0.85em; color: var(--text-light);">
                                        手续费: ${channelData.fee}%
                                    </div>
                                </div>
                            </label>
                        </div>
                    `;
                });

                paymentContent.innerHTML = optionsHTML;

            } catch (error) {
            }
        }



        // 获取支付方式图标类名
        function getPaymentIconClass(paymentType) {
            const iconMap = {
                'wxpay': 'wxpay',
                'alipay': 'alipay',
                'qqpay': 'qqpay'
            };
            return iconMap[paymentType] || 'default';
        }

        // 获取支付方式显示名称
        function getPaymentDisplayName(paymentType) {
            const nameMap = {
                'wxpay': '微信支付',
                'alipay': '支付宝',
                'qqpay': 'QQ钱包'
            };
            return nameMap[paymentType] || paymentType;
        }

        // 获取支付方式图标符号
        function getPaymentIconSymbol(paymentType) {
            const symbolMap = {
                'wxpay': '<i class="fa-brands fa-weixin"></i>',
                'alipay': '<i class="fa-brands fa-alipay"></i>',
                'qqpay': '<i class="fa-brands fa-qq"></i>'
            };
            return symbolMap[paymentType] || '<i class="fa-solid fa-credit-card"></i>';
        }

        // 显示空支付状态
        function showEmptyPaymentState() {
            const paymentNav = document.getElementById('rechargePaymentNav');
            const paymentContent = document.getElementById('rechargePaymentOptions');

            if (paymentNav) paymentNav.style.display = 'none';
            if (paymentContent) {
                paymentContent.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #999;">
                        <i class="fa-solid fa-exclamation-circle" style="font-size: 24px; margin-bottom: 15px;"></i>
                        <p>暂无可用的支付方式</p>
                    </div>
                `;
            }
        }

        // 提交充值请求
        async function submitRechargeRequest(amount, paymentChannelId, paymentWay) {
            try {
                // 确保用户信息已获取
                if (!currentUser) {
                    await getCurrentUser();
                }

                if (!currentUser) {
                    throw new Error('用户信息获取失败');
                }

                // 构建请求数据
                const requestData = {
                    payMent: paymentChannelId,
                    way: paymentWay,
                    userId: currentUser.id,
                    money: parseFloat(amount)
                };

                // 生成签名
                const sign = await generateRechargeSign(requestData, currentUser.user_key);
                requestData.sign = sign;

                // 获取Token
                const token = sessionStorage.getItem('authToken');

                // 发送充值请求
                const response = await fetch('/user/api/recharge_balance/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Token': token || ''
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const result = await response.json();

                if (result.code === 200) {
                    // 成功，关闭模态框并重定向到支付页面
                    rechargeModal.style.display = 'none';

                    // 自动重定向到支付链接
                    if (result.url) {
                        window.location.href = result.url;
                    } else {
                        throw new Error('支付链接获取失败');
                    }
                } else {
                    throw new Error(result.msg || '充值请求失败');
                }

            } catch (error) {
                throw error;
            }
        }

        // 生成充值请求签名
        async function generateRechargeSign(data, userKey) {
            try {
                // 按ASCII码排序字段
                const sortedKeys = Object.keys(data).sort();

                // 构建签名字符串
                let signString = '';
                sortedKeys.forEach(key => {
                    signString += `${key}=${data[key]}&`;
                });

                // 移除最后的&符号，添加用户密钥
                signString = signString.slice(0, -1) + userKey;

                // 生成MD5签名
                const sign = await generateMD5(signString);
                return sign;

            } catch (error) {
                throw new Error('签名生成失败');
            }
        }

        // 订单表格功能 - 使用后端传递的真实数据
        let orderData = [];

        // 安全地解析订单数据
        const ordersJsonStr = '{{ orders_json|escapejs }}';
        if (ordersJsonStr && ordersJsonStr !== '') {
            try {
                orderData = JSON.parse(ordersJsonStr);
            } catch (e) {
                orderData = [];
            }
        }

        let isExpanded = false;
        let currentPage = 1;
        const itemsPerPage = 8;

        // 初始化订单表格
        function initOrderTable() {
            displayOrders();
        }

        // 显示订单数据
        function displayOrders() {
            const tableBody = document.getElementById('orderTableBody');

            // 检查是否有订单数据
            if (!orderData || orderData.length === 0) {
                // 没有订单数据时显示提示信息
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 40px 20px; color: var(--text-light);">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: 15px;">
                                <i class="fa-solid fa-shopping-cart" style="font-size: 3rem; color: rgba(64, 169, 255, 0.3);"></i>
                                <div style="font-size: 1.1rem; font-weight: 500;">暂无订单记录</div>
                                <div style="font-size: 0.9rem; color: var(--text-light);">您还没有任何订单，快去购买商品吧！</div>
                            </div>
                        </td>
                    </tr>
                `;

                // 隐藏分页和查看更多按钮
                document.getElementById('orderPagination').style.display = 'none';
                document.querySelector('.order-actions').style.display = 'none';
                return;
            }

            let displayData;

            if (isExpanded) {
                // 展开状态：显示当前页的数据（每页8条）
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, orderData.length);
                displayData = orderData.slice(startIndex, endIndex);
            } else {
                // 收缩状态：只显示前5条数据
                displayData = orderData.slice(0, 5);
            }

            let tableHTML = '';
            displayData.forEach(order => {
                const statusClass = getStatusClass(order.status);
                tableHTML += `
                    <tr>
                        <td class="order-id">${order.id}</td>
                        <td>${order.product_name}</td>
                        <td>¥${order.final_price}</td>
                        <td>${order.create_time}</td>
                        <td><span class="status-badge ${statusClass}">${order.status_text}</span></td>
                        <td><a href="javascript:void(0);" class="action-btn" title="查看详情"><i class="fa-solid fa-eye"></i></a></td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHTML;

            // 更新分页信息
            updatePagination();

            // 更新查看更多按钮
            updateViewMoreButton();
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusMap = {
                'completed': 'status-completed',
                'pending': 'status-pending',
                'failed': 'status-failed',
                'canceled': 'status-failed',
                'refunded': 'status-failed',
                'processing': 'status-pending'
            };
            return statusMap[status] || 'status-pending';
        }

        // 更新分页信息
        function updatePagination() {
            const pagination = document.getElementById('orderPagination');
            const paginationInfo = document.getElementById('paginationInfo');

            if (isExpanded) {
                pagination.style.display = 'flex';
                const totalItems = orderData.length;
                const startIndex = (currentPage - 1) * itemsPerPage + 1;
                const endIndex = Math.min(currentPage * itemsPerPage, totalItems);
                paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${totalItems} 条记录`;

                // 更新分页按钮
                updatePaginationButtons();
            } else {
                pagination.style.display = 'none';
            }
        }

        // 更新分页按钮
        function updatePaginationButtons() {
            const totalPages = Math.ceil(orderData.length / itemsPerPage);
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const pagesContainer = document.getElementById('paginationPages');

            // 更新上一页/下一页按钮状态
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;

            // 生成页码按钮
            let pagesHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                pagesHTML += `<button class="page-btn ${activeClass}" onclick="goToPage(${i})">${i}</button>`;
            }
            pagesContainer.innerHTML = pagesHTML;
        }

        // 更新查看更多按钮
        function updateViewMoreButton() {
            const viewMoreBtn = document.getElementById('viewMoreBtn');
            const orderActions = document.querySelector('.order-actions');

            // 如果没有订单数据或订单数据少于等于5条，隐藏查看更多按钮
            if (!orderData || orderData.length === 0 || orderData.length <= 5) {
                orderActions.style.display = 'none';
                return;
            } else {
                orderActions.style.display = 'block';
            }

            if (isExpanded) {
                viewMoreBtn.innerHTML = '<i class="fa-solid fa-chevron-down"></i>收缩表格';
                viewMoreBtn.classList.add('expanded');
            } else {
                viewMoreBtn.innerHTML = '<i class="fa-solid fa-chevron-down"></i>查看更多';
                viewMoreBtn.classList.remove('expanded');
            }
        }

        // 切换订单视图（查看更多/收缩）
        function toggleOrderView() {
            isExpanded = !isExpanded;
            currentPage = 1; // 重置到第一页

            // 重新显示订单数据
            displayOrders();
        }

        // 分页功能
        function changeOrderPage(direction) {
            const totalPages = Math.ceil(orderData.length / itemsPerPage);
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                displayOrders();
            }
        }

        function goToPage(page) {
            currentPage = page;
            displayOrders();
        }

        // 表单提交
        const settingsForm = document.querySelector('#settings form');
        
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('个人信息已更新');
        });

        // 订单操作
        const actionBtns = document.querySelectorAll('.action-btn');
        
        actionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.closest('tr').querySelector('.order-id').textContent;
                window.location.href = `order-status.html?id=${orderId}`;
            });
        });

        // 用户认证相关函数
        // 从localStorage获取Token
        function getTokenFromStorage() {
            return localStorage.getItem('token');
        }
        
        // 检查用户是否已登录
        function checkUserLogin() {
            const token = getTokenFromStorage();
            if (!token) {
                return false;
            }
            return true;
        }
        
        // 处理需要认证的链接点击
        function handleAuthenticatedLink(e, targetUrl) {
            e.preventDefault();
            
            if (checkUserLogin()) {
                // 已登录，跳转到目标页面
                window.location.href = targetUrl;
            } else {
                // 未登录，跳转到登录页面
                window.location.href = '/login/';
            }
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化订单表格
            initOrderTable();

            // 处理导航链接
            const profileLink = document.getElementById('profileLink');
            profileLink.addEventListener('click', function(e) {
                handleAuthenticatedLink(e, '/profile/');
            });
            
            // 处理退出登录
            const logoutLink = document.querySelector('.sidebar-link i.fa-right-from-bracket').parentNode;
            logoutLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 清除localStorage中的认证信息
                localStorage.removeItem('token');
                localStorage.removeItem('userId');
                localStorage.removeItem('userKey');
                
                // 清除Cookie中的token
                document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                
                // 跳转到登录页面
                window.location.href = '/login/';
            });
        });