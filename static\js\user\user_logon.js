document.addEventListener('DOMContentLoaded', function() {
            // 确保表单使用自定义验证
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.setAttribute('novalidate', '');
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 始终阻止默认提交
                    // 验证逻辑在各自的处理函数中执行
                });
            });
            
            // 表单切换
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');
            const forgotForm = document.getElementById('forgot-form');
            const toRegister = document.getElementById('to-register');
            const toLogin = document.getElementById('to-login');
            const forgotPassword = document.getElementById('forgot-password');
            const backToLogin = document.getElementById('back-to-login');
            
            // 初始化表单显示状态
            loginForm.style.display = 'flex';
            registerForm.style.display = 'none';
            forgotForm.style.display = 'none';
            
            function switchToLogin() {
                // 先添加inactive类以触发淡出动画
                if (registerForm.style.display === 'flex') registerForm.classList.add('inactive');
                if (forgotForm.style.display === 'flex') forgotForm.classList.add('inactive');
                
                // 延迟后切换显示状态并移除inactive类
                setTimeout(() => {
                    document.querySelector('.image-container h2').textContent = 'Welcome to SUP';
                    // document.querySelector('.image-container p').textContent = '探索更多优质商品，享受便捷购物体验';
                    
                    loginForm.style.display = 'flex';
                    registerForm.style.display = 'none';
                    forgotForm.style.display = 'none';
                    
                    // 触发淡入动画
                    loginForm.classList.remove('inactive');
                    // 添加动画效果到标题
                    const title = loginForm.querySelector('h1');
                    title.classList.remove('fade-in');
                    void title.offsetWidth; // 触发重排
                    title.classList.add('fade-in');
                    
                    setTimeout(() => {
                        // 确保所有表单元素获得正确的焦点
                        const firstInput = loginForm.querySelector('input:not([type=hidden])');
                        if (firstInput) firstInput.focus();
                    }, 100);
                }, 300);
            }
            
            function switchToRegister() {
                // 先添加inactive类以触发淡出动画
                if (loginForm.style.display === 'flex') loginForm.classList.add('inactive');
                if (forgotForm.style.display === 'flex') forgotForm.classList.add('inactive');
                
                // 延迟后切换显示状态并移除inactive类
                setTimeout(() => {
                    document.querySelector('.image-container h2').textContent = 'Welcome to SUP';
                    // document.querySelector('.image-container p').textContent = '注册账号，开启您的购物之旅';
                    
                    loginForm.style.display = 'none';
                    registerForm.style.display = 'flex';
                    forgotForm.style.display = 'none';
                    
                    // 触发淡入动画
                    registerForm.classList.remove('inactive');
                    // 添加动画效果到标题
                    const title = registerForm.querySelector('h1');
                    title.classList.remove('fade-in');
                    void title.offsetWidth; // 触发重排
                    title.classList.add('fade-in');
                    
                    setTimeout(() => {
                        // 确保所有表单元素获得正确的焦点
                        const firstInput = registerForm.querySelector('input:not([type=hidden])');
                        if (firstInput) firstInput.focus();
                    }, 100);
                }, 300);
            }
            
            function switchToForgot() {
                // 先添加inactive类以触发淡出动画
                if (loginForm.style.display === 'flex') loginForm.classList.add('inactive');
                if (registerForm.style.display === 'flex') registerForm.classList.add('inactive');
                
                // 延迟后切换显示状态
                setTimeout(() => {
                    document.querySelector('.image-container h2').textContent = '找回您的账号';
                    document.querySelector('.image-container p').textContent = '重置密码，安全访问您的账户';
                    
                    loginForm.style.display = 'none';
                    registerForm.style.display = 'none';
                    forgotForm.style.display = 'flex';
                    
                    // 同时添加active类触发淡入动画
                    forgotForm.classList.add('active');
                    // 添加动画效果到标题
                    const title = forgotForm.querySelector('h1');
                    title.classList.remove('fade-in');
                    void title.offsetWidth; // 触发重排
                    title.classList.add('fade-in');
                    
                    setTimeout(() => {
                        // 确保所有表单元素获得正确的焦点
                        const firstInput = forgotForm.querySelector('input:not([type=hidden])');
                        if (firstInput) firstInput.focus();
                    }, 100);
                }, 300);
            }
            
            toRegister.addEventListener('click', function(e) {
                e.preventDefault();
                switchToRegister();
            });
            
            toLogin.addEventListener('click', function(e) {
                e.preventDefault();
                switchToLogin();
            });
            
            forgotPassword.addEventListener('click', function(e) {
                e.preventDefault();
                switchToForgot();
            });
            
            backToLogin.addEventListener('click', function(e) {
                e.preventDefault();
                switchToLogin();
            });
            
            // 显示通知
            function showNotification(message, type) {
                
                try {
                    const notification = document.getElementById('notification');
                    if (!notification) {
                        return;
                    }
                    
                    const notificationMessage = document.getElementById('notificationMessage');
                    if (!notificationMessage) {
                        return;
                    }
                    
                    const notificationIcon = notification.querySelector('.notification-icon');
                    if (!notificationIcon) {
                        return;
                    }
                    
                    // 设置通知类型和图标
                    notification.className = 'notification';
                    notification.classList.add(type);
                    
                    if (type === 'success') {
                        notificationIcon.className = 'notification-icon fas fa-check-circle';
                    } else if (type === 'error') {
                        notificationIcon.className = 'notification-icon fas fa-exclamation-circle';
                    }
                    
                    // 设置通知消息
                    notificationMessage.textContent = message;
                    
                    // 显示通知
                    notification.classList.add('show');
                    
                    // 3秒后自动隐藏
                    setTimeout(function() {
                        notification.classList.remove('show');
                    }, 3000);
                } catch (error) {
                    // 如果通知系统失败，退而求其次使用alert
                    if (type === 'error') {
                        alert('错误: ' + message);
                    }
                }
            }
            
            // 验证码生成
            function generateCaptcha(element) {
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 50;
                const ctx = canvas.getContext('2d');
                
                // 背景
                ctx.fillStyle = '#f9f9f9';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 随机生成4位验证码
                const chars = '0123456789ABCDEFGHJKLMNPQRSTUVWXYZ'; // 去除容易混淆的字符
                let captchaText = '';
                
                for (let i = 0; i < 4; i++) {
                    const index = Math.floor(Math.random() * chars.length);
                    captchaText += chars[index];
                }
                
                // 存储验证码文本
                element.dataset.captcha = captchaText;
                
                // 绘制文字
                ctx.textBaseline = 'middle';
                ctx.textAlign = 'center';
                
                for (let i = 0; i < captchaText.length; i++) {
                    const char = captchaText[i];
                    // 调整字符位置，增加间距，避免重叠
                    const x = 40 + i * 40; // 将验证码字符平均分布，调整为适应200px宽度
                    const y = canvas.height / 2 + Math.random() * 6 - 3; // 减少上下波动
                    const rotate = (Math.random() - 0.5) * 0.15; // 减少旋转角度
                    
                    // 随机颜色但避免使用太亮或太暗的颜色
                    const hue = Math.floor(Math.random() * 360);
                    ctx.fillStyle = `hsla(${hue}, 80%, 40%, 0.95)`;
                    
                    // 增大字体大小
                    const fontSize = Math.floor(Math.random() * 2) + 30; // 字体更大、范围更小
                    ctx.font = `bold ${fontSize}px Arial, sans-serif`;
                    
                    // 旋转文字
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.rotate(rotate);
                    ctx.fillText(char, 0, 0);
                    ctx.restore();
                }
                
                // 添加少量干扰线，不影响识别
                for (let i = 0; i < 2; i++) {
                    ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
                    ctx.beginPath();
                    ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
                    ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
                    ctx.stroke();
                }
                
                // 添加少量干扰点
                for (let i = 0; i < 30; i++) {
                    ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
                    ctx.beginPath();
                    ctx.arc(Math.random() * canvas.width, Math.random() * canvas.height, 1, 0, 2 * Math.PI);
                    ctx.fill();
                }
                
                element.innerHTML = '';
                element.appendChild(canvas);
            }
            
            // 生成初始验证码
            const loginCaptchaImage = document.getElementById('login-captcha-image');
            const registerCaptchaImage = document.getElementById('register-captcha-image');
            const forgotCaptchaImage = document.getElementById('forgot-captcha-image');
            
            generateCaptcha(loginCaptchaImage);
            generateCaptcha(registerCaptchaImage);
            generateCaptcha(forgotCaptchaImage);
            
            // 点击验证码图片可以刷新
            loginCaptchaImage.addEventListener('click', function() {
                generateCaptcha(loginCaptchaImage);
            });
            
            registerCaptchaImage.addEventListener('click', function() {
                generateCaptcha(registerCaptchaImage);
            });
            
            forgotCaptchaImage.addEventListener('click', function() {
                generateCaptcha(forgotCaptchaImage);
            });
            
            // 密码强度检测
            const registerPassword = document.getElementById('register-password');
            const passwordStrength = document.getElementById('password-strength');
            const strengthText = document.getElementById('strength-text');
            const resetPassword = document.getElementById('forgot-password-input');
            const resetPasswordStrength = document.getElementById('forgot-password-strength');
            const resetStrengthText = document.getElementById('forgot-strength-text');
            
            function checkPasswordStrength(password, strengthElement, textElement) {
                let strength = 0;
                
                // 长度检查
                if (password.length >= 8) strength += 1;
                
                // 包含小写字母
                if (/[a-z]/.test(password)) strength += 1;
                
                // 包含大写字母
                if (/[A-Z]/.test(password)) strength += 1;
                
                // 包含数字
                if (/[0-9]/.test(password)) strength += 1;
                
                // 包含特殊字符
                if (/[^A-Za-z0-9]/.test(password)) strength += 1;
                
                // 更新密码强度显示
                switch(strength) {
                    case 0:
                        strengthElement.style.width = '0%';
                        strengthElement.style.backgroundColor = '';
                        textElement.textContent = '';
                        break;
                    case 1:
                        strengthElement.style.width = '20%';
                        strengthElement.style.backgroundColor = '#f00';
                        textElement.textContent = '非常弱';
                        textElement.style.color = '#f00';
                        break;
                    case 2:
                        strengthElement.style.width = '40%';
                        strengthElement.style.backgroundColor = '#ff7800';
                        textElement.textContent = '弱';
                        textElement.style.color = '#ff7800';
                        break;
                    case 3:
                        strengthElement.style.width = '60%';
                        strengthElement.style.backgroundColor = '#ffbb00';
                        textElement.textContent = '中等';
                        textElement.style.color = '#ffbb00';
                        break;
                    case 4:
                        strengthElement.style.width = '80%';
                        strengthElement.style.backgroundColor = '#9dff00';
                        textElement.textContent = '强';
                        textElement.style.color = '#9dff00';
                        break;
                    case 5:
                        strengthElement.style.width = '100%';
                        strengthElement.style.backgroundColor = '#00ca00';
                        textElement.textContent = '非常强';
                        textElement.style.color = '#00ca00';
                        break;
                }
            }
            
            registerPassword.addEventListener('input', function() {
                checkPasswordStrength(this.value, passwordStrength, strengthText);
            });
            
            resetPassword.addEventListener('input', function() {
                checkPasswordStrength(this.value, resetPasswordStrength, resetStrengthText);
            });
            
            // 表单提交处理
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;
                const captcha = document.getElementById('login-captcha').value;
                const captchaText = loginCaptchaImage.dataset.captcha;
                
                // 验证表单
                if (!email) {
                    showNotification('请输入邮箱地址', 'error');
                    return;
                }
                
                if (!password) {
                    showNotification('请输入密码', 'error');
                    return;
                }
                
                if (!captcha) {
                    showNotification('请输入验证码', 'error');
                    return;
                }
                
                if (captcha.toLowerCase() !== captchaText.toLowerCase()) {
                    showNotification('验证码错误，请重新输入', 'error');
                    generateCaptcha(loginCaptchaImage);
                    document.getElementById('login-captcha').value = '';
                    return;
                }
                
                // 发送登录请求到后端
                fetch('/user/api/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        captcha: captcha
                    })
                })
                .then(response => {
                    // 从响应头中获取token
                    const token = response.headers.get('Token');
                    if (token) {
                        localStorage.setItem('token', token);
                        
                        // 同时将Token保存到Cookie中，设置路径为根路径，使所有页面都能访问
                        document.cookie = `token=${token}; path=/`;
                    } else {
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const token = localStorage.getItem('token');
                        
                        showNotification('登录成功，即将跳转...', 'success');
                        // 跳转到首页或其他指定页面
                        setTimeout(function() {
                            window.location.href = '/';
                        }, 1500);
                    } else {
                        showNotification(data.message || '登录失败，请检查账号密码', 'error');
                        generateCaptcha(loginCaptchaImage);
                    }
                })
                .catch(error => {
                    showNotification('登录失败，请稍后再试', 'error');
                    generateCaptcha(loginCaptchaImage);
                });
            });
            
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('register-username').value;
                const email = document.getElementById('register-email').value;
                const emailCode = document.getElementById('register-email-code').value;
                const password = document.getElementById('register-password').value;
                const confirmPassword = document.getElementById('register-confirm-password').value;
                const captcha = document.getElementById('register-captcha').value;
                const captchaText = registerCaptchaImage.dataset.captcha;
                
                // 验证表单
                if (!username) {
                    showNotification('请输入用户名', 'error');
                    return;
                }
                
                if (!email) {
                    showNotification('请输入邮箱地址', 'error');
                    return;
                }
                
                if (!emailCode) {
                    showNotification('请输入邮箱验证码', 'error');
                    return;
                }
                
                if (!password) {
                    showNotification('请输入密码', 'error');
                    return;
                }
                
                if (!captcha) {
                    showNotification('请输入验证码', 'error');
                    return;
                }
                
                // 验证密码长度
                if (password.length < 8) {
                    showNotification('密码长度至少为8位', 'error');
                    return;
                }
                
                // 验证两次密码是否一致
                if (password !== confirmPassword) {
                    showNotification('两次输入的密码不一致', 'error');
                    return;
                }
                
                // 验证码检查
                if (captcha.toLowerCase() !== captchaText.toLowerCase()) {
                    showNotification('图片验证码错误，请重新输入', 'error');
                    generateCaptcha(registerCaptchaImage);
                    document.getElementById('register-captcha').value = '';
                    return;
                }
                
                // 发送注册请求到后端
                fetch('/user/api/register/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        email_code: emailCode,
                        password: password,
                        captcha: captcha
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('注册成功！请登录', 'success');
                        switchToLogin();
                    } else {
                        showNotification(data.message || '注册失败', 'error');
                        generateCaptcha(registerCaptchaImage);
                    }
                })
                .catch(error => {
                    showNotification('注册失败，请稍后再试', 'error');
                    generateCaptcha(registerCaptchaImage);
                });
            });
            
            // 忘记密码表单提交
            forgotForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('forgot-email').value;
                const emailCode = document.getElementById('forgot-email-code').value;
                const password = document.getElementById('forgot-password-input').value;
                const confirmPassword = document.getElementById('forgot-confirm-password').value;
                const captcha = document.getElementById('forgot-captcha').value;
                const captchaText = forgotCaptchaImage.dataset.captcha;
                
                // 基本验证
                if (!email) {
                    showNotification('请输入邮箱地址', 'error');
                    return;
                }
                
                if (!emailCode) {
                    showNotification('请输入邮箱验证码', 'error');
                    return;
                }
                
                if (!password) {
                    showNotification('请输入新密码', 'error');
                    return;
                }
                
                if (!confirmPassword) {
                    showNotification('请确认新密码', 'error');
                    return;
                }
                
                if (!captcha) {
                    showNotification('请输入验证码', 'error');
                    return;
                }
                
                // 验证密码长度
                if (password.length < 8) {
                    showNotification('密码长度至少为8位', 'error');
                    return;
                }
                
                // 验证两次密码是否一致
                if (password !== confirmPassword) {
                    showNotification('两次输入的密码不一致', 'error');
                    return;
                }
                
                // 验证码检查
                if (captcha.toLowerCase() !== captchaText.toLowerCase()) {
                    showNotification('图片验证码错误，请重新输入', 'error');
                    generateCaptcha(forgotCaptchaImage);
                    document.getElementById('forgot-captcha').value = '';
                    return;
                }
                
                // 发送重置密码请求
                fetch('/user/api/reset_password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        email: email,
                        email_code: emailCode,
                        new_password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('密码重置成功，请使用新密码登录', 'success');
                        forgotForm.reset();
                        switchToLogin();
                    } else {
                        showNotification(data.message || '重置密码失败', 'error');
                    }
                })
                .catch(error => {
                    showNotification('重置密码失败，请稍后再试', 'error');
                });
            });
            
            // 发送邮箱验证码
            document.getElementById('send-code').addEventListener('click', function() {
                const email = document.getElementById('register-email').value;
                
                if (!email) {
                    showNotification('请输入邮箱地址', 'error');
                    return;
                }
                
                if (!email.includes('@')) {
                    showNotification('请输入有效的邮箱地址', 'error');
                    return;
                }
                
                // 禁用按钮
                const sendCodeBtn = document.getElementById('send-code');
                sendCodeBtn.disabled = true;
                
                // 发送邮箱验证码请求
                fetch('/user/api/send_email_code/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let countdown = 60;
                        const timer = setInterval(() => {
                            if (countdown > 0) {
                                sendCodeBtn.textContent = `${countdown}秒后重新发送`;
                                countdown--;
                            } else {
                                clearInterval(timer);
                                sendCodeBtn.disabled = false;
                                sendCodeBtn.textContent = '发送验证码';
                            }
                        }, 1000);
                    } else {
                        alert(data.message);
                        sendCodeBtn.disabled = false;
                    }
                })
                .catch(error => {
                    alert('发送失败，请稍后再试');
                    sendCodeBtn.disabled = false;
                });
            });
            
            // 处理忘记密码发送验证码功能
            document.getElementById('forgot-send-code').addEventListener('click', function() {
                const email = document.getElementById('forgot-email').value;
                
                if (!email) {
                    showNotification('请输入邮箱地址', 'error');
                    return;
                }
                
                if (!email.includes('@')) {
                    showNotification('请输入有效的邮箱地址', 'error');
                    return;
                }
                
                // 禁用按钮
                const sendCodeBtn = document.getElementById('forgot-send-code');
                sendCodeBtn.disabled = true;
                
                // 发送邮箱验证码请求
                fetch('/user/api/send_email_code/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('验证码已发送至邮箱', 'success');
                        let countdown = 60;
                        const timer = setInterval(() => {
                            if (countdown > 0) {
                                sendCodeBtn.textContent = `${countdown}秒后重新发送`;
                                countdown--;
                            } else {
                                clearInterval(timer);
                                sendCodeBtn.disabled = false;
                                sendCodeBtn.textContent = '发送验证码';
                            }
                        }, 1000);
                    } else {
                        showNotification(data.message || '发送失败', 'error');
                        sendCodeBtn.disabled = false;
                    }
                })
                .catch(error => {
                    showNotification('发送失败，请稍后再试', 'error');
                    sendCodeBtn.disabled = false;
                });
            });
            
            // 忘记密码检测密码强度
            const forgotPasswordInput = document.getElementById('forgot-password-input');
            const forgotPasswordStrength = document.getElementById('forgot-password-strength');
            const forgotStrengthText = document.getElementById('forgot-strength-text');
            
            forgotPasswordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value, forgotPasswordStrength, forgotStrengthText);
            });
            
            // 获取CSRF Token的辅助函数
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }
        });