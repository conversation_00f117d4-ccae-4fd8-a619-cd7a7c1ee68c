/**
 * 系统主入口文件
 * 负责检查系统初始化状态并提示用户配置
 */

// 在文档加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {

    // 检查配置是否存在
    if (typeof config === 'undefined') {
        window.config = {
            apiBaseUrl: 'http://localhost:8000',
            debug: true
        };
    }

    try {
        // 初始化通信并检查系统状态
        await initCommunicationForSystemCheck();
    } catch (error) {
    }
});


/**
 * 初始化通信并检查系统状态
 */
async function initCommunicationForSystemCheck() {
    try {
        // 使用唯一页面标识符，防止多标签页同时访问时的冲突
        const pageId = Date.now().toString() + Math.random().toString(36).substring(2, 10);

        // 清除之前的会话数据，防止重复使用同一个userId导致的400错误
        sessionStorage.removeItem('tempUserId');

        // 在检查之前，清除所有用户的密钥存储
        if (window['CommunicationModule'] && window['CommunicationModule'].clearUserKeys) {
            window['CommunicationModule'].clearUserKeys();
        }

        // 生成临时用户ID并保存
        const userId = generateTempUserId();
        sessionStorage.setItem('tempUserId', userId);

        // 延迟队列 - 控制并发请求
        const queueWaitTime = Math.floor(Math.random() * 100); // 添加随机延迟，错开高并发访问
        await new Promise(resolve => setTimeout(resolve, queueWaitTime));

        // 尝试初始化加密通信
        let useEncryption = false;

        if (window['CommunicationModule']) {

            // 使用超时控制，防止初始化阻塞太久
            const initTimeout = 8000;
            useEncryption = await Promise.race([
                window['CommunicationModule'].initCommunication(userId, {
                    logCallback: (message, type) => {
                    }
                }),
                new Promise(resolve => {
                    setTimeout(() => {
                        resolve(false);
                    }, initTimeout);
                })
            ]);

            if (!useEncryption) {
                displayErrorModal('通信错误', '无法建立加密通信通道，请刷新页面重试。');
                return;
            }

            // 等待一小段时间确保通信通道完全建立
            // 在高并发环境下增加等待时间，确保通信通道稳定
            await new Promise(resolve => setTimeout(resolve, 1200));

            // 验证用户密钥是否正确设置
            if (window['CommunicationModule'].hasSessionKey &&
                !window['CommunicationModule'].hasSessionKey(userId)) {
                await new Promise(resolve => setTimeout(resolve, 600));

                // 再次尝试初始化，使用更短的超时时间
                useEncryption = await Promise.race([
                    window['CommunicationModule'].initCommunication(userId, {
                        logCallback: (message, type) => {
                        }
                    }),
                    new Promise(resolve => {
                        setTimeout(() => {
                            resolve(false);
                        }, 4000);
                    })
                ]);

                if (!window['CommunicationModule'].hasSessionKey(userId)) {
                    displayErrorModal('通信错误', '无法建立安全通信，会话密钥设置失败。');
                    return;
                }
            }


            // 检查系统初始化状态 - 添加重试机制
            let retryCount = 0;
            const maxRetries = 2;
            let success = false;

            while (!success && retryCount <= maxRetries) {
                try {
                    await checkInitializationStatus(userId, useEncryption);
                    success = true;
                } catch (error) {
                    retryCount++;
                    if (retryCount <= maxRetries) {
                        // 增加指数退避等待时间
                        await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount - 1)));
                    } else {
                        throw error;
                    }
                }
            }
        } else {
            displayErrorModal('模块加载错误', '通信模块未加载成功，请刷新页面或检查网络连接。');
        }
    } catch (error) {
        displayErrorModal('初始化错误', `通信初始化失败: ${error.message}`);
    }
}

/**
 * 生成临时用户ID
 * @returns {string} 用户ID
 */
function generateTempUserId() {
    if (window['CommunicationModule']) {
        return window['CommunicationModule'].generateUserId();
    } else {
        return 'user_' + Math.random().toString(36).substr(2, 9);
    }
}

/**
 * 检查系统初始化状态
 * @param {string} userId - 用户ID
 * @param {boolean} useEncryption - 是否使用加密通信
 */
async function checkInitializationStatus(userId, useEncryption) {
    // 添加请求ID，跟踪每个请求
    const requestId = Date.now().toString(36) + Math.random().toString(36).substring(2, 5);

    try {
        if (!useEncryption || !window['CommunicationModule']) {
            displayErrorModal('通信状态错误', '通信通道未正确建立，请刷新页面重试。');
            return;
        }

        // 获取通信密钥
        if (!window['CommunicationModule'].hasSessionKey(userId)) {
            const initSuccess = await window['CommunicationModule'].initCommunication(userId, {
                logCallback: (message, type) => {
                }
            });

            if (!initSuccess) {
                throw new Error('无法建立加密通信，会话密钥初始化失败');
            }
        }

        // 添加延迟，确保后端准备好接收请求
        // 并发环境下，使用随机延迟防止请求聚集
        const randomDelay = 500 + Math.floor(Math.random() * 300);
        await new Promise(resolve => setTimeout(resolve, randomDelay));

        // 使用加密通信

        try {
            // 设置请求超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时

            try {
                // 准备请求数据
                const requestData = {
                    operation: 'get_initialization_status',
                    data: {}
                };

                // 加密数据
                const encryptedData = window['CommunicationModule'].encryptData(requestData, userId);

                if (!encryptedData) {
                    throw new Error('数据加密失败');
                }

                const response = await fetch(config.apiBaseUrl + '/api/admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-ID': userId,
                        'X-Request-ID': requestId
                    },
                    body: JSON.stringify({
                        data: encryptedData  // 使用服务器端预期的字段名
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`服务器返回错误状态: ${response.status}`);
                }

                // 成功获取响应
                const responseData = await response.json();

                let result;

                // 检查响应中是否包含加密数据
                if (responseData.success && responseData.data && typeof responseData.data === 'string') {
                    try {
                        // 解密服务器返回的数据
                        result = window['CommunicationModule'].decryptData(responseData.data, userId);
                    } catch (decryptError) {
                        throw new Error(`解密响应失败: ${decryptError.message}`);
                    }
                } else {
                    // 如果没有加密，直接使用响应数据
                    result = responseData;
                }

                // 处理初始化状态
                if (result && typeof result.initialized !== 'undefined') {
                    handleInitializationStatus(result, userId);
                    return;
                } else {
                    throw new Error('响应数据格式不正确');
                }
            } finally {
                clearTimeout(timeoutId);
            }
        } catch (fetchError) {
            displayErrorModal('请求错误', `无法完成初始化状态请求: ${fetchError.message}`);
        }
    } catch (error) {
        displayErrorModal('状态检查错误', `检查系统状态失败: ${error.message}`);
    }
}

/**
 * 处理初始化状态结果
 * @param {Object} result - 初始化状态结果
 * @param {string} userId - 用户ID
 */
function handleInitializationStatus(result, userId) {
    if (!result.success || result.initialized === false) {
        displayInitializationModal();
    } else if (result.success && result.initialized === true) {

        // 如果在登录页面并且定义了onSystemInitialized回调，调用它
        if (window.onSystemInitialized && typeof window.onSystemInitialized === 'function') {
            window.onSystemInitialized(userId);
        }
    } else {
        // 未知状态
        displayErrorModal('状态未知', '无法确定系统初始化状态，请联系管理员');
    }
}

/**
 * 显示初始化提示模态框
 */
function displayInitializationModal() {
    // 检查是否已存在模态框
    if (document.getElementById('initModal')) {
        return;
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.id = 'initModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    // 模态框内容
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 10px;
        max-width: 450px;
        width: 90%;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        transform: translateY(20px);
        transition: transform 0.3s ease;
    `;

    // 标题
    const title = document.createElement('h3');
    title.textContent = '系统未初始化';
    title.style.cssText = `
        margin-bottom: 15px;
        color: #333;
        font-size: 1.5rem;
    `;

    // 消息
    const message = document.createElement('p');
    message.textContent = '检测到系统尚未进行初始化配置，请先完成系统初始配置才能正常使用。';
    message.style.cssText = `
        margin-bottom: 20px;
        color: #666;
        line-height: 1.5;
    `;

    // 按钮容器
    const btnContainer = document.createElement('div');
    btnContainer.style.cssText = `
        display: flex;
        justify-content: center;
        gap: 10px;
    `;

    // 确认按钮
    const confirmBtn = document.createElement('button');
    confirmBtn.textContent = '立即配置';
    confirmBtn.style.cssText = `
        background: #ff7eb9;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: background 0.2s;
    `;
    confirmBtn.onmouseover = () => {
        confirmBtn.style.background = '#ff6ba9';
    };
    confirmBtn.onmouseout = () => {
        confirmBtn.style.background = '#ff7eb9';
    };
    confirmBtn.onclick = () => {
        window.location.href = '/Initialize.html';
    };

    // 取消按钮
    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = '稍后再说';
    cancelBtn.style.cssText = `
        background: #f0f0f0;
        color: #666;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: background 0.2s;
    `;
    cancelBtn.onmouseover = () => {
        cancelBtn.style.background = '#e0e0e0';
    };
    cancelBtn.onmouseout = () => {
        cancelBtn.style.background = '#f0f0f0';
    };
    cancelBtn.onclick = () => {
        document.body.removeChild(modal);
    };

    // 组装模态框
    btnContainer.appendChild(confirmBtn);
    btnContainer.appendChild(cancelBtn);
    modalContent.appendChild(title);
    modalContent.appendChild(message);
    modalContent.appendChild(btnContainer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 触发动画
    setTimeout(() => {
        modal.style.opacity = '1';
        modalContent.style.transform = 'translateY(0)';
    }, 10);
}

/**
 * 显示错误提示模态框
 * @param {string} title - 错误标题
 * @param {string} message - 错误消息
 */
function displayErrorModal(title, message) {
    // 检查是否已存在模态框
    let modal = document.getElementById('errorModal');
    if (modal) {
        document.body.removeChild(modal);
    }

    // 创建模态框
    modal = document.createElement('div');
    modal.id = 'errorModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    // 模态框内容
    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 10px;
        max-width: 450px;
        width: 90%;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        transform: translateY(20px);
        transition: transform 0.3s ease;
    `;

    // 标题
    const titleEl = document.createElement('h3');
    titleEl.textContent = title || '系统错误';
    titleEl.style.cssText = `
        margin-bottom: 15px;
        color: #d32f2f;
        font-size: 1.5rem;
    `;

    // 消息
    const messageEl = document.createElement('p');
    messageEl.textContent = message || '发生未知错误，请刷新页面重试';
    messageEl.style.cssText = `
        margin-bottom: 20px;
        color: #666;
        line-height: 1.5;
    `;

    // 按钮
    const btnContainer = document.createElement('div');
    btnContainer.style.cssText = `
        display: flex;
        justify-content: center;
    `;

    // 刷新按钮
    const refreshBtn = document.createElement('button');
    refreshBtn.textContent = '刷新页面';
    refreshBtn.style.cssText = `
        background: #f44336;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
        transition: background 0.2s;
    `;
    refreshBtn.onmouseover = () => {
        refreshBtn.style.background = '#d32f2f';
    };
    refreshBtn.onmouseout = () => {
        refreshBtn.style.background = '#f44336';
    };
    refreshBtn.onclick = () => {
        location.reload();
    };

    // 组装模态框
    btnContainer.appendChild(refreshBtn);
    modalContent.appendChild(titleEl);
    modalContent.appendChild(messageEl);
    modalContent.appendChild(btnContainer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 触发动画
    setTimeout(() => {
        modal.style.opacity = '1';
        modalContent.style.transform = 'translateY(0)';
    }, 10);
}

// 检查特殊页面是否需要初始化
function initializeSpecialPages(url) {

    // 分类管理页面初始化
    if (url.includes('product/category.html')) {

        // 如果通信模块可用，确保已初始化
        if (window.CommunicationModule && typeof window.CommunicationModule.initCommunication === 'function') {
            // 优先从sessionStorage中获取系统初始化时设置的用户ID
            const storedUserId = sessionStorage.getItem('tempUserId');
            // 然后尝试window.currentUserId
            const userId = storedUserId || window.currentUserId;

            if (!userId) {
                return false;
            }

            // 保存到全局变量，确保所有页面使用相同ID
            window.currentUserId = userId;


            if (!window.CommunicationModule.hasSessionKey(userId)) {
                window.CommunicationModule.initCommunication(userId).then(success => {
                });
            } else {
            }
        }

        // 查找分类管理页面的初始化标识元素
        const categoryPageElement = document.getElementById('category-management-page');

        if (categoryPageElement) {
            // 如果找到分类页面并且有全局初始化函数，调用它
            if (window.initCategoryManagement) {
                window.initCategoryManagement();
                return true;
            }
        }

        // 如果初始化失败，延迟尝试
        setTimeout(() => {
            if (window.initCategoryManagement) {
                window.initCategoryManagement();
            }
        }, 500);
    }

    return false;
} 