<!-- 对接中心页面 -->
<link rel="stylesheet" href="/static/css/pages/DockingCenter.css">
<div class="docking-center-container">
    <!-- 页面标题和添加按钮 -->
    <div class="page-header">
        <h1 class="page-title">对接中心</h1>
        <div class="header-actions">
            <button id="addDockingSiteBtn" class="add-docking-btn">
                <i class="fas fa-plus"></i> 添加对接网站
            </button>
            <!-- 插件注入点：顶部功能按钮 -->
            <div id="plugin-injection-docking-buttons" class="plugin-injection-point">
                <!-- 这里将由插件系统动态注入内容 -->
            </div>
        </div>
    </div>

    <!-- 数据列表 -->
    <div class="docking-list-container">
        <!-- 插件注入点：支持第三方插件添加对接渠道 -->
        <div id="plugin-injection-docking-sources" class="plugin-injection-point">
            <!-- 这里将由插件系统动态注入内容 -->
            <!-- 默认只有聚比价平台 -->
            <div class="default-docking-source">
                <!-- 原有的聚比价平台对接内容将继续显示在这里 -->
            </div>
        </div>
        
        <table class="docking-table">
            <thead>
                <tr>
                    <th><i class="fas fa-store-alt"></i> 网站名称</th>
                    <th><i class="fas fa-globe"></i> 网站域名</th>
                    <th><i class="fas fa-tag"></i> 网站类型</th>
                    <th><i class="fas fa-user-tag"></i> 对接账号</th>
                    <th><i class="fas fa-wallet"></i> 账户余额</th>
                    <th><i class="fas fa-cog"></i> 操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 动态加载对接网站数据 -->
            </tbody>
        </table>
        
        <!-- 空状态显示 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-handshake-slash"></i>
            </div>
            <div class="empty-state-text">暂无对接网站，请点击"添加对接网站"按钮添加</div>
        </div>
    </div>

    <!-- 添加对接网站模态框 -->
    <div id="addDockingSiteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>添加对接网站</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addDockingSiteForm">
                    <div class="form-group">
                        <label for="siteType"><i class="fas fa-sitemap"></i> 网站类型</label>
                        <select id="siteType" class="form-control">
                            <option value="" disabled selected>请选择网站类型</option>
                            <option value="jubijia">聚比价</option>
                            <!-- 注意：插件网站类型选项将通过JavaScript动态添加到此处 -->
                        </select>
                        <!-- 插件注入点：用于动态加载网站类型 -->
                        <div id="plugin-injection-site-types" class="plugin-injection-point" style="display:none;">
                            <!-- 这里将由插件系统动态处理网站类型选项注入 -->
                        </div>
                    </div>
                    
                    <!-- 移除重复的插件注入点 -->
                    <div class="form-group">
                        <label for="siteName"><i class="fas fa-store"></i> 网站名称</label>
                        <input type="text" id="siteName" class="form-control" placeholder="请输入网站名称">
                    </div>
                    <div class="form-group">
                        <label for="siteDomain"><i class="fas fa-globe"></i> 网站域名</label>
                        <input type="text" id="siteDomain" class="form-control" placeholder="请输入网站域名">
                    </div>
                    <div class="form-group">
                        <label for="siteAppId"><i class="fas fa-id-card"></i> 网站APPID</label>
                        <input type="text" id="siteAppId" class="form-control" placeholder="请输入网站APPID">
                    </div>
                    <div class="form-group">
                        <label for="siteSecret"><i class="fas fa-key"></i> 网站密钥</label>
                        <input type="password" id="siteSecret" class="form-control" placeholder="请输入网站密钥">
                        <!-- 聚比价推送地址提示 -->
                        <div id="jubijiaCallbackTip" class="jubijia-callback-tip" style="display: none;">
                            <div class="tip-content">
                                <i class="fas fa-info-circle"></i>
                                <span>请在聚比价后台-日志中心-推送地址管理页面添加推送地址"<strong id="jubijiaCallbackUrl"></strong>"</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancelAddSite" class="btn-cancel">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="submit" class="btn-confirm">
                            <i class="fas fa-check"></i> 确认添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteDockingSiteModal" class="modal">
        <div class="modal-content delete-modal-content">
            <div class="modal-header delete-modal-header">
                <h2><i class="fas fa-exclamation-triangle"></i> 删除确认</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body delete-modal-body">
                <div class="delete-warning-icon">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <div class="delete-warning-text">
                    <h3>确定要删除这个对接网站吗？</h3>
                    <p>此操作不可恢复，删除后将无法找回该网站的配置信息。</p>
                </div>
                <div class="delete-site-info">
                    <div class="site-info-item">
                        <span class="info-label"><i class="fas fa-tag"></i> 网站名称：</span>
                        <span class="info-value" id="deleteSiteName">-</span>
                    </div>
                    <div class="site-info-item">
                        <span class="info-label"><i class="fas fa-sitemap"></i> 网站类型：</span>
                        <span class="info-value" id="deleteSiteType">-</span>
                    </div>
                    <div class="site-info-item">
                        <span class="info-label"><i class="fas fa-globe"></i> 网站域名：</span>
                        <span class="info-value" id="deleteSiteUrl">-</span>
                    </div>
                </div>
                <div class="delete-actions">
                    <button type="button" class="btn-cancel" id="cancelDeleteSite">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn-delete-confirm" id="confirmDeleteSite">
                        <i class="fas fa-trash-alt"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑对接网站模态框 -->
    <div id="editDockingSiteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> 编辑对接网站</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editDockingSiteForm">
                    <input type="hidden" id="editSiteId">
                    <div class="form-group">
                        <label for="editSiteType"><i class="fas fa-sitemap"></i> 网站类型</label>
                        <select id="editSiteType" class="form-control">
                            <option value="" disabled>请选择网站类型</option>
                            <option value="jubijia">聚比价</option>
                            <!-- 注意：插件网站类型选项将通过JavaScript动态添加到此处 -->
                        </select>
                        <!-- 插件注入点：用于动态加载编辑模态框的网站类型 -->
                        <div id="plugin-injection-edit-site-types" class="plugin-injection-point" style="display:none;">
                            <!-- 这里将由插件系统动态处理网站类型选项注入 -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="editSiteName"><i class="fas fa-store"></i> 网站名称</label>
                        <input type="text" id="editSiteName" class="form-control" placeholder="请输入网站名称">
                    </div>
                    <div class="form-group">
                        <label for="editSiteDomain"><i class="fas fa-globe"></i> 网站域名</label>
                        <input type="text" id="editSiteDomain" class="form-control" placeholder="请输入网站域名">
                    </div>
                    <div class="form-group">
                        <label for="editSiteAppId"><i class="fas fa-id-card"></i> 网站APPID</label>
                        <input type="text" id="editSiteAppId" class="form-control" placeholder="请输入网站APPID">
                    </div>
                    <div class="form-group">
                        <label for="editSiteSecret"><i class="fas fa-key"></i> 网站密钥</label>
                        <input type="password" id="editSiteSecret" class="form-control" placeholder="如需修改请填写新的密钥">
                        <!-- 聚比价推送地址提示 -->
                        <div id="editJubijiaCallbackTip" class="jubijia-callback-tip" style="display: none;">
                            <div class="tip-content">
                                <i class="fas fa-info-circle"></i>
                                <span>请在聚比价后台-日志中心-推送地址管理页面添加推送地址"<strong id="editJubijiaCallbackUrl"></strong>"</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancelEditSite" class="btn-cancel">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="submit" class="btn-confirm">
                            <i class="fas fa-check"></i> 确认修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 通知容器 -->
<div class="toast-container" id="toast-container"></div>

<!-- 加载中指示器 -->
<div id="loading-indicator" class="loading-indicator">
    <div class="spinner"></div>
    <div class="loading-text">加载中...</div>
</div>

<!-- 进货模态框 -->
<div id="purchaseModal" class="modal">
    <div class="modal-content purchase-modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-shopping-cart"></i> 商品选品</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body purchase-modal-body">
            <div class="purchase-container">
                <!-- 左侧分类树 -->
                <div class="category-tree-container">
                    <div class="tree-search">
                        <input type="text" id="categorySearch" placeholder="搜索分类..." class="form-control">
                    </div>
                    <div class="category-tree">
                        <ul id="categoryTreeList">
                            <!-- 分类树将通过JavaScript动态生成 -->
                        </ul>
                    </div>
                </div>
                
                <!-- 右侧商品列表 -->
                <div class="product-list-container">
                    <div class="product-filter">
                        <div class="filter-group">
                            <input type="text" id="productSearch" placeholder="商品关键字" class="form-control">
                            <input type="text" id="priceSearch" placeholder="最高价格" class="form-control">
                            <select id="stockFilter" class="form-control">
                                <option value="">对接状态</option>
                                <option value="all">全部商品</option>
                                <option value="connected">已对接</option>
                                <option value="not-connected">未对接</option>
                            </select>
                            <button class="search-btn"><i class="fas fa-search"></i> 查询</button>
                        </div>
                    </div>
                    
                    <div class="product-table-container">
                        <table class="product-table">
                            <thead>
                                <tr>
                                    <th width="40" style="text-align: center; padding: 10px;">
                                        <div class="kawaii-checkbox">
                                            <input type="checkbox" id="selectAllProducts" class="kawaii-checkbox-input">
                                            <label for="selectAllProducts" class="kawaii-checkbox-label">
                                                <div class="kawaii-checkbox-face">
                                                    <div class="kawaii-checkbox-eyes"></div>
                                                    <div class="kawaii-checkbox-mouth"></div>
                                                </div>
                                            </label>
                                        </div>
                                    </th>
                                    <th width="25%">商品名称</th>
                                    <th width="60">商品图片</th>
                                    <th width="130">商品编号</th>
                                    <th width="100">购买价格</th>
                                    <th width="80">商品类型</th>
                                    <th width="80">销售状态</th>
                                    <th width="100">对接状态</th>
                                </tr>
                            </thead>
                            <tbody id="productList">
                                <!-- 商品列表将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                        
                        <!-- 加载中状态 -->
                        <div class="product-loading" style="display: none;">
                            <div class="spinner"></div>
                            <div>加载中...</div>
                        </div>
                        
                        <!-- 空状态 -->
                        <div class="product-empty" style="display: none;">
                            <i class="fas fa-box-open"></i>
                            <div>暂无商品数据</div>
                            <div class="empty-hint">请选择其他分类或修改搜索条件试试</div>
                        </div>
                    </div>
                    
                    <div class="purchase-actions">
                        <div class="batch-action">
                            <button class="batch-btn batch-purchase-btn"><i class="fas fa-shopping-cart"></i> 批量添加</button>
                        </div>
                        <div class="pagination">
                            <button class="page-btn"><i class="fas fa-angle-left"></i></button>
                            <span class="current-page">1</span>
                            <button class="page-btn"><i class="fas fa-angle-right"></i></button>
                            <select class="page-size-select">
                                <option value="10">10条/页</option>
                                <option value="20">20条/页</option>
                                <option value="50">50条/页</option>
                            </select>
                            <span class="page-info">共0条</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量对接设置模态框 -->
<div id="batchDockingModal" class="modal">
    <div class="modal-content batch-modal-content">
        <div class="modal-header batch-modal-header">
            <h2><i class="fas fa-magic"></i> 批量对接设置</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body batch-modal-body">
            <form id="batchDockingForm">
                <div class="kawaii-illustration">
                    <div class="kawaii-cloud"></div>
                    <div class="kawaii-robot">
                        <div class="robot-head">
                            <div class="robot-eyes"></div>
                            <div class="robot-mouth"></div>
                        </div>
                        <div class="robot-body"></div>
                    </div>
                </div>
                <div class="form-group category-row">
                    <div class="category-col">
                        <label for="primaryCategory" class="kawaii-label"><i class="fas fa-folder"></i> 一级分类</label>
                        <div class="kawaii-select-wrapper">
                            <select id="primaryCategory" class="form-control kawaii-select">
                                <option value="" disabled selected>请选择一级分类</option>
                                <!-- 一级分类选项将通过JavaScript动态加载 -->
                            </select>
                            <div class="select-arrow"><i class="fas fa-chevron-down"></i></div>
                        </div>
                    </div>
                    <div class="category-col">
                        <label for="secondaryCategory" class="kawaii-label"><i class="fas fa-folder-open"></i> 二级分类</label>
                        <div class="kawaii-select-wrapper">
                            <select id="secondaryCategory" class="form-control kawaii-select">
                                <option value="" disabled selected>请先选择一级分类</option>
                                <!-- 二级分类选项将通过JavaScript动态加载 -->
                            </select>
                            <div class="select-arrow"><i class="fas fa-chevron-down"></i></div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="pricingTemplate" class="kawaii-label"><i class="fas fa-tag"></i> 定价模板</label>
                    <div class="kawaii-select-wrapper">
                        <select id="pricingTemplate" class="form-control kawaii-select">
                            <option value="" disabled selected>请选择定价模板</option>
                            <option value="default">默认模板</option>
                            <option value="discount5">5%折扣模板</option>
                            <option value="discount10">10%折扣模板</option>
                            <option value="markup10">10%加价模板</option>
                            <option value="markup20">20%加价模板</option>
                        </select>
                        <div class="select-arrow"><i class="fas fa-chevron-down"></i></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="kawaii-label"><i class="fas fa-image"></i> 保存图片方式</label>
                    <div class="kawaii-radio-group">
                        <label class="kawaii-radio-item">
                            <input type="radio" id="imageSourceUrl" name="imageSaveMode" value="source" checked class="kawaii-radio-input">
                            <span class="radio-bubble"></span>
                            <span class="radio-text">取对接地址</span>
                        </label>
                        <label class="kawaii-radio-item">
                            <input type="radio" id="imageLocalSave" name="imageSaveMode" value="local" class="kawaii-radio-input">
                            <span class="radio-bubble"></span>
                            <span class="radio-text">下载至本地(暂不支持)</span>
                        </label>
                    </div>
                </div>
                <div class="kawaii-notice">
                    <div class="notice-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notice-text">会自动对接货源地址的商品名称、图片、详情等信息</div>
                </div>
                <div class="kawaii-form-actions">
                    <button type="button" id="cancelBatchDocking" class="kawaii-btn kawaii-btn-cancel">
                        <i class="fas fa-times"></i>
                        <span>取消</span>
                    </button>
                    <button type="submit" class="kawaii-btn kawaii-btn-confirm">
                        <i class="fas fa-check"></i>
                        <span>保存</span>
                        <div class="btn-sparkles"></div>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="/static/js/pages/DockingCenter.js"></script>

