<!-- 卡密仓库管理内容 -->
<link rel="stylesheet" href="/static/css/pages/cardstock.css">
<!-- 添加MD5库 -->
<script src="https://cdn.bootcdn.net/ajax/libs/blueimp-md5/2.19.0/js/md5.min.js"></script>

<div id="stock-container">
    <!-- 加载中指示器 -->
    <div id="loading-overlay">
        <div class="spinner"></div>
        <div class="loading-text">正在加载数据...</div>
    </div>

    <!-- 卡密仓库列表卡片 -->
    <div class="stock-card">
        <div class="card-title">
            <div style="font-size: 24px; font-weight: 600; color: #333333;">卡密仓库</div>
            <div class="card-actions">
                <button id="refresh-btn" class="btn btn-light me-2">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button id="addRepoBtn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加卡库
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="stock-table cardstock-table">
                <thead>
                    <tr>
                        <th width="40%"><i class="fas fa-hashtag"></i> 卡库ID</th>
                        <th width="25%"><i class="fas fa-box"></i> 卡库名称</th>
                        <th width="15%"><i class="fas fa-layer-group"></i> 卡库库存</th>
                        <th width="20%"><i class="fas fa-cog"></i> 操作</th>
                    </tr>
                </thead>
                <tbody id="repoTableBody">
                    <!-- 初始加载提示 -->
                    <tr>
                        <td colspan="4">
                            <div class="empty-state">
                                <i class="fas fa-spinner fa-spin"></i>
                                <h3>正在加载卡库数据</h3>
                                <p>请稍候，正在从服务器获取数据...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-check-circle"></i>
        <span id="notification-message"></span>
    </div>
</div>

<!-- 添加/编辑卡库模态框 -->
<div id="repoModal" class="modal" tabindex="-1" aria-labelledby="repoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-plus-circle me-2"></i><span id="repoModalLabel">添加卡库</span></h3>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                    onclick="hideModal('repoModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="repoForm">
                    <input type="hidden" id="repoId" value="">

                    <div class="form-group mb-3">
                        <label for="repoName" class="form-label">卡库名称</label>
                        <input type="text" class="form-control" id="repoName" required placeholder="请输入卡库名称">
                        <div class="invalid-feedback" id="repoNameError">请输入卡库名称</div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">操作类型</label>
                        <div class="radio-options">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="operationType" id="addCardKeys"
                                    value="add" checked>
                                <label class="form-check-label" for="addCardKeys">
                                    添加卡密
                                </label>
                            </div>
                            <div class="form-check" id="deleteCardKeysOption" style="display:none;">
                                <input class="form-check-input" type="radio" name="operationType" id="deleteCardKeys"
                                    value="delete">
                                <label class="form-check-label" for="deleteCardKeys">
                                    删除卡密
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="cardKeys" class="form-label">卡密列表</label>
                        <textarea class="form-control" id="cardKeys" rows="6" placeholder="一行一卡密"></textarea>
                        <div class="invalid-feedback" id="cardKeysError">请输入卡密列表</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"
                    onclick="hideModal('repoModal')">取消</button>
                <button type="button" class="btn btn-primary" id="saveRepoBtn">
                    <span id="saveSpinner" class="spinner-border spinner-border-sm" role="status" style="display: none; margin-right: 5px;"></span>
                    <span id="saveBtnText">保存</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认对话框 -->
<div id="deleteModal" class="confirm-dialog">
    <div class="confirm-box">
        <div class="confirm-title">
            <i class="fas fa-exclamation-triangle"></i> 确认删除
        </div>
        <div class="confirm-message">
            确定要删除此卡库吗？此操作不可撤销，库内所有卡密将一并删除。
        </div>
        <div class="confirm-actions">
            <button id="cancelDelete" class="btn btn-light" onclick="hideModal('deleteModal')">取消</button>
            <button id="confirmDeleteBtn" class="btn btn-danger">
                <span id="deleteSpinner" class="spinner-border spinner-border-sm" role="status" style="display: none; margin-right: 5px;"></span>
                <span id="deleteBtnText">确认删除</span>
            </button>
        </div>
    </div>
</div>

<!-- 添加页面样式 -->


<!-- 超简化版JS代码 -->

<script src="/static/js/pages/cardstock.js"></script>
