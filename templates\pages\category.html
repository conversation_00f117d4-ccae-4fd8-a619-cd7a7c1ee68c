<!-- 分类列表管理页面 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="/static/css/pages/category.css">
<!-- 添加md5库引用 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.19.0/js/md5.min.js"></script>
<!-- 不再手动加载配置文件，依赖已加载的config.js -->


<!-- 不再使用特殊标记阻止main.js初始化 -->

<div id="category-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
        <h1 class="page-title">分类管理</h1>
        <div class="action-buttons">
            <button id="addCategoryBtn" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加分类
            </button>
        </div>
    </div>

    <!-- 分类列表 -->
    <div class="category-list-container">
        <div class="table-responsive">
            <table class="category-list categories-table">
                <thead>
                    <tr>
                        <th class="id-column"><i class="fas fa-hashtag"></i> 分类ID</th>
                        <th class="image-column"><i class="fas fa-image"></i> 分类图片</th>
                        <th class="name-column"><i class="fas fa-tag"></i> 分类名称</th>
                        <th class="level-column"><i class="fas fa-layer-group"></i> 分类层级</th>
                        <th class="date-column"><i class="fas fa-calendar-alt"></i> 创建时间</th>
                        <th class="actions-column"><i class="fas fa-cog"></i> 操作</th>
                    </tr>
                </thead>
                <tbody id="category-table-body">
                    <tr>
                        <td colspan="6">
                            <div class="loading">
                                <div class="spinner"></div>
                                <p style="margin-top: 15px; color: var(--primary-dark);">正在加载分类数据...</p>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div id="category-modal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-plus-circle"></i> 添加分类</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="category-form">
                    <input type="hidden" id="category-id" value="">
                    <input type="hidden" id="parent-id" value="">

                    <div class="form-group">
                        <label for="category-name" class="form-label">
                            <i class="fas fa-bookmark" style="color: var(--primary-color); margin-right: 8px;"></i>
                            分类名称
                        </label>
                        <input type="text" id="category-name" class="form-control" placeholder="请输入分类名称" required>
                    </div>

                    <div class="form-group" id="level-group">
                        <label for="category-level" class="form-label">
                            <i class="fas fa-layer-group" style="color: var(--primary-color); margin-right: 8px;"></i>
                            分类层级
                        </label>
                        <select id="category-level" class="form-control">
                            <option value="1">一级分类</option>
                            <option value="2">二级分类</option>
                        </select>
                    </div>

                    <div class="form-group" id="parent-category-group" style="display: none;">
                        <label for="parent-category" class="form-label">
                            <i class="fas fa-sitemap" style="color: var(--primary-color); margin-right: 8px;"></i>
                            父级分类
                        </label>
                        <select id="parent-category" class="form-control">
                            <!-- 父级分类选项将通过JavaScript动态填充 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="category-image" class="form-label">
                            <i class="fas fa-image" style="color: var(--primary-color); margin-right: 8px;"></i>
                            分类图片
                        </label>
                        <input type="text" id="category-image" class="form-control" placeholder="请输入图片URL">
                        <div class="image-upload">
                            <div id="image-preview-container">
                                <img id="image-preview" class="image-preview" src="" alt="分类图片预览">
                            </div>
                            <label for="image-upload" class="upload-btn">
                                <i class="fas fa-upload"></i> 选择图片
                            </label>
                            <input type="file" id="image-upload" style="display: none;" accept="image/*">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="cancel-btn" class="btn btn-cancel cancel-btn" type="button">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button id="save-btn" class="btn btn-primary save-btn" type="button">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>

    <!-- 确认删除对话框 -->
    <div id="confirm-dialog" class="confirm-dialog">
        <div class="confirm-box">
            <div class="confirm-title">
                <i class="fas fa-exclamation-triangle"></i> 确认删除
            </div>
            <div class="confirm-message">
                确定要删除该分类吗？如果是一级分类，其下的所有二级分类也将被删除！
            </div>
            <div class="confirm-actions">
                <button id="cancel-delete-btn" class="btn btn-cancel">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button id="confirm-delete-btn" class="btn btn-danger">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <i class="fas fa-check-circle"></i>
        <span id="notification-message"></span>
        <button class="close-notification">&times;</button>
    </div>
</div>



<!-- 确保页面加载后立即初始化 -->

<script src="/static/js/pages/category.js"></script>