<!-- 卡券管理页面 -->
<link rel="stylesheet" href="/static/css/pages/coupon.css">
<div class="coupon-management-container">
    <!-- 页面标题和筛选操作区域 -->
    <div class="page-header">
        <div class="header-left">
            <h1 class="page-title">
                卡券管理
            </h1>
        </div>
        <div class="header-center">
            <!-- 筛选和搜索区域移动到这里 -->
            <div class="filter-group">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜索卡券代码..." class="search-input">
                </div>
                <select id="typeFilter" class="filter-select">
                    <option value="">全部类型</option>
                    <option value="fixed">固定金额</option>
                    <option value="percentage">百分比折扣</option>
                </select>
                <select id="statusFilter" class="filter-select">
                    <option value="">全部状态</option>
                    <option value="active">有效</option>
                    <option value="used">已使用</option>
                    <option value="expired">已过期</option>
                </select>
                <button id="resetFiltersBtn" class="reset-btn">
                    <i class="fas fa-undo"></i>
                </button>
                <button id="fixLayoutBtn" class="fix-layout-btn" title="修复布局重叠问题" style="display: none;">
                    <i class="fas fa-wrench"></i>
                </button>
            </div>
        </div>
        <div class="header-actions">
            <button id="addCouponBtn" class="add-coupon-btn">
                <i class="fas fa-plus"></i> 添加卡券
            </button>
            <button id="exportSelectedBtn" class="export-btn" disabled>
                <i class="fas fa-download"></i> 导出选中
            </button>
            <button id="deleteSelectedBtn" class="delete-selected-btn" disabled>
                <i class="fas fa-trash-alt"></i> 批量删除
            </button>
        </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="totalCoupons">0</div>
                <div class="stat-label">总卡券数</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon active">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="activeCoupons">0</div>
                <div class="stat-label">有效卡券</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon used">
                <i class="fas fa-history"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="usedCoupons">0</div>
                <div class="stat-label">已使用</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon expired">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number" id="expiredCoupons">0</div>
                <div class="stat-label">已过期</div>
            </div>
        </div>
    </div>

    <!-- 卡券列表 -->
    <div class="coupon-list-container table-container">
        <div class="table-content" id="tableContent">
            <table class="coupon-table">
                <thead>
                    <tr>
                        <th class="checkbox-column">
                            <div class="kawaii-checkbox">
                                <input type="checkbox" id="selectAllCoupons" class="kawaii-checkbox-input">
                                <label for="selectAllCoupons" class="kawaii-checkbox-label">
                                    <div class="kawaii-checkbox-face">
                                        <div class="kawaii-checkbox-eyes"></div>
                                        <div class="kawaii-checkbox-mouth"></div>
                                    </div>
                                </label>
                            </div>
                        </th>
                        <th><i class="fas fa-barcode"></i> 卡券代码</th>
                        <th><i class="fas fa-percentage"></i> 优惠类型</th>
                        <th><i class="fas fa-coins"></i> 优惠值</th>
                        <th><i class="fas fa-calendar-alt"></i> 创建时间</th>
                        <th><i class="fas fa-clock"></i> 有效期</th>
                        <th><i class="fas fa-info-circle"></i> 状态</th>
                        <th><i class="fas fa-cog"></i> 操作</th>
                    </tr>
                </thead>
                <tbody id="couponTableBody">
                    <!-- 卡券数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 骨架屏 -->
        <div class="skeleton-loader" id="skeletonLoader">
            <div class="skeleton-row">
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
            </div>
            <div class="skeleton-row">
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
            </div>
            <div class="skeleton-row">
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
            </div>
            <div class="skeleton-row">
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
            </div>
            <div class="skeleton-row">
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
                <div class="skeleton-cell"></div>
            </div>
        </div>
        
        <!-- 空状态显示 -->
        <div class="empty-state" id="emptyState">
            <div class="empty-state-icon">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <div class="empty-state-text">暂无卡券数据</div>
            <div class="empty-state-hint">点击"添加卡券"按钮创建您的第一张卡券</div>
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-state" id="loadingState" style="display: none;">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container">
        <div class="pagination-info">
            <span>共 <span id="totalCount">0</span> 条记录</span>
        </div>
        <div class="pagination-controls">
            <button class="page-btn" id="prevPageBtn" disabled>
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="page-numbers" id="pageNumbers">
                <!-- 页码将动态生成 -->
            </span>
            <button class="page-btn" id="nextPageBtn" disabled>
                <i class="fas fa-chevron-right"></i>
            </button>
            <select class="page-size-select" id="pageSizeSelect">
                <option value="10">10条/页</option>
                <option value="20" selected>20条/页</option>
                <option value="50">50条/页</option>
            </select>
        </div>
    </div>
</div>

<!-- 添加卡券模态框 -->
<div id="addCouponModal" class="modal">
    <div class="modal-content coupon-modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-plus-circle"></i> 添加卡券</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <form id="addCouponForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="couponCount"><i class="fas fa-sort-numeric-up"></i> 生成数量</label>
                        <input type="number" id="couponCount" class="form-control" placeholder="请输入生成数量" min="1" max="1000" required>
                    </div>
                    <div class="form-group">
                        <label for="discountType"><i class="fas fa-percentage"></i> 优惠类型</label>
                        <select id="discountType" class="form-control" required>
                            <option value="">请选择优惠类型</option>
                            <option value="fixed">固定金额减免</option>
                            <option value="percentage">百分比折扣</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="discountValue"><i class="fas fa-coins"></i> 优惠值</label>
                        <input type="number" id="discountValue" class="form-control" placeholder="请输入优惠值" step="0.01" min="0" required>
                        <small class="form-hint" id="discountHint">请先选择优惠类型</small>
                    </div>
                    <div class="form-group">
                        <label for="validDays"><i class="fas fa-calendar-days"></i> 有效天数</label>
                        <input type="number" id="validDays" class="form-control" placeholder="请输入有效天数" min="1" max="365" required>
                        <small class="form-hint">卡券从创建日期开始计算有效期</small>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="minOrderAmount"><i class="fas fa-shopping-cart"></i> 最低消费金额</label>
                        <input type="number" id="minOrderAmount" class="form-control" placeholder="0表示无限制" step="0.01" min="0">
                        <small class="form-hint">使用卡券的最低订单金额要求</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="couponDescription"><i class="fas fa-align-left"></i> 卡券描述</label>
                    <textarea id="couponDescription" class="form-control" rows="3" placeholder="请输入卡券使用说明（可选）"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancelAddCoupon" class="btn-cancel">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn-confirm">
                        <i class="fas fa-check"></i> 生成卡券
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteCouponModal" class="modal">
    <div class="modal-content delete-modal-content">
        <div class="modal-header delete-modal-header">
            <h2><i class="fas fa-exclamation-triangle"></i> 删除确认</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body delete-modal-body">
            <div class="delete-warning-icon">
                <i class="fas fa-trash-alt"></i>
            </div>
            <div class="delete-warning-text">
                <h3>确定要删除选中的卡券吗？</h3>
                <p>此操作不可恢复，删除后将无法找回这些卡券信息。</p>
            </div>
            <div class="delete-coupon-info">
                <div class="info-item">
                    <span class="info-label"><i class="fas fa-sort-numeric-up"></i> 选中数量：</span>
                    <span class="info-value" id="deleteCount">0</span>
                </div>
            </div>
            <div class="delete-actions">
                <button type="button" class="btn-cancel" id="cancelDeleteCoupon">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn-delete-confirm" id="confirmDeleteCoupon">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 卡券详情模态框 -->
<div id="couponDetailModal" class="modal">
    <div class="modal-content detail-modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-info-circle"></i> 卡券详情</h2>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <div class="coupon-detail-card">
                <div class="coupon-visual">
                    <div class="coupon-ticket">
                        <div class="ticket-header">
                            <div class="ticket-value">
                                <span id="detailDiscountValue">0</span>
                                <span id="detailDiscountUnit">元</span>
                            </div>
                        </div>
                        <div class="ticket-body">
                            <div class="ticket-code">
                                <span>卡券代码</span>
                                <strong id="detailCouponCode">XXXX-XXXX-XXXX</strong>
                            </div>
                            <div class="ticket-info">
                                <div class="info-row">
                                    <span>有效期至：</span>
                                    <span id="detailExpiryDate">2024-12-31</span>
                                </div>
                                <div class="info-row">
                                    <span>最低消费：</span>
                                    <span id="detailMinAmount">无限制</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="coupon-meta">
                    <div class="meta-item">
                        <label>创建时间：</label>
                        <span id="detailCreateTime">-</span>
                    </div>
                    <div class="meta-item">
                        <label>当前状态：</label>
                        <span id="detailStatus" class="status-badge">-</span>
                    </div>
                    <div class="meta-item">
                        <label>使用说明：</label>
                        <span id="detailDescription">暂无说明</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="/static/js/pages/coupon.js"></script>

