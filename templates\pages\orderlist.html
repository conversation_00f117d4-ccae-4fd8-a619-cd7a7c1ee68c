{% load static %}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 无名SUP</title>

    <!-- 引入框架样式 -->
    <link rel="stylesheet" href="{% static 'css/admin-framework.css' %}">
    <link rel="stylesheet" href="{% static 'css/mobile-responsive.css' %}">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- SheetJS 库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    
    <link rel="stylesheet" href="/static/css/pages/orderlist.css">
</head>
<body>
    <!-- 页面容器 -->
    <div id="order-container" class="order-container">
        <!-- 加载中指示器 -->
        <div id="loading-overlay" style="display: none;">
            <div class="loading-animation">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载订单数据...</div>
                <div class="loading-subtext">请稍候，正在获取最新数据</div>
            </div>
        </div>

        <!-- 订单列表卡片 -->
        <div class="orders-card">
            <div class="card-title">
                <h2>
                    订单管理
                </h2>
                <div class="card-actions">
                    <!-- 批量操作区域 -->
                    <div id="batchActions" class="batch-actions" style="display: none;">
                        <span id="selectedCount" class="selected-count">已选择 0 项</span>
                        <button id="batchExportBtn" class="btn btn-primary">
                            <i class="fas fa-file-excel"></i> 导出选中
                        </button>
                        <button id="clearSelectionBtn" class="btn btn-light">
                            <i class="fas fa-times"></i> 清除选择
                        </button>
                    </div>
                    <!-- 常规操作区域 -->
                    <div class="regular-actions">
                        <button id="refreshBtn" class="btn btn-light">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button id="exportAllBtn" class="btn btn-primary">
                            <i class="fas fa-download"></i> 导出全部
                        </button>
                    </div>
                </div>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="search-filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">关键字搜索</label>
                        <input type="text" id="searchKeyword" class="form-control" placeholder="订单号、商品名称、客户信息...">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">订单状态</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">全部状态</option>
                            <option value="pending">待付款</option>
                            <option value="paid">已付款</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="refunded">已退款</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">货源渠道</label>
                        <select id="sourceFilter" class="form-control">
                            <option value="">全部货源</option>
                            <option value="direct">自营</option>
                            <option value="docking">对接</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品类型</label>
                        <select id="typeFilter" class="form-control">
                            <option value="">全部类型</option>
                            <option value="2">虚拟商品</option>
                            <option value="1">卡密商品</option>
                            <option value="3">对接商品</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button class="search-btn" id="searchBtn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button class="refresh-btn" id="refreshBtn" title="刷新数据">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表格容器 -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="orders-table">
                    <thead>
                        <tr>
                            <th style="width: 40px; min-width: 40px; max-width: 40px;">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="selectAllCheckbox" class="custom-checkbox">
                                    <label for="selectAllCheckbox" class="checkbox-label">
                                        <i class="fas fa-check"></i>
                                    </label>
                                </div>
                            </th>
                            <th style="min-width: 140px;"><i class="fas fa-clock"></i> 时间</th>
                            <th style="min-width: 300px;"><i class="fas fa-hashtag"></i> 订单号</th>
                            <th style="min-width: 110px;"><i class="fas fa-info-circle"></i> 状态</th>
                            <th style="min-width: 280px;"><i class="fas fa-barcode"></i> 商品编号</th>
                            <th style="min-width: 250px;"><i class="fas fa-box"></i> 商品名称</th>
                            <th style="min-width: 120px;"><i class="fas fa-store"></i> 下单来源</th>
                            <th style="min-width: 120px;"><i class="fas fa-tag"></i> 商品类型</th>
                            <th style="min-width: 130px;"><i class="fas fa-truck"></i> 货源渠道</th>
                            <th style="min-width: 110px;"><i class="fas fa-user"></i> 购买客户</th>
                            <th style="min-width: 300px;"><i class="fas fa-credit-card"></i> 支付订单</th>
                            <th style="min-width: 100px;"><i class="fas fa-yen-sign"></i> 销售价</th>
                            <th style="min-width: 100px;">进货价</th>
                            <th style="min-width: 100px;"><i class="fas fa-chart-line"></i> 利润</th>
                            <th style="min-width: 140px;"><i class="fas fa-cog"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody id="orderListTable">
                        <!-- 订单列表将通过JavaScript动态加载 -->
                        <tr id="initialLoadingState">
                            <td colspan="15">
                                <div class="empty-state">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <h3>正在加载订单数据...</h3>
                                    <p>请稍候</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="paginationInfo">显示第 1-10 条，共 0 条记录</span>
                </div>
                <div class="pagination-controls">
                    <select id="pageSizeSelect" class="page-size-select">
                        <option value="10">10条/页</option>
                        <option value="20">20条/页</option>
                        <option value="50">50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                    <div class="pagination-buttons">
                        <button id="prevPageBtn" class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <div id="pageNumbers" class="page-numbers">
                            <!-- 页码按钮由JavaScript动态生成 -->
                        </div>
                        <button id="nextPageBtn" class="pagination-btn" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单详情模态框 -->
        <div id="orderDetailModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-file-alt"></i> 订单详情</h3>
                    <button class="modal-close" id="closeDetailModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="orderDetailContent">
                    <!-- 订单详情内容将动态加载 -->
                </div>
            </div>
        </div>
    </div>

    
    

    <!-- 删除确认模态框 - 版本 2024-01-07-v2.0 -->
    <div id="deleteConfirmModal" class="delete-confirm-modal" style="display: none;">
        <div class="delete-modal-overlay"></div>
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <div class="delete-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="delete-modal-title">确认删除订单</h3>
            </div>
            <div class="delete-modal-body">
                <p class="delete-warning-text">
                    您确定要删除这个订单吗？
                </p>
                <p class="delete-order-info">
                    <strong>订单号：</strong><span id="deleteOrderNumber"></span><br>
                    <strong>商品名称：</strong><span id="deleteProductName"></span>
                </p>
                <div class="delete-warning-notice">
                    <i class="fas fa-info-circle"></i>
                    此操作不可恢复，请谨慎操作！
                </div>
            </div>
            <div class="delete-modal-footer">
                <button type="button" class="btn-cancel" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button type="button" class="btn-confirm-delete" onclick="confirmDeleteOrder()">
                    <i class="fas fa-trash"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>

    
    <script src="/static/js/pages/orderlist.js"></script>
</body>
</html>
