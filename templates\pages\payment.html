<!-- 支付渠道配置页面 -->
<link rel="stylesheet" href="/static/css/pages/payment.css">
<div class="payment-config-container">
    <!-- 页面标题和添加按钮 -->
    <div class="page-header">
        <h1 class="page-title">支付渠道配置</h1>
        <div class="header-actions">
            <button id="addPaymentChannelBtn" class="add-payment-btn" onclick="openAddModal()">
                <i class="fas fa-plus"></i> 添加支付渠道
            </button>
        </div>
    </div>

    <!-- 数据列表 -->
    <div class="payment-list-container">
        <table class="payment-table">
            <thead>
                <tr>
                    <th><i class="fas fa-id-badge"></i> ID</th>
                    <th><i class="fas fa-credit-card"></i> 通道名称</th>
                    <th><i class="fas fa-tag"></i> 通道类型</th>
                    <th><i class="fas fa-percentage"></i> 手续费率</th>
                    <th><i class="fas fa-toggle-on"></i> 状态</th>
                    <th><i class="fas fa-cog"></i> 操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 动态加载支付渠道数据 -->
            </tbody>
        </table>
        
        <!-- 空状态显示 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="empty-state-text">暂无支付渠道，请点击"添加支付渠道"按钮添加</div>
        </div>
    </div>

    <!-- 添加支付渠道模态框 -->
    <div id="addPaymentChannelModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus-circle"></i> 添加支付渠道</h2>
                <span class="close-modal" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addPaymentChannelForm" onsubmit="return false;">
                    <div class="form-group">
                        <label for="paymentType"><i class="fas fa-tag"></i> 支付类型</label>
                        <select id="paymentType" class="form-control">
                            <option value="epay" selected>码/易支付通用渠道</option>
                            <option value="alipay_official">支付宝官方支付渠道</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="paymentName"><i class="fas fa-credit-card"></i> 支付名称</label>
                        <input type="text" id="paymentName" class="form-control" placeholder="请输入支付名称">
                    </div>
                    <div class="form-group">
                        <label for="paymentFee"><i class="fas fa-percentage"></i> 手续费率(%)</label>
                        <input type="number" id="paymentFee" class="form-control" placeholder="请输入手续费率" step="0.01" min="0" max="100" value="0">
                    </div>
                    
                    <!-- 不同支付类型的特定配置字段 - 默认隐藏 -->
                    <div id="epayConfig" class="payment-specific-config">
                        <h3 class="config-section-title">易支付配置</h3>
                        <div class="form-group">
                            <label><i class="fas fa-plug"></i> 接口类型</label>
                            <div class="radio-group api-type-group">
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="apiType" value="submit.php" checked>
                                    <span>submit.php</span>
                                </label>
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="apiType" value="mapi.php">
                                    <span>mapi.php</span>
                                </label>
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="apiType" value="apisubmit.php">
                                    <span>apisubmit.php</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="epayDomain"><i class="fas fa-link"></i> 对接域名</label>
                            <input type="text" id="epayDomain" class="form-control" placeholder="请输入对接域名">
                        </div>
                        <div class="form-group">
                            <label for="epayAppId"><i class="fas fa-id-badge"></i> 对接APPID</label>
                            <input type="text" id="epayAppId" class="form-control" placeholder="请输入对接APPID">
                        </div>
                        <div class="form-group">
                            <label for="epayKey"><i class="fas fa-key"></i> 对接Key</label>
                            <input type="password" id="epayKey" class="form-control" placeholder="请输入对接Key">
                        </div>
                        <h3 class="config-section-title">支付渠道选择</h3>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fab fa-weixin"></i> 微信</label>
                            <label class="switch">
                                <input type="checkbox" id="epayWechat">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fas fa-credit-card"></i> 支付宝</label>
                            <label class="switch">
                                <input type="checkbox" id="epayAlipay">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fab fa-qq"></i> QQ</label>
                            <label class="switch">
                                <input type="checkbox" id="epayQQ">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>

                    <!-- 支付宝官方配置字段 -->
                    <div id="alipayOfficialConfig" class="payment-specific-config" style="display: none;">
                        <h3 class="config-section-title">支付宝官方配置</h3>
                        <div class="form-group">
                            <label for="alipayAppId"><i class="fas fa-id-badge"></i> 应用ID</label>
                            <input type="text" id="alipayAppId" class="form-control" placeholder="请输入支付宝应用ID">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-key"></i> 支付宝应用私钥</label>
                            <div class="key-input-tabs">
                                <button type="button" class="key-input-tab active" data-target="alipayPrivateKeyFile">
                                    <i class="fas fa-upload"></i> 上传文件
                                </button>
                                <button type="button" class="key-input-tab" data-target="alipayPrivateKeyText">
                                    <i class="fas fa-paste"></i> 直接粘贴
                                </button>
                            </div>
                            <div id="alipayPrivateKeyFile" class="key-input-content active">
                                <div class="file-upload-container">
                                    <input type="file" id="alipayPrivateKey" class="file-input" accept=".pem" />
                                    <label for="alipayPrivateKey" class="file-upload-label">
                                        <i class="fas fa-upload"></i>
                                        <span class="file-upload-text">选择应用私钥文件 (.pem)</span>
                                    </label>
                                    <div class="file-upload-info"></div>
                                </div>
                            </div>
                            <div id="alipayPrivateKeyText" class="key-input-content">
                                <div class="key-input-hint" style="margin-bottom: 8px; color: #ff7eb9; font-weight: 500;">
                                    <i class="fas fa-info-circle"></i> 重要提醒：请在支付宝开放平台选择"非JAVA语言"的应用私钥进行复制
                                </div>
                                <textarea id="alipayPrivateKeyTextarea" class="key-textarea"
                                    placeholder="请直接粘贴从支付宝开放平台复制的应用私钥内容&#10;&#10;支持以下格式：&#10;1. 完整PEM格式（包含BEGIN/END标记）&#10;2. 纯密钥内容（系统将自动添加PEM格式标记）&#10;&#10;示例：MIIEpAIBAAKCAQEA..."></textarea>
                                <div class="key-input-hint">系统将自动检测并格式化您粘贴的密钥内容，支持完整PEM格式或纯密钥内容</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-certificate"></i> 支付宝公钥</label>
                            <div class="key-input-tabs">
                                <button type="button" class="key-input-tab active" data-target="alipayPublicKeyFile">
                                    <i class="fas fa-upload"></i> 上传文件
                                </button>
                                <button type="button" class="key-input-tab" data-target="alipayPublicKeyText">
                                    <i class="fas fa-paste"></i> 直接粘贴
                                </button>
                            </div>
                            <div id="alipayPublicKeyFile" class="key-input-content active">
                                <div class="file-upload-container">
                                    <input type="file" id="alipayPublicKey" class="file-input" accept=".pem" />
                                    <label for="alipayPublicKey" class="file-upload-label">
                                        <i class="fas fa-upload"></i>
                                        <span class="file-upload-text">选择支付宝公钥文件 (.pem)</span>
                                    </label>
                                    <div class="file-upload-info"></div>
                                </div>
                            </div>
                            <div id="alipayPublicKeyText" class="key-input-content">
                                <textarea id="alipayPublicKeyTextarea" class="key-textarea"
                                    placeholder="请直接粘贴从支付宝开放平台复制的支付宝公钥内容&#10;&#10;支持以下格式：&#10;1. 完整PEM格式（包含BEGIN/END标记）&#10;2. 纯密钥内容（系统将自动添加PEM格式标记）&#10;&#10;示例：MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A..."></textarea>
                                <div class="key-input-hint">系统将自动检测并格式化您粘贴的密钥内容，支持完整PEM格式或纯密钥内容</div>
                            </div>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label">
                                <i class="fas fa-flask"></i> 沙箱模式
                                <span class="switch-description">（开启后使用支付宝沙箱环境进行测试）</span>
                            </label>
                            <label class="switch">
                                <input type="checkbox" id="alipaySandbox">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group switch-group">
                        <label class="switch-label"><i class="fas fa-toggle-on"></i> 启用状态</label>
                        <label class="switch">
                            <input type="checkbox" id="paymentStatus" checked>
                            <span class="slider round"></span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="cancelAddPayment" class="btn-cancel" onclick="closeAllModals()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" id="confirmAddPayment" class="btn-confirm" onclick="submitAddForm()">
                            <i class="fas fa-check"></i> 确认添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 编辑支付渠道模态框 -->
    <div id="editPaymentChannelModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> 编辑支付渠道</h2>
                <span class="close-modal" onclick="closeAllModals()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editPaymentChannelForm" onsubmit="return false;">
                    <input type="hidden" id="editPaymentId">
                    <!-- 编辑表单内容将与添加表单类似，但有预填充的值 -->
                    <div class="form-group">
                        <label for="editPaymentType"><i class="fas fa-tag"></i> 支付类型</label>
                        <select id="editPaymentType" class="form-control">
                            <option value="epay" selected>码/易支付通用渠道</option>
                            <option value="alipay_official">支付宝官方支付渠道</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editPaymentName"><i class="fas fa-credit-card"></i> 支付名称</label>
                        <input type="text" id="editPaymentName" class="form-control" placeholder="请输入支付名称">
                    </div>
                    <div class="form-group">
                        <label for="editPaymentFee"><i class="fas fa-percentage"></i> 手续费率(%)</label>
                        <input type="number" id="editPaymentFee" class="form-control" placeholder="请输入手续费率" step="0.01" min="0" max="100">
                    </div>

                    <!-- 易支付配置字段 -->
                    <div id="editEpayConfig" class="payment-specific-config">
                        <h3 class="config-section-title">易支付配置</h3>
                        <div class="form-group">
                            <label><i class="fas fa-plug"></i> 接口类型</label>
                            <div class="radio-group api-type-group">
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="editApiType" value="submit.php" checked>
                                    <span>submit.php</span>
                                </label>
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="editApiType" value="mapi.php">
                                    <span>mapi.php</span>
                                </label>
                                <label class="radio-label api-type-label">
                                    <input type="radio" name="editApiType" value="apisubmit.php">
                                    <span>apisubmit.php</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editEpayDomain"><i class="fas fa-link"></i> 对接域名</label>
                            <input type="text" id="editEpayDomain" class="form-control" placeholder="请输入对接域名">
                        </div>
                        <div class="form-group">
                            <label for="editEpayAppId"><i class="fas fa-id-badge"></i> 对接APPID</label>
                            <input type="text" id="editEpayAppId" class="form-control" placeholder="请输入对接APPID">
                        </div>
                        <div class="form-group">
                            <label for="editEpayKey"><i class="fas fa-key"></i> 对接Key</label>
                            <input type="password" id="editEpayKey" class="form-control" placeholder="请输入对接Key">
                        </div>
                        <h3 class="config-section-title">支付渠道选择</h3>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fab fa-weixin"></i> 微信</label>
                            <label class="switch">
                                <input type="checkbox" id="editEpayWechat">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fas fa-credit-card"></i> 支付宝</label>
                            <label class="switch">
                                <input type="checkbox" id="editEpayAlipay">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label"><i class="fab fa-qq"></i> QQ</label>
                            <label class="switch">
                                <input type="checkbox" id="editEpayQQ">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>

                    <!-- 支付宝官方配置字段 -->
                    <div id="editAlipayOfficialConfig" class="payment-specific-config" style="display: none;">
                        <h3 class="config-section-title">支付宝官方配置</h3>
                        <div class="form-group">
                            <label for="editAlipayAppId"><i class="fas fa-id-badge"></i> 应用ID</label>
                            <input type="text" id="editAlipayAppId" class="form-control" placeholder="请输入支付宝应用ID">
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-key"></i> 支付宝应用私钥</label>
                            <div class="key-input-tabs">
                                <button type="button" class="key-input-tab active" data-target="editAlipayPrivateKeyFile">
                                    <i class="fas fa-upload"></i> 上传文件
                                </button>
                                <button type="button" class="key-input-tab" data-target="editAlipayPrivateKeyText">
                                    <i class="fas fa-paste"></i> 直接粘贴
                                </button>
                            </div>
                            <div id="editAlipayPrivateKeyFile" class="key-input-content active">
                                <div class="file-upload-container">
                                    <input type="file" id="editAlipayPrivateKey" class="file-input" accept=".pem" />
                                    <label for="editAlipayPrivateKey" class="file-upload-label">
                                        <i class="fas fa-upload"></i>
                                        <span class="file-upload-text">选择应用私钥文件 (.pem)</span>
                                    </label>
                                    <div class="file-upload-info"></div>
                                </div>
                            </div>
                            <div id="editAlipayPrivateKeyText" class="key-input-content">
                                <div class="key-input-hint" style="margin-bottom: 8px; color: #ff7eb9; font-weight: 500;">
                                    <i class="fas fa-info-circle"></i> 重要提醒：请在支付宝开放平台选择"非JAVA语言"的应用私钥进行复制
                                </div>
                                <textarea id="editAlipayPrivateKeyTextarea" class="key-textarea"
                                    placeholder="请直接粘贴从支付宝开放平台复制的应用私钥内容&#10;&#10;支持以下格式：&#10;1. 完整PEM格式（包含BEGIN/END标记）&#10;2. 纯密钥内容（系统将自动添加PEM格式标记）&#10;&#10;示例：MIIEpAIBAAKCAQEA..."></textarea>
                                <div class="key-input-hint">系统将自动检测并格式化您粘贴的密钥内容，支持完整PEM格式或纯密钥内容</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label><i class="fas fa-certificate"></i> 支付宝公钥</label>
                            <div class="key-input-tabs">
                                <button type="button" class="key-input-tab active" data-target="editAlipayPublicKeyFile">
                                    <i class="fas fa-upload"></i> 上传文件
                                </button>
                                <button type="button" class="key-input-tab" data-target="editAlipayPublicKeyText">
                                    <i class="fas fa-paste"></i> 直接粘贴
                                </button>
                            </div>
                            <div id="editAlipayPublicKeyFile" class="key-input-content active">
                                <div class="file-upload-container">
                                    <input type="file" id="editAlipayPublicKey" class="file-input" accept=".pem" />
                                    <label for="editAlipayPublicKey" class="file-upload-label">
                                        <i class="fas fa-upload"></i>
                                        <span class="file-upload-text">选择支付宝公钥文件 (.pem)</span>
                                    </label>
                                    <div class="file-upload-info"></div>
                                </div>
                            </div>
                            <div id="editAlipayPublicKeyText" class="key-input-content">
                                <textarea id="editAlipayPublicKeyTextarea" class="key-textarea"
                                    placeholder="请直接粘贴从支付宝开放平台复制的支付宝公钥内容&#10;&#10;支持以下格式：&#10;1. 完整PEM格式（包含BEGIN/END标记）&#10;2. 纯密钥内容（系统将自动添加PEM格式标记）&#10;&#10;示例：MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A..."></textarea>
                                <div class="key-input-hint">系统将自动检测并格式化您粘贴的密钥内容，支持完整PEM格式或纯密钥内容</div>
                            </div>
                        </div>
                        <div class="form-group switch-group">
                            <label class="switch-label">
                                <i class="fas fa-flask"></i> 沙箱模式
                                <span class="switch-description">（开启后使用支付宝沙箱环境进行测试）</span>
                            </label>
                            <label class="switch">
                                <input type="checkbox" id="editAlipaySandbox">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group switch-group">
                        <label class="switch-label"><i class="fas fa-toggle-on"></i> 启用状态</label>
                        <label class="switch">
                            <input type="checkbox" id="editPaymentStatus">
                            <span class="slider round"></span>
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="cancelEditPayment" class="btn-cancel" onclick="closeAllModals()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" id="confirmEditPayment" class="btn-confirm" onclick="submitEditForm()">
                            <i class="fas fa-check"></i> 确认修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 通知容器 -->
<div class="toast-container" id="toast-container"></div>

<!-- 加载中指示器 -->
<div id="loading-indicator" class="loading-indicator">
    <div class="spinner"></div>
    <div class="loading-text">加载中...</div>
</div>



<!-- 引入字体图标库 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<!-- 基本函数定义 -->


<!-- 支付渠道配置脚本 -->

<script src="/static/js/pages/payment.js"></script>
