<!-- 插件市场页面 -->
<link rel="stylesheet" href="/static/css/pages/plugin.css">

<div class="plugin-market-container">
    <!-- 页面标题和搜索栏 -->
    <div class="page-header">
        <h1 class="page-title">插件市场</h1>
        <div class="search-container">
            <input type="text" id="pluginSearch" class="search-input" placeholder="搜索插件...">
            <button id="searchBtn" class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- 开发者后台按钮 -->
    <div class="developer-btn-container">
        <button id="developerBtn" class="developer-btn" onclick="window.emergencyShowModal(); return false;">
            <i class="fas fa-code"></i> 开发者后台
            <span class="btn-sparkle"></span>
        </button>
    </div>

    <!-- 筛选标签栏 -->
    <div class="filter-tags">
        <button class="filter-tag active" data-filter="all">
            <i class="fas fa-th-large"></i> 所有插件
        </button>
        <button class="filter-tag" data-filter="installed">
            <i class="fas fa-check-circle"></i> 已安装
        </button>
        <button class="filter-tag" data-filter="notInstalled">
            <i class="fas fa-download"></i> 未安装
        </button>
        <button class="filter-tag" data-filter="free">
            <i class="fas fa-gift"></i> 免费
        </button>
        <button class="filter-tag" data-filter="paid">
            <i class="fas fa-coins"></i> 付费
        </button>
    </div>

    <!-- 插件列表容器 -->
    <div class="plugin-list-container">
        <div class="plugin-grid">
            <!-- 插件卡片将通过JavaScript动态加载 -->
        </div>
        
        <!-- 空状态显示 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-plug"></i>
            </div>
            <div class="empty-state-text">插件市场为空</div>
        </div>
    </div>

    <!-- 加载中指示器 -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>
</div>

<!-- 通知容器 -->
<div class="toast-container" id="toast-container"></div>

<!-- 确认对话框 -->
<div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-dialog-content">
        <div class="confirm-dialog-header">
            <h3><i class="fas fa-question-circle"></i> 确认操作</h3>
            <button class="confirm-close-btn"><i class="fas fa-times"></i></button>
        </div>
        <div class="confirm-dialog-body">
            <p id="confirmMessage">您确定要执行此操作吗？</p>
        </div>
        <div class="confirm-dialog-footer">
            <button id="cancelActionBtn" class="confirm-btn cancel-btn"><i class="fas fa-times"></i> 取消</button>
            <button id="confirmActionBtn" class="confirm-btn confirm-btn-primary"><i class="fas fa-check"></i> 确认</button>
        </div>
    </div>
</div>

<!-- 开发者登录/注册模态框 -->
<div id="developerModal" class="developer-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 20000; display: none; align-items: center; justify-content: center;">
    <div class="developer-modal-content">
        <!-- 模态框顶部装饰 -->
        <div class="modal-decorations">
            <div class="decoration-bubble bubble-1"></div>
            <div class="decoration-bubble bubble-2"></div>
            <div class="decoration-bubble bubble-3"></div>
            <div class="decoration-star star-1"></div>
            <div class="decoration-star star-2"></div>
            <div class="decoration-dot dot-1"></div>
            <div class="decoration-dot dot-2"></div>
            <div class="decoration-dot dot-3"></div>
            <div class="decoration-cloud cloud-1"></div>
            <div class="decoration-cloud cloud-2"></div>
        </div>
        
        <!-- 模态框头部 -->
        <div class="developer-modal-header">
            <div class="header-icon">
                <i class="fas fa-code"></i>
            </div>
            <h3>开发者平台</h3>
            <button class="developer-modal-close-btn" onclick="window.emergencyShowModal(); return false;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- 标签页切换 -->
        <div class="developer-modal-tabs">
            <button class="developer-tab-btn active" data-tab="login">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
            <button class="developer-tab-btn" data-tab="register">
                <i class="fas fa-user-plus"></i> 注册
            </button>
            <div class="tab-indicator"></div>
        </div>
        
        <!-- 登录表单 -->
        <div class="developer-tab-content active" id="login-tab">
            <div class="tab-illustration">
                <div class="illustration-container">
                    <div class="illustration-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                </div>
            </div>
            <form id="loginForm" class="developer-form">
                <div class="form-group">
                    <label for="loginEmail">
                        <i class="fas fa-envelope"></i> 邮箱
                    </label>
                    <input type="email" id="loginEmail" name="email" class="form-input" placeholder="请输入邮箱">
                    <div class="input-highlight"></div>
                </div>
                <div class="form-group">
                    <label for="loginPassword">
                        <i class="fas fa-lock"></i> 密码
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="loginPassword" name="password" class="form-input" placeholder="请输入密码">
                        <button type="button" class="toggle-password-btn">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="input-highlight"></div>
                    </div>
                </div>
                <button type="submit" class="form-submit-btn">
                    <span class="btn-text">登录</span>
                    <i class="fas fa-arrow-right btn-icon"></i>
                    <span class="btn-shine"></span>
                </button>
                <div class="form-footer">
                    <a href="#" class="form-link">忘记密码？</a>
                </div>
            </form>
        </div>
        
        <!-- 注册表单 -->
        <div class="developer-tab-content" id="register-tab">
            <div class="tab-illustration">
                <div class="illustration-container">
                    <div class="illustration-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                </div>
            </div>
            <form id="registerForm" class="developer-form">
                <div class="form-group">
                    <label for="registerEmail">
                        <i class="fas fa-envelope"></i> 邮箱
                    </label>
                    <input type="email" id="registerEmail" name="email" class="form-input" placeholder="请输入邮箱">
                    <div class="input-highlight"></div>
                </div>
                <div class="form-group">
                    <label for="registerQQ">
                        <i class="fab fa-qq"></i> QQ号
                    </label>
                    <input type="text" id="registerQQ" name="qq" class="form-input" placeholder="请输入QQ号">
                    <div class="input-highlight"></div>
                </div>
                <div class="form-group">
                    <label for="registerPassword">
                        <i class="fas fa-lock"></i> 密码
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="registerPassword" name="password" class="form-input" placeholder="请设置密码">
                        <button type="button" class="toggle-password-btn">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="input-highlight"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="registerPasswordConfirm">
                        <i class="fas fa-lock"></i> 确认密码
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="registerPasswordConfirm" name="passwordConfirm" class="form-input" placeholder="请再次输入密码">
                        <button type="button" class="toggle-password-btn">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="input-highlight"></div>
                    </div>
                </div>
                <div class="form-checkbox agreement-checkbox">
                    <input type="checkbox" id="agreeTerms" name="agreeTerms">
                    <label for="agreeTerms">
                        <span class="checkbox-text">我同意</span>
                        <a href="#" class="inline-link terms-link">服务条款</a>
                        <span class="checkbox-text">和</span>
                        <a href="#" class="inline-link privacy-link">隐私政策</a>
                    </label>
                </div>
                <button type="submit" class="form-submit-btn">
                    <span class="btn-text">注册</span>
                    <i class="fas fa-user-plus btn-icon"></i>
                    <span class="btn-shine"></span>
                </button>
            </form>
        </div>
        
        <!-- 模态框底部装饰 -->
        <div class="modal-bottom-decoration">
            <svg viewBox="0 0 500 150" preserveAspectRatio="none">
                <path d="M0,50 C150,150 350,0 500,50 L500,150 L0,150 Z" style="stroke: none; fill: #ffe6f2;"></path>
            </svg>
        </div>
    </div>
</div>



 

<!-- 会话超时模态框 -->
<div id="sessionTimeoutModal" class="session-timeout-modal">
    <div class="session-timeout-content">
        <h3 class="session-timeout-title">会话已超时</h3>
        <p class="session-timeout-message">由于长时间未操作，您的会话已超时。请重新登录以继续。</p>
        <button id="reloginButton" class="session-timeout-button">重新登录</button>
    </div>
</div>

<!-- 账号后台模态框 -->
<div id="accountDashboardModal" class="account-dashboard-modal">
    <div class="account-dashboard-content">
        <div class="account-modal-header">
            <h3 class="account-modal-title">账号后台</h3>
            <button class="account-modal-close-btn"><i class="fas fa-times"></i></button>
        </div>
        
        <div class="account-modal-tabs">
            <div class="account-tab-item active" data-tab="home">
                <i class="fas fa-home"></i> 首页
            </div>
            <div class="account-tab-item" data-tab="plugins">
                <i class="fas fa-puzzle-piece"></i> 我的插件
            </div>
            <div class="account-tab-item" data-tab="finance">
                <i class="fas fa-wallet"></i> 资金管理
            </div>
            <div class="account-tab-item" data-tab="settings">
                <i class="fas fa-cog"></i> 账号设置
            </div>
            <div class="tab-indicator"></div>
        </div>
        
        <div class="account-tab-content">
            <div class="tab-pane active" id="home-pane">
                <div class="account-welcome">
                    <div class="welcome-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <h3 class="welcome-title">欢迎回来</h3>
                    <p class="welcome-message">在这里管理您的账号和插件</p>
                </div>
                
                <div class="account-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">0</span>
                            <span class="stat-label">我的插件</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">0</span>
                            <span class="stat-label">订单数量</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value">¥0.00</span>
                            <span class="stat-label">账户余额</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane" id="plugins-pane">
                <div class="plugins-header">
                    <h3>我的插件</h3>
                    <button class="new-plugin-btn">
                        <i class="fas fa-plus"></i> 新建插件
                    </button>
                </div>
                
                <div class="plugins-list">
                    <div class="empty-plugins">
                        <div class="empty-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="empty-text">您还没有上传任何插件</div>
                        <button class="create-plugin-btn">
                            <i class="fas fa-plus-circle"></i> 创建第一个插件
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane" id="finance-pane">
                <div class="finance-container">
                    <div class="balance-card">
                        <div class="balance-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="balance-info">
                            <div class="balance-label">账户余额</div>
                            <div class="balance-amount">¥0.00</div>
                        </div>
                        <button class="withdraw-btn">
                            <i class="fas fa-money-bill-wave"></i> 申请提现
                        </button>
                    </div>

                    <div class="qr-code-section">
                        <h3 class="section-title">提现二维码</h3>
                        <div class="qr-code-container">
                            <div class="qr-code-preview" id="qrCodePreview">
                                <div class="qr-code-placeholder">
                                    <i class="fas fa-qrcode"></i>
                                    <p>暂无二维码</p>
                                </div>
                            </div>
                            <div class="qr-code-actions">
                                <label for="qrCodeUpload" class="upload-btn">
                                    <i class="fas fa-upload"></i> 上传二维码
                                    <input type="file" id="qrCodeUpload" accept="image/*" style="display: none;">
                                </label>
                                <button class="delete-btn" id="deleteQrCode" style="display: none;">
                                    <i class="fas fa-trash-alt"></i> 删除二维码
                                </button>
                            </div>
                        </div>
                        <div class="qr-code-tips">
                            <p>支持的格式：JPG、PNG、GIF</p>
                            <p>建议尺寸：300x300像素</p>
                            <p>文件大小：不超过2MB</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane" id="settings-pane">
                <div class="settings-form">
                    <h3>账号设置</h3>
                    
                    <div class="form-group">
                        <label>
                            <i class="fas fa-envelope"></i> 邮箱
                        </label>
                        <input type="email" id="userEmail" class="form-control" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <i class="fab fa-qq"></i> QQ号
                        </label>
                        <input type="text" id="userQQ" class="form-control" readonly>
                    </div>
                    
                    <div class="form-divider"></div>
                    
                    <h4>修改密码</h4>
                    <div class="form-group">
                        <label>
                            <i class="fas fa-lock"></i> 当前密码
                        </label>
                        <div class="password-input-container">
                            <input type="password" id="currentPassword" class="form-control">
                            <button type="button" class="toggle-password-btn">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <i class="fas fa-lock"></i> 新密码
                        </label>
                        <div class="password-input-container">
                            <input type="password" id="newPassword" class="form-control">
                            <button type="button" class="toggle-password-btn">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <i class="fas fa-lock"></i> 确认新密码
                        </label>
                        <div class="password-input-container">
                            <input type="password" id="confirmPassword" class="form-control">
                            <button type="button" class="toggle-password-btn">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <button type="button" class="save-password-btn">保存修改</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入Font Awesome图标库 -->

<script src="/static/js/pages/plugin.js"></script>