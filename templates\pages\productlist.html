<!DOCTYPE html>
<html lang="zh-CN" data-fully-loadable="true">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品管理</title>

    <!-- 使用阿里云 CDN -->
    <link rel="stylesheet" href="https://g.alicdn.com/code/lib/font-awesome/6.4.0/css/all.min.css">
    <link href="https://g.alicdn.com/code/lib/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">

    
    <link rel="stylesheet" href="/static/css/pages/productlist.css">
</head>

<body>
    <!-- 页面容器 -->
    <div id="product-container">
        <!-- 加载中指示器 -->
        <div id="loading-overlay">
            <div class="spinner"></div>
            <div class="loading-text">正在加载数据...</div>
        </div>

        <!-- 商品列表卡片 -->
        <div class="products-card">
            <div class="card-title">
                <div style="font-size: 24px; font-weight: 600; color: #333333;">商品列表</div>
                <div class="card-actions">
                    <button id="refreshBtn" class="btn btn-light me-2">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button id="addProductBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加商品
                    </button>
                </div>
            </div>

            <!-- 搜索筛选区域 -->
            <div class="search-filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label class="filter-label">关键字搜索</label>
                        <input type="text" id="searchKeyword" class="form-control" placeholder="商品ID、商品名称...">
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">一级分类</label>
                        <select id="primaryCategoryFilter" class="form-control">
                            <option value="">全部分类</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">二级分类</label>
                        <select id="secondaryCategoryFilter" class="form-control">
                            <option value="">全部分类</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品类型</label>
                        <select id="productTypeFilter" class="form-control">
                            <option value="">全部类型</option>
                            <option value="2">虚拟商品</option>
                            <option value="1">卡密商品</option>
                            <option value="3">对接商品</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">商品状态</label>
                        <select id="productStatusFilter" class="form-control">
                            <option value="">全部状态</option>
                            <option value="1">上架</option>
                            <option value="0">下架</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button class="search-btn" id="searchBtn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">&nbsp;</label>
                        <button class="reset-btn" id="resetBtn" title="重置筛选条件">
                            <i class="fas fa-undo"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th width="8%"><i class="fas fa-hashtag"></i> ID</th>
                            <th width="18%"><i class="fas fa-box"></i> 商品名称</th>
                            <th width="8%"><i class="fas fa-image"></i> 图片</th>
                            <th width="12%"><i class="fas fa-tag"></i> 分类</th>
                            <th width="10%"><i class="fas fa-yen-sign"></i> 基础价格</th>
                            <th width="8%"><i class="fas fa-chart-line"></i> 销量</th>
                            <th width="8%"><i class="fas fa-warehouse"></i> 库存</th>
                            <th width="12%"><i class="fas fa-info-circle"></i> 商品类型</th>
                            <th width="10%"><i class="fas fa-check-circle"></i> 状态</th>
                            <th width="6%"><i class="fas fa-cog"></i> 操作</th>
                        </tr>
                    </thead>
                    <tbody id="productListTable">
                        <!-- 商品列表将通过JavaScript动态加载 -->
                        <!-- 空状态 -->
                        <tr>
                            <td colspan="10">
                                <div class="empty-state">
                                    <i class="fas fa-box-open"></i>
                                    <h3>暂无商品数据</h3>
                                    <p>点击"添加商品"按钮创建您的第一个商品</p>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
            </div>
            <div class="pagination-controls">
                <select id="pageSizeSelect" class="page-size-select">
                    <option value="10">10条/页</option>
                    <option value="20" selected>20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
                <div class="pagination-buttons">
                    <button id="prevPageBtn" class="pagination-btn" disabled>
                        <i class="fas fa-chevron-left"></i> 上一页
                    </button>
                    <div id="pageNumbers" class="page-numbers">
                        <!-- 页码按钮由JavaScript动态生成 -->
                    </div>
                    <button id="nextPageBtn" class="pagination-btn" disabled>
                        下一页 <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品模态框 -->
    <div id="productModal" class="modal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- 标题栏 -->
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-plus-circle me-2"></i><span id="modalTitle">添加商品</span>
                    </h3>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">&times;</button>
                </div>

                <!-- 表单内容 -->
                <div class="modal-body">
                    <form id="productForm">
                        <input type="hidden" id="productId" value="">

                        <!-- 导航菜单 -->
                        <ul class="nav nav-tabs" id="productTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab"
                                    data-bs-target="#basic" type="button" role="tab" aria-controls="basic"
                                    aria-selected="true">
                                    <i class="fas fa-info-circle me-1"></i> 基础信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="params-tab" data-bs-toggle="tab" data-bs-target="#params"
                                    type="button" role="tab" aria-controls="params" aria-selected="false">
                                    <i class="fas fa-list-alt me-1"></i> 附带参数
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="price-tab" data-bs-toggle="tab" data-bs-target="#price"
                                    type="button" role="tab" aria-controls="price" aria-selected="false">
                                    <i class="fas fa-tag me-1"></i> 价格设置
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="detail-tab" data-bs-toggle="tab" data-bs-target="#detail"
                                    type="button" role="tab" aria-controls="detail" aria-selected="false">
                                    <i class="fas fa-file-alt me-1"></i> 详情信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="postaction-tab" data-bs-toggle="tab"
                                    data-bs-target="#postaction" type="button" role="tab" aria-controls="postaction"
                                    aria-selected="false">
                                    <i class="fas fa-cogs me-1"></i> 后置操作
                                </button>
                            </li>
                        </ul>

                        <!-- 内容区域 -->
                        <div class="tab-content" id="productTabContent">
                            <!-- 基础信息 -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel"
                                aria-labelledby="basic-tab">
                                <h5 class="mb-3 mt-3">商品类型</h5>
                                <div class="type-card-container">
                                    <div class="type-card selected" data-type="1">
                                        <i class="fas fa-key"></i>
                                        <div class="type-card-content">
                                            <h5>卡密商品</h5>
                                            <p class="text-muted">适用于卡密、激活码等自动发货</p>
                                        </div>
                                    </div>
                                    <div class="type-card" data-type="2">
                                        <i class="fas fa-cloud-download-alt"></i>
                                        <div class="type-card-content">
                                            <h5>虚拟商品</h5>
                                            <p class="text-muted">适用于虚拟服务、下载等商品</p>
                                        </div>
                                    </div>
                                    <div class="type-card" data-type="3">
                                        <i class="fas fa-sync-alt"></i>
                                        <div class="type-card-content">
                                            <h5>对接商品</h5>
                                            <p class="text-muted">从外部平台对接的商品，不可更改类型</p>
                                        </div>
                                    </div>
                                </div>

                                <div id="cardLibraryContainer" class="mt-4">
                                    <h5 class="mb-3">选择卡库</h5>
                                    <div class="form-group">
                                        <select class="form-control" id="cardLibrary">
                                            <!-- 卡库选项将通过API获取 -->
                                        </select>
                                    </div>
                                </div>

                                <h5 class="mb-3 mt-4">商品分类</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">一级分类</label>
                                            <select class="form-control" id="categoryParent">
                                                <!-- 一级分类选项将通过API获取 -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">二级分类</label>
                                            <select class="form-control" id="categoryChild">
                                                <!-- 二级分类选项将根据一级分类动态显示 -->
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <h5 class="mb-3 mt-4">商品信息</h5>
                                <div class="form-group">
                                    <label class="form-label">商品名称</label>
                                    <input type="text" class="form-control" id="productName" placeholder="请输入商品名称">
                                </div>

                                <h5 class="mb-3 mt-4">库存管理</h5>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="form-label">库存数量</label>
                                            <input type="number" class="form-control" id="productStock"
                                                placeholder="请输入库存数量" min="0" step="1">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="form-check" style="padding-top: 8px;">
                                                <input class="form-check-input" type="checkbox" id="unlimitedStock">
                                                <label class="form-check-label" for="unlimitedStock">
                                                    无限库存
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">商品图片</label>
                                    <div class="custom-file-upload"
                                        style="border: 2px dashed var(--border-color); border-radius: 15px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s; background-color: #fff9fc;">
                                        <input type="file" id="productImage" style="display: none;">
                                        <label for="productImage" style="cursor: pointer; margin-bottom: 0;">
                                            <i class="fas fa-cloud-upload-alt"
                                                style="font-size: 2.5rem; color: var(--primary-light); margin-bottom: 15px; display: block;"></i>
                                            <div style="color: var(--primary-dark); font-weight: 500;">点击或拖拽上传图片</div>
                                            <div
                                                style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 5px;">
                                                支持 JPG、PNG 格式图片</div>
                                        </label>
                                    </div>
                                    <div id="imagePreview" class="mt-3" style="text-align: center;"></div>
                                </div>
                            </div>

                            <!-- 附带参数 -->
                            <div class="tab-pane fade" id="params" role="tabpanel" aria-labelledby="params-tab">
                                <div class="d-flex justify-content-between mb-3 mt-3">
                                    <h5>商品参数</h5>
                                    <button type="button" id="addParamBtn" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i> 添加参数
                                    </button>
                                </div>

                                <div id="paramContainer">
                                    <!-- 参数行将动态添加 -->
                                </div>
                            </div>

                            <!-- 价格设置 -->
                            <div class="tab-pane fade" id="price" role="tabpanel" aria-labelledby="price-tab">
                                <div class="form-group mt-3">
                                    <label class="form-label">基础价格</label>
                                    <div class="input-group">
                                        <span class="input-group-text"
                                            style="background-color: var(--primary-color); color: white; border-color: var(--primary-color);">¥</span>
                                        <input type="number" class="form-control" id="basePrice" placeholder="请输入商品基础价格"
                                            step="0.01" min="0">
                                    </div>
                                </div>

                                <div class="form-group mt-3">
                                    <label class="form-label">价格模板</label>
                                    <select class="form-control" id="priceTemplate">
                                        <!-- 价格模板选项将通过API获取 -->
                                    </select>
                                </div>

                                <div id="priceCalculation" class="price-calculation mt-4 d-none">
                                    <div class="price-calculation-header">价格计算预览</div>
                                    <div>
                                        <p class="mb-1">基础价格: <span id="previewBasePrice" class="price">¥0.00</span></p>
                                        <p class="mb-2">模板类型: <span id="templateType">-</span></p>
                                    </div>
                                    <hr style="border-color: var(--border-color);">
                                    <div id="memberPrices">
                                        <!-- 会员价格将根据价格模板动态显示 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 详情信息 -->
                            <div class="tab-pane fade" id="detail" role="tabpanel" aria-labelledby="detail-tab">
                                <div class="form-group mt-3">
                                    <label class="form-label">商品详情</label>
                                    <textarea class="form-control" id="productInfo" rows="12"
                                        placeholder="请输入商品详情，支持HTML格式"
                                        style="background-color: #fff9fc; min-height: 200px;"></textarea>
                                </div>

                                <div class="form-group mt-3">
                                    <label class="form-label">页面弹窗</label>
                                    <textarea class="form-control" id="productPopupTip" rows="6"
                                        placeholder="将在查看商品时自动弹窗显示提示内容，支持HTML格式"
                                        style="background-color: #fff9fc; min-height: 120px;"></textarea>
                                </div>
                            </div>

                            <!-- 后置操作 -->
                            <div class="tab-pane fade" id="postaction" role="tabpanel" aria-labelledby="postaction-tab">
                                <div class="alert alert-info mt-3"
                                    style="background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 12px; margin-bottom: 20px;">
                                    <i class="fas fa-info-circle me-2" style="color: #0c5460;"></i>
                                    <span style="color: #0c5460; font-size: 14px;">后置操作为异步操作，用户下单后由服务器完成</span>
                                </div>

                                <div class="form-group mt-3">
                                    <label class="form-label">后置操作类型</label>
                                    <select class="form-control" id="postActionType">
                                        <option value="none">无后置操作</option>
                                        <option value="url">请求指定URL</option>
                                    </select>
                                </div>

                                <div id="urlConfigSection" class="mt-4" style="display: none;">
                                    <!-- 系统变量信息展示区域 -->
                                    <div class="form-group">
                                        <div class="variable-info-toggle"
                                            style="cursor: pointer; padding: 10px; background-color: #f8f9fa; border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 15px;">
                                            <i class="fas fa-chevron-down me-2" id="variableToggleIcon"></i>
                                            <span
                                                style="font-weight: 500; color: var(--text-primary);">下拉展示系统变量信息</span>
                                        </div>

                                        <div id="variableInfoContent"
                                            style="display: none; background-color: #fff9fc; border: 1px solid var(--border-color); border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                                            <div
                                                style="margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid var(--border-color);">
                                                <strong style="color: var(--primary-color);">变量格式：&lt;变量名&gt;</strong>
                                            </div>

                                            <div style="margin-bottom: 15px;">
                                                <h6 style="color: var(--text-primary); margin-bottom: 10px;">可用参数列表：
                                                </h6>
                                                <div
                                                    style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 8px;">
                                                    <span class="variable-tag">10位时间戳</span>
                                                    <span class="variable-tag">13位时间戳</span>
                                                    <span class="variable-tag">参数1</span>
                                                    <span class="variable-tag">参数2</span>
                                                    <span class="variable-tag">参数3</span>
                                                    <span class="variable-tag">参数4</span>
                                                    <span class="variable-tag">参数5</span>
                                                    <span class="variable-tag">用户ID</span>
                                                    <span class="variable-tag">订单号</span>
                                                    <span class="variable-tag">支付方式</span>
                                                    <span class="variable-tag">请求方式</span>
                                                    <span class="variable-tag">商品名称</span>
                                                    <span class="variable-tag">商品价格</span>
                                                    <span class="variable-tag">支付价格</span>
                                                    <span class="variable-tag">签名信息</span>
                                                </div>
                                            </div>

                                            <div
                                                style="padding-top: 10px; border-top: 1px solid var(--border-color); color: var(--text-secondary); font-size: 14px;">
                                                <i class="fas fa-info-circle me-2"></i>
                                                变量可填写在URL、请求头、请求体、签名中
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">请求URL</label>
                                        <div class="editable-content" id="postActionUrl" contenteditable="true"
                                            data-placeholder="请输入请求URL"></div>
                                    </div>

                                    <div class="form-group mt-3">
                                        <label class="form-label">请求方式</label>
                                        <div class="method-button-group"
                                            style="display: flex; gap: 10px; padding-top: 8px;">
                                            <button type="button" class="method-btn active" data-method="GET"
                                                style="flex: 1; padding: 10px 20px; border: 2px solid var(--primary-color); border-radius: 8px; background-color: var(--primary-color); color: white; font-weight: 500; transition: all 0.3s ease; cursor: pointer;">
                                                <i class="fas fa-download me-2"></i>GET
                                            </button>
                                            <button type="button" class="method-btn" data-method="POST"
                                                style="flex: 1; padding: 10px 20px; border: 2px solid var(--border-color); border-radius: 8px; background-color: white; color: var(--text-secondary); font-weight: 500; transition: all 0.3s ease; cursor: pointer;">
                                                <i class="fas fa-upload me-2"></i>POST
                                            </button>
                                        </div>
                                        <input type="hidden" id="postActionMethod" value="GET">
                                    </div>

                                    <div class="form-group mt-3">
                                        <label class="form-label">请求头</label>
                                        <div id="headersContainer" class="headers-container">
                                            <!-- 请求头行将动态添加 -->
                                        </div>
                                        <button type="button" id="addHeaderBtn" class="btn btn-outline-primary mt-2"
                                            style="width: 100%; border-color: var(--primary-color); color: var(--primary-color); border-radius: 8px;">
                                            <i class="fas fa-plus me-1"></i> 添加请求头
                                        </button>
                                    </div>

                                    <div class="form-group mt-3" id="requestBodySection" style="display: none;">
                                        <label class="form-label">请求体</label>
                                        <div style="display: flex; gap: 15px;">
                                            <div style="flex: 1;">
                                                <div class="editable-content multi-line" id="postActionBody"
                                                    contenteditable="true" data-placeholder="请输入请求体内容，支持JSON格式"
                                                    style="height: 200px;"></div>
                                            </div>
                                            <div class="variable-shortcuts" style="flex: 0 0 200px;">
                                                <div
                                                    style="font-weight: 500; margin-bottom: 8px; color: var(--text-primary); font-size: 13px;">
                                                    变量快捷输入</div>
                                                <button type="button" class="variable-btn"
                                                    data-variable="10位时间戳">10位时间戳</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="13位时间戳">13位时间戳</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="参数1">参数1</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="参数2">参数2</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="参数3">参数3</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="参数4">参数4</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="参数5">参数5</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="用户ID">用户ID</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="订单号">订单号</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="支付方式">支付方式</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="请求方式">请求方式</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="商品名称">商品名称</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="商品价格">商品价格</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="支付价格">支付价格</button>
                                                <button type="button" class="variable-btn"
                                                    data-variable="签名信息">签名信息</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 签名配置区域 -->
                                    <div class="form-group mt-4">
                                        <label class="form-label">签名配置</label>

                                        <div class="form-group mt-2">
                                            <label class="form-label" style="font-size: 14px;">签名内容</label>
                                            <div style="display: flex; gap: 15px;">
                                                <div style="flex: 1;">
                                                    <div class="editable-content multi-line" id="signatureContent"
                                                        contenteditable="true" data-placeholder="请输入签名内容"></div>
                                                </div>
                                                <div class="variable-shortcuts" style="flex: 0 0 200px;">
                                                    <div
                                                        style="font-weight: 500; margin-bottom: 8px; color: var(--text-primary); font-size: 13px;">
                                                        变量快捷输入</div>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="10位时间戳">10位时间戳</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="13位时间戳">13位时间戳</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="参数1">参数1</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="参数2">参数2</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="参数3">参数3</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="参数4">参数4</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="参数5">参数5</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="用户ID">用户ID</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="订单号">订单号</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="支付方式">支付方式</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="请求方式">请求方式</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="商品名称">商品名称</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="商品价格">商品价格</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="支付价格">支付价格</button>
                                                    <button type="button" class="variable-btn signature-variable-btn"
                                                        data-variable="签名信息">签名信息</button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label" style="font-size: 14px;">加密方式</label>
                                                    <select class="form-control" id="encryptionMethod">
                                                        <option value="direct_md5">直接MD5签名</option>
                                                        <option value="lowercase_md5">内容小写后MD5签名</option>
                                                        <option value="uppercase_md5">内容大写后MD5签名</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label" style="font-size: 14px;">加密后方式</label>
                                                    <select class="form-control" id="postEncryptionMethod">
                                                        <option value="direct_output">直接输出加密内容</option>
                                                        <option value="lowercase_output">加密内容转小写后输出</option>
                                                        <option value="uppercase_output">加密内容转大写后输出</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 底部按钮 -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProductBtn">
                        <span id="saveSpinner" class="spinner-border spinner-border-sm" role="status"
                            style="display: none; margin-right: 5px;"></span>
                        <span id="saveButtonText">保存</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除对话框 -->
    <div id="confirmDialog" class="confirm-dialog">
        <div class="confirm-box">
            <div class="confirm-title">
                <i class="fas fa-exclamation-triangle"></i> 确认删除
            </div>
            <div class="confirm-message">
                确定要删除这个商品吗？此操作无法撤销。
            </div>
            <div class="confirm-actions">
                <button id="cancelDelete" class="btn btn-light">取消</button>
                <button id="confirmDelete" class="btn btn-danger">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-check-circle"></i>
        <span id="notificationMessage"></span>
    </div>

    <!-- 添加依赖检测和自动加载脚本 -->
    

    <script src="/static/js/pages/productlist.js"></script>
</body>

</html>