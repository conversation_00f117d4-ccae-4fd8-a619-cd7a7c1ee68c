<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无名SUP - 分类页</title>
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 移除原有图标字体和preconnect -->
    
    <link rel="stylesheet" href="/static/css/user/category.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fa-solid fa-heart"></i>
            无名SUP
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link active">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link" id="profileLink">我的</a>
            </li>
        </ul>
        
        <div class="nav-icons">
            <!-- 搜索图标已移除 -->
        </div>
        
        <div class="mobile-menu-btn">
            <i class="fa-solid fa-bars"></i>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <h1 class="page-title">商品分类</h1>
        
        <!-- 分类界面 -->
        <div class="mobile-category-container">

            
            <!-- 左侧一级分类列表容器 -->
            <div class="primary-category-wrapper" id="primaryCategoryWrapper">
                <!-- 一级分类下拉按钮 -->
                <div class="primary-category-dropdown-btn" id="primaryCategoryDropdownBtn">
                    <span class="fa-solid fa-chevron-down"></span>
                </div>

                <!-- 左侧一级分类列表 -->
                <div class="primary-category-list" id="primaryCategoryList">
                    <!-- 一级分类将通过JS动态加载 -->
                    <div class="loading-state" id="primaryLoadingState">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">加载分类中...</div>
                    </div>
                </div>

                <!-- 内嵌网格容器 -->
                <div class="primary-category-inline-grid" id="primaryCategoryInlineGrid">
                    <!-- 网格项将通过JS动态加载 -->
                </div>
            </div>
            

            
            <!-- 右侧二级分类区域 -->
            <div class="secondary-category-container" id="secondaryCategoryContainer">
                <!-- 二级分类将通过JS动态加载 -->
                <div class="empty-state" id="secondaryEmptyState">
                    <i class="fa-solid fa-folder-open empty-state-icon"></i>
                    <div class="empty-state-text">该分类暂无子分类</div>
                    <div class="empty-state-subtext">请选择其他分类查看商品</div>
                </div>
                <div class="loading-state" id="secondaryLoadingState" style="display:none;">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">加载中...</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-heart"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>
    
    
    <script src="/static/js/user/category.js"></script>
</body>
</html> 