<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下单确认 - 无名SUP</title>
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/css/user/order.css">
</head>
<body>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fa-solid fa-heart"></i>
            无名SUP
        </div>

        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link" id="profileLink">我的</a>
            </li>
        </ul>

        <div class="nav-icons">
            <!-- 搜索图标已移除 -->
        </div>

        <div class="mobile-menu-btn">
            <i class="fa-solid fa-bars"></i>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <div class="breadcrumb-item"><a href="/">首页</a></div>
            <div class="breadcrumb-item"><a href="#" id="productListLink">商品列表</a></div>
            <div class="breadcrumb-item"><span id="productNameBreadcrumb">下单确认</span></div>
        </div>
        

        
        <!-- 下单容器 -->
        <div class="order-container">
            <!-- 左侧：商品信息和参数 -->
            <div class="order-form">
                <!-- 商品信息区域 -->
                <div class="product-info-section">
                    <h3 class="section-title">
                        <i class="fa-solid fa-box"></i>
                        商品信息
                    </h3>
                    <div class="product-display">
                        <img src="https://via.placeholder.com/120x120/FFD0DB/333333?text=加载中" class="product-image" id="productImage" alt="商品图片">
                        <div class="product-details">
                            <h4 class="product-name" id="productName">
                                <i class="fa-solid fa-spinner fa-spin"></i>
                                正在加载商品信息...
                            </h4>
                            <div class="product-meta" id="productMeta">
                                <span>商品ID: 加载中...</span>
                                <span>销量: 加载中...</span>
                            </div>
                            <div class="product-price" id="productPrice">¥---.--</div>
                        </div>
                    </div>

                    <!-- 商品信息操作按钮 -->
                    <div class="product-actions">
                        <button class="action-btn secondary" id="backToProductBtn">
                            <i class="fa-solid fa-arrow-left"></i>
                            返回商品页
                        </button>
                    </div>

                </div>

                <!-- 参数输入区域 -->
                <div class="params-section" id="paramsSection" style="display: none;">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fa-solid fa-cogs"></i>
                            商品参数
                        </h3>
                        <button class="action-btn primary" id="batchOrderBtn">
                            <i class="fa-solid fa-layer-group"></i>
                            批量下单
                        </button>
                    </div>
                    <div id="paramsContainer">
                        <!-- 参数输入字段将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 优惠券区域 -->
                <div class="coupon-section">
                    <h3 class="section-title">
                        <i class="fa-solid fa-ticket"></i>
                        优惠券
                    </h3>
                    <div class="coupon-group">
                        <input type="text" class="coupon-input" id="couponInput" placeholder="输入优惠券代码">
                        <button class="coupon-btn" id="applyCouponBtn">
                            <i class="fa-solid fa-check"></i>
                            应用
                        </button>
                        <button class="coupon-btn" id="resetCouponBtn" style="display: none; background: #f44336;">
                            <i class="fa-solid fa-times"></i>
                            清除
                        </button>
                    </div>
                    <div id="couponMessage" style="margin-top: 10px; font-size: 0.9rem;"></div>
                </div>

                <!-- 邮箱区域 -->
                <div class="coupon-section">
                    <h3 class="section-title">
                        <i class="fa-solid fa-envelope"></i>
                        接收邮箱
                    </h3>
                    <div class="coupon-group">
                        <input type="email" class="coupon-input" id="userEmail" placeholder="请输入接收订单信息的邮箱地址" required>
                    </div>
                    <div style="font-size: 0.85rem; color: #666; margin-top: 8px;">
                        <i class="fa-solid fa-info-circle"></i>
                        订单信息将发送到此邮箱
                    </div>
                </div>

                <!-- 支付方式区域 -->
                <div class="payment-section">
                    <h3 class="section-title">
                        <i class="fa-solid fa-credit-card"></i>
                        支付方式
                    </h3>

                    <!-- 支付通道导航栏 -->
                    <div class="payment-nav" id="paymentNav" style="display: none;">
                        <!-- 导航项将通过JavaScript动态生成 -->
                    </div>

                    <!-- 支付方式内容区域 -->
                    <div class="payment-content">
                        <div class="payment-options" id="paymentOptions">
                            <!-- 加载状态 -->
                            <div class="payment-loading" id="paymentLoading">
                                <div style="text-align: center; padding: 40px; color: #999;">
                                    <i class="fa-solid fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 15px;"></i>
                                    <p>正在加载支付方式...</p>
                                </div>
                            </div>

                            <!-- 空状态 -->
                            <div class="payment-empty" id="paymentEmpty" style="display: none;">
                                <div style="text-align: center; padding: 40px; color: #999;">
                                    <i class="fa-solid fa-exclamation-circle" style="font-size: 24px; margin-bottom: 15px;"></i>
                                    <p>暂无可用的支付方式</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：订单摘要 -->
            <div class="order-summary">
                <h3 class="summary-title">
                    <i class="fa-solid fa-receipt"></i>
                    订单摘要
                </h3>

                <div class="order-item">
                    <div class="item-info">
                        <img src="https://via.placeholder.com/60x60/FFD0DB/333333?text=商品" alt="商品" class="item-img" id="summaryItemImg">
                        <div class="item-details">
                            <div class="item-name" id="summaryItemName">正在加载...</div>
                            <div class="item-quantity">数量: 1</div>
                        </div>
                    </div>
                    <div class="item-price" id="summaryItemPrice">¥---.--</div>
                </div>

                <div class="summary-rows">
                    <div class="summary-row">
                        <div class="summary-label">商品总额</div>
                        <div class="summary-value" id="subtotalAmount">¥---.--</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">优惠折扣</div>
                        <div class="summary-value" id="discountAmount">-¥0.00</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">服务费</div>
                        <div class="summary-value" id="serviceAmount">¥0.00</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">应付总额</div>
                        <div class="summary-value total-amount" id="totalAmount">¥---.--</div>
                    </div>
                </div>

                <button class="order-btn" id="submitOrderBtn">
                    <i class="fa-solid fa-shopping-cart"></i>
                    确认下单
                </button>

                <div class="security-info">
                    <div class="security-badge">
                        <i class="fa-solid fa-lock"></i>
                        安全支付
                    </div>
                    <div class="security-badge">
                        <i class="fa-solid fa-shield-alt"></i>
                        正品保障
                    </div>
                    <div class="security-badge">
                        <i class="fa-solid fa-headset"></i>
                        7x24客服
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 移动端固定底部操作栏 -->
    <div class="mobile-order-bar">
        <div class="mobile-order-info">
            <div class="mobile-total" id="mobileTotalAmount">¥---.--</div>
            <button class="mobile-order-btn" id="mobileSubmitOrderBtn">
                <i class="fa-solid fa-shopping-cart"></i>
                确认下单
            </button>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-heart"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>
    
    
    <script src="/static/js/user/order.js"></script>
</body>
</html>
