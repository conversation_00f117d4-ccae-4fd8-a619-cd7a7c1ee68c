<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情</title>
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 添加Bootstrap CSS -->
    <link href="https://g.alicdn.com/code/lib/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/css/user/product-detail.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fa-solid fa-heart"></i>
            无名SUP
        </div>

        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link" id="profileLink">我的</a>
            </li>
        </ul>

        <div class="nav-icons">
            <!-- 搜索图标已移除 -->
        </div>

        <div class="mobile-menu-btn">
            <i class="fa-solid fa-bars"></i>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <div class="breadcrumb-item"><a href="/">首页</a></div>
            <div class="breadcrumb-item"><span>商品列表</span></div>
            <div class="breadcrumb-item"></div>
        </div>
        
        <!-- 商品详情区 -->
        <div class="product-detail">
            <div class="product-gallery">
                <img src="" class="product-main-image" alt="">
            </div>
            
            <div class="product-info">
                <h1 class="product-title">
                    <i class="fa-solid fa-spinner fa-spin" style="margin-right: 10px;"></i>
                    正在加载商品信息...
                </h1>
                <div class="product-meta">
                    <span>商品ID: 加载中...</span>
                    <span>销量: 加载中...</span>
                </div>

                <div class="product-price-box">
                    <div class="product-price">¥---.--</div>
                </div>

                <div class="product-stock">
                    <i class="fa-solid fa-spinner fa-spin"></i>
                    正在获取库存信息...
                </div>

                <!-- 商品参数区域 -->
                <div class="product-params" id="product-params">
                    <!-- 参数将通过JavaScript动态生成 -->
                </div>

                <div class="buy-actions">
                    <button class="buy-btn buy-now-btn">
                        <i class="fa-solid fa-bolt"></i>
                        立即购买
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 商品详情标签 -->
        <div class="product-tabs">
            <div class="tabs-header">
                <div class="tab-item active" data-tab="description">商品详情</div>
            </div>

            <div class="tab-content active" id="description">
                <!-- 商品详情内容将通过JavaScript动态加载 -->
                <div style="text-align: center; padding: 40px; color: #999;">
                    <i class="fa-solid fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 15px;"></i>
                    <p>正在加载商品详情...</p>
                </div>
            </div>
        </div>
    </div>

    
    <!-- 移动端固定底部购买栏 -->
    <div class="mobile-buy-bar">
        <div class="mobile-buy-info">
            <div class="mobile-price">¥---.--</div>
            <button class="mobile-buy-btn">
                <i class="fa-solid fa-bolt"></i>
                立即购买
            </button>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-heart"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>
    
    
    <!-- 添加Bootstrap JavaScript -->
    <script src="https://g.alicdn.com/code/lib/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <!-- 将Django模板变量传递给JavaScript -->
    <script>
        // 安全地将Django的notice字段传递给JavaScript全局变量
        window.productNotice = '{{ notice|default:""|escapejs }}';
    </script>
    
    <script src="/static/js/user/product-detail.js"></script>
</body>
</html>