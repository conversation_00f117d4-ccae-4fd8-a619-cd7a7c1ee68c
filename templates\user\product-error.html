<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ error_title|default:"商品不存在" }} - 无名SUP</title>
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/css/user/product-error.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fa-solid fa-heart"></i>
            无名SUP
        </div>

        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link" id="profileLink">我的</a>
            </li>
        </ul>

        <div class="nav-icons">
            <!-- 搜索图标已移除 -->
        </div>

        <div class="mobile-menu-btn">
            <i class="fa-solid fa-bars"></i>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <div class="error-container">
            <div class="error-icon">
                <i class="fa-solid {{ error_icon|default:'fa-exclamation-triangle' }}"></i>
            </div>
            
            <h1 class="error-title">{{ error_title|default:"商品不存在" }}</h1>
            
            <p class="error-message">
                {{ error_message|default:"抱歉，您访问的商品不存在或已被删除。请检查商品链接是否正确，或浏览其他商品。" }}
            </p>
            
            <div class="error-actions">
                <a href="/" class="action-btn primary">
                    <i class="fa-solid fa-home"></i>
                    返回首页
                </a>

                <button onclick="history.back()" class="action-btn secondary">
                    <i class="fa-solid fa-arrow-left"></i>
                    返回上页
                </button>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-heart"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>
    
    
    <script src="/static/js/user/product-error.js"></script>
</body>
</html>
