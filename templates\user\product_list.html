<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无名SUP - 商品列表</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <link rel="stylesheet" href="/static/css/user/product_list.css">
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <span class="material-icons">shopping_cart</span>
            无名SUP
        </div>

        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link" id="profileLink">我的</a>
            </li>
        </ul>

        <div class="nav-icons">
        </div>

        <div class="mobile-menu-btn">
            <span class="material-icons">menu</span>
        </div>
    </nav>

    <!-- 遮罩层 -->
    <div class="filter-backdrop" id="filterBackdrop"></div>

    <!-- 高级筛选面板 -->
    <div class="advanced-filter-panel" id="advancedFilterPanel">
        <div class="filter-panel-header">
            <h3>商品分类</h3>
            <span class="material-icons close-filter">close</span>
        </div>
        <div class="filter-panel-body">
            <!-- 分类树结构将通过JS动态加载 -->
            <div class="category-tree" id="categoryTree">
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">加载分类中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 商品列表区域 -->
        <div class="product-list-container">
            <!-- 筛选栏 -->
            <div class="filter-bar">
                <div class="filter-title">
                    <span class="material-icons">sort</span>
                    排序
                </div>
                <div class="sort-options">
                    <div class="sort-option active" data-sort="default">综合</div>
                    <div class="sort-option" data-sort="sales">销量</div>
                    <div class="sort-option" data-sort="price-asc">价格 ↑</div>
                    <div class="sort-option" data-sort="price-desc">价格 ↓</div>
                </div>
                <div class="filter-options">
                    <span class="material-icons filter-toggle" id="filterToggle">filter_list</span>
                </div>
            </div>

            <!-- 商品列表 -->
            <div class="products-grid" id="productsGrid">
                <!-- 加载状态，初始显示 -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">加载商品中...</div>
                </div>

                <!-- 空状态，无商品时显示 -->
                <div class="empty-state" id="emptyState" style="display:none;">
                    <div class="empty-state-icon">🛒</div>
                    <div class="empty-state-text">暂无商品</div>
                </div>

                <!-- 商品将通过JS动态加载 -->
            </div>

            <!-- 分页控制 -->
            <div class="pagination" id="pagination" style="display:none;">
                <div class="page-nav" id="prevPage">
                    <span class="material-icons">chevron_left</span>
                </div>
                <div class="page-item active">1</div>
                <div class="page-item">2</div>
                <div class="page-item">3</div>
                <div class="page-nav" id="nextPage">
                    <span class="material-icons">chevron_right</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>


    <script src="/static/js/user/product_list.js"></script>
</body>

</html>