<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 无名SUP</title>
    <!-- 使用阿里云CDN的Font Awesome链接 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 添加备用CDN，确保图标能够加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- 保留中文字体 -->
    <link href="https://fonts.loli.net/css2?family=Montserrat:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="/static/css/user/profile.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <i class="fa-solid fa-heart"></i>
            无名SUP
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="/" class="nav-link">分类</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">查询</a>
            </li>
            <li class="nav-item">
                <a href="/profile/" class="nav-link active" id="profileLink">我的</a>
            </li>
        </ul>
        
        <div class="nav-icons">
        </div>
        
        <div class="mobile-menu-btn">
            <i class="fa-solid fa-bars"></i>
        </div>
    </nav>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-profile">
                <div class="avatar">
                    <img src="https://via.placeholder.com/200x200/FFD0DB/333333?text=U" alt="用户头像">
                </div>
                <div class="user-info">
                    <div class="user-name">{{ user_data.username|default:"用户" }}</div>
                    <div class="user-email">{{ user_data.email|default:"未设置邮箱" }}</div>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="#overview" class="sidebar-link active">
                        <i class="fa-solid fa-gauge-high"></i>
                        账户概览
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#orders" class="sidebar-link">
                        <i class="fa-solid fa-receipt"></i>
                        订单记录
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#settings" class="sidebar-link">
                        <i class="fa-solid fa-gear"></i>
                        个人设置
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#integration" class="sidebar-link">
                        <i class="fa-solid fa-link"></i>
                        对接中心
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="#" class="sidebar-link">
                        <i class="fa-solid fa-right-from-bracket"></i>
                        退出登录
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- 主要内容容器 -->
        <div class="content-container">
            <!-- 账户概览 -->
            <div class="section-card" id="overview">
                <h2 class="section-title">
                    <i class="fa-solid fa-gauge-high"></i>
                    账户概览
                </h2>
                
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-title">账户余额</div>
                        <div class="stat-value">¥258.50</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-title">累计消费</div>
                        <div class="stat-value">¥1,260.00</div>
                    </div>
                </div>
                
                <div class="balance-actions">
                    <button class="balance-btn recharge-btn">
                        <i class="fa-solid fa-circle-plus"></i>
                        充值
                    </button>
                </div>
            </div>
            
            <!-- 最近订单 -->
            <div class="section-card" id="orders">
                <h2 class="section-title">
                    <i class="fa-solid fa-receipt"></i>
                    最近订单
                </h2>
                
                <div class="order-table-wrapper">
                    <div class="order-table-container" id="orderTableContainer">
                        <table class="order-table">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>商品名称</th>
                                    <th>金额</th>
                                    <th>日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="orderTableBody">
                                <!-- 订单数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>

                        <!-- 分页控件 -->
                        <div class="order-pagination" id="orderPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="paginationInfo">显示第 1-5 条，共 0 条记录</span>
                            </div>
                            <div class="pagination-controls">
                                <button class="pagination-btn" id="prevPageBtn" onclick="changeOrderPage(-1)">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </button>
                                <span class="pagination-pages" id="paginationPages">
                                    <!-- 页码按钮将动态生成 -->
                                </span>
                                <button class="pagination-btn" id="nextPageBtn" onclick="changeOrderPage(1)">
                                    <i class="fa-solid fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 查看更多/收缩按钮 -->
                    <div class="order-actions">
                        <button class="view-more-btn" id="viewMoreBtn" onclick="toggleOrderView()">
                            <i class="fa-solid fa-chevron-down"></i>
                            查看更多
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 个人设置 -->
            <div class="section-card" id="settings">
                <h2 class="section-title">
                    <i class="fa-solid fa-gear"></i>
                    个人设置
                </h2>
                
                <form>
                    <div class="form-group">
                        <label class="form-label" for="username">用户名</label>
                        <input type="text" id="username" class="form-input" value="{{ user_data.username|default:'' }}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="email">邮箱</label>
                        <input type="email" id="email" class="form-input" value="{{ user_data.email|default:'' }}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="password">修改密码</label>
                        <input type="password" id="password" class="form-input" placeholder="输入新密码">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="confirm-password">确认修改密码</label>
                        <input type="password" id="confirm-password" class="form-input" placeholder="确认新密码">
                    </div>
                    
                    <button type="submit" class="form-submit">
                        <i class="fa-solid fa-floppy-disk"></i>
                        保存修改
                    </button>
                </form>
            </div>
            
            <!-- 对接中心 -->
            <div class="section-card" id="integration">
                <h2 class="section-title">
                    <i class="fa-solid fa-link"></i>
                    对接中心
                </h2>
                
                <form>
                    <div class="form-group">
                        <label class="form-label" for="integration-account">对接账户</label>
                        <div style="display: flex; align-items: center;">
                            <input type="text" id="integration-account" class="form-input" value="{{ user_data.id|default:'' }}" readonly>
                            <button type="button" class="copy-account-btn">
                                <i class="fa-solid fa-copy"></i>
                                复制账号
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="integration-key">对接密钥</label>
                        <div style="display: flex; align-items: center;">
                            <input type="password" id="integration-key" class="form-input" value="{{ user_data.user_key|default:'********' }}" readonly>
                            <button type="button" class="reset-key-btn">
                                <i class="fa-solid fa-arrows-rotate"></i>
                                重置密钥
                            </button>
                            <button type="button" class="copy-key-btn">
                                <i class="fa-solid fa-copy"></i>
                                复制密钥
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 充值模态框 -->
    <div id="rechargeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>账户充值</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label" for="recharge-amount">充值金额</label>
                    <input type="number" id="recharge-amount" class="form-input" placeholder="请输入充值金额" min="1" step="1">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="payment-method">支付方式</label>

                    <!-- 支付通道导航栏 -->
                    <div class="payment-nav" id="rechargePaymentNav" style="display: none;">
                        <!-- 导航项将通过JavaScript动态生成 -->
                    </div>

                    <!-- 支付方式内容区域 -->
                    <div class="payment-content">
                        <div class="payment-options" id="rechargePaymentOptions">
                            <!-- 支付宝选项 -->
                            <div class="payment-option payment-card" data-way="alipay">
                                <input type="radio" class="payment-input" id="alipay-scan" name="recharge_payment" value="alipay-scan" checked>
                                <label class="payment-label" for="alipay-scan">
                                    <div class="payment-icon" style="background: linear-gradient(45deg, #1677ff, #4990e2); color: white;">
                                        <i class="fa-brands fa-alipay"></i>
                                    </div>
                                    <div class="payment-details">
                                        <div class="payment-name">支付宝扫码</div>
                                        <div style="font-size: 0.85em; color: var(--text-light);">手续费: 0%</div>
                                    </div>
                                </label>
                            </div>

                            <div class="payment-option payment-card" data-way="alipay">
                                <input type="radio" class="payment-input" id="alipay-h5" name="recharge_payment" value="alipay-h5">
                                <label class="payment-label" for="alipay-h5">
                                    <div class="payment-icon" style="background: linear-gradient(45deg, #1677ff, #4990e2); color: white;">
                                        <i class="fa-brands fa-alipay"></i>
                                    </div>
                                    <div class="payment-details">
                                        <div class="payment-name">支付宝H5</div>
                                        <div style="font-size: 0.85em; color: var(--text-light);">手续费: 0.5%</div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="cancel-btn">取消</button>
                <button type="button" class="confirm-btn">确认支付</button>
            </div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="logo">
                <i class="fa-solid fa-heart"></i>
                无名SUP
            </div>
            <p class="copyright">© 2025 无名SUP 版权所有</p>
        </div>
    </footer>
    
    
    <script src="/static/js/user/profile.js"></script>
</body>
</html> 