<!DOCTYPE html>
{% load static %}
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录与注册 - 商城</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/twitter-bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link rel="stylesheet" href="/static/css/user/user_logon.css">
</head>
<body>
    <div class="login-container">
        <div class="form-container">
            <form id="login-form" novalidate>
                <h1 class="fade-in">欢迎回来</h1>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" class="form-control" id="login-email" placeholder=" " required>
                        <label for="login-email" class="form-label">邮箱地址</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" class="form-control" id="login-password" placeholder=" " required>
                        <label for="login-password" class="form-label">密码</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="captcha-container">
                        <div class="position-relative">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                                <input type="text" class="form-control" id="login-captcha" placeholder=" " required>
                                <label for="login-captcha" class="form-label">验证码</label>
                            </div>
                        </div>
                        <div class="captcha-image" id="login-captcha-image" title="点击刷新验证码">
                            <!-- 验证码图片将由JS生成 -->
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-between mb-4">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember-me">
                        <label class="form-check-label" for="remember-me">记住我</label>
                    </div>
                    <a href="#" class="text-decoration-none" style="color: var(--primary-color);" id="forgot-password">忘记密码?</a>
                </div>
                <button type="submit" class="btn btn-primary w-100 mb-4 pulse" id="login-button">
                    <i class="fas fa-sign-in-alt me-2"></i>登录
                </button>
                <p class="text-center">还没有账号? <a href="#" id="to-register" class="link-effect" style="color: var(--primary-color);">立即注册</a></p>
            </form>
            
            <!-- 忘记密码表单 -->
            <form id="forgot-form" novalidate>
                <h1 class="fade-in">找回密码</h1>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" class="form-control" id="forgot-email" placeholder=" " required>
                        <label for="forgot-email" class="form-label">邮箱地址</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-group">
                        <div class="input-icon">
                            <i class="fas fa-key"></i>
                            <input type="text" class="form-control" id="forgot-email-code" placeholder=" " required>
                            <label for="forgot-email-code" class="form-label">邮箱验证码</label>
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="forgot-send-code">发送验证码</button>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" class="form-control" id="forgot-password-input" placeholder=" " required>
                        <label for="forgot-password-input" class="form-label">新密码</label>
                    </div>
                    <div class="password-strength" id="forgot-password-strength"></div>
                    <div class="strength-text" id="forgot-strength-text"></div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-check-circle"></i>
                        <input type="password" class="form-control" id="forgot-confirm-password" placeholder=" " required>
                        <label for="forgot-confirm-password" class="form-label">确认新密码</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="captcha-container">
                        <div class="position-relative">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                                <input type="text" class="form-control" id="forgot-captcha" placeholder=" " required>
                                <label for="forgot-captcha" class="form-label">验证码</label>
                            </div>
                        </div>
                        <div class="captcha-image" id="forgot-captcha-image" title="点击刷新验证码">
                            <!-- 验证码图片将由JS生成 -->
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary w-100 mb-4 pulse">
                    <i class="fas fa-redo-alt me-2"></i>重置密码
                </button>
                <p class="text-center"><a href="#" id="back-to-login" class="link-effect" style="color: var(--primary-color);">返回登录</a></p>
            </form>
            
            <form id="register-form" novalidate>
                <h1 class="fade-in">创建账号</h1>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-user"></i>
                        <input type="text" class="form-control" id="register-username" placeholder=" " required>
                        <label for="register-username" class="form-label">用户名</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" class="form-control" id="register-email" placeholder=" " required>
                        <label for="register-email" class="form-label">邮箱地址</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <div class="input-group">
                        <div class="input-icon">
                            <i class="fas fa-key"></i>
                            <input type="text" class="form-control" id="register-email-code" placeholder=" " required>
                            <label for="register-email-code" class="form-label">邮箱验证码</label>
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="send-code">发送验证码</button>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <div class="input-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" class="form-control" id="register-password" placeholder=" " required>
                        <label for="register-password" class="form-label">密码</label>
                    </div>
                    <div class="password-strength" id="password-strength"></div>
                    <div class="strength-text" id="strength-text"></div>
                </div>
                <div class="form-group mb-4">
                    <div class="input-icon">
                        <i class="fas fa-check-circle"></i>
                        <input type="password" class="form-control" id="register-confirm-password" placeholder=" " required>
                        <label for="register-confirm-password" class="form-label">确认密码</label>
                    </div>
                </div>
                <div class="form-group mb-4">
                    <div class="captcha-container">
                        <div class="position-relative">
                            <div class="input-icon">
                                <i class="fas fa-shield-alt"></i>
                                <input type="text" class="form-control" id="register-captcha" placeholder=" " required>
                                <label for="register-captcha" class="form-label">验证码</label>
                            </div>
                        </div>
                        <div class="captcha-image" id="register-captcha-image" title="点击刷新验证码">
                            <!-- 验证码图片将由JS生成 -->
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary w-100 mb-4 pulse">
                    <i class="fas fa-user-plus me-2"></i>注册
                </button>
                <p class="text-center">已有账号? <a href="#" id="to-login" class="link-effect" style="color: var(--primary-color);">立即登录</a></p>
            </form>
        </div>
        <div class="image-container">
            <h2>Welcome to SUP</h2>
            <!-- <p>探索更多优质商品，享受便捷购物体验</p> -->
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification">
        <i class="notification-icon fas fa-check-circle"></i>
        <span id="notificationMessage"></span>
    </div>

    <script src="https://cdn.staticfile.org/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    
    <script src="/static/js/user/user_logon.js"></script>
</body>
</html>
