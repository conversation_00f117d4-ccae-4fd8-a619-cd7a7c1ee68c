from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


class SaltedEmailBackend(BaseBackend):
    """
    自定义认证后端，支持邮箱登录和加盐密码验证
    """
    
    def authenticate(self, request, email=None, password=None, **kwargs):
        """
        使用邮箱和密码进行认证
        支持加盐密码处理
        """
        if email is None or password is None:
            return None
        
        try:
            # 通过邮箱查找用户
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            # 运行默认密码哈希器以防止时序攻击
            User().set_password(password)
            return None
        
        # 使用自定义的加盐密码验证方法
        if user.check_password_with_salt(password) and self.user_can_authenticate(user):
            return user
        
        return None
    
    def user_can_authenticate(self, user):
        """
        检查用户是否可以进行认证
        """
        is_active = getattr(user, 'is_active', None)
        return is_active or is_active is None
    
    def get_user(self, user_id):
        """
        根据用户ID获取用户对象
        """
        try:
            user = User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
        
        return user if self.user_can_authenticate(user) else None
