from django.db import models, transaction
import uuid
import secrets
import hashlib
from django.utils import timezone
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
import json
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db.models import F


# 自定义用户管理器
class UserManager(BaseUserManager):
    def generate_salt(self):
        """生成随机盐值"""
        return secrets.token_hex(32)  # 生成64字符的十六进制盐值

    def hash_password_with_salt(self, password, salt):
        """使用盐值对密码进行哈希处理"""
        # 将密码和盐值结合进行SHA256哈希
        combined = f"{password}{salt}"
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()

    def create_user(self, email, username, password=None, **extra_fields):
        if not email:
            raise ValueError('用户必须有邮箱地址')
        email = self.normalize_email(email)

        # 生成盐值
        salt = self.generate_salt()

        # 创建用户实例
        user = self.model(email=email, username=username, password_salt=salt, **extra_fields)

        # 使用Django内置的密码哈希 + 自定义盐值双重处理
        if password:
            # 先用自定义盐值处理密码
            salted_password = self.hash_password_with_salt(password, salt)
            # 再使用Django的set_password进行标准哈希处理
            user.set_password(salted_password)

        user.save(using=self._db)
        return user

    def create_superuser(self, email, username, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        return self.create_user(email, username, password, **extra_fields)


# 自定义用户模型
class User(AbstractBaseUser, PermissionsMixin):
    username = models.CharField(max_length=50, unique=True, verbose_name='用户名')
    email = models.EmailField(max_length=100, unique=True, verbose_name='邮箱')
    user_key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='用户密钥')
    password_salt = models.CharField(max_length=64, verbose_name='密码盐值', help_text='用于增强密码安全性的随机盐值')
    is_active = models.BooleanField(default=True, verbose_name='激活状态')
    is_staff = models.BooleanField(default=False, verbose_name='管理员状态')
    date_joined = models.DateTimeField(default=timezone.now, verbose_name='注册时间')
    # 添加用户会员等级 用户余额 用户累计消费 用户唯一ID
    membership_level = models.CharField(max_length=20, default='普通用户', verbose_name='会员等级')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name='用户余额')
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='累计消费')
    user_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='用户唯一ID')

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-date_joined']

    def __str__(self):
        return self.username

    def check_password_with_salt(self, raw_password):
        """验证密码（包含盐值处理）"""
        if not self.password_salt:
            # 如果没有盐值，使用Django默认验证
            return self.check_password(raw_password)

        # 使用相同的盐值处理输入密码
        salted_password = UserManager().hash_password_with_salt(raw_password, self.password_salt)
        # 使用Django的check_password验证处理后的密码
        return self.check_password(salted_password)


# 邮箱验证码模型
class EmailVerificationCode(models.Model):
    email = models.EmailField(verbose_name='邮箱')
    code = models.CharField(max_length=6, verbose_name='验证码')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_used = models.BooleanField(default=False, verbose_name='是否已使用')

    class Meta:
        verbose_name = '邮箱验证码'
        verbose_name_plural = '邮箱验证码'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.email}: {self.code}"

    @property
    def is_expired(self):
        return timezone.now() > self.expires_at

@receiver(post_save, sender=User)
def update_user_registration_stats(sender, instance, created, **kwargs):

    if created:
        try:
            # 延迟导入避免循环导入
            from api.models import dataStatistics
            
            # 获取或创建今日统计记录
            today_stats, stats_created = dataStatistics.get_or_create_today_stats()
            
            with transaction.atomic():
                dataStatistics.objects.filter(date=today_stats.date).update(
                    daily_new_users=F('daily_new_users') + 1,
                    total_users=F('total_users') + 1
                )
            
        except Exception as e:
            return

