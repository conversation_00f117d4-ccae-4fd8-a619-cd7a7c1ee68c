from django.urls import path
from . import views
# django
app_name = 'user'

urlpatterns = [
    # 页面路由
    path('', views.category_page, name='category'),  # 根路由指向category页面
    path('login/', views.user_login_page, name='login_page'),
    path('profile/', views.profile_page, name='profile'),
    path('productlist/', views.product_list_page, name='product_list'),  # 商品列表页面
    path('product-detail/', views.product_detail_page, name='product_detail'),  # 商品详情页面
    path('confirmOrder/', views.order_page, name='order'),  # 订单结算页面
    path('order-failed/', views.order_failed_page, name='order_failed'),  # 订单失败页面
    path('payment/qrcode/', views.payment_qrcode_page, name='payment_qrcode'),  # 支付二维码页面
    path('payment/success/', views.payment_success_page, name='payment_success'),  # 支付成功页面
    path('order-detail/<str:order_id>/', views.order_detail_page, name='order_detail'),  # 订单详情页面

    # API路由
    path('user/api/login/', views.user_login, name='api_login'),
    path('user/api/register/', views.user_register, name='api_register'),
    path('user/api/send_email_code/', views.send_email_code, name='api_send_email_code'),
    path('user/api/verify_token/', views.verify_token_api, name='api_verify_token'),
    path('user/api/user_info/', views.get_user_info, name='api_user_info'),
    path('user/api/reset_password/', views.reset_password, name='api_reset_password'),
    path('user/api/GetUser/', views.get_user, name='get_user'),
    path('user/api/refresh_token/', views.refresh_token_api, name='api_refresh_token'),
    path('user/api/categories/', views.get_categories, name='api_categories'),
    path('user/api/productlist/', views.get_product_list, name='api_product_list'),
    path('user/api/productInfo/', views.get_product_info, name='api_product_info'),
    path('user/api/payment_methods/', views.get_payment_methods, name='api_payment_methods'),
    path('user/api/couponsDetail/', views.get_coupon_detail, name='api_coupon_detail'),
    path('user/api/submit_order/', views.submit_order_api, name='api_submit_order'),  # 订单提交API -> 跳转支付页
    path('user/api/recharge_balance/', views.recharge_balance_api, name='api_recharge_balance'),  # 充值余额API
    path('user/api/check_payment_status/', views.check_payment_status_api, name='api_check_payment_status'),  # 支付状态检查API
    path('user/api/get_order_history/', views.get_order_history_api, name='api_get_order_history'),  # 订单历史查询API
]

