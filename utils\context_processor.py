"""
统一Context处理器
负责处理HTML和Vue模板的统一数据传递
"""
import json
import logging
from datetime import datetime, date
from decimal import Decimal
from django.conf import settings
from django.middleware.csrf import get_token
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone

logger = logging.getLogger(__name__)


class UnifiedContextProcessor:
    """统一上下文处理器"""
    
    def process_context(self, request, context, template_type):
        """
        处理模板上下文数据
        
        Args:
            request: Django请求对象
            context: 原始上下文数据
            template_type: 模板类型 ('html' 或 'vue')
            
        Returns:
            dict: 处理后的上下文数据
        """
        try:
            # 构建基础上下文
            base_context = self._build_base_context(request)
            
            # 合并用户提供的上下文
            merged_context = {**base_context, **(context or {})}
            
            if template_type == 'vue':
                # Vue模板需要序列化处理
                return self._process_vue_context(merged_context)
            else:
                # HTML模板使用原生Django上下文
                return merged_context
                
        except Exception as e:
            logger.error(f"上下文处理失败: {str(e)}")
            return context or {}
    
    def _build_base_context(self, request):
        """构建基础上下文数据"""
        try:
            base_context = {
                # 请求相关
                'request': request,
                'user': request.user if hasattr(request, 'user') else AnonymousUser(),
                'csrf_token': get_token(request),
                
                # 系统相关
                'debug': settings.DEBUG,
                'now': timezone.now(),
                'today': timezone.now().date(),
                
                # 应用相关
                'site_name': getattr(settings, 'SITE_NAME', 'Django Vue应用'),
                'version': getattr(settings, 'VERSION', '1.0.0'),
                
                # Vue相关配置
                'vue_settings': getattr(settings, 'VUE_SETTINGS', {}),
            }
            
            # 添加用户相关信息
            if hasattr(request, 'user') and request.user.is_authenticated:
                base_context.update({
                    'user_id': str(request.user.id) if hasattr(request.user, 'id') else None,
                    'username': getattr(request.user, 'username', ''),
                    'is_authenticated': True,
                    'is_staff': getattr(request.user, 'is_staff', False),
                    'is_superuser': getattr(request.user, 'is_superuser', False),
                })
            else:
                base_context.update({
                    'user_id': None,
                    'username': '',
                    'is_authenticated': False,
                    'is_staff': False,
                    'is_superuser': False,
                })
            
            return base_context
            
        except Exception as e:
            logger.error(f"构建基础上下文失败: {str(e)}")
            return {}
    
    def _process_vue_context(self, context):
        """
        处理Vue模板的上下文数据
        将Django对象序列化为JSON可序列化的格式
        
        Args:
            context: 原始上下文数据
            
        Returns:
            dict: 序列化后的上下文数据
        """
        try:
            serialized_context = {}
            
            for key, value in context.items():
                serialized_context[key] = self._serialize_value(value)
            
            return serialized_context
            
        except Exception as e:
            logger.error(f"Vue上下文序列化失败: {str(e)}")
            return {}
    
    def _serialize_value(self, value):
        """
        序列化单个值为JSON可序列化的格式
        
        Args:
            value: 要序列化的值
            
        Returns:
            序列化后的值
        """
        try:
            # 基本类型直接返回
            if value is None or isinstance(value, (bool, int, float, str)):
                return value
            
            # 日期时间类型
            elif isinstance(value, datetime):
                return value.isoformat()
            elif isinstance(value, date):
                return value.isoformat()
            
            # Decimal类型
            elif isinstance(value, Decimal):
                return float(value)
            
            # Django用户对象
            elif hasattr(value, '_meta') and hasattr(value._meta, 'model_name'):
                return self._serialize_model_instance(value)
            
            # 列表类型
            elif isinstance(value, (list, tuple)):
                return [self._serialize_value(item) for item in value]
            
            # 字典类型
            elif isinstance(value, dict):
                return {k: self._serialize_value(v) for k, v in value.items()}
            
            # QuerySet类型
            elif hasattr(value, 'model') and hasattr(value, 'all'):
                return [self._serialize_model_instance(item) for item in value.all()]
            
            # 其他对象尝试转换为字符串
            else:
                return str(value)
                
        except Exception as e:
            logger.warning(f"值序列化失败: {type(value)} - {str(e)}")
            return str(value) if value is not None else None
    
    def _serialize_model_instance(self, instance):
        """
        序列化Django模型实例
        
        Args:
            instance: Django模型实例
            
        Returns:
            dict: 序列化后的字典
        """
        try:
            serialized = {}
            
            # 获取模型字段
            for field in instance._meta.fields:
                field_name = field.name
                field_value = getattr(instance, field_name, None)
                
                # 跳过敏感字段
                if field_name in ['password', 'secret_key', 'private_key']:
                    continue
                
                serialized[field_name] = self._serialize_value(field_value)
            
            # 添加模型元信息
            serialized['_model'] = instance._meta.model_name
            serialized['_app'] = instance._meta.app_label
            
            return serialized
            
        except Exception as e:
            logger.warning(f"模型实例序列化失败: {type(instance)} - {str(e)}")
            return {'_error': f'序列化失败: {str(e)}'}
    
    def inject_vue_context(self, html_content, context):
        """
        将上下文数据注入到HTML中供Vue使用
        
        Args:
            html_content: HTML内容
            context: 上下文数据
            
        Returns:
            str: 注入数据后的HTML内容
        """
        try:
            # 序列化上下文数据
            context_json = json.dumps(context, ensure_ascii=False, default=str)
            
            # 创建数据注入脚本
            inject_script = f"""
            <script>
                // Django数据注入
                window.__DJANGO_CONTEXT__ = {context_json};
                
                // 工具函数
                window.getDjangoData = function(key, defaultValue) {{
                    return window.__DJANGO_CONTEXT__[key] || defaultValue;
                }};
                
                window.getCSRFToken = function() {{
                    return window.__DJANGO_CONTEXT__.csrf_token || '';
                }};
            </script>
            """
            
            # 在</head>标签前插入脚本
            if '</head>' in html_content:
                html_content = html_content.replace('</head>', f'{inject_script}</head>')
            else:
                # 如果没有</head>标签，在<body>标签后插入
                if '<body>' in html_content:
                    html_content = html_content.replace('<body>', f'<body>{inject_script}')
                else:
                    # 如果都没有，直接在开头插入
                    html_content = inject_script + html_content
            
            return html_content
            
        except Exception as e:
            logger.error(f"Vue上下文注入失败: {str(e)}")
            return html_content
    
    def get_api_context(self, request, context):
        """
        获取API格式的上下文数据（用于AJAX请求）
        
        Args:
            request: Django请求对象
            context: 上下文数据
            
        Returns:
            dict: API格式的上下文数据
        """
        try:
            processed_context = self.process_context(request, context, 'vue')
            
            # 移除不适合API返回的字段
            api_context = {k: v for k, v in processed_context.items() 
                          if k not in ['request', 'csrf_token']}
            
            # 添加API特定字段
            api_context.update({
                'timestamp': timezone.now().isoformat(),
                'success': True,
                'message': 'success'
            })
            
            return api_context
            
        except Exception as e:
            logger.error(f"API上下文生成失败: {str(e)}")
            return {
                'success': False,
                'message': f'上下文处理失败: {str(e)}',
                'timestamp': timezone.now().isoformat()
            }


# 全局上下文处理器实例
context_processor = UnifiedContextProcessor()