"""
共享服务层模块

这个模块提供了统一的服务层架构，用于处理管理端和用户端的共同业务逻辑，
减少代码重复，提高可维护性。

主要组件：
- base: 基础服务类和通用组件
- category: 分类相关服务
- product: 商品相关服务  
- order: 订单相关服务
- user: 用户相关服务
- payment: 支付相关服务
- common: 通用服务（认证、验证、格式化等）
"""

__version__ = '1.0.0'
__author__ = 'API重构团队'

# 导出主要服务类（将在后续任务中实现）
# from .category.category_service import CategoryService
# from .product.product_service import ProductService
# from .order.order_service import OrderService
# from .user.user_service import UserService
# from .payment.payment_service import PaymentService