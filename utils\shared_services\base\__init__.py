"""
基础服务模块

提供所有服务类的基础功能，包括：
- BaseService: 所有服务类的基类
- 自定义异常类
- 通用装饰器
"""

from .base_service import BaseService
from .exceptions import (
    ServiceException, 
    PermissionDeniedException, 
    ValidationException,
    ResourceNotFoundException,
    BusinessLogicException,
    ExternalServiceException,
    DatabaseException,
    ConfigurationException,
    RateLimitException,
    CacheException
)
from .decorators import (
    require_permission,
    log_service_call,
    monitor_performance,
    cache_result,
    validate_params,
    handle_exceptions,
    rate_limit,
    retry_on_failure
)