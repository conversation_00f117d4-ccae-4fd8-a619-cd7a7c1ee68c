"""
基础服务类模块

提供所有服务类的基类BaseService，包含通用功能：
- 服务初始化和上下文管理
- 权限检查
- 响应格式化
- 统一错误处理
- 日志记录
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime


class BaseService:
    """
    所有服务类的基类，提供通用功能
    
    主要功能：
    1. 服务上下文管理（用户信息、权限、请求类型等）
    2. 权限检查机制
    3. 响应数据格式化
    4. 统一错误处理
    5. 日志记录
    """
    
    def __init__(self, context: Optional[Dict[str, Any]] = None):
        """
        初始化服务
        
        Args:
            context: 请求上下文，包含用户信息、权限等
                - user: 用户对象
                - permissions: 权限列表
                - request_type: 请求类型 ('admin' or 'user')
                - request: Django请求对象（可选）
                - user_id: 用户ID（可选）
                - session_data: 会话数据（可选）
        """
        self.context = context or {}
        self.user = self.context.get('user')
        self.permissions = self.context.get('permissions', [])
        self.request_type = self.context.get('request_type', 'user')  # 'admin' or 'user'
        self.request = self.context.get('request')
        self.user_id = self.context.get('user_id')
        self.session_data = self.context.get('session_data', {})
        
        # 初始化日志记录器
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        
        # 记录服务初始化
        self.logger.debug(f"服务初始化: {self.__class__.__name__}, 请求类型: {self.request_type}")
    
    def check_permission(self, required_permission: str) -> bool:
        """
        检查权限
        
        Args:
            required_permission: 需要的权限标识
            
        Returns:
            bool: 是否有权限
            
        Raises:
            PermissionDeniedException: 权限不足时抛出
        """
        try:
            # 管理端权限检查
            if self.request_type == 'admin':
                # 管理端通常通过装饰器已经验证过权限，这里做二次确认
                if hasattr(self.user, 'is_staff') and self.user.is_staff:
                    self.logger.debug(f"管理端权限检查通过: {required_permission}")
                    return True
                elif required_permission in self.permissions:
                    self.logger.debug(f"管理端权限检查通过: {required_permission}")
                    return True
                else:
                    self.logger.warning(f"管理端权限检查失败: {required_permission}")
                    from .exceptions import PermissionDeniedException
                    raise PermissionDeniedException(f"缺少权限: {required_permission}")
            
            # 用户端权限检查
            else:
                # 检查是否有所需权限
                if required_permission in self.permissions:
                    self.logger.debug(f"用户端权限检查通过: {required_permission}")
                    return True
                # 用户端基本权限检查（已登录用户）
                elif self.user and hasattr(self.user, 'is_authenticated') and self.user.is_authenticated:
                    self.logger.debug(f"用户端权限检查通过: {required_permission}")
                    return True
                elif self.user_id:  # 通过用户ID验证
                    self.logger.debug(f"用户端权限检查通过: {required_permission}")
                    return True
                else:
                    self.logger.warning(f"用户端权限检查失败: {required_permission}")
                    from .exceptions import PermissionDeniedException
                    raise PermissionDeniedException(f"用户未登录或权限不足: {required_permission}")
                    
        except Exception as e:
            self.logger.error(f"权限检查异常: {str(e)}")
            raise
    
    def format_response(self, data: Any, format_type: str = None) -> Dict[str, Any]:
        """
        格式化响应数据
        
        Args:
            data: 要格式化的数据
            format_type: 格式化类型 ('admin', 'user', 或 None 使用默认)
            
        Returns:
            Dict: 格式化后的响应数据
        """
        if format_type is None:
            format_type = self.request_type
            
        try:
            # 管理端格式化
            if format_type == 'admin':
                formatted_data = {
                    'code': 200,
                    'msg': '操作成功',
                    'data': data,
                    'timestamp': datetime.now().isoformat(),
                    'request_type': 'admin'
                }
            
            # 用户端格式化
            else:
                formatted_data = {
                    'success': True,
                    'message': '操作成功',
                    'data': data,
                    'timestamp': datetime.now().isoformat(),
                    'request_type': 'user'
                }
            
            self.logger.debug(f"响应数据格式化完成: {format_type}")
            return formatted_data
            
        except Exception as e:
            self.logger.error(f"响应格式化异常: {str(e)}")
            return self._format_error_response(str(e), format_type)
    
    def format_error_response(self, error_message: str, error_code: int = -1, 
                            format_type: str = None) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_message: 错误消息
            error_code: 错误代码
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的错误响应
        """
        if format_type is None:
            format_type = self.request_type
            
        try:
            # 管理端错误格式
            if format_type == 'admin':
                error_response = {
                    'code': error_code,
                    'msg': f'操作失败: {error_message}',
                    'data': None,
                    'timestamp': datetime.now().isoformat(),
                    'request_type': 'admin'
                }
            
            # 用户端错误格式
            else:
                error_response = {
                    'success': False,
                    'message': f'操作失败: {error_message}',
                    'data': None,
                    'timestamp': datetime.now().isoformat(),
                    'request_type': 'user'
                }
            
            self.logger.error(f"错误响应格式化: {error_message}")
            return error_response
            
        except Exception as e:
            self.logger.critical(f"错误响应格式化异常: {str(e)}")
            # 返回最基本的错误格式
            return {
                'error': True,
                'message': f'系统错误: {error_message}',
                'timestamp': datetime.now().isoformat()
            }
    
    def _format_error_response(self, error_message: str, format_type: str = None) -> Dict[str, Any]:
        """
        内部错误响应格式化方法
        """
        return self.format_error_response(error_message, format_type=format_type)
    
    def handle_error(self, error: Exception) -> Dict[str, Any]:
        """
        统一错误处理
        
        Args:
            error: 异常对象
            
        Returns:
            Dict: 格式化后的错误响应
        """
        try:
            # 导入自定义异常类
            from .exceptions import (
                ServiceException, 
                PermissionDeniedException, 
                ValidationException,
                ResourceNotFoundException
            )
            
            # 根据异常类型进行不同处理
            if isinstance(error, PermissionDeniedException):
                self.logger.warning(f"权限异常: {str(error)}")
                return self.format_error_response(str(error), error_code=403)
                
            elif isinstance(error, ValidationException):
                self.logger.warning(f"验证异常: {str(error)}")
                return self.format_error_response(str(error), error_code=400)
                
            elif isinstance(error, ResourceNotFoundException):
                self.logger.warning(f"资源未找到异常: {str(error)}")
                return self.format_error_response(str(error), error_code=404)
                
            elif isinstance(error, ServiceException):
                self.logger.error(f"服务异常: {str(error)}")
                return self.format_error_response(str(error), error_code=500)
                
            else:
                # 未知异常
                self.logger.error(f"未知异常: {str(error)}", exc_info=True)
                return self.format_error_response("系统内部错误", error_code=500)
                
        except Exception as e:
            self.logger.critical(f"错误处理异常: {str(e)}", exc_info=True)
            return {
                'error': True,
                'message': '系统严重错误',
                'timestamp': datetime.now().isoformat()
            }
    
    def log_service_call(self, method_name: str, params: Dict[str, Any] = None):
        """
        记录服务调用日志
        
        Args:
            method_name: 方法名称
            params: 调用参数
        """
        try:
            log_data = {
                'service': self.__class__.__name__,
                'method': method_name,
                'user_id': self.user_id,
                'request_type': self.request_type,
                'timestamp': datetime.now().isoformat()
            }
            
            if params:
                # 过滤敏感信息
                safe_params = self._filter_sensitive_data(params)
                log_data['params'] = safe_params
            
            self.logger.info(f"服务调用: {log_data}")
            
        except Exception as e:
            self.logger.error(f"日志记录异常: {str(e)}")
    
    def _filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤敏感数据
        
        Args:
            data: 原始数据
            
        Returns:
            Dict: 过滤后的数据
        """
        sensitive_keys = ['password', 'token', 'secret', 'key', 'auth']
        filtered_data = {}
        
        for key, value in data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                filtered_data[key] = '***'
            else:
                filtered_data[key] = value
                
        return filtered_data
    
    def get_user_context(self) -> Dict[str, Any]:
        """
        获取用户上下文信息
        
        Returns:
            Dict: 用户上下文信息
        """
        return {
            'user_id': self.user_id,
            'request_type': self.request_type,
            'permissions': self.permissions,
            'session_data': self.session_data,
            'has_user': self.user is not None
        }
    
    def is_admin_request(self) -> bool:
        """
        判断是否为管理端请求
        
        Returns:
            bool: 是否为管理端请求
        """
        return self.request_type == 'admin'
    
    def is_user_request(self) -> bool:
        """
        判断是否为用户端请求
        
        Returns:
            bool: 是否为用户端请求
        """
        return self.request_type == 'user'