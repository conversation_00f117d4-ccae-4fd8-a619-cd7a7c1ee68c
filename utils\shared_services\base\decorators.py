"""
服务装饰器模块

提供各种横切关注点的装饰器，包括：
- 权限检查装饰器
- 日志记录装饰器
- 性能监控装饰器
- 异常处理装饰器
- 缓存装饰器
- 参数验证装饰器
- 频率限制装饰器
"""

import time
import logging
import functools
from typing import Any, Callable, Dict, List, Optional, Union
from datetime import datetime, timedelta
from django.core.cache import cache

from .exceptions import (
    ServiceException,
    PermissionDeniedException,
    ValidationException,
    RateLimitException,
    CacheException
)


# 全局日志记录器
logger = logging.getLogger(__name__)


def require_permission(permission: str, resource: Optional[str] = None):
    """
    权限检查装饰器
    
    检查服务方法调用者是否具有指定权限
    
    Args:
        permission: 需要的权限标识
        resource: 资源标识（可选）
        
    Usage:
        @require_permission('admin', 'category')
        def delete_category(self, category_id):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                # 检查服务实例是否有权限检查方法
                if not hasattr(self, 'check_permission'):
                    raise ServiceException("服务类必须继承BaseService才能使用权限装饰器")
                
                # 执行权限检查
                self.check_permission(permission)
                
                # 记录权限检查成功
                logger.debug(f"权限检查通过: {permission}, 方法: {func.__name__}")
                
                # 执行原方法
                return func(self, *args, **kwargs)
                
            except PermissionDeniedException:
                # 权限异常直接重新抛出
                raise
            except Exception as e:
                # 其他异常包装后抛出
                raise ServiceException(f"权限检查异常: {str(e)}")
        
        return wrapper
    return decorator


def log_service_call(include_params: bool = True, include_result: bool = False,
                    sensitive_params: Optional[List[str]] = None):
    """
    日志记录装饰器
    
    记录服务方法的调用信息
    
    Args:
        include_params: 是否记录参数
        include_result: 是否记录返回结果
        sensitive_params: 敏感参数列表，这些参数将被过滤
        
    Usage:
        @log_service_call(include_params=True, sensitive_params=['password'])
        def login_user(self, username, password):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = time.time()
            method_name = func.__name__
            service_name = self.__class__.__name__
            
            # 准备日志数据
            log_data = {
                'service': service_name,
                'method': method_name,
                'timestamp': datetime.now().isoformat(),
                'user_id': getattr(self, 'user_id', None),
                'request_type': getattr(self, 'request_type', 'unknown')
            }
            
            # 记录参数
            if include_params and (args or kwargs):
                filtered_params = _filter_sensitive_params(
                    {'args': args, 'kwargs': kwargs},
                    sensitive_params or []
                )
                log_data['params'] = filtered_params
            
            try:
                # 记录方法调用开始
                logger.info(f"服务调用开始: {log_data}")
                
                # 执行原方法
                result = func(self, *args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                log_data['execution_time'] = f"{execution_time:.3f}s"
                log_data['status'] = 'success'
                
                # 记录返回结果
                if include_result and result is not None:
                    # 限制结果大小，避免日志过大
                    result_str = str(result)
                    if len(result_str) > 1000:
                        log_data['result'] = result_str[:1000] + '...[truncated]'
                    else:
                        log_data['result'] = result
                
                # 记录方法调用成功
                logger.info(f"服务调用成功: {log_data}")
                
                return result
                
            except Exception as e:
                # 计算执行时间
                execution_time = time.time() - start_time
                log_data['execution_time'] = f"{execution_time:.3f}s"
                log_data['status'] = 'error'
                log_data['error'] = str(e)
                
                # 记录方法调用失败
                logger.error(f"服务调用失败: {log_data}")
                
                # 重新抛出异常
                raise
        
        return wrapper
    return decorator


def monitor_performance(threshold_seconds: float = 1.0, alert_on_slow: bool = True):
    """
    性能监控装饰器
    
    监控服务方法的执行时间，超过阈值时发出警告
    
    Args:
        threshold_seconds: 性能阈值（秒）
        alert_on_slow: 是否在慢查询时发出警告
        
    Usage:
        @monitor_performance(threshold_seconds=0.5)
        def get_product_list(self):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            start_time = time.time()
            method_name = func.__name__
            service_name = self.__class__.__name__
            
            try:
                # 执行原方法
                result = func(self, *args, **kwargs)
                
                # 计算执行时间
                execution_time = time.time() - start_time
                
                # 检查是否超过阈值
                if execution_time > threshold_seconds:
                    warning_msg = (
                        f"性能警告: {service_name}.{method_name} "
                        f"执行时间 {execution_time:.3f}s 超过阈值 {threshold_seconds}s"
                    )
                    
                    if alert_on_slow:
                        logger.warning(warning_msg)
                    
                    # 可以在这里添加性能监控系统的集成
                    # 例如发送到监控服务、记录到性能数据库等
                
                # 记录性能数据
                logger.debug(f"性能监控: {service_name}.{method_name} 执行时间: {execution_time:.3f}s")
                
                return result
                
            except Exception as e:
                # 即使出现异常也要记录执行时间
                execution_time = time.time() - start_time
                logger.debug(f"性能监控(异常): {service_name}.{method_name} 执行时间: {execution_time:.3f}s")
                raise
        
        return wrapper
    return decorator


def handle_exceptions(default_return: Any = None, reraise: bool = True,
                     exception_mapping: Optional[Dict[type, type]] = None):
    """
    异常处理装饰器
    
    统一处理服务方法的异常
    
    Args:
        default_return: 异常时的默认返回值
        reraise: 是否重新抛出异常
        exception_mapping: 异常映射字典，将原异常转换为目标异常
        
    Usage:
        @handle_exceptions(default_return=[], exception_mapping={ValueError: ValidationException})
        def get_categories(self):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
                
            except ServiceException:
                # 服务异常直接重新抛出
                if reraise:
                    raise
                return default_return
                
            except Exception as e:
                method_name = func.__name__
                service_name = self.__class__.__name__
                
                # 记录异常
                logger.error(f"服务方法异常: {service_name}.{method_name}, 错误: {str(e)}", exc_info=True)
                
                # 异常映射
                if exception_mapping:
                    for source_exception, target_exception in exception_mapping.items():
                        if isinstance(e, source_exception):
                            mapped_exception = target_exception(f"方法 {method_name} 执行失败: {str(e)}")
                            if reraise:
                                raise mapped_exception
                            return default_return
                
                # 包装为服务异常
                service_exception = ServiceException(f"服务方法 {method_name} 执行异常: {str(e)}")
                
                if reraise:
                    raise service_exception
                
                return default_return
        
        return wrapper
    return decorator


def cache_result(cache_key_template: str, timeout: int = 300, 
                cache_condition: Optional[Callable] = None):
    """
    缓存装饰器
    
    缓存服务方法的返回结果
    
    Args:
        cache_key_template: 缓存键模板，支持参数替换
        timeout: 缓存超时时间（秒）
        cache_condition: 缓存条件函数，返回True时才缓存
        
    Usage:
        @cache_result('category_list_{request_type}', timeout=600)
        def get_category_list(self):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                # 生成缓存键
                cache_key = _generate_cache_key(
                    cache_key_template, 
                    self, 
                    args, 
                    kwargs
                )
                
                # 尝试从缓存获取
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"缓存命中: {cache_key}")
                    return cached_result
                
                # 执行原方法
                result = func(self, *args, **kwargs)
                
                # 检查缓存条件
                should_cache = True
                if cache_condition:
                    should_cache = cache_condition(result)
                
                # 缓存结果
                if should_cache and result is not None:
                    cache.set(cache_key, result, timeout)
                    logger.debug(f"结果已缓存: {cache_key}, 超时: {timeout}s")
                
                return result
                
            except Exception as e:
                logger.error(f"缓存装饰器异常: {str(e)}")
                # 缓存异常不应影响业务逻辑，直接执行原方法
                return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def validate_params(**validators):
    """
    参数验证装饰器
    
    验证服务方法的参数
    
    Args:
        **validators: 参数验证器字典，键为参数名，值为验证函数
        
    Usage:
        @validate_params(
            category_id=lambda x: x and len(x) > 0,
            name=lambda x: x and len(x.strip()) > 0
        )
        def update_category(self, category_id, name):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 获取函数参数名
            import inspect
            sig = inspect.signature(func)
            param_names = list(sig.parameters.keys())[1:]  # 排除self参数
            
            # 构建参数字典
            params = {}
            for i, value in enumerate(args):
                if i < len(param_names):
                    params[param_names[i]] = value
            params.update(kwargs)
            
            # 执行参数验证
            validation_errors = {}
            for param_name, validator in validators.items():
                if param_name in params:
                    param_value = params[param_name]
                    try:
                        if not validator(param_value):
                            validation_errors[param_name] = f"参数 {param_name} 验证失败"
                    except Exception as e:
                        validation_errors[param_name] = f"参数 {param_name} 验证异常: {str(e)}"
            
            # 如果有验证错误，抛出异常
            if validation_errors:
                raise ValidationException(
                    "参数验证失败",
                    validation_errors=validation_errors
                )
            
            # 执行原方法
            return func(self, *args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit(max_calls: int, window_seconds: int, key_func: Optional[Callable] = None):
    """
    频率限制装饰器
    
    限制服务方法的调用频率
    
    Args:
        max_calls: 时间窗口内最大调用次数
        window_seconds: 时间窗口大小（秒）
        key_func: 生成限制键的函数，默认使用用户ID
        
    Usage:
        @rate_limit(max_calls=10, window_seconds=60)
        def send_verification_code(self):
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 生成限制键
            if key_func:
                limit_key = key_func(self, *args, **kwargs)
            else:
                user_id = getattr(self, 'user_id', 'anonymous')
                method_name = func.__name__
                limit_key = f"rate_limit:{method_name}:{user_id}"
            
            # 检查当前调用次数
            current_calls = cache.get(limit_key, 0)
            
            if current_calls >= max_calls:
                raise RateLimitException(
                    f"调用频率超过限制: {max_calls}次/{window_seconds}秒",
                    limit=max_calls,
                    window=window_seconds,
                    retry_after=window_seconds
                )
            
            # 增加调用次数
            try:
                # 使用原子操作增加计数
                if current_calls == 0:
                    cache.set(limit_key, 1, window_seconds)
                else:
                    cache.set(limit_key, current_calls + 1, window_seconds)
                
                # 执行原方法
                return func(self, *args, **kwargs)
                
            except Exception as e:
                # 如果方法执行失败，可以考虑是否回滚计数
                # 这里选择不回滚，避免通过异常绕过频率限制
                raise
        
        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3, delay_seconds: float = 1.0,
                    backoff_factor: float = 2.0, 
                    retry_exceptions: Optional[tuple] = None):
    """
    失败重试装饰器
    
    在方法执行失败时自动重试
    
    Args:
        max_retries: 最大重试次数
        delay_seconds: 初始延迟时间（秒）
        backoff_factor: 退避因子
        retry_exceptions: 需要重试的异常类型元组
        
    Usage:
        @retry_on_failure(max_retries=3, retry_exceptions=(ConnectionError,))
        def call_external_api(self):
            pass
    """
    if retry_exceptions is None:
        retry_exceptions = (Exception,)
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            last_exception = None
            delay = delay_seconds
            
            for attempt in range(max_retries + 1):
                try:
                    return func(self, *args, **kwargs)
                    
                except retry_exceptions as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(
                            f"方法 {func.__name__} 第 {attempt + 1} 次尝试失败: {str(e)}, "
                            f"{delay:.1f}秒后重试"
                        )
                        time.sleep(delay)
                        delay *= backoff_factor
                    else:
                        logger.error(f"方法 {func.__name__} 重试 {max_retries} 次后仍然失败")
                        
                except Exception as e:
                    # 不在重试范围内的异常直接抛出
                    raise
            
            # 重试次数用完，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator


# 辅助函数

def _filter_sensitive_params(params: Dict[str, Any], 
                           sensitive_keys: List[str]) -> Dict[str, Any]:
    """
    过滤敏感参数
    
    Args:
        params: 参数字典
        sensitive_keys: 敏感键列表
        
    Returns:
        Dict: 过滤后的参数字典
    """
    filtered = {}
    
    for key, value in params.items():
        if key == 'args' and isinstance(value, tuple):
            # 处理位置参数
            filtered[key] = ['***' if _is_sensitive_value(arg, sensitive_keys) else arg 
                           for arg in value]
        elif key == 'kwargs' and isinstance(value, dict):
            # 处理关键字参数
            filtered[key] = {
                k: '***' if any(sens in k.lower() for sens in sensitive_keys) else v
                for k, v in value.items()
            }
        else:
            # 处理普通参数
            if any(sens in key.lower() for sens in sensitive_keys):
                filtered[key] = '***'
            else:
                filtered[key] = value
    
    return filtered


def _is_sensitive_value(value: Any, sensitive_keys: List[str]) -> bool:
    """
    判断值是否为敏感信息
    
    Args:
        value: 要检查的值
        sensitive_keys: 敏感键列表
        
    Returns:
        bool: 是否为敏感信息
    """
    if isinstance(value, str):
        return any(sens in value.lower() for sens in sensitive_keys)
    return False


def _generate_cache_key(template: str, service_instance: Any, 
                       args: tuple, kwargs: Dict[str, Any]) -> str:
    """
    生成缓存键
    
    Args:
        template: 缓存键模板
        service_instance: 服务实例
        args: 位置参数
        kwargs: 关键字参数
        
    Returns:
        str: 生成的缓存键
    """
    try:
        # 准备替换变量
        replacements = {
            'service_name': service_instance.__class__.__name__,
            'user_id': getattr(service_instance, 'user_id', 'anonymous'),
            'request_type': getattr(service_instance, 'request_type', 'user')
        }
        
        # 添加参数到替换变量
        for i, arg in enumerate(args):
            replacements[f'arg_{i}'] = str(arg)
        
        for key, value in kwargs.items():
            replacements[key] = str(value)
        
        # 执行模板替换
        cache_key = template.format(**replacements)
        
        # 确保缓存键长度合理
        if len(cache_key) > 200:
            import hashlib
            hash_suffix = hashlib.md5(cache_key.encode()).hexdigest()[:8]
            cache_key = cache_key[:180] + '_' + hash_suffix
        
        return cache_key
        
    except Exception as e:
        logger.error(f"生成缓存键失败: {str(e)}")
        # 返回一个基本的缓存键
        return f"service_cache_{service_instance.__class__.__name__}_{int(time.time())}"


# 组合装饰器示例

def service_method(permission: Optional[str] = None, 
                  cache_timeout: Optional[int] = None,
                  cache_key: Optional[str] = None,
                  monitor_performance: bool = True,
                  log_calls: bool = True):
    """
    组合装饰器，提供常用的服务方法装饰功能
    
    Args:
        permission: 需要的权限
        cache_timeout: 缓存超时时间
        cache_key: 缓存键模板
        monitor_performance: 是否监控性能
        log_calls: 是否记录调用日志
        
    Usage:
        @service_method(permission='read', cache_timeout=300, cache_key='categories_{request_type}')
        def get_categories(self):
            pass
    """
    def decorator(func: Callable) -> Callable:
        # 应用各种装饰器
        decorated_func = func
        
        # 异常处理（最外层）
        decorated_func = handle_exceptions()(decorated_func)
        
        # 权限检查
        if permission:
            decorated_func = require_permission(permission)(decorated_func)
        
        # 缓存
        if cache_timeout and cache_key:
            decorated_func = cache_result(cache_key, cache_timeout)(decorated_func)
        
        # 性能监控
        if monitor_performance:
            decorated_func = monitor_performance()(decorated_func)
        
        # 日志记录
        if log_calls:
            decorated_func = log_service_call()(decorated_func)
        
        return decorated_func
    
    return decorator