"""
自定义异常体系模块

定义服务层使用的所有自定义异常类，提供精确的错误处理和分类。

异常层次结构：
- ServiceException (基础异常)
  - PermissionDeniedException (权限拒绝)
  - ValidationException (数据验证)
  - ResourceNotFoundException (资源未找到)
  - BusinessLogicException (业务逻辑)
  - ExternalServiceException (外部服务)
  - DatabaseException (数据库)
"""

from typing import Optional, Dict, Any


class ServiceException(Exception):
    """
    服务层基础异常类
    
    所有服务层异常的基类，提供统一的异常处理接口
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or 'SERVICE_ERROR'
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict: 异常信息字典
        """
        return {
            'exception_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.message}"


class PermissionDeniedException(ServiceException):
    """
    权限拒绝异常
    
    当用户没有足够权限执行某个操作时抛出
    """
    
    def __init__(self, message: str = "权限不足", required_permission: Optional[str] = None,
                 user_id: Optional[str] = None, resource: Optional[str] = None):
        """
        初始化权限异常
        
        Args:
            message: 错误消息
            required_permission: 需要的权限
            user_id: 用户ID
            resource: 资源标识
        """
        details = {}
        if required_permission:
            details['required_permission'] = required_permission
        if user_id:
            details['user_id'] = user_id
        if resource:
            details['resource'] = resource
            
        super().__init__(message, 'PERMISSION_DENIED', details)


class ValidationException(ServiceException):
    """
    数据验证异常
    
    当输入数据不符合验证规则时抛出
    """
    
    def __init__(self, message: str = "数据验证失败", field: Optional[str] = None,
                 validation_errors: Optional[Dict[str, str]] = None):
        """
        初始化验证异常
        
        Args:
            message: 错误消息
            field: 验证失败的字段
            validation_errors: 详细的验证错误信息
        """
        details = {}
        if field:
            details['field'] = field
        if validation_errors:
            details['validation_errors'] = validation_errors
            
        super().__init__(message, 'VALIDATION_ERROR', details)


class ResourceNotFoundException(ServiceException):
    """
    资源未找到异常
    
    当请求的资源不存在时抛出
    """
    
    def __init__(self, message: str = "资源未找到", resource_type: Optional[str] = None,
                 resource_id: Optional[str] = None):
        """
        初始化资源未找到异常
        
        Args:
            message: 错误消息
            resource_type: 资源类型
            resource_id: 资源ID
        """
        details = {}
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = resource_id
            
        super().__init__(message, 'RESOURCE_NOT_FOUND', details)


class BusinessLogicException(ServiceException):
    """
    业务逻辑异常
    
    当业务逻辑执行失败时抛出
    """
    
    def __init__(self, message: str = "业务逻辑错误", operation: Optional[str] = None,
                 business_rule: Optional[str] = None):
        """
        初始化业务逻辑异常
        
        Args:
            message: 错误消息
            operation: 执行的操作
            business_rule: 违反的业务规则
        """
        details = {}
        if operation:
            details['operation'] = operation
        if business_rule:
            details['business_rule'] = business_rule
            
        super().__init__(message, 'BUSINESS_LOGIC_ERROR', details)


class ExternalServiceException(ServiceException):
    """
    外部服务异常
    
    当调用外部服务失败时抛出
    """
    
    def __init__(self, message: str = "外部服务调用失败", service_name: Optional[str] = None,
                 status_code: Optional[int] = None, response_data: Optional[str] = None):
        """
        初始化外部服务异常
        
        Args:
            message: 错误消息
            service_name: 服务名称
            status_code: HTTP状态码
            response_data: 响应数据
        """
        details = {}
        if service_name:
            details['service_name'] = service_name
        if status_code:
            details['status_code'] = status_code
        if response_data:
            details['response_data'] = response_data
            
        super().__init__(message, 'EXTERNAL_SERVICE_ERROR', details)


class DatabaseException(ServiceException):
    """
    数据库异常
    
    当数据库操作失败时抛出
    """
    
    def __init__(self, message: str = "数据库操作失败", operation: Optional[str] = None,
                 table: Optional[str] = None, query: Optional[str] = None):
        """
        初始化数据库异常
        
        Args:
            message: 错误消息
            operation: 数据库操作类型
            table: 表名
            query: 查询语句
        """
        details = {}
        if operation:
            details['operation'] = operation
        if table:
            details['table'] = table
        if query:
            # 不记录完整查询语句，避免敏感信息泄露
            details['query_type'] = query.split()[0].upper() if query else None
            
        super().__init__(message, 'DATABASE_ERROR', details)


class ConfigurationException(ServiceException):
    """
    配置异常
    
    当系统配置错误时抛出
    """
    
    def __init__(self, message: str = "配置错误", config_key: Optional[str] = None,
                 config_file: Optional[str] = None):
        """
        初始化配置异常
        
        Args:
            message: 错误消息
            config_key: 配置键
            config_file: 配置文件
        """
        details = {}
        if config_key:
            details['config_key'] = config_key
        if config_file:
            details['config_file'] = config_file
            
        super().__init__(message, 'CONFIGURATION_ERROR', details)


class RateLimitException(ServiceException):
    """
    频率限制异常
    
    当请求频率超过限制时抛出
    """
    
    def __init__(self, message: str = "请求频率超过限制", limit: Optional[int] = None,
                 window: Optional[int] = None, retry_after: Optional[int] = None):
        """
        初始化频率限制异常
        
        Args:
            message: 错误消息
            limit: 限制次数
            window: 时间窗口（秒）
            retry_after: 重试等待时间（秒）
        """
        details = {}
        if limit:
            details['limit'] = limit
        if window:
            details['window'] = window
        if retry_after:
            details['retry_after'] = retry_after
            
        super().__init__(message, 'RATE_LIMIT_ERROR', details)


class CacheException(ServiceException):
    """
    缓存异常
    
    当缓存操作失败时抛出
    """
    
    def __init__(self, message: str = "缓存操作失败", operation: Optional[str] = None,
                 cache_key: Optional[str] = None):
        """
        初始化缓存异常
        
        Args:
            message: 错误消息
            operation: 缓存操作类型
            cache_key: 缓存键
        """
        details = {}
        if operation:
            details['operation'] = operation
        if cache_key:
            details['cache_key'] = cache_key
            
        super().__init__(message, 'CACHE_ERROR', details)


# 异常工厂函数，用于快速创建常见异常
def create_permission_error(required_permission: str, user_id: str = None) -> PermissionDeniedException:
    """创建权限错误异常"""
    return PermissionDeniedException(
        f"缺少权限: {required_permission}",
        required_permission=required_permission,
        user_id=user_id
    )


def create_validation_error(field: str, message: str) -> ValidationException:
    """创建验证错误异常"""
    return ValidationException(
        f"字段 {field} 验证失败: {message}",
        field=field,
        validation_errors={field: message}
    )


def create_not_found_error(resource_type: str, resource_id: str) -> ResourceNotFoundException:
    """创建资源未找到异常"""
    return ResourceNotFoundException(
        f"{resource_type} (ID: {resource_id}) 未找到",
        resource_type=resource_type,
        resource_id=resource_id
    )


def create_business_error(operation: str, rule: str) -> BusinessLogicException:
    """创建业务逻辑错误异常"""
    return BusinessLogicException(
        f"操作 {operation} 违反业务规则: {rule}",
        operation=operation,
        business_rule=rule
    )


# 异常处理装饰器
def handle_service_exceptions(func):
    """
    服务异常处理装饰器
    
    自动捕获和处理服务层异常
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ServiceException:
            # 服务异常直接重新抛出
            raise
        except Exception as e:
            # 其他异常包装为服务异常
            raise ServiceException(f"服务执行异常: {str(e)}")
    
    return wrapper