"""
分类数据传输对象模块

定义分类相关的数据传输对象，包括：
- CategoryDTO: 分类数据传输对象
- 数据格式化方法
- 数据验证方法
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..common.format_service import FormatService
from ..common.validation_service import ValidationService
from ..base.exceptions import ValidationException


@dataclass
class CategoryDTO:
    """
    分类数据传输对象
    
    用于在服务层和API层之间传输分类数据
    """
    
    id: str
    name: str
    level: int
    image: Optional[str] = None
    parent_id: Optional[str] = None
    children: Optional[List['CategoryDTO']] = field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后验证"""
        self.validate()
    
    def validate(self) -> None:
        """
        验证分类数据
        
        Raises:
            ValidationException: 验证失败时抛出
        """
        # 验证必填字段
        ValidationService.validate_required(self.id, "分类ID")
        ValidationService.validate_required(self.name, "分类名称")
        ValidationService.validate_required(self.level, "分类层级")
        
        # 验证数据类型和格式
        ValidationService.validate_string(self.id, "分类ID", min_length=1)
        ValidationService.validate_string(self.name, "分类名称", min_length=1, max_length=100)
        ValidationService.validate_integer(self.level, "分类层级", min_value=1, max_value=3)
        
        # 验证图片URL（如果提供）
        if self.image:
            ValidationService.validate_string(self.image, "分类图片", max_length=500)
        
        # 验证父分类ID（如果提供）
        if self.parent_id:
            ValidationService.validate_string(self.parent_id, "父分类ID", min_length=1)
        
        # 验证层级逻辑
        if self.level == 1 and self.parent_id:
            raise ValidationException("一级分类不能有父分类")
        
        if self.level > 1 and not self.parent_id:
            raise ValidationException("二级及以上分类必须有父分类")
    
    @classmethod
    def from_model(cls, category_model) -> 'CategoryDTO':
        """
        从Django模型创建DTO对象
        
        Args:
            category_model: Django分类模型实例
            
        Returns:
            CategoryDTO: 分类DTO对象
        """
        return cls(
            id=str(category_model.id),
            name=category_model.name,
            level=category_model.level,
            image=category_model.image,
            parent_id=str(category_model.parent_id) if category_model.parent_id else None,
            created_at=category_model.created_at,
            updated_at=category_model.updated_at
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CategoryDTO':
        """
        从字典创建DTO对象
        
        Args:
            data: 分类数据字典
            
        Returns:
            CategoryDTO: 分类DTO对象
        """
        # 处理子分类
        children = []
        if 'children' in data and data['children']:
            children = [cls.from_dict(child) for child in data['children']]
        
        # 处理日期时间
        created_at = None
        if data.get('created_at'):
            if isinstance(data['created_at'], str):
                try:
                    created_at = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))
                except ValueError:
                    created_at = None
            elif isinstance(data['created_at'], datetime):
                created_at = data['created_at']
        
        updated_at = None
        if data.get('updated_at'):
            if isinstance(data['updated_at'], str):
                try:
                    updated_at = datetime.fromisoformat(data['updated_at'].replace('Z', '+00:00'))
                except ValueError:
                    updated_at = None
            elif isinstance(data['updated_at'], datetime):
                updated_at = data['updated_at']
        
        return cls(
            id=str(data['id']),
            name=data['name'],
            level=int(data['level']),
            image=data.get('image'),
            parent_id=str(data['parent_id']) if data.get('parent_id') else None,
            children=children,
            created_at=created_at,
            updated_at=updated_at
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict: 分类数据字典
        """
        result = {
            'id': self.id,
            'name': self.name,
            'level': self.level,
            'image': self.image or '/static/images/category-placeholder.svg',
            'parent_id': self.parent_id or '',
            'children': [child.to_dict() for child in (self.children or [])]
        }
        
        if self.created_at:
            result['created_at'] = self.created_at.isoformat()
        
        if self.updated_at:
            result['updated_at'] = self.updated_at.isoformat()
        
        return result
    
    def to_admin_dict(self) -> Dict[str, Any]:
        """
        转换为管理端格式
        
        Returns:
            Dict: 管理端格式的分类数据
        """
        result = {
            'id': self.id,
            'name': self.name,
            'level': self.level,
            'image': self.image or '/static/images/category-placeholder.svg',
            'parent_id': self.parent_id or '',
            'created_at': FormatService.format_datetime(self.created_at),
            'updated_at': FormatService.format_datetime(self.updated_at),
            'children': [child.to_admin_dict() for child in (self.children or [])]
        }
        
        return result
    
    def to_user_dict(self) -> Dict[str, Any]:
        """
        转换为用户端格式
        
        Returns:
            Dict: 用户端格式的分类数据
        """
        result = {
            'id': self.id,
            'name': self.name,
            'level': self.level,
            'image': self.image or '/static/images/category-placeholder.svg',
            'parent_id': self.parent_id or '',
            'children': [child.to_user_dict() for child in (self.children or [])]
        }
        
        return result
    
    def add_child(self, child: 'CategoryDTO') -> None:
        """
        添加子分类
        
        Args:
            child: 子分类DTO对象
        """
        if self.children is None:
            self.children = []
        
        # 验证子分类的层级关系
        if child.level != self.level + 1:
            raise ValidationException(f"子分类层级应为{self.level + 1}")
        
        if child.parent_id != self.id:
            raise ValidationException("子分类的父分类ID不匹配")
        
        self.children.append(child)
    
    def remove_child(self, child_id: str) -> bool:
        """
        移除子分类
        
        Args:
            child_id: 子分类ID
            
        Returns:
            bool: 是否成功移除
        """
        if not self.children:
            return False
        
        for i, child in enumerate(self.children):
            if child.id == child_id:
                del self.children[i]
                return True
        
        return False
    
    def get_child_by_id(self, child_id: str) -> Optional['CategoryDTO']:
        """
        根据ID获取子分类
        
        Args:
            child_id: 子分类ID
            
        Returns:
            Optional[CategoryDTO]: 子分类DTO对象或None
        """
        if not self.children:
            return None
        
        for child in self.children:
            if child.id == child_id:
                return child
        
        return None
    
    def has_children(self) -> bool:
        """
        检查是否有子分类
        
        Returns:
            bool: 是否有子分类
        """
        return bool(self.children)
    
    def get_children_count(self) -> int:
        """
        获取子分类数量
        
        Returns:
            int: 子分类数量
        """
        return len(self.children) if self.children else 0
    
    def is_root_category(self) -> bool:
        """
        检查是否为根分类（一级分类）
        
        Returns:
            bool: 是否为根分类
        """
        return self.level == 1 and not self.parent_id
    
    def is_leaf_category(self) -> bool:
        """
        检查是否为叶子分类（没有子分类）
        
        Returns:
            bool: 是否为叶子分类
        """
        return not self.has_children()
    
    def get_all_descendant_ids(self) -> List[str]:
        """
        获取所有后代分类ID
        
        Returns:
            List[str]: 后代分类ID列表
        """
        descendant_ids = []
        
        if self.children:
            for child in self.children:
                descendant_ids.append(child.id)
                descendant_ids.extend(child.get_all_descendant_ids())
        
        return descendant_ids
    
    def clone(self, new_id: Optional[str] = None) -> 'CategoryDTO':
        """
        克隆分类对象
        
        Args:
            new_id: 新的分类ID（可选）
            
        Returns:
            CategoryDTO: 克隆的分类对象
        """
        cloned_children = []
        if self.children:
            cloned_children = [child.clone() for child in self.children]
        
        return CategoryDTO(
            id=new_id or self.id,
            name=self.name,
            level=self.level,
            image=self.image,
            parent_id=self.parent_id,
            children=cloned_children,
            created_at=self.created_at,
            updated_at=self.updated_at
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"CategoryDTO(id={self.id}, name={self.name}, level={self.level})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"CategoryDTO(id={self.id}, name={self.name}, level={self.level}, "
                f"parent_id={self.parent_id}, children_count={self.get_children_count()})")


class CategoryTreeBuilder:
    """
    分类树构建器
    
    用于构建分类树结构
    """
    
    @staticmethod
    def build_tree(categories: List[CategoryDTO]) -> List[CategoryDTO]:
        """
        构建分类树
        
        Args:
            categories: 分类DTO列表
            
        Returns:
            List[CategoryDTO]: 分类树（根分类列表）
        """
        # 按层级排序
        sorted_categories = sorted(categories, key=lambda x: x.level)
        
        # 创建ID到分类的映射
        category_map = {cat.id: cat for cat in sorted_categories}
        
        # 构建树结构
        root_categories = []
        
        for category in sorted_categories:
            if category.is_root_category():
                root_categories.append(category)
            else:
                parent = category_map.get(category.parent_id)
                if parent:
                    parent.add_child(category)
        
        return root_categories
    
    @staticmethod
    def flatten_tree(root_categories: List[CategoryDTO]) -> List[CategoryDTO]:
        """
        展平分类树
        
        Args:
            root_categories: 根分类列表
            
        Returns:
            List[CategoryDTO]: 展平的分类列表
        """
        flattened = []
        
        def _flatten(categories: List[CategoryDTO]):
            for category in categories:
                flattened.append(category)
                if category.children:
                    _flatten(category.children)
        
        _flatten(root_categories)
        return flattened
    
    @staticmethod
    def find_category_path(root_categories: List[CategoryDTO], 
                          target_id: str) -> Optional[List[CategoryDTO]]:
        """
        查找分类路径
        
        Args:
            root_categories: 根分类列表
            target_id: 目标分类ID
            
        Returns:
            Optional[List[CategoryDTO]]: 分类路径或None
        """
        def _find_path(categories: List[CategoryDTO], 
                      path: List[CategoryDTO]) -> Optional[List[CategoryDTO]]:
            for category in categories:
                current_path = path + [category]
                
                if category.id == target_id:
                    return current_path
                
                if category.children:
                    result = _find_path(category.children, current_path)
                    if result:
                        return result
            
            return None
        
        return _find_path(root_categories, [])

class CategoryFilter:
    """
    分类过滤器
    
    用于过滤和搜索分类
    """
    
    @staticmethod
    def filter_by_level(categories: List[CategoryDTO], level: int) -> List[CategoryDTO]:
        """
        按层级过滤分类
        
        Args:
            categories: 分类列表
            level: 目标层级
            
        Returns:
            List[CategoryDTO]: 过滤后的分类列表
        """
        return [cat for cat in categories if cat.level == level]
    
    @staticmethod
    def filter_by_parent(categories: List[CategoryDTO], parent_id: str) -> List[CategoryDTO]:
        """
        按父分类过滤
        
        Args:
            categories: 分类列表
            parent_id: 父分类ID
            
        Returns:
            List[CategoryDTO]: 过滤后的分类列表
        """
        return [cat for cat in categories if cat.parent_id == parent_id]
    
    @staticmethod
    def search_by_name(categories: List[CategoryDTO], keyword: str) -> List[CategoryDTO]:
        """
        按名称搜索分类
        
        Args:
            categories: 分类列表
            keyword: 搜索关键词
            
        Returns:
            List[CategoryDTO]: 搜索结果列表
        """
        if not keyword:
            return categories
        
        keyword_lower = keyword.lower()
        return [cat for cat in categories if keyword_lower in cat.name.lower()]
    
    @staticmethod
    def filter_active_categories(categories: List[CategoryDTO]) -> List[CategoryDTO]:
        """
        过滤活跃分类（这里假设所有分类都是活跃的，实际可能需要状态字段）
        
        Args:
            categories: 分类列表
            
        Returns:
            List[CategoryDTO]: 活跃分类列表
        """
        # 这里可以根据实际的状态字段进行过滤
        # 目前返回所有分类
        return categories


class CategoryValidator:
    """
    分类验证器
    
    提供分类相关的验证功能
    """
    
    @staticmethod
    def validate_category_hierarchy(categories: List[CategoryDTO]) -> List[str]:
        """
        验证分类层级结构
        
        Args:
            categories: 分类列表
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        category_map = {cat.id: cat for cat in categories}
        
        for category in categories:
            # 验证父分类存在性
            if category.parent_id and category.parent_id not in category_map:
                errors.append(f"分类 {category.name} 的父分类不存在")
            
            # 验证层级逻辑
            if category.parent_id:
                parent = category_map.get(category.parent_id)
                if parent and parent.level != category.level - 1:
                    errors.append(f"分类 {category.name} 的层级关系不正确")
            
            # 验证循环引用
            if CategoryValidator._has_circular_reference(category, category_map):
                errors.append(f"分类 {category.name} 存在循环引用")
        
        return errors
    
    @staticmethod
    def _has_circular_reference(category: CategoryDTO, 
                              category_map: Dict[str, CategoryDTO],
                              visited: Optional[set] = None) -> bool:
        """
        检查是否存在循环引用
        
        Args:
            category: 当前分类
            category_map: 分类映射
            visited: 已访问的分类ID集合
            
        Returns:
            bool: 是否存在循环引用
        """
        if visited is None:
            visited = set()
        
        if category.id in visited:
            return True
        
        visited.add(category.id)
        
        if category.parent_id and category.parent_id in category_map:
            parent = category_map[category.parent_id]
            return CategoryValidator._has_circular_reference(parent, category_map, visited)
        
        return False
    
    @staticmethod
    def validate_category_name_uniqueness(categories: List[CategoryDTO], 
                                        same_level_only: bool = True) -> List[str]:
        """
        验证分类名称唯一性
        
        Args:
            categories: 分类列表
            same_level_only: 是否只在同一层级内检查唯一性
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        
        if same_level_only:
            # 按层级分组检查
            level_groups = {}
            for category in categories:
                level = category.level
                if level not in level_groups:
                    level_groups[level] = []
                level_groups[level].append(category)
            
            for level, level_categories in level_groups.items():
                names = [cat.name for cat in level_categories]
                duplicates = [name for name in names if names.count(name) > 1]
                if duplicates:
                    errors.append(f"第{level}级分类中存在重复名称: {set(duplicates)}")
        else:
            # 全局检查
            names = [cat.name for cat in categories]
            duplicates = [name for name in names if names.count(name) > 1]
            if duplicates:
                errors.append(f"存在重复的分类名称: {set(duplicates)}")
        
        return errors


class CategoryStatistics:
    """
    分类统计器
    
    提供分类相关的统计功能
    """
    
    @staticmethod
    def get_category_stats(categories: List[CategoryDTO]) -> Dict[str, Any]:
        """
        获取分类统计信息
        
        Args:
            categories: 分类列表
            
        Returns:
            Dict: 统计信息
        """
        if not categories:
            return {
                'total_count': 0,
                'level_distribution': {},
                'max_depth': 0,
                'root_count': 0,
                'leaf_count': 0
            }
        
        level_distribution = {}
        root_count = 0
        leaf_count = 0
        max_depth = 0
        
        for category in categories:
            # 层级分布
            level = category.level
            level_distribution[level] = level_distribution.get(level, 0) + 1
            
            # 最大深度
            max_depth = max(max_depth, level)
            
            # 根分类计数
            if category.is_root_category():
                root_count += 1
            
            # 叶子分类计数
            if category.is_leaf_category():
                leaf_count += 1
        
        return {
            'total_count': len(categories),
            'level_distribution': level_distribution,
            'max_depth': max_depth,
            'root_count': root_count,
            'leaf_count': leaf_count
        }
    
    @staticmethod
    def get_tree_stats(root_categories: List[CategoryDTO]) -> Dict[str, Any]:
        """
        获取分类树统计信息
        
        Args:
            root_categories: 根分类列表
            
        Returns:
            Dict: 树统计信息
        """
        all_categories = CategoryTreeBuilder.flatten_tree(root_categories)
        basic_stats = CategoryStatistics.get_category_stats(all_categories)
        
        # 计算每个根分类的子分类数量
        root_children_count = {}
        for root in root_categories:
            descendant_ids = root.get_all_descendant_ids()
            root_children_count[root.id] = {
                'name': root.name,
                'direct_children': root.get_children_count(),
                'total_descendants': len(descendant_ids)
            }
        
        basic_stats['root_children_distribution'] = root_children_count
        return basic_stats


class CategoryExporter:
    """
    分类导出器
    
    提供分类数据的导出功能
    """
    
    @staticmethod
    def export_to_flat_list(categories: List[CategoryDTO], 
                           format_type: str = 'user') -> List[Dict[str, Any]]:
        """
        导出为扁平列表
        
        Args:
            categories: 分类列表
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 扁平化的分类数据列表
        """
        result = []
        
        for category in categories:
            if format_type == 'admin':
                result.append(category.to_admin_dict())
            else:
                result.append(category.to_user_dict())
        
        return result
    
    @staticmethod
    def export_tree_structure(root_categories: List[CategoryDTO],
                            format_type: str = 'user') -> List[Dict[str, Any]]:
        """
        导出树形结构
        
        Args:
            root_categories: 根分类列表
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 树形结构的分类数据
        """
        result = []
        
        for root in root_categories:
            if format_type == 'admin':
                result.append(root.to_admin_dict())
            else:
                result.append(root.to_user_dict())
        
        return result
    
    @staticmethod
    def export_category_paths(root_categories: List[CategoryDTO]) -> List[Dict[str, Any]]:
        """
        导出分类路径信息
        
        Args:
            root_categories: 根分类列表
            
        Returns:
            List[Dict]: 分类路径信息列表
        """
        result = []
        all_categories = CategoryTreeBuilder.flatten_tree(root_categories)
        
        for category in all_categories:
            path = CategoryTreeBuilder.find_category_path(root_categories, category.id)
            if path:
                path_names = [cat.name for cat in path]
                result.append({
                    'id': category.id,
                    'name': category.name,
                    'level': category.level,
                    'path': ' > '.join(path_names),
                    'path_ids': [cat.id for cat in path]
                })
        
        return result