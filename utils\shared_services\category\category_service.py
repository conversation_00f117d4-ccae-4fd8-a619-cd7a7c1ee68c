"""
分类服务模块

提供分类相关的业务逻辑处理，包括：
- 分类查询、创建、更新、删除
- 分类层级结构构建
- 数据格式化和验证
"""

import uuid
from typing import List, Dict, Any, Optional
from django.db import transaction

from ..base.base_service import BaseService
from ..base.decorators import (
    log_service_call, 
    require_permission, 
    monitor_performance,
    handle_exceptions
)
from ..base.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    PermissionDeniedException,
    BusinessLogicException
)
from ..common.validation_service import ValidationService
from ..common.format_service import FormatService
from .category_dto import CategoryDTO, CategoryTreeBuilder


class CategoryService(BaseService):
    """
    分类服务类
    
    提供分类相关的业务逻辑处理
    """
    
    @log_service_call(include_params=True)
    @monitor_performance(threshold_seconds=0.5)
    def get_category_list(self, include_children: bool = True, 
                         format_type: Optional[str] = None) -> List[Dict[str, Any]]:

        try:
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 从数据库获取分类数据
            from api.models import Category
            
            if include_children:
                # 获取所有分类并构建树结构
                all_categories = Category.objects.all().order_by('level', '-created_at')
                category_dtos = [CategoryDTO.from_model(cat) for cat in all_categories]
                
                # 构建分类树
                root_categories = CategoryTreeBuilder.build_tree(category_dtos)
                
                # 格式化输出
                if format_type == 'admin':
                    return [cat.to_admin_dict() for cat in root_categories]
                else:
                    return [cat.to_user_dict() for cat in root_categories]
            else:
                # 只获取一级分类
                parent_categories = Category.objects.filter(level=1).order_by('-created_at')
                category_dtos = [CategoryDTO.from_model(cat) for cat in parent_categories]
                
                # 格式化输出
                if format_type == 'admin':
                    return [cat.to_admin_dict() for cat in category_dtos]
                else:
                    return [cat.to_user_dict() for cat in category_dtos]
                    
        except Exception as e:
            self.logger.error(f"获取分类列表失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def get_category_by_id(self, category_id: str, 
                          format_type: Optional[str] = None) -> Dict[str, Any]:

        try:
            # 验证参数
            ValidationService.validate_required(category_id, "分类ID")
            ValidationService.validate_string(category_id, "分类ID", min_length=1)
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 从数据库获取分类
            from api.models import Category
            
            try:
                category = Category.objects.get(id=category_id)
            except Category.DoesNotExist:
                raise ResourceNotFoundException(
                    f"分类不存在",
                    resource_type="category",
                    resource_id=category_id
                )
            
            # 转换为DTO并格式化
            category_dto = CategoryDTO.from_model(category)
            
            if format_type == 'admin':
                return category_dto.to_admin_dict()
            else:
                return category_dto.to_user_dict()
                
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f"获取分类详情失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def create_category(self, category_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建分类（仅管理端）
        
        Args:
            category_data: 分类数据
            
        Returns:
            Dict: 创建后的分类数据
            
        Raises:
            ValidationException: 数据验证失败时抛出
            BusinessLogicException: 业务逻辑错误时抛出
        """
        try:
            # 验证必填字段
            ValidationService.validate_required(category_data.get('name'), "分类名称")
            ValidationService.validate_required(category_data.get('level'), "分类层级")
            
            # 数据验证
            name = ValidationService.validate_string(
                category_data['name'], "分类名称", min_length=1, max_length=100
            )
            level = ValidationService.validate_integer(
                category_data['level'], "分类层级", min_value=1, max_value=3
            )
            image = category_data.get('image', '')
            parent_id = category_data.get('parent_id', '')
            
            # 业务逻辑验证
            if level == 1 and parent_id:
                raise BusinessLogicException("一级分类不能有父分类")
            
            if level > 1 and not parent_id:
                raise BusinessLogicException("二级及以上分类必须有父分类")
            
            # 如果是二级分类，验证父分类是否存在
            if level == 2 and parent_id:
                from api.models import Category
                parent_exists = Category.objects.filter(id=parent_id, level=1).exists()
                if not parent_exists:
                    raise BusinessLogicException("父分类不存在或不是一级分类")
            
            # 开始事务创建分类
            with transaction.atomic():
                from api.models import Category
                
                # 生成新分类ID
                new_id = str(uuid.uuid4())
                
                # 创建分类
                category = Category(
                    id=new_id,
                    name=name,
                    level=level,
                    image=image if image else None,
                    parent_id=parent_id if level == 2 and parent_id else None
                )
                category.save()
                
                # 转换为DTO并返回
                category_dto = CategoryDTO.from_model(category)
                
                self.logger.info(f"分类创建成功: {new_id}")
                return category_dto.to_admin_dict()
                
        except (ValidationException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"创建分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def update_category(self, category_id: str, 
                       update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新分类（仅管理端）
        
        Args:
            category_id: 分类ID
            update_data: 更新数据
            
        Returns:
            Dict: 更新后的分类数据
            
        Raises:
            ResourceNotFoundException: 分类不存在时抛出
            ValidationException: 数据验证失败时抛出
            BusinessLogicException: 业务逻辑错误时抛出
        """
        try:
            # 验证分类ID
            ValidationService.validate_required(category_id, "分类ID")
            ValidationService.validate_string(category_id, "分类ID", min_length=1)
            
            # 检查是否有可更新的字段
            updatable_fields = ['name', 'image', 'parent_id']
            has_updates = any(field in update_data for field in updatable_fields)
            
            if not has_updates:
                raise ValidationException("没有提供可更新的字段")
            
            # 开始事务更新
            with transaction.atomic():
                from api.models import Category
                
                # 获取要更新的分类
                try:
                    category = Category.objects.get(id=category_id)
                except Category.DoesNotExist:
                    raise ResourceNotFoundException(
                        "分类不存在",
                        resource_type="category",
                        resource_id=category_id
                    )
                
                # 更新字段
                if 'name' in update_data:
                    name = ValidationService.validate_string(
                        update_data['name'], "分类名称", min_length=1, max_length=100
                    )
                    category.name = name
                
                if 'image' in update_data:
                    image = update_data['image']
                    if image:
                        ValidationService.validate_string(image, "分类图片", max_length=500)
                    category.image = image
                
                # 如果是二级分类且提供了父分类ID
                if category.level == 2 and 'parent_id' in update_data:
                    parent_id = update_data['parent_id']
                    if parent_id:
                        # 检查新父分类是否存在且为一级分类
                        parent_exists = Category.objects.filter(id=parent_id, level=1).exists()
                        if not parent_exists:
                            raise BusinessLogicException("父分类不存在或不是一级分类")
                        category.parent_id = parent_id
                
                # 保存更新
                category.save()
                
                # 转换为DTO并返回
                category_dto = CategoryDTO.from_model(category)
                
                self.logger.info(f"分类更新成功: {category_id}")
                return category_dto.to_admin_dict()
                
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"更新分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def delete_category(self, category_id: str) -> bool:
        """
        删除分类（仅管理端）
        
        Args:
            category_id: 分类ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            ResourceNotFoundException: 分类不存在时抛出
            BusinessLogicException: 业务逻辑错误时抛出
        """
        try:
            # 验证分类ID
            ValidationService.validate_required(category_id, "分类ID")
            ValidationService.validate_string(category_id, "分类ID", min_length=1)
            
            # 开始事务删除
            with transaction.atomic():
                from api.models import Category
                
                # 查找要删除的分类
                try:
                    category = Category.objects.get(id=category_id)
                except Category.DoesNotExist:
                    raise ResourceNotFoundException(
                        "分类不存在",
                        resource_type="category",
                        resource_id=category_id
                    )
                
                # 检查是否有子分类依赖该分类（如果是一级分类）
                if category.level == 1:
                    child_categories = Category.objects.filter(parent_id=category_id)
                    if child_categories.exists():
                        raise BusinessLogicException("该分类下有子分类，无法删除")
                
                # 检查是否有商品使用该分类
                from api.models import Goods
                goods_using_category = Goods.objects.filter(category_id=category_id)
                if goods_using_category.exists():
                    raise BusinessLogicException("该分类下有商品，无法删除")
                
                # 执行删除
                category.delete()
                
                self.logger.info(f"分类删除成功: {category_id}")
                return True
                
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"删除分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=False)
    @monitor_performance(threshold_seconds=0.3)
    def get_category_tree(self, format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取完整的分类树结构
        
        Args:
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 分类树数据
        """
        return self.get_category_list(include_children=True, format_type=format_type)
    
    @log_service_call(include_params=True)
    def get_categories_by_level(self, level: int, 
                               format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据层级获取分类列表
        
        Args:
            level: 分类层级
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 指定层级的分类列表
        """
        try:
            # 验证层级参数
            level = ValidationService.validate_integer(level, "分类层级", min_value=1, max_value=3)
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 从数据库获取指定层级的分类
            from api.models import Category
            categories = Category.objects.filter(level=level).order_by('-created_at')
            
            # 转换为DTO并格式化
            category_dtos = [CategoryDTO.from_model(cat) for cat in categories]
            
            if format_type == 'admin':
                return [cat.to_admin_dict() for cat in category_dtos]
            else:
                return [cat.to_user_dict() for cat in category_dtos]
                
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"根据层级获取分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def get_children_categories(self, parent_id: str, 
                               format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取指定分类的子分类
        
        Args:
            parent_id: 父分类ID
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 子分类列表
        """
        try:
            # 验证父分类ID
            ValidationService.validate_required(parent_id, "父分类ID")
            ValidationService.validate_string(parent_id, "父分类ID", min_length=1)
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 验证父分类是否存在
            from api.models import Category
            if not Category.objects.filter(id=parent_id).exists():
                raise ResourceNotFoundException(
                    "父分类不存在",
                    resource_type="category",
                    resource_id=parent_id
                )
            
            # 获取子分类
            children = Category.objects.filter(parent_id=parent_id).order_by('-created_at')
            category_dtos = [CategoryDTO.from_model(cat) for cat in children]
            
            if format_type == 'admin':
                return [cat.to_admin_dict() for cat in category_dtos]
            else:
                return [cat.to_user_dict() for cat in category_dtos]
                
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f"获取子分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def search_categories(self, keyword: str, 
                         format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        搜索分类
        
        Args:
            keyword: 搜索关键词
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            # 验证搜索关键词
            ValidationService.validate_required(keyword, "搜索关键词")
            keyword = ValidationService.validate_string(keyword, "搜索关键词", min_length=1)
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 从数据库搜索分类
            from api.models import Category
            categories = Category.objects.filter(
                name__icontains=keyword
            ).order_by('level', '-created_at')
            
            # 转换为DTO并格式化
            category_dtos = [CategoryDTO.from_model(cat) for cat in categories]
            
            if format_type == 'admin':
                return [cat.to_admin_dict() for cat in category_dtos]
            else:
                return [cat.to_user_dict() for cat in category_dtos]
                
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"搜索分类失败: {str(e)}")
            raise
    
    def _build_category_tree(self, categories: List[CategoryDTO]) -> List[CategoryDTO]:
        """
        构建分类树结构（私有方法）
        
        Args:
            categories: 分类DTO列表
            
        Returns:
            List[CategoryDTO]: 分类树（根分类列表）
        """
        return CategoryTreeBuilder.build_tree(categories)
    
    def _format_category_data(self, category: CategoryDTO, 
                             format_type: str) -> Dict[str, Any]:
        """
        格式化单个分类数据（私有方法）
        
        Args:
            category: 分类DTO对象
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的分类数据
        """
        if format_type == 'admin':
            return category.to_admin_dict()
        else:
            return category.to_user_dict()  
  
    @log_service_call(include_params=True)
    def get_category_statistics(self) -> Dict[str, Any]:
        """
        获取分类统计信息
        
        Returns:
            Dict: 分类统计数据
        """
        try:
            from api.models import Category
            from .category_dto import CategoryStatistics
            
            # 获取所有分类
            all_categories = Category.objects.all()
            category_dtos = [CategoryDTO.from_model(cat) for cat in all_categories]
            
            # 构建分类树
            root_categories = CategoryTreeBuilder.build_tree(category_dtos)
            
            # 获取统计信息
            stats = CategoryStatistics.get_tree_stats(root_categories)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取分类统计失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def validate_category_hierarchy(self) -> Dict[str, Any]:
        """
        验证分类层级结构的完整性
        
        Returns:
            Dict: 验证结果
        """
        try:
            from api.models import Category
            from .category_dto import CategoryValidator
            
            # 获取所有分类
            all_categories = Category.objects.all()
            category_dtos = [CategoryDTO.from_model(cat) for cat in all_categories]
            
            # 执行验证
            hierarchy_errors = CategoryValidator.validate_category_hierarchy(category_dtos)
            name_errors = CategoryValidator.validate_category_name_uniqueness(category_dtos)
            
            return {
                'is_valid': len(hierarchy_errors) == 0 and len(name_errors) == 0,
                'hierarchy_errors': hierarchy_errors,
                'name_errors': name_errors,
                'total_errors': len(hierarchy_errors) + len(name_errors)
            }
            
        except Exception as e:
            self.logger.error(f"验证分类层级结构失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def get_category_path(self, category_id: str) -> List[Dict[str, Any]]:
        """
        获取分类的完整路径
        
        Args:
            category_id: 分类ID
            
        Returns:
            List[Dict]: 分类路径
        """
        try:
            # 验证分类ID
            ValidationService.validate_required(category_id, "分类ID")
            ValidationService.validate_string(category_id, "分类ID", min_length=1)
            
            from api.models import Category
            
            # 检查分类是否存在
            if not Category.objects.filter(id=category_id).exists():
                raise ResourceNotFoundException(
                    "分类不存在",
                    resource_type="category",
                    resource_id=category_id
                )
            
            # 获取所有分类并构建树
            all_categories = Category.objects.all()
            category_dtos = [CategoryDTO.from_model(cat) for cat in all_categories]
            root_categories = CategoryTreeBuilder.build_tree(category_dtos)
            
            # 查找分类路径
            path = CategoryTreeBuilder.find_category_path(root_categories, category_id)
            
            if path:
                return [cat.to_dict() for cat in path]
            else:
                return []
                
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f"获取分类路径失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def batch_update_categories(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量更新分类
        
        Args:
            updates: 更新数据列表，每项包含id和update_data
            
        Returns:
            Dict: 批量更新结果
        """
        try:
            # 验证输入
            if not isinstance(updates, list) or len(updates) == 0:
                raise ValidationException("更新数据不能为空")
            
            success_count = 0
            failed_count = 0
            errors = []
            
            # 开始事务
            with transaction.atomic():
                for update_item in updates:
                    try:
                        category_id = update_item.get('id')
                        update_data = update_item.get('update_data', {})
                        
                        if not category_id:
                            errors.append({"error": "缺少分类ID", "item": update_item})
                            failed_count += 1
                            continue
                        
                        # 调用单个更新方法
                        self.update_category(category_id, update_data)
                        success_count += 1
                        
                    except Exception as e:
                        errors.append({
                            "category_id": update_item.get('id'),
                            "error": str(e),
                            "item": update_item
                        })
                        failed_count += 1
            
            return {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(updates),
                'errors': errors
            }
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"批量更新分类失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def export_categories(self, export_format: str = 'tree', 
                         format_type: Optional[str] = None) -> Dict[str, Any]:
        """
        导出分类数据
        
        Args:
            export_format: 导出格式 ('tree', 'flat', 'paths')
            format_type: 格式化类型
            
        Returns:
            Dict: 导出的分类数据
        """
        try:
            # 验证导出格式
            valid_formats = ['tree', 'flat', 'paths']
            export_format = ValidationService.validate_choice(
                export_format, valid_formats, "导出格式"
            )
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            from api.models import Category
            from .category_dto import CategoryExporter
            
            # 获取所有分类
            all_categories = Category.objects.all().order_by('level', '-created_at')
            category_dtos = [CategoryDTO.from_model(cat) for cat in all_categories]
            
            # 根据导出格式处理数据
            if export_format == 'tree':
                root_categories = CategoryTreeBuilder.build_tree(category_dtos)
                exported_data = CategoryExporter.export_tree_structure(
                    root_categories, format_type
                )
            elif export_format == 'flat':
                exported_data = CategoryExporter.export_to_flat_list(
                    category_dtos, format_type
                )
            else:  # paths
                root_categories = CategoryTreeBuilder.build_tree(category_dtos)
                exported_data = CategoryExporter.export_category_paths(root_categories)
            
            return FormatService.format_export_data(
                exported_data, 
                'json', 
                f'categories_{export_format}'
            )
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"导出分类数据失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def import_categories(self, import_data: List[Dict[str, Any]], 
                         overwrite: bool = False) -> Dict[str, Any]:
        """
        导入分类数据
        
        Args:
            import_data: 导入的分类数据列表
            overwrite: 是否覆盖已存在的分类
            
        Returns:
            Dict: 导入结果
        """
        try:
            # 验证导入数据
            if not isinstance(import_data, list) or len(import_data) == 0:
                raise ValidationException("导入数据不能为空")
            
            success_count = 0
            failed_count = 0
            skipped_count = 0
            errors = []
            
            # 开始事务
            with transaction.atomic():
                from api.models import Category
                
                for item in import_data:
                    try:
                        category_id = item.get('id')
                        if not category_id:
                            errors.append({"error": "缺少分类ID", "item": item})
                            failed_count += 1
                            continue
                        
                        # 检查分类是否已存在
                        exists = Category.objects.filter(id=category_id).exists()
                        
                        if exists and not overwrite:
                            skipped_count += 1
                            continue
                        
                        # 验证并创建/更新分类
                        if exists and overwrite:
                            # 更新现有分类
                            self.update_category(category_id, item)
                        else:
                            # 创建新分类
                            self.create_category(item)
                        
                        success_count += 1
                        
                    except Exception as e:
                        errors.append({
                            "category_id": item.get('id'),
                            "error": str(e),
                            "item": item
                        })
                        failed_count += 1
            
            return {
                'success_count': success_count,
                'failed_count': failed_count,
                'skipped_count': skipped_count,
                'total_count': len(import_data),
                'errors': errors
            }
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"导入分类数据失败: {str(e)}")
            raise