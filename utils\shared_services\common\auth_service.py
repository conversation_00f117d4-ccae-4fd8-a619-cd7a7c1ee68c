"""
统一认证服务模块

提供管理端和用户端的统一认证处理，包括：
- 上下文创建和管理
- JWT令牌验证
- 权限验证
- 用户信息提取
"""

import jwt
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from django.conf import settings
from django.http import HttpRequest

from ..base.exceptions import (
    ServiceException,
    PermissionDeniedException,
    ValidationException
)


class AuthService:
    """
    统一认证服务类
    
    提供管理端和用户端的统一认证处理功能
    """
    
    # JWT配置
    ADMIN_JWT_SECRET_KEY = getattr(settings, 'ADMIN_JWT_SECRET_KEY', 'admin_jwt_secret_key_2025')
    USER_JWT_SECRET_KEY = getattr(settings, 'JWT_SECRET_KEY', 'user_jwt_secret_key')
    JWT_ALGORITHM = 'HS256'
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def create_context_from_admin_request(request: HttpRequest) -> Dict[str, Any]:
        """
        从管理端请求创建服务上下文
        
        Args:
            request: Django请求对象
            
        Returns:
            Dict: 服务上下文字典
            
        Raises:
            PermissionDeniedException: 认证失败时抛出
        """
        try:
            # 提取管理员令牌
            admin_token = AuthService._extract_admin_token(request)
            if not admin_token:
                raise PermissionDeniedException("未提供管理员认证令牌")
            
            # 验证管理员令牌
            is_valid, token_payload = AuthService._verify_admin_token(admin_token)
            if not is_valid:
                error_msg = token_payload.get('error', '管理员令牌验证失败')
                raise PermissionDeniedException(error_msg)
            
            # 构建管理端上下文
            context = {
                'request': request,
                'request_type': 'admin',
                'user_id': token_payload.get('admin_id'),
                'user': None,  # 管理端不使用Django用户对象
                'permissions': ['admin', 'read', 'write', 'delete'],  # 管理员拥有所有权限
                'token_payload': token_payload,
                'session_data': {
                    'admin_id': token_payload.get('admin_id'),
                    'admin_name': token_payload.get('admin_name', 'Administrator'),
                    'login_time': token_payload.get('iat'),
                    'token_type': token_payload.get('token_type')
                }
            }
            
            return context
            
        except PermissionDeniedException:
            raise
        except Exception as e:
            raise ServiceException(f"创建管理端上下文失败: {str(e)}")
    
    @staticmethod
    def create_context_from_user_request(request: HttpRequest) -> Dict[str, Any]:
        """
        从用户端请求创建服务上下文
        
        Args:
            request: Django请求对象
            
        Returns:
            Dict: 服务上下文字典
        """
        try:
            # 提取用户令牌
            user_token = AuthService._extract_user_token(request)
            
            context = {
                'request': request,
                'request_type': 'user',
                'user_id': None,
                'user': None,
                'permissions': ['read'],  # 默认只读权限
                'token_payload': None,
                'session_data': {}
            }
            
            # 如果有令牌，验证并提取用户信息
            if user_token:
                is_valid, token_result = AuthService._verify_user_token(user_token)
                if is_valid:
                    context.update({
                        'user_id': token_result.get('user_id'),
                        'permissions': ['read', 'write'],  # 已登录用户有读写权限
                        'token_payload': token_result,
                        'session_data': {
                            'user_id': token_result.get('user_id'),
                            'username': token_result.get('username'),
                            'user_key': token_result.get('user_key'),
                            'is_guest': token_result.get('guest', False),
                            'membership_level': token_result.get('membership_level', 'NormalUser'),
                            'login_time': token_result.get('iat')
                        }
                    })
                    
                    # 如果不是游客用户，尝试获取用户对象
                    if not token_result.get('guest', False):
                        try:
                            from user.models import User
                            user = User.objects.get(user_id=token_result.get('user_id'))
                            context['user'] = user
                        except Exception:
                            # 用户不存在或查询失败，保持为None
                            pass
            
            return context
            
        except Exception as e:
            # 用户端认证失败不抛出异常，返回匿名用户上下文
            return {
                'request': request,
                'request_type': 'user',
                'user_id': None,
                'user': None,
                'permissions': ['read'],  # 匿名用户只有读权限
                'token_payload': None,
                'session_data': {}
            }
    
    @staticmethod
    def _extract_admin_token(request: HttpRequest) -> Optional[str]:
        """
        提取管理员令牌
        
        Args:
            request: Django请求对象
            
        Returns:
            Optional[str]: 管理员令牌或None
        """
        # 优先从X-Admin-Access-Token头获取
        admin_token = request.META.get('HTTP_X_ADMIN_ACCESS_TOKEN')
        if admin_token:
            return admin_token
        
        # 从Authorization头获取
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header[7:]
        
        # 从Admin-Token头获取
        admin_token = request.META.get('HTTP_ADMIN_TOKEN')
        if admin_token:
            return admin_token
        
        # 从Cookie获取
        admin_token = request.COOKIES.get('admin_token')
        if admin_token:
            return admin_token
        
        return None
    
    @staticmethod
    def _extract_user_token(request: HttpRequest) -> Optional[str]:
        """
        提取用户令牌
        
        Args:
            request: Django请求对象
            
        Returns:
            Optional[str]: 用户令牌或None
        """
        # 从Token头获取
        user_token = request.headers.get('Token')
        if user_token:
            return user_token
        
        # 从Cookie获取
        user_token = request.COOKIES.get('token')
        if user_token:
            return user_token
        
        # 从GET/POST参数获取
        user_token = request.GET.get('token') or request.POST.get('token')
        if user_token:
            return user_token
        
        return None
    
    @staticmethod
    def _verify_admin_token(token: str) -> Tuple[bool, Dict[str, Any]]:
        """
        验证管理员JWT令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            Tuple[bool, Dict]: (是否有效, 令牌载荷或错误信息)
        """
        try:
            # 使用现有的AdminJWTManager进行验证
            from api.admin_auth import AdminJWTManager
            return AdminJWTManager.verify_admin_token(token)
            
        except Exception as e:
            return False, {'error': f'管理员令牌验证异常: {str(e)}'}
    
    @staticmethod
    def _verify_user_token(token: str) -> Tuple[bool, Dict[str, Any]]:
        """
        验证用户JWT令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            Tuple[bool, Dict]: (是否有效, 令牌载荷或错误信息)
        """
        try:
            # 解析用户JWT令牌
            payload = jwt.decode(
                token, 
                AuthService.USER_JWT_SECRET_KEY, 
                algorithms=[AuthService.JWT_ALGORITHM]
            )
            
            # 检查必要字段
            if 'user_id' not in payload:
                return False, {'error': '令牌缺少用户ID'}
            
            return True, payload
            
        except jwt.ExpiredSignatureError:
            return False, {'error': '用户令牌已过期'}
        except jwt.InvalidTokenError:
            return False, {'error': '无效的用户令牌'}
        except Exception as e:
            return False, {'error': f'用户令牌验证异常: {str(e)}'}
    
    @staticmethod
    def validate_admin_token(token: str) -> bool:
        """
        验证管理端JWT令牌（简化版本）
        
        Args:
            token: JWT令牌
            
        Returns:
            bool: 是否有效
        """
        is_valid, _ = AuthService._verify_admin_token(token)
        return is_valid
    
    @staticmethod
    def validate_user_token(token: str) -> bool:
        """
        验证用户端Token（简化版本）
        
        Args:
            token: JWT令牌
            
        Returns:
            bool: 是否有效
        """
        is_valid, _ = AuthService._verify_user_token(token)
        return is_valid
    
    @staticmethod
    def get_user_membership_level(context: Dict[str, Any]) -> str:
        """
        获取用户会员等级
        
        Args:
            context: 服务上下文
            
        Returns:
            str: 会员等级标识
        """
        try:
            # 从会话数据获取
            membership_level = context.get('session_data', {}).get('membership_level')
            if membership_level:
                return membership_level
            
            # 从用户对象获取
            user = context.get('user')
            if user and hasattr(user, 'membership_level'):
                return user.membership_level
            
            # 默认为普通用户
            return 'NormalUser'
            
        except Exception:
            return 'NormalUser'
    
    @staticmethod
    def is_authenticated_user(context: Dict[str, Any]) -> bool:
        """
        检查是否为已认证用户
        
        Args:
            context: 服务上下文
            
        Returns:
            bool: 是否已认证
        """
        return (
            context.get('user_id') is not None or
            context.get('user') is not None
        )
    
    @staticmethod
    def is_admin_context(context: Dict[str, Any]) -> bool:
        """
        检查是否为管理端上下文
        
        Args:
            context: 服务上下文
            
        Returns:
            bool: 是否为管理端
        """
        return context.get('request_type') == 'admin'
    
    @staticmethod
    def is_user_context(context: Dict[str, Any]) -> bool:
        """
        检查是否为用户端上下文
        
        Args:
            context: 服务上下文
            
        Returns:
            bool: 是否为用户端
        """
        return context.get('request_type') == 'user'
    
    @staticmethod
    def get_client_ip(request: HttpRequest) -> str:
        """
        获取客户端IP地址
        
        Args:
            request: Django请求对象
            
        Returns:
            str: 客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return ip
    
    @staticmethod
    def get_user_agent(request: HttpRequest) -> str:
        """
        获取用户代理字符串
        
        Args:
            request: Django请求对象
            
        Returns:
            str: 用户代理字符串
        """
        return request.META.get('HTTP_USER_AGENT', 'unknown')
    
    @staticmethod
    def create_anonymous_context(request: HttpRequest = None) -> Dict[str, Any]:
        """
        创建匿名用户上下文
        
        Args:
            request: Django请求对象（可选）
            
        Returns:
            Dict: 匿名用户上下文
        """
        return {
            'request': request,
            'request_type': 'user',
            'user_id': None,
            'user': None,
            'permissions': ['read'],  # 匿名用户只有读权限
            'token_payload': None,
            'session_data': {}
        }