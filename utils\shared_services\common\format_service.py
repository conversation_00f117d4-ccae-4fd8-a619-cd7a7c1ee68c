"""
格式化服务模块

提供管理端和用户端的数据格式化功能，包括：
- 响应数据格式化
- 日期时间格式化
- 数值格式化
- 文件大小格式化
- 数据脱敏处理
"""

import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
from decimal import Decimal


class FormatService:
    """
    数据格式化服务类
    
    提供各种数据格式化功能
    """
    
    @staticmethod
    def format_admin_response(data: Any, message: str = "操作成功", 
                            code: int = 200) -> Dict[str, Any]:
        """
        格式化管理端响应数据
        
        Args:
            data: 响应数据
            message: 响应消息
            code: 响应代码
            
        Returns:
            Dict: 格式化后的管理端响应
        """
        return {
            'code': code,
            'msg': message,
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'request_type': 'admin'
        }
    
    @staticmethod
    def format_user_response(data: Any, message: str = "操作成功", 
                           success: bool = True) -> Dict[str, Any]:
        """
        格式化用户端响应数据
        
        Args:
            data: 响应数据
            message: 响应消息
            success: 是否成功
            
        Returns:
            Dict: 格式化后的用户端响应
        """
        return {
            'success': success,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'request_type': 'user'
        }
    
    @staticmethod
    def format_error_response(message: str, code: int = -1, 
                            format_type: str = 'user') -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            message: 错误消息
            code: 错误代码
            format_type: 格式化类型 ('admin' or 'user')
            
        Returns:
            Dict: 格式化后的错误响应
        """
        if format_type == 'admin':
            return {
                'code': code,
                'msg': f'操作失败: {message}',
                'data': None,
                'timestamp': datetime.now().isoformat(),
                'request_type': 'admin'
            }
        else:
            return {
                'success': False,
                'message': f'操作失败: {message}',
                'data': None,
                'timestamp': datetime.now().isoformat(),
                'request_type': 'user'
            }
    
    @staticmethod
    def format_datetime(dt: Union[datetime, date, str, None], 
                       format_str: str = '%Y-%m-%d %H:%M:%S') -> Optional[str]:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_str: 格式化字符串
            
        Returns:
            Optional[str]: 格式化后的日期时间字符串
        """
        if dt is None:
            return None
        
        try:
            if isinstance(dt, str):
                # 尝试解析字符串
                if 'T' in dt:
                    dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
                else:
                    dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
            elif isinstance(dt, date) and not isinstance(dt, datetime):
                dt = datetime.combine(dt, datetime.min.time())
            
            return dt.strftime(format_str)
        except (ValueError, AttributeError):
            return str(dt) if dt else None
    
    @staticmethod
    def format_date(d: Union[date, datetime, str, None], 
                   format_str: str = '%Y-%m-%d') -> Optional[str]:
        """
        格式化日期
        
        Args:
            d: 日期对象
            format_str: 格式化字符串
            
        Returns:
            Optional[str]: 格式化后的日期字符串
        """
        if d is None:
            return None
        
        try:
            if isinstance(d, str):
                d = datetime.fromisoformat(d.split('T')[0]).date()
            elif isinstance(d, datetime):
                d = d.date()
            
            return d.strftime(format_str)
        except (ValueError, AttributeError):
            return str(d) if d else None
    
    @staticmethod
    def format_decimal(value: Union[Decimal, float, int, str, None], 
                      decimal_places: int = 2) -> Optional[str]:
        """
        格式化小数
        
        Args:
            value: 数值
            decimal_places: 小数位数
            
        Returns:
            Optional[str]: 格式化后的数值字符串
        """
        if value is None:
            return None
        
        try:
            decimal_value = Decimal(str(value))
            format_str = f"{{:.{decimal_places}f}}"
            return format_str.format(float(decimal_value))
        except (ValueError, TypeError):
            return str(value) if value is not None else None
    
    @staticmethod
    def format_currency(value: Union[Decimal, float, int, str, None], 
                       currency: str = '¥', decimal_places: int = 2) -> Optional[str]:
        """
        格式化货币
        
        Args:
            value: 数值
            currency: 货币符号
            decimal_places: 小数位数
            
        Returns:
            Optional[str]: 格式化后的货币字符串
        """
        if value is None:
            return None
        
        formatted_value = FormatService.format_decimal(value, decimal_places)
        if formatted_value:
            return f"{currency}{formatted_value}"
        return None
    
    @staticmethod
    def format_file_size(size_bytes: Union[int, str, None]) -> Optional[str]:
        """
        格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            Optional[str]: 格式化后的文件大小字符串
        """
        if size_bytes is None:
            return None
        
        try:
            size = int(size_bytes)
            if size == 0:
                return "0 B"
            
            units = ['B', 'KB', 'MB', 'GB', 'TB']
            unit_index = 0
            
            while size >= 1024 and unit_index < len(units) - 1:
                size /= 1024
                unit_index += 1
            
            if unit_index == 0:
                return f"{size} {units[unit_index]}"
            else:
                return f"{size:.1f} {units[unit_index]}"
        except (ValueError, TypeError):
            return str(size_bytes) if size_bytes is not None else None
    
    @staticmethod
    def format_percentage(value: Union[float, int, str, None], 
                         decimal_places: int = 1) -> Optional[str]:
        """
        格式化百分比
        
        Args:
            value: 数值（0-1之间或0-100之间）
            decimal_places: 小数位数
            
        Returns:
            Optional[str]: 格式化后的百分比字符串
        """
        if value is None:
            return None
        
        try:
            num_value = float(value)
            
            # 如果值在0-1之间，转换为百分比
            if 0 <= num_value <= 1:
                num_value *= 100
            
            format_str = f"{{:.{decimal_places}f}}%"
            return format_str.format(num_value)
        except (ValueError, TypeError):
            return str(value) if value is not None else None
    
    @staticmethod
    def format_phone(phone: Union[str, None], mask: bool = False) -> Optional[str]:
        """
        格式化手机号
        
        Args:
            phone: 手机号
            mask: 是否脱敏
            
        Returns:
            Optional[str]: 格式化后的手机号
        """
        if not phone:
            return None
        
        phone_str = str(phone).strip()
        
        # 中国手机号格式化
        if len(phone_str) == 11 and phone_str.isdigit():
            if mask:
                return f"{phone_str[:3]}****{phone_str[-4:]}"
            else:
                return f"{phone_str[:3]} {phone_str[3:7]} {phone_str[7:]}"
        
        return phone_str
    
    @staticmethod
    def format_email(email: Union[str, None], mask: bool = False) -> Optional[str]:
        """
        格式化邮箱
        
        Args:
            email: 邮箱地址
            mask: 是否脱敏
            
        Returns:
            Optional[str]: 格式化后的邮箱
        """
        if not email:
            return None
        
        email_str = str(email).strip().lower()
        
        if mask and '@' in email_str:
            local, domain = email_str.split('@', 1)
            if len(local) > 2:
                masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
            else:
                masked_local = local[0] + '*'
            return f"{masked_local}@{domain}"
        
        return email_str
    
    @staticmethod
    def format_id_card(id_card: Union[str, None], mask: bool = True) -> Optional[str]:
        """
        格式化身份证号
        
        Args:
            id_card: 身份证号
            mask: 是否脱敏
            
        Returns:
            Optional[str]: 格式化后的身份证号
        """
        if not id_card:
            return None
        
        id_str = str(id_card).strip()
        
        if mask and len(id_str) >= 8:
            return f"{id_str[:4]}**********{id_str[-4:]}"
        
        return id_str
    
    @staticmethod
    def format_list_data(data_list: List[Dict], format_type: str = 'user',
                        page: Optional[int] = None, 
                        page_size: Optional[int] = None,
                        total: Optional[int] = None) -> Dict[str, Any]:
        """
        格式化列表数据
        
        Args:
            data_list: 数据列表
            format_type: 格式化类型
            page: 当前页码
            page_size: 每页大小
            total: 总数量
            
        Returns:
            Dict: 格式化后的列表响应
        """
        result = {
            'list': data_list,
            'count': len(data_list)
        }
        
        # 添加分页信息
        if page is not None and page_size is not None:
            result.update({
                'page': page,
                'page_size': page_size,
                'has_next': total is not None and (page * page_size) < total,
                'has_prev': page > 1
            })
        
        if total is not None:
            result['total'] = total
            if page is not None and page_size is not None:
                result['total_pages'] = (total + page_size - 1) // page_size
        
        return result
    
    @staticmethod
    def format_category_tree(categories: List[Dict], 
                           format_type: str = 'user') -> List[Dict]:
        """
        格式化分类树结构
        
        Args:
            categories: 分类列表
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 格式化后的分类树
        """
        formatted_categories = []
        
        for category in categories:
            formatted_category = {
                'id': category.get('id'),
                'name': category.get('name'),
                'level': category.get('level'),
                'image': category.get('image') or '/static/images/category-placeholder.svg',
                'parent_id': category.get('parent_id') or '',
            }
            
            # 管理端包含更多信息
            if format_type == 'admin':
                formatted_category.update({
                    'created_at': FormatService.format_datetime(category.get('created_at')),
                    'updated_at': FormatService.format_datetime(category.get('updated_at')),
                })
            
            # 处理子分类
            if 'children' in category and category['children']:
                formatted_category['children'] = FormatService.format_category_tree(
                    category['children'], format_type
                )
            else:
                formatted_category['children'] = []
            
            formatted_categories.append(formatted_category)
        
        return formatted_categories
    
    @staticmethod
    def format_product_data(product: Dict, format_type: str = 'user',
                          user_membership: str = 'NormalUser') -> Dict[str, Any]:
        """
        格式化商品数据
        
        Args:
            product: 商品数据
            format_type: 格式化类型
            user_membership: 用户会员等级
            
        Returns:
            Dict: 格式化后的商品数据
        """
        formatted_product = {
            'id': product.get('id'),
            'name': product.get('name'),
            'price': FormatService.format_currency(product.get('price')),
            'image': product.get('image') or '/static/images/product-placeholder.svg',
            'status': product.get('status', '1'),
            'stock': product.get('stock', 0),
        }
        
        # 计算实际价格（会员价格）
        if 'actual_price' in product:
            formatted_product['actual_price'] = FormatService.format_currency(
                product['actual_price']
            )
        
        # 用户端格式
        if format_type == 'user':
            formatted_product.update({
                'description': product.get('info', ''),
                'category_id': product.get('category_id'),
            })
            
            # 处理商品参数
            if 'attach_data' in product and product['attach_data']:
                formatted_product['params'] = product['attach_data']
        
        # 管理端格式
        elif format_type == 'admin':
            formatted_product.update({
                'info': product.get('info', ''),
                'category_id': product.get('category_id'),
                'type': product.get('type', '1'),
                'price_template': product.get('price_template'),
                'created_at': FormatService.format_datetime(product.get('created_at')),
                'updated_at': FormatService.format_datetime(product.get('updated_at')),
            })
            
            # 管理端显示原始附加数据
            if 'attach' in product:
                formatted_product['attach'] = product['attach']
        
        return formatted_product
    
    @staticmethod
    def format_order_data(order: Dict, format_type: str = 'user') -> Dict[str, Any]:
        """
        格式化订单数据
        
        Args:
            order: 订单数据
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的订单数据
        """
        # 解析订单数据JSON
        order_data = order.get('data', {})
        if isinstance(order_data, str):
            try:
                order_data = json.loads(order_data)
            except json.JSONDecodeError:
                order_data = {}
        
        formatted_order = {
            'id': order.get('id'),
            'status': order_data.get('status', 'unknown'),
            'created_at': FormatService.format_datetime(order.get('created_at')),
        }
        
        # 商品信息
        product_info = order_data.get('product', {})
        if product_info:
            formatted_order['product'] = {
                'name': product_info.get('name', '未知商品'),
                'id': product_info.get('id'),
                'image': product_info.get('image')
            }
        
        # 价格信息
        price_info = order_data.get('price', {})
        if price_info:
            formatted_order['price'] = {
                'original_price': FormatService.format_currency(price_info.get('original_price')),
                'final_price': FormatService.format_currency(price_info.get('final_price')),
            }
        
        # 用户端格式
        if format_type == 'user':
            # 状态映射
            status_mapping = {
                'failed': '失败',
                'pending': '待付款',
                'processing': '处理中',
                'completed': '已完成',
                'canceled': '已取消',
                'refunded': '已退款'
            }
            formatted_order['status_text'] = status_mapping.get(
                formatted_order['status'], '未知状态'
            )
        
        # 管理端格式
        elif format_type == 'admin':
            formatted_order.update({
                'user_id': order_data.get('user_id'),
                'payment_info': order_data.get('payment', {}),
                'raw_data': order_data
            })
        
        return formatted_order
    
    @staticmethod
    def sanitize_data(data: Any, sensitive_fields: List[str] = None) -> Any:
        """
        数据脱敏处理
        
        Args:
            data: 要处理的数据
            sensitive_fields: 敏感字段列表
            
        Returns:
            Any: 脱敏后的数据
        """
        if sensitive_fields is None:
            sensitive_fields = ['password', 'token', 'secret', 'key', 'auth']
        
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                if any(sensitive_field in key.lower() for sensitive_field in sensitive_fields):
                    sanitized[key] = '***'
                else:
                    sanitized[key] = FormatService.sanitize_data(value, sensitive_fields)
            return sanitized
        
        elif isinstance(data, list):
            return [FormatService.sanitize_data(item, sensitive_fields) for item in data]
        
        else:
            return data   
 
    @staticmethod
    def format_user_data(user: Dict, format_type: str = 'user', 
                        mask_sensitive: bool = True) -> Dict[str, Any]:
        """
        格式化用户数据
        
        Args:
            user: 用户数据
            format_type: 格式化类型
            mask_sensitive: 是否脱敏敏感信息
            
        Returns:
            Dict: 格式化后的用户数据
        """
        formatted_user = {
            'user_id': user.get('user_id'),
            'username': user.get('username'),
            'membership_level': user.get('membership_level', 'NormalUser'),
            'created_at': FormatService.format_datetime(user.get('created_at')),
        }
        
        # 处理敏感信息
        if 'email' in user:
            formatted_user['email'] = FormatService.format_email(
                user['email'], mask=mask_sensitive
            )
        
        if 'phone' in user:
            formatted_user['phone'] = FormatService.format_phone(
                user['phone'], mask=mask_sensitive
            )
        
        # 管理端显示更多信息
        if format_type == 'admin':
            formatted_user.update({
                'status': user.get('status', 'active'),
                'last_login': FormatService.format_datetime(user.get('last_login')),
                'updated_at': FormatService.format_datetime(user.get('updated_at')),
            })
            
            # 管理端可以选择不脱敏
            if not mask_sensitive:
                formatted_user['email'] = user.get('email')
                formatted_user['phone'] = user.get('phone')
        
        return formatted_user
    
    @staticmethod
    def format_payment_data(payment: Dict, format_type: str = 'user') -> Dict[str, Any]:
        """
        格式化支付数据
        
        Args:
            payment: 支付数据
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的支付数据
        """
        formatted_payment = {
            'method': payment.get('method'),
            'name': payment.get('name'),
            'status': payment.get('status', 'active'),
        }
        
        # 用户端格式
        if format_type == 'user':
            formatted_payment.update({
                'icon': payment.get('icon'),
                'description': payment.get('description'),
                'is_available': payment.get('status') == 'active'
            })
        
        # 管理端格式
        elif format_type == 'admin':
            formatted_payment.update({
                'config': payment.get('config', {}),
                'created_at': FormatService.format_datetime(payment.get('created_at')),
                'updated_at': FormatService.format_datetime(payment.get('updated_at')),
            })
        
        return formatted_payment
    
    @staticmethod
    def format_api_response(data: Any, format_type: str = 'user', 
                          success: bool = True, message: str = None,
                          error_code: int = None) -> Dict[str, Any]:
        """
        统一格式化API响应
        
        Args:
            data: 响应数据
            format_type: 格式化类型
            success: 是否成功
            message: 响应消息
            error_code: 错误代码
            
        Returns:
            Dict: 格式化后的API响应
        """
        if success:
            if format_type == 'admin':
                return FormatService.format_admin_response(
                    data, 
                    message or "操作成功"
                )
            else:
                return FormatService.format_user_response(
                    data, 
                    message or "操作成功"
                )
        else:
            return FormatService.format_error_response(
                message or "操作失败",
                error_code or -1,
                format_type
            )
    
    @staticmethod
    def format_search_results(results: List[Dict], query: str,
                            format_type: str = 'user',
                            highlight_fields: List[str] = None) -> Dict[str, Any]:
        """
        格式化搜索结果
        
        Args:
            results: 搜索结果列表
            query: 搜索查询
            format_type: 格式化类型
            highlight_fields: 需要高亮的字段
            
        Returns:
            Dict: 格式化后的搜索结果
        """
        if highlight_fields is None:
            highlight_fields = ['name', 'title', 'description']
        
        formatted_results = []
        
        for result in results:
            formatted_result = dict(result)
            
            # 高亮搜索关键词
            if query:
                for field in highlight_fields:
                    if field in formatted_result and formatted_result[field]:
                        text = str(formatted_result[field])
                        highlighted_text = text.replace(
                            query, 
                            f"<mark>{query}</mark>"
                        )
                        formatted_result[f"{field}_highlighted"] = highlighted_text
            
            formatted_results.append(formatted_result)
        
        return {
            'results': formatted_results,
            'query': query,
            'count': len(formatted_results),
            'has_results': len(formatted_results) > 0
        }
    
    @staticmethod
    def format_statistics_data(stats: Dict, format_type: str = 'admin') -> Dict[str, Any]:
        """
        格式化统计数据
        
        Args:
            stats: 统计数据
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的统计数据
        """
        formatted_stats = {}
        
        for key, value in stats.items():
            if isinstance(value, (int, float)):
                # 数值类型格式化
                if 'rate' in key.lower() or 'percent' in key.lower():
                    formatted_stats[key] = FormatService.format_percentage(value)
                elif 'amount' in key.lower() or 'price' in key.lower():
                    formatted_stats[key] = FormatService.format_currency(value)
                else:
                    formatted_stats[key] = f"{value:,}"  # 千分位分隔符
            elif isinstance(value, datetime):
                formatted_stats[key] = FormatService.format_datetime(value)
            else:
                formatted_stats[key] = value
        
        return formatted_stats
    
    @staticmethod
    def format_export_data(data: List[Dict], export_format: str = 'json',
                          filename: str = None) -> Dict[str, Any]:
        """
        格式化导出数据
        
        Args:
            data: 要导出的数据
            export_format: 导出格式 ('json', 'csv', 'excel')
            filename: 文件名
            
        Returns:
            Dict: 格式化后的导出数据信息
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"export_{timestamp}.{export_format}"
        
        return {
            'data': data,
            'format': export_format,
            'filename': filename,
            'count': len(data),
            'generated_at': datetime.now().isoformat(),
            'size_estimate': len(json.dumps(data)) if export_format == 'json' else None
        }
    
    @staticmethod
    def format_validation_errors(errors: Dict[str, str]) -> Dict[str, Any]:
        """
        格式化验证错误信息
        
        Args:
            errors: 验证错误字典
            
        Returns:
            Dict: 格式化后的错误信息
        """
        return {
            'validation_errors': errors,
            'error_count': len(errors),
            'fields_with_errors': list(errors.keys()),
            'first_error': next(iter(errors.values())) if errors else None
        }
    
    @staticmethod
    def format_cache_info(cache_key: str, hit: bool, ttl: int = None) -> Dict[str, Any]:
        """
        格式化缓存信息
        
        Args:
            cache_key: 缓存键
            hit: 是否命中缓存
            ttl: 缓存过期时间
            
        Returns:
            Dict: 格式化后的缓存信息
        """
        return {
            'cache_key': cache_key,
            'cache_hit': hit,
            'cache_status': 'HIT' if hit else 'MISS',
            'ttl': ttl,
            'timestamp': datetime.now().isoformat()
        }
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 100, 
                     suffix: str = '...') -> str:
        """
        截断文本
        
        Args:
            text: 要截断的文本
            max_length: 最大长度
            suffix: 截断后缀
            
        Returns:
            str: 截断后的文本
        """
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def format_relative_time(dt: Union[datetime, str], base_time: datetime = None) -> str:
        """
        格式化相对时间
        
        Args:
            dt: 目标时间
            base_time: 基准时间，默认为当前时间
            
        Returns:
            str: 相对时间描述
        """
        if base_time is None:
            base_time = datetime.now()
        
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except ValueError:
                return str(dt)
        
        if not isinstance(dt, datetime):
            return str(dt)
        
        diff = base_time - dt
        
        if diff.days > 0:
            if diff.days == 1:
                return "1天前"
            elif diff.days < 30:
                return f"{diff.days}天前"
            elif diff.days < 365:
                months = diff.days // 30
                return f"{months}个月前"
            else:
                years = diff.days // 365
                return f"{years}年前"
        
        seconds = diff.seconds
        if seconds < 60:
            return "刚刚"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}分钟前"
        else:
            hours = seconds // 3600
            return f"{hours}小时前"