
import re
import json
import uuid
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, date
from decimal import Decimal, InvalidOperation

from ..base.exceptions import ValidationException


class ValidationService:
    """
    数据验证服务类
    
    提供各种数据验证功能和验证规则
    """
    
    # 常用正则表达式
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    PHONE_PATTERN = re.compile(r'^1[3-9]\d{9}$')  # 中国手机号
    ID_CARD_PATTERN = re.compile(r'^\d{17}[\dXx]$')  # 身份证号
    URL_PATTERN = re.compile(r'^https?://[^\s/$.?#].[^\s]*$')
    
    # 业务相关验证规则
    CATEGORY_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{1,50}$')
    PRODUCT_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{1,50}$')
    ORDER_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{1,50}$')
    USER_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{1,50}$')
    
    @staticmethod
    def validate_required(value: Any, field_name: str = "字段") -> Any:
        """
        验证必填字段
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            Any: 验证通过的值
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if value is None or value == "" or (isinstance(value, (list, dict)) and len(value) == 0):
            raise ValidationException(f"{field_name}不能为空", field=field_name)
        return value
    
    @staticmethod
    def validate_string(value: Any, field_name: str = "字符串", 
                       min_length: Optional[int] = None, 
                       max_length: Optional[int] = None,
                       pattern: Optional[str] = None) -> str:
        """
        验证字符串
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            min_length: 最小长度
            max_length: 最大长度
            pattern: 正则表达式模式
            
        Returns:
            str: 验证通过的字符串
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, str):
            raise ValidationException(f"{field_name}必须是字符串类型", field=field_name)
        
        # 长度验证
        if min_length is not None and len(value) < min_length:
            raise ValidationException(f"{field_name}长度不能少于{min_length}个字符", field=field_name)
        
        if max_length is not None and len(value) > max_length:
            raise ValidationException(f"{field_name}长度不能超过{max_length}个字符", field=field_name)
        
        # 正则表达式验证
        if pattern and not re.match(pattern, value):
            raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        return value
    
    @staticmethod
    def validate_integer(value: Any, field_name: str = "整数",
                        min_value: Optional[int] = None,
                        max_value: Optional[int] = None) -> int:
        """
        验证整数
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            min_value: 最小值
            max_value: 最大值
            
        Returns:
            int: 验证通过的整数
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationException(f"{field_name}必须是整数", field=field_name)
        
        # 范围验证
        if min_value is not None and int_value < min_value:
            raise ValidationException(f"{field_name}不能小于{min_value}", field=field_name)
        
        if max_value is not None and int_value > max_value:
            raise ValidationException(f"{field_name}不能大于{max_value}", field=field_name)
        
        return int_value
    
    @staticmethod
    def validate_decimal(value: Any, field_name: str = "数值",
                        min_value: Optional[Decimal] = None,
                        max_value: Optional[Decimal] = None,
                        decimal_places: Optional[int] = None) -> Decimal:
        """
        验证小数
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            min_value: 最小值
            max_value: 最大值
            decimal_places: 小数位数
            
        Returns:
            Decimal: 验证通过的小数
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        try:
            decimal_value = Decimal(str(value))
        except (InvalidOperation, ValueError, TypeError):
            raise ValidationException(f"{field_name}必须是有效的数值", field=field_name)
        
        # 范围验证
        if min_value is not None and decimal_value < min_value:
            raise ValidationException(f"{field_name}不能小于{min_value}", field=field_name)
        
        if max_value is not None and decimal_value > max_value:
            raise ValidationException(f"{field_name}不能大于{max_value}", field=field_name)
        
        # 小数位数验证
        if decimal_places is not None:
            if decimal_value.as_tuple().exponent < -decimal_places:
                raise ValidationException(f"{field_name}小数位数不能超过{decimal_places}位", field=field_name)
        
        return decimal_value
    
    @staticmethod
    def validate_boolean(value: Any, field_name: str = "布尔值") -> bool:
        """
        验证布尔值
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            bool: 验证通过的布尔值
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            if value.lower() in ('true', '1', 'yes', 'on'):
                return True
            elif value.lower() in ('false', '0', 'no', 'off'):
                return False
        
        if isinstance(value, int):
            if value in (0, 1):
                return bool(value)
        
        raise ValidationException(f"{field_name}必须是布尔值", field=field_name)
    
    @staticmethod
    def validate_email(value: Any, field_name: str = "邮箱") -> str:
        """
        验证邮箱地址
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的邮箱地址
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, str):
            raise ValidationException(f"{field_name}必须是字符串", field=field_name)
        
        if not ValidationService.EMAIL_PATTERN.match(value):
            raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        return value.lower()
    
    @staticmethod
    def validate_phone(value: Any, field_name: str = "手机号") -> str:
        """
        验证手机号
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的手机号
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, str):
            value = str(value)
        
        if not ValidationService.PHONE_PATTERN.match(value):
            raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        return value
    
    @staticmethod
    def validate_url(value: Any, field_name: str = "URL") -> str:
        """
        验证URL
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的URL
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, str):
            raise ValidationException(f"{field_name}必须是字符串", field=field_name)
        
        if not ValidationService.URL_PATTERN.match(value):
            raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        return value
    
    @staticmethod
    def validate_uuid(value: Any, field_name: str = "UUID") -> str:
        """
        验证UUID
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的UUID字符串
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, str):
            value = str(value)
        
        try:
            uuid.UUID(value)
        except ValueError:
            raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        return value
    
    @staticmethod
    def validate_json(value: Any, field_name: str = "JSON") -> Union[Dict, List]:
        """
        验证JSON格式
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            Union[Dict, List]: 解析后的JSON对象
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if isinstance(value, (dict, list)):
            return value
        
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValidationException(f"{field_name}不是有效的JSON格式", field=field_name)
        
        raise ValidationException(f"{field_name}必须是JSON格式", field=field_name)
    
    @staticmethod
    def validate_choice(value: Any, choices: List[Any], field_name: str = "选项") -> Any:
        """
        验证选择项
        
        Args:
            value: 要验证的值
            choices: 可选择的值列表
            field_name: 字段名称
            
        Returns:
            Any: 验证通过的值
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if value not in choices:
            raise ValidationException(f"{field_name}必须是以下值之一: {choices}", field=field_name)
        
        return value
    
    @staticmethod
    def validate_date(value: Any, field_name: str = "日期") -> date:
        """
        验证日期
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            date: 验证通过的日期对象
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if isinstance(value, date):
            return value
        
        if isinstance(value, datetime):
            return value.date()
        
        if isinstance(value, str):
            try:
                return datetime.strptime(value, '%Y-%m-%d').date()
            except ValueError:
                try:
                    return datetime.fromisoformat(value).date()
                except ValueError:
                    raise ValidationException(f"{field_name}格式不正确，应为YYYY-MM-DD", field=field_name)
        
        raise ValidationException(f"{field_name}必须是日期类型", field=field_name)
    
    @staticmethod
    def validate_datetime(value: Any, field_name: str = "日期时间") -> datetime:
        """
        验证日期时间
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            datetime: 验证通过的日期时间对象
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if isinstance(value, datetime):
            return value
        
        if isinstance(value, str):
            try:
                return datetime.fromisoformat(value)
            except ValueError:
                try:
                    return datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    raise ValidationException(f"{field_name}格式不正确", field=field_name)
        
        raise ValidationException(f"{field_name}必须是日期时间类型", field=field_name)
    
    @staticmethod
    def validate_list(value: Any, field_name: str = "列表",
                     min_length: Optional[int] = None,
                     max_length: Optional[int] = None,
                     item_validator: Optional[Callable] = None) -> List:
        """
        验证列表
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            min_length: 最小长度
            max_length: 最大长度
            item_validator: 列表项验证器
            
        Returns:
            List: 验证通过的列表
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, list):
            raise ValidationException(f"{field_name}必须是列表类型", field=field_name)
        
        # 长度验证
        if min_length is not None and len(value) < min_length:
            raise ValidationException(f"{field_name}长度不能少于{min_length}项", field=field_name)
        
        if max_length is not None and len(value) > max_length:
            raise ValidationException(f"{field_name}长度不能超过{max_length}项", field=field_name)
        
        # 列表项验证
        if item_validator:
            validated_items = []
            for i, item in enumerate(value):
                try:
                    validated_items.append(item_validator(item))
                except ValidationException as e:
                    raise ValidationException(f"{field_name}第{i+1}项{e.message}", field=field_name)
            return validated_items
        
        return value
    
    @staticmethod
    def validate_dict(value: Any, field_name: str = "字典",
                     required_keys: Optional[List[str]] = None,
                     key_validators: Optional[Dict[str, Callable]] = None) -> Dict:
        """
        验证字典
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            required_keys: 必需的键列表
            key_validators: 键值验证器字典
            
        Returns:
            Dict: 验证通过的字典
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(value, dict):
            raise ValidationException(f"{field_name}必须是字典类型", field=field_name)
        
        # 必需键验证
        if required_keys:
            missing_keys = [key for key in required_keys if key not in value]
            if missing_keys:
                raise ValidationException(f"{field_name}缺少必需的键: {missing_keys}", field=field_name)
        
        # 键值验证
        if key_validators:
            validated_dict = {}
            for key, validator in key_validators.items():
                if key in value:
                    try:
                        validated_dict[key] = validator(value[key])
                    except ValidationException as e:
                        raise ValidationException(f"{field_name}.{key}: {e.message}", field=field_name)
                else:
                    validated_dict[key] = value.get(key)
            
            # 添加未验证的键
            for key, val in value.items():
                if key not in key_validators:
                    validated_dict[key] = val
            
            return validated_dict
        
        return value
    
    @staticmethod
    def validate_data(data: Dict[str, Any], validators: Dict[str, Callable]) -> Dict[str, Any]:
        """
        批量验证数据
        
        Args:
            data: 要验证的数据字典
            validators: 验证器字典
            
        Returns:
            Dict: 验证通过的数据字典
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        validated_data = {}
        validation_errors = {}
        
        for field_name, validator in validators.items():
            try:
                if field_name in data:
                    validated_data[field_name] = validator(data[field_name])
                else:
                    # 尝试调用验证器，可能会抛出必填验证异常
                    validated_data[field_name] = validator(None)
            except ValidationException as e:
                validation_errors[field_name] = e.message
        
        if validation_errors:
            raise ValidationException(
                "数据验证失败",
                validation_errors=validation_errors
            )
        
        return validated_data
    
    @staticmethod
    def create_validator_chain(*validators: Callable) -> Callable:
        """
        创建验证器链
        
        Args:
            *validators: 验证器函数列表
            
        Returns:
            Callable: 组合验证器函数
        """
        def combined_validator(value):
            result = value
            for validator in validators:
                result = validator(result)
            return result
        
        return combined_validator   
 
    # 业务特定验证方法
    
    @staticmethod
    def validate_category_id(value: Any, field_name: str = "分类ID") -> str:
        """
        验证分类ID
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的分类ID
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        ValidationService.validate_required(value, field_name)
        value_str = ValidationService.validate_string(value, field_name, min_length=1, max_length=50)
        
        if not ValidationService.CATEGORY_ID_PATTERN.match(value_str):
            raise ValidationException(f"{field_name}只能包含字母、数字、下划线和连字符", field=field_name)
        
        return value_str
    
    @staticmethod
    def validate_product_id(value: Any, field_name: str = "商品ID") -> str:
        """
        验证商品ID
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的商品ID
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        ValidationService.validate_required(value, field_name)
        value_str = ValidationService.validate_string(value, field_name, min_length=1, max_length=50)
        
        if not ValidationService.PRODUCT_ID_PATTERN.match(value_str):
            raise ValidationException(f"{field_name}只能包含字母、数字、下划线和连字符", field=field_name)
        
        return value_str
    
    @staticmethod
    def validate_order_id(value: Any, field_name: str = "订单ID") -> str:
        """
        验证订单ID
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的订单ID
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        ValidationService.validate_required(value, field_name)
        value_str = ValidationService.validate_string(value, field_name, min_length=1, max_length=50)
        
        if not ValidationService.ORDER_ID_PATTERN.match(value_str):
            raise ValidationException(f"{field_name}只能包含字母、数字、下划线和连字符", field=field_name)
        
        return value_str
    
    @staticmethod
    def validate_user_id(value: Any, field_name: str = "用户ID") -> str:
        """
        验证用户ID
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的用户ID
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        ValidationService.validate_required(value, field_name)
        value_str = ValidationService.validate_string(value, field_name, min_length=1, max_length=50)
        
        if not ValidationService.USER_ID_PATTERN.match(value_str):
            raise ValidationException(f"{field_name}只能包含字母、数字、下划线和连字符", field=field_name)
        
        return value_str
    
    @staticmethod
    def validate_price(value: Any, field_name: str = "价格") -> Decimal:
        """
        验证价格
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            Decimal: 验证通过的价格
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        price = ValidationService.validate_decimal(
            value, 
            field_name, 
            min_value=Decimal('0'), 
            decimal_places=2
        )
        
        if price < 0:
            raise ValidationException(f"{field_name}不能为负数", field=field_name)
        
        return price
    
    @staticmethod
    def validate_stock(value: Any, field_name: str = "库存") -> int:
        """
        验证库存数量
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            int: 验证通过的库存数量
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        stock = ValidationService.validate_integer(value, field_name, min_value=0)
        return stock
    
    @staticmethod
    def validate_status(value: Any, field_name: str = "状态") -> str:
        """
        验证状态值
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的状态值
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        # 常见状态值
        valid_statuses = ['0', '1', 'active', 'inactive', 'enabled', 'disabled', 'pending', 'approved', 'rejected']
        return ValidationService.validate_choice(value, valid_statuses, field_name)
    
    @staticmethod
    def validate_membership_level(value: Any, field_name: str = "会员等级") -> str:
        """
        验证会员等级
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            str: 验证通过的会员等级
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        valid_levels = ['NormalUser', 'VipUser', 'SuperVip', 'Guest']
        return ValidationService.validate_choice(value, valid_levels, field_name)
    
    @staticmethod
    def validate_pagination(page: Any = 1, page_size: Any = 20) -> Dict[str, int]:
        """
        验证分页参数
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            Dict: 验证通过的分页参数
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        validated_page = ValidationService.validate_integer(page, "页码", min_value=1)
        validated_page_size = ValidationService.validate_integer(page_size, "每页大小", min_value=1, max_value=100)
        
        return {
            'page': validated_page,
            'page_size': validated_page_size,
            'offset': (validated_page - 1) * validated_page_size
        }
    
    @staticmethod
    def validate_sort_params(sort_field: Any = None, sort_order: Any = 'asc') -> Dict[str, str]:
        """
        验证排序参数
        
        Args:
            sort_field: 排序字段
            sort_order: 排序顺序
            
        Returns:
            Dict: 验证通过的排序参数
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        result = {}
        
        if sort_field is not None:
            result['sort_field'] = ValidationService.validate_string(sort_field, "排序字段", min_length=1, max_length=50)
        
        if sort_order is not None:
            result['sort_order'] = ValidationService.validate_choice(sort_order, ['asc', 'desc'], "排序顺序")
        
        return result
    
    @staticmethod
    def validate_filter_params(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证过滤参数
        
        Args:
            filters: 过滤参数字典
            
        Returns:
            Dict: 验证通过的过滤参数
            
        Raises:
            ValidationException: 验证失败时抛出
        """
        if not isinstance(filters, dict):
            raise ValidationException("过滤参数必须是字典类型", field="filters")
        
        validated_filters = {}
        
        for key, value in filters.items():
            # 验证过滤字段名
            field_name = ValidationService.validate_string(key, "过滤字段名", min_length=1, max_length=50)
            
            # 过滤空值
            if value is not None and value != "":
                validated_filters[field_name] = value
        
        return validated_filters
    
    # 复合验证器工厂方法
    
    @staticmethod
    def create_category_validator() -> Callable:
        """
        创建分类数据验证器
        
        Returns:
            Callable: 分类验证器函数
        """
        def validate_category_data(data: Dict[str, Any]) -> Dict[str, Any]:
            validators = {
                'name': lambda x: ValidationService.validate_string(
                    ValidationService.validate_required(x, "分类名称"), 
                    "分类名称", 
                    min_length=1, 
                    max_length=100
                ),
                'level': lambda x: ValidationService.validate_integer(x, "分类层级", min_value=1, max_value=5),
                'parent_id': lambda x: ValidationService.validate_category_id(x, "父分类ID") if x else None,
                'image': lambda x: ValidationService.validate_url(x, "分类图片") if x else None,
                'status': lambda x: ValidationService.validate_status(x, "分类状态") if x else '1'
            }
            
            return ValidationService.validate_data(data, validators)
        
        return validate_category_data
    
    @staticmethod
    def create_product_validator() -> Callable:
        """
        创建商品数据验证器
        
        Returns:
            Callable: 商品验证器函数
        """
        def validate_product_data(data: Dict[str, Any]) -> Dict[str, Any]:
            validators = {
                'name': lambda x: ValidationService.validate_string(
                    ValidationService.validate_required(x, "商品名称"), 
                    "商品名称", 
                    min_length=1, 
                    max_length=200
                ),
                'price': lambda x: ValidationService.validate_price(
                    ValidationService.validate_required(x, "商品价格"), 
                    "商品价格"
                ),
                'stock': lambda x: ValidationService.validate_stock(x, "库存数量") if x is not None else 0,
                'category_id': lambda x: ValidationService.validate_category_id(x, "分类ID") if x else None,
                'status': lambda x: ValidationService.validate_status(x, "商品状态") if x else '1',
                'description': lambda x: ValidationService.validate_string(x, "商品描述", max_length=1000) if x else None,
                'image': lambda x: ValidationService.validate_url(x, "商品图片") if x else None
            }
            
            return ValidationService.validate_data(data, validators)
        
        return validate_product_data
    
    @staticmethod
    def create_order_validator() -> Callable:
        """
        创建订单数据验证器
        
        Returns:
            Callable: 订单验证器函数
        """
        def validate_order_data(data: Dict[str, Any]) -> Dict[str, Any]:
            validators = {
                'user_id': lambda x: ValidationService.validate_user_id(
                    ValidationService.validate_required(x, "用户ID"), 
                    "用户ID"
                ),
                'total_amount': lambda x: ValidationService.validate_price(
                    ValidationService.validate_required(x, "订单总金额"), 
                    "订单总金额"
                ),
                'status': lambda x: ValidationService.validate_choice(
                    x if x else 'pending', 
                    ['pending', 'paid', 'shipped', 'completed', 'cancelled'], 
                    "订单状态"
                ),
                'items': lambda x: ValidationService.validate_list(
                    ValidationService.validate_required(x, "订单商品"), 
                    "订单商品", 
                    min_length=1
                )
            }
            
            return ValidationService.validate_data(data, validators)
        
        return validate_order_data
    
    @staticmethod
    def create_user_validator() -> Callable:
        """
        创建用户数据验证器
        
        Returns:
            Callable: 用户验证器函数
        """
        def validate_user_data(data: Dict[str, Any]) -> Dict[str, Any]:
            validators = {
                'username': lambda x: ValidationService.validate_string(
                    ValidationService.validate_required(x, "用户名"), 
                    "用户名", 
                    min_length=3, 
                    max_length=50,
                    pattern=r'^[a-zA-Z0-9_]+$'
                ),
                'email': lambda x: ValidationService.validate_email(x, "邮箱") if x else None,
                'phone': lambda x: ValidationService.validate_phone(x, "手机号") if x else None,
                'membership_level': lambda x: ValidationService.validate_membership_level(
                    x if x else 'NormalUser', 
                    "会员等级"
                )
            }
            
            return ValidationService.validate_data(data, validators)
        
        return validate_user_data