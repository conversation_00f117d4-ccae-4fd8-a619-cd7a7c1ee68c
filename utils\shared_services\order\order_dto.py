"""
订单数据传输对象 (OrderDTO)

定义订单数据的结构化表示，支持：
- 不同权限级别的数据访问控制
- 数据格式化和验证
- 订单状态管理
- 多种输出格式支持
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import json


@dataclass
class OrderDTO:
    """
    订单数据传输对象
    
    用于在不同层之间传递订单数据，支持权限控制和格式化
    """
    
    # 基本字段
    id: str
    user_id: str
    order_no: str
    status: str
    total_amount: Decimal
    created_at: datetime
    
    # 可选字段
    updated_at: Optional[datetime] = None
    payment_method: Optional[str] = None
    payment_status: Optional[str] = None
    shipping_address: Optional[str] = None
    shipping_method: Optional[str] = None
    notes: Optional[str] = None
    data: Optional[str] = None  # JSON格式的订单详细数据
    
    # 扩展字段
    items: List[Dict[str, Any]] = field(default_factory=list)
    user_info: Optional[Dict[str, Any]] = None
    payment_info: Optional[Dict[str, Any]] = None
    
    def to_dict(self, permission_level: str = 'user') -> Dict[str, Any]:
        """
        转换为字典格式
        
        Args:
            permission_level: 权限级别 ('user', 'admin', 'system')
            
        Returns:
            Dict[str, Any]: 格式化后的订单数据
        """
        base_data = {
            'id': self.id,
            'order_no': self.order_no,
            'status': self.status,
            'total_amount': str(self.total_amount),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
        
        # 用户级别数据
        if permission_level in ['user', 'admin', 'system']:
            base_data.update({
                'payment_method': self.payment_method,
                'payment_status': self.payment_status,
                'shipping_address': self.shipping_address,
                'shipping_method': self.shipping_method,
                'notes': self.notes,
                'items': self.items
            })
        
        # 管理员级别数据
        if permission_level in ['admin', 'system']:
            base_data.update({
                'user_id': self.user_id,
                'user_info': self.user_info,
                'payment_info': self.payment_info
            })
            
            # 解析订单详细数据
            if self.data:
                try:
                    detailed_data = json.loads(self.data)
                    base_data['detailed_data'] = detailed_data
                except json.JSONDecodeError:
                    base_data['detailed_data'] = {}
        
        # 系统级别数据（包含所有字段）
        if permission_level == 'system':
            base_data['raw_data'] = self.data
        
        return base_data
    
    def to_admin_format(self) -> Dict[str, Any]:
        """
        转换为管理端格式
        
        Returns:
            Dict[str, Any]: 管理端格式的订单数据
        """
        data = self.to_dict('admin')
        
        # 管理端特定格式调整
        data.update({
            'amount': f'¥{self.total_amount}',
            'create_time': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else '',
            'update_time': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else '',
            'status_text': self._get_status_text(self.status),
            'payment_status_text': self._get_payment_status_text(self.payment_status)
        })
        
        return data
    
    def to_user_format(self) -> Dict[str, Any]:
        """
        转换为用户端格式
        
        Returns:
            Dict[str, Any]: 用户端格式的订单数据
        """
        data = self.to_dict('user')
        
        # 用户端特定格式调整
        data.update({
            'amount': f'¥{self.total_amount}',
            'create_time': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else '',
            'status_text': self._get_status_text(self.status),
            'payment_status_text': self._get_payment_status_text(self.payment_status),
            'can_cancel': self._can_cancel(),
            'can_pay': self._can_pay()
        })
        
        # 移除敏感字段
        data.pop('user_id', None)
        
        return data
    
    def to_api_format(self) -> Dict[str, Any]:
        """
        转换为API响应格式
        
        Returns:
            Dict[str, Any]: API格式的订单数据
        """
        return {
            'orderId': self.id,
            'orderNo': self.order_no,
            'status': self.status,
            'totalAmount': float(self.total_amount),
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'paymentMethod': self.payment_method,
            'paymentStatus': self.payment_status,
            'shippingAddress': self.shipping_address,
            'items': self.items
        }
    
    def _get_status_text(self, status: Optional[str]) -> str:
        """
        获取状态文本描述
        
        Args:
            status: 状态代码
            
        Returns:
            str: 状态文本
        """
        status_map = {
            '0': '待支付',
            '1': '已支付',
            '2': '已发货',
            '3': '已完成',
            '4': '已取消',
            '5': '已退款'
        }
        return status_map.get(status, '未知状态')
    
    def _get_payment_status_text(self, payment_status: Optional[str]) -> str:
        """
        获取支付状态文本描述
        
        Args:
            payment_status: 支付状态代码
            
        Returns:
            str: 支付状态文本
        """
        payment_status_map = {
            '0': '未支付',
            '1': '支付中',
            '2': '已支付',
            '3': '支付失败',
            '4': '已退款'
        }
        return payment_status_map.get(payment_status, '未知状态')
    
    def _can_cancel(self) -> bool:
        """
        判断订单是否可以取消
        
        Returns:
            bool: 是否可以取消
        """
        # 只有待支付和支付中的订单可以取消
        return self.status in ['0', '1']
    
    def _can_pay(self) -> bool:
        """
        判断订单是否可以支付
        
        Returns:
            bool: 是否可以支付
        """
        # 只有待支付状态的订单可以支付
        return self.status == '0' and self.payment_status in ['0', '3']
    
    def get_items_summary(self) -> Dict[str, Any]:
        """
        获取订单商品摘要
        
        Returns:
            Dict[str, Any]: 商品摘要信息
        """
        if not self.items:
            return {
                'total_items': 0,
                'total_quantity': 0,
                'item_names': []
            }
        
        total_quantity = sum(item.get('quantity', 0) for item in self.items)
        item_names = [item.get('name', '') for item in self.items if item.get('name')]
        
        return {
            'total_items': len(self.items),
            'total_quantity': total_quantity,
            'item_names': item_names[:3],  # 只显示前3个商品名称
            'has_more': len(item_names) > 3
        }
    
    def calculate_total_amount(self) -> Decimal:
        """
        计算订单总金额
        
        Returns:
            Decimal: 总金额
        """
        if not self.items:
            return self.total_amount
        
        calculated_total = Decimal('0')
        for item in self.items:
            price = Decimal(str(item.get('price', 0)))
            quantity = int(item.get('quantity', 0))
            calculated_total += price * quantity
        
        return calculated_total
    
    def validate_data(self) -> List[str]:
        """
        验证订单数据
        
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        # 基本字段验证
        if not self.id:
            errors.append('订单ID不能为空')
        
        if not self.user_id:
            errors.append('用户ID不能为空')
        
        if not self.order_no:
            errors.append('订单号不能为空')
        
        if not self.status:
            errors.append('订单状态不能为空')
        
        if self.total_amount <= 0:
            errors.append('订单金额必须大于0')
        
        # 状态值验证
        valid_statuses = ['0', '1', '2', '3', '4', '5']
        if self.status not in valid_statuses:
            errors.append(f'无效的订单状态: {self.status}')
        
        if self.payment_status and self.payment_status not in ['0', '1', '2', '3', '4']:
            errors.append(f'无效的支付状态: {self.payment_status}')
        
        # 商品数据验证
        if self.items:
            for i, item in enumerate(self.items):
                if not item.get('id'):
                    errors.append(f'第{i+1}个商品缺少ID')
                if not item.get('name'):
                    errors.append(f'第{i+1}个商品缺少名称')
                if not item.get('price') or float(item.get('price', 0)) <= 0:
                    errors.append(f'第{i+1}个商品价格无效')
                if not item.get('quantity') or int(item.get('quantity', 0)) <= 0:
                    errors.append(f'第{i+1}个商品数量无效')
        
        return errors
    
    def is_valid(self) -> bool:
        """
        检查订单数据是否有效
        
        Returns:
            bool: 数据是否有效
        """
        return len(self.validate_data()) == 0
    
    @classmethod
    def from_model(cls, order_model, include_items: bool = True) -> 'OrderDTO':
        """
        从Django模型创建DTO
        
        Args:
            order_model: Django订单模型实例
            include_items: 是否包含商品信息
            
        Returns:
            OrderDTO: 订单DTO实例
        """
        # 解析订单详细数据
        items = []
        if include_items and order_model.data:
            try:
                order_data = json.loads(order_model.data)
                items = order_data.get('items', [])
            except json.JSONDecodeError:
                items = []
        
        return cls(
            id=order_model.id,
            user_id=order_model.user_id,
            order_no=order_model.order_no,
            status=order_model.status,
            total_amount=order_model.total_amount,
            created_at=order_model.created_at,
            updated_at=order_model.updated_at,
            payment_method=getattr(order_model, 'payment_method', None),
            payment_status=getattr(order_model, 'payment_status', None),
            shipping_address=getattr(order_model, 'shipping_address', None),
            shipping_method=getattr(order_model, 'shipping_method', None),
            notes=getattr(order_model, 'notes', None),
            data=order_model.data,
            items=items
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OrderDTO':
        """
        从字典创建DTO
        
        Args:
            data: 订单数据字典
            
        Returns:
            OrderDTO: 订单DTO实例
        """
        return cls(
            id=data.get('id', ''),
            user_id=data.get('user_id', ''),
            order_no=data.get('order_no', ''),
            status=data.get('status', '0'),
            total_amount=Decimal(str(data.get('total_amount', 0))),
            created_at=data.get('created_at') or datetime.now(),
            updated_at=data.get('updated_at'),
            payment_method=data.get('payment_method'),
            payment_status=data.get('payment_status', '0'),
            shipping_address=data.get('shipping_address'),
            shipping_method=data.get('shipping_method'),
            notes=data.get('notes'),
            data=data.get('data'),
            items=data.get('items', [])
        )