"""
订单服务 (OrderService)

提供订单相关的业务逻辑处理，包括：
- 订单创建和管理
- 订单状态跟踪和更新
- 订单查询和分页
- 订单数据验证和格式化
- 订单权限控制
"""

import uuid
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, Any, List, Optional, Tuple
from django.core.paginator import Paginator
from django.db import transaction
from django.utils import timezone

from ..base.base_service import BaseService
from ..base.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    PermissionDeniedException,
    BusinessLogicException
)
from .order_dto import OrderDTO
from api.models import Order, Goods, User


class OrderService(BaseService):
    """
    订单服务类
    
    提供订单相关的所有业务逻辑处理
    """
    
    def __init__(self, context: Dict[str, Any]):
        """
        初始化订单服务
        
        Args:
            context: 请求上下文，包含用户信息和权限
        """
        super().__init__(context)
        self.model = Order
    
    def get_order_list(
        self,
        filters: Optional[Dict[str, Any]] = None,
        pagination: Optional[Dict[str, Any]] = None,
        format_type: str = 'admin'
    ) -> Dict[str, Any]:
        """
        获取订单列表
        
        Args:
            filters: 过滤条件
            pagination: 分页参数
            format_type: 格式类型 ('admin', 'user', 'api')
            
        Returns:
            Dict[str, Any]: 订单列表数据
        """
        # 权限检查
        if not self._check_permission('read'):
            raise PermissionDeniedException('没有权限查看订单列表')
        
        try:
            # 构建查询
            query = self.model.objects.all()
            
            # 用户端只能查看自己的订单
            if self.request_type == 'user':
                query = query.filter(user_id=self.user_id)
            
            # 应用过滤条件
            if filters:
                query = self._apply_filters(query, filters)
            
            # 排序
            query = query.order_by('-created_at')
            
            # 分页处理
            page = pagination.get('page', 1) if pagination else 1
            page_size = pagination.get('page_size', 20) if pagination else 20
            
            paginator = Paginator(query, page_size)
            page_obj = paginator.get_page(page)
            
            # 格式化数据
            result = []
            for order in page_obj:
                order_dto = OrderDTO.from_model(order, include_items=True)
                
                if format_type == 'admin':
                    formatted_order = order_dto.to_admin_format()
                elif format_type == 'user':
                    formatted_order = order_dto.to_user_format()
                else:  # api
                    formatted_order = order_dto.to_api_format()
                
                result.append(formatted_order)
            
            return {
                'list': result,
                'total': paginator.count,
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages
            }
            
        except Exception as e:
            self.logger.error(f'获取订单列表失败: {str(e)}')
            raise
    
    def get_order_detail(self, order_id: str, format_type: str = 'admin') -> Dict[str, Any]:
        """
        获取订单详情
        
        Args:
            order_id: 订单ID
            format_type: 格式类型 ('admin', 'user', 'api')
            
        Returns:
            Dict[str, Any]: 订单详情数据
        """
        # 参数验证
        if not order_id:
            raise ValidationException('订单ID不能为空')
        
        # 权限检查
        if not self._check_permission('read'):
            raise PermissionDeniedException('没有权限查看订单详情')
        
        try:
            # 查询订单
            query = self.model.objects
            
            # 用户端只能查看自己的订单
            if self.request_type == 'user':
                order = query.get(id=order_id, user_id=self.user_id)
            else:
                order = query.get(id=order_id)
            
            # 创建DTO并格式化
            order_dto = OrderDTO.from_model(order, include_items=True)
            
            if format_type == 'admin':
                return order_dto.to_admin_format()
            elif format_type == 'user':
                return order_dto.to_user_format()
            else:  # api
                return order_dto.to_api_format()
                
        except Order.DoesNotExist:
            raise ResourceNotFoundException('订单不存在')
        except Exception as e:
            self.logger.error(f'获取订单详情失败: {str(e)}')
            raise
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建订单
        
        Args:
            order_data: 订单数据
            
        Returns:
            Dict[str, Any]: 创建的订单信息
        """
        # 权限检查
        if not self._check_permission('write'):
            raise PermissionDeniedException('没有权限创建订单')
        
        # 数据验证
        self._validate_order_data(order_data)
        
        try:
            with transaction.atomic():
                # 生成订单ID和订单号
                order_id = str(uuid.uuid4())
                order_no = self._generate_order_no()
                
                # 验证商品信息并计算总金额
                items, total_amount = self._validate_and_calculate_items(order_data.get('items', []))
                
                # 检查库存
                self._check_stock_availability(items)
                
                # 创建订单数据
                order_detail_data = {
                    'items': items,
                    'user_info': order_data.get('user_info', {}),
                    'shipping_info': order_data.get('shipping_info', {}),
                    'payment_info': order_data.get('payment_info', {}),
                    'created_by': self.user_id,
                    'created_at': datetime.now().isoformat()
                }
                
                # 创建订单
                order = Order(
                    id=order_id,
                    user_id=order_data.get('user_id', self.user_id),
                    order_no=order_no,
                    status='0',  # 待支付
                    total_amount=total_amount,
                    data=json.dumps(order_detail_data, ensure_ascii=False)
                )
                order.save()
                
                # 更新库存
                self._update_stock(items, 'decrease')
                
                # 创建DTO并返回
                order_dto = OrderDTO.from_model(order, include_items=True)
                
                self.logger.info(f'订单创建成功: {order_id}')
                return order_dto.to_admin_format()
                
        except Exception as e:
            self.logger.error(f'创建订单失败: {str(e)}')
            raise\n    \n    def update_order_status(self, order_id: str, new_status: str, notes: Optional[str] = None) -> Dict[str, Any]:\n        \"\"\"\n        更新订单状态\n        \n        Args:\n            order_id: 订单ID\n            new_status: 新状态\n            notes: 备注信息\n            \n        Returns:\n            Dict[str, Any]: 更新后的订单信息\n        \"\"\"\n        # 参数验证\n        if not order_id:\n            raise ValidationException('订单ID不能为空')\n        \n        if not new_status:\n            raise ValidationException('订单状态不能为空')\n        \n        # 权限检查\n        if not self._check_permission('write'):\n            raise PermissionDeniedException('没有权限更新订单状态')\n        \n        # 状态验证\n        valid_statuses = ['0', '1', '2', '3', '4', '5']\n        if new_status not in valid_statuses:\n            raise ValidationException(f'无效的订单状态: {new_status}')\n        \n        try:\n            with transaction.atomic():\n                # 查询订单\n                order = self.model.objects.get(id=order_id)\n                \n                # 用户端只能操作自己的订单\n                if self.request_type == 'user' and order.user_id != self.user_id:\n                    raise PermissionDeniedException('没有权限操作此订单')\n                \n                # 状态转换验证\n                if not self._is_valid_status_transition(order.status, new_status):\n                    raise BusinessLogicException(f'不能从状态 {order.status} 转换到 {new_status}')\n                \n                # 更新状态\n                old_status = order.status\n                order.status = new_status\n                order.updated_at = timezone.now()\n                \n                # 处理状态变更的业务逻辑\n                self._handle_status_change(order, old_status, new_status, notes)\n                \n                order.save()\n                \n                # 创建DTO并返回\n                order_dto = OrderDTO.from_model(order, include_items=True)\n                \n                self.logger.info(f'订单状态更新成功: {order_id} {old_status} -> {new_status}')\n                return order_dto.to_admin_format()\n                \n        except Order.DoesNotExist:\n            raise ResourceNotFoundException('订单不存在')\n        except Exception as e:\n            self.logger.error(f'更新订单状态失败: {str(e)}')\n            raise\n    \n    def cancel_order(self, order_id: str, reason: Optional[str] = None) -> Dict[str, Any]:\n        \"\"\"\n        取消订单\n        \n        Args:\n            order_id: 订单ID\n            reason: 取消原因\n            \n        Returns:\n            Dict[str, Any]: 取消后的订单信息\n        \"\"\"\n        # 参数验证\n        if not order_id:\n            raise ValidationException('订单ID不能为空')\n        \n        try:\n            with transaction.atomic():\n                # 查询订单\n                order = self.model.objects.get(id=order_id)\n                \n                # 用户端只能取消自己的订单\n                if self.request_type == 'user' and order.user_id != self.user_id:\n                    raise PermissionDeniedException('没有权限取消此订单')\n                \n                # 检查是否可以取消\n                if order.status not in ['0', '1']:  # 只有待支付和支付中的订单可以取消\n                    raise BusinessLogicException('当前订单状态不允许取消')\n                \n                # 恢复库存\n                if order.data:\n                    try:\n                        order_detail = json.loads(order.data)\n                        items = order_detail.get('items', [])\n                        self._update_stock(items, 'increase')\n                    except json.JSONDecodeError:\n                        pass\n                \n                # 更新订单状态\n                order.status = '4'  # 已取消\n                order.updated_at = timezone.now()\n                \n                # 添加取消原因到订单数据\n                if order.data:\n                    try:\n                        order_detail = json.loads(order.data)\n                        order_detail['cancel_info'] = {\n                            'reason': reason or '用户取消',\n                            'cancelled_by': self.user_id,\n                            'cancelled_at': datetime.now().isoformat()\n                        }\n                        order.data = json.dumps(order_detail, ensure_ascii=False)\n                    except json.JSONDecodeError:\n                        pass\n                \n                order.save()\n                \n                # 创建DTO并返回\n                order_dto = OrderDTO.from_model(order, include_items=True)\n                \n                self.logger.info(f'订单取消成功: {order_id}')\n                return order_dto.to_admin_format()\n                \n        except Order.DoesNotExist:\n            raise ResourceNotFoundException('订单不存在')\n        except Exception as e:\n            self.logger.error(f'取消订单失败: {str(e)}')\n            raise\n    \n    def search_orders(self, keyword: str, **kwargs) -> Dict[str, Any]:\n        \"\"\"\n        搜索订单\n        \n        Args:\n            keyword: 搜索关键词\n            **kwargs: 其他搜索参数\n            \n        Returns:\n            Dict[str, Any]: 搜索结果\n        \"\"\"\n        filters = {'search': keyword}\n        filters.update(kwargs)\n        \n        return self.get_order_list(\n            filters=filters,\n            pagination=kwargs.get('pagination'),\n            format_type=kwargs.get('format_type', 'admin')\n        )\n    \n    def get_order_statistics(self, date_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:\n        \"\"\"\n        获取订单统计信息\n        \n        Args:\n            date_range: 日期范围 {'start': 'YYYY-MM-DD', 'end': 'YYYY-MM-DD'}\n            \n        Returns:\n            Dict[str, Any]: 统计信息\n        \"\"\"\n        # 权限检查\n        if not self._check_permission('read'):\n            raise PermissionDeniedException('没有权限查看订单统计')\n        \n        try:\n            query = self.model.objects.all()\n            \n            # 用户端只统计自己的订单\n            if self.request_type == 'user':\n                query = query.filter(user_id=self.user_id)\n            \n            # 日期范围过滤\n            if date_range:\n                if date_range.get('start'):\n                    query = query.filter(created_at__gte=date_range['start'])\n                if date_range.get('end'):\n                    query = query.filter(created_at__lte=date_range['end'])\n            \n            # 统计各种状态的订单数量\n            stats = {\n                'total_orders': query.count(),\n                'pending_payment': query.filter(status='0').count(),\n                'paid': query.filter(status='1').count(),\n                'shipped': query.filter(status='2').count(),\n                'completed': query.filter(status='3').count(),\n                'cancelled': query.filter(status='4').count(),\n                'refunded': query.filter(status='5').count()\n            }\n            \n            # 计算总金额\n            total_amount = sum(\n                order.total_amount for order in query.filter(status__in=['1', '2', '3'])\n            )\n            stats['total_amount'] = float(total_amount)\n            \n            return stats\n            \n        except Exception as e:\n            self.logger.error(f'获取订单统计失败: {str(e)}')\n            raise\n    \n    def _apply_filters(self, query, filters: Dict[str, Any]):\n        \"\"\"\n        应用过滤条件\n        \n        Args:\n            query: 查询对象\n            filters: 过滤条件\n            \n        Returns:\n            查询对象\n        \"\"\"\n        # 用户ID过滤\n        if filters.get('user_id'):\n            query = query.filter(user_id=filters['user_id'])\n        \n        # 订单状态过滤\n        if filters.get('status'):\n            query = query.filter(status=filters['status'])\n        \n        # 订单号搜索\n        if filters.get('order_no'):\n            query = query.filter(order_no__icontains=filters['order_no'])\n        \n        # 关键词搜索\n        if filters.get('search'):\n            keyword = filters['search']\n            query = query.filter(\n                models.Q(order_no__icontains=keyword) |\n                models.Q(user_id__icontains=keyword)\n            )\n        \n        # 日期范围过滤\n        if filters.get('start_date'):\n            query = query.filter(created_at__gte=filters['start_date'])\n        \n        if filters.get('end_date'):\n            query = query.filter(created_at__lte=filters['end_date'])\n        \n        return query\n    \n    def _validate_order_data(self, order_data: Dict[str, Any]):\n        \"\"\"\n        验证订单数据\n        \n        Args:\n            order_data: 订单数据\n        \"\"\"\n        # 必填字段检查\n        if not order_data.get('items'):\n            raise ValidationException('订单商品不能为空')\n        \n        # 商品数据验证\n        items = order_data['items']\n        if not isinstance(items, list) or len(items) == 0:\n            raise ValidationException('订单必须包含至少一个商品')\n        \n        for i, item in enumerate(items):\n            if not item.get('id'):\n                raise ValidationException(f'第{i+1}个商品缺少ID')\n            if not item.get('quantity') or int(item.get('quantity', 0)) <= 0:\n                raise ValidationException(f'第{i+1}个商品数量无效')\n    \n    def _validate_and_calculate_items(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Decimal]:\n        \"\"\"\n        验证商品信息并计算总金额\n        \n        Args:\n            items: 商品列表\n            \n        Returns:\n            Tuple[List[Dict[str, Any]], Decimal]: 验证后的商品列表和总金额\n        \"\"\"\n        validated_items = []\n        total_amount = Decimal('0')\n        \n        for item in items:\n            # 查询商品信息\n            try:\n                goods = Goods.objects.get(id=item['id'], status='1')\n            except Goods.DoesNotExist:\n                raise ValidationException(f'商品不存在或已下架: {item[\"id\"]}')\n            \n            quantity = int(item['quantity'])\n            price = goods.price\n            \n            # 构建验证后的商品信息\n            validated_item = {\n                'id': goods.id,\n                'name': goods.name,\n                'price': float(price),\n                'quantity': quantity,\n                'subtotal': float(price * quantity),\n                'image': goods.image or '',\n                'category_id': goods.category_id or ''\n            }\n            \n            validated_items.append(validated_item)\n            total_amount += price * quantity\n        \n        return validated_items, total_amount\n    \n    def _check_stock_availability(self, items: List[Dict[str, Any]]):\n        \"\"\"\n        检查库存可用性\n        \n        Args:\n            items: 商品列表\n        \"\"\"\n        for item in items:\n            try:\n                goods = Goods.objects.get(id=item['id'])\n                if goods.stock < item['quantity']:\n                    raise BusinessLogicException(f'商品 {goods.name} 库存不足，当前库存: {goods.stock}')\n            except Goods.DoesNotExist:\n                raise ValidationException(f'商品不存在: {item[\"id\"]}')\n    \n    def _update_stock(self, items: List[Dict[str, Any]], operation: str):\n        \"\"\"\n        更新库存\n        \n        Args:\n            items: 商品列表\n            operation: 操作类型 ('increase', 'decrease')\n        \"\"\"\n        for item in items:\n            try:\n                goods = Goods.objects.get(id=item['id'])\n                quantity = item['quantity']\n                \n                if operation == 'decrease':\n                    goods.stock -= quantity\n                elif operation == 'increase':\n                    goods.stock += quantity\n                \n                goods.save()\n            except Goods.DoesNotExist:\n                self.logger.warning(f'更新库存时商品不存在: {item[\"id\"]}')\n    \n    def _generate_order_no(self) -> str:\n        \"\"\"\n        生成订单号\n        \n        Returns:\n            str: 订单号\n        \"\"\"\n        now = datetime.now()\n        timestamp = now.strftime('%Y%m%d%H%M%S')\n        random_suffix = str(uuid.uuid4())[:8].upper()\n        return f'ORD{timestamp}{random_suffix}'\n    \n    def _is_valid_status_transition(self, from_status: str, to_status: str) -> bool:\n        \"\"\"\n        检查状态转换是否有效\n        \n        Args:\n            from_status: 原状态\n            to_status: 目标状态\n            \n        Returns:\n            bool: 是否有效\n        \"\"\"\n        # 定义有效的状态转换\n        valid_transitions = {\n            '0': ['1', '4'],  # 待支付 -> 已支付/已取消\n            '1': ['2', '4', '5'],  # 已支付 -> 已发货/已取消/已退款\n            '2': ['3'],  # 已发货 -> 已完成\n            '3': ['5'],  # 已完成 -> 已退款\n            '4': [],  # 已取消 -> 无\n            '5': []   # 已退款 -> 无\n        }\n        \n        return to_status in valid_transitions.get(from_status, [])\n    \n    def _handle_status_change(self, order, old_status: str, new_status: str, notes: Optional[str]):\n        \"\"\"\n        处理状态变更的业务逻辑\n        \n        Args:\n            order: 订单对象\n            old_status: 原状态\n            new_status: 新状态\n            notes: 备注\n        \"\"\"\n        # 记录状态变更历史\n        if order.data:\n            try:\n                order_detail = json.loads(order.data)\n                if 'status_history' not in order_detail:\n                    order_detail['status_history'] = []\n                \n                order_detail['status_history'].append({\n                    'from_status': old_status,\n                    'to_status': new_status,\n                    'changed_by': self.user_id,\n                    'changed_at': datetime.now().isoformat(),\n                    'notes': notes\n                })\n                \n                order.data = json.dumps(order_detail, ensure_ascii=False)\n            except json.JSONDecodeError:\n                pass\n        \n        # 处理特定状态变更的业务逻辑\n        if new_status == '4':  # 取消订单\n            # 恢复库存已在cancel_order方法中处理\n            pass\n        elif new_status == '5':  # 退款\n            # 恢复库存\n            if order.data:\n                try:\n                    order_detail = json.loads(order.data)\n                    items = order_detail.get('items', [])\n                    self._update_stock(items, 'increase')\n                except json.JSONDecodeError:\n                    pass