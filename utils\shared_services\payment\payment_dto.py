"""
支付数据传输对象 (PaymentDTO)

定义支付相关数据的结构化表示，支持：
- 不同支付方式的数据处理
- 支付状态管理
- 数据格式化和验证
- 多种输出格式支持
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
import json


@dataclass
class PaymentMethodDTO:
    """
    支付方式数据传输对象
    
    用于在不同层之间传递支付方式数据
    """
    
    # 基本字段
    id: str
    name: str
    interface_type: str
    fee: Decimal
    is_active: bool
    created_at: datetime
    
    # 可选字段
    updated_at: Optional[datetime] = None
    description: Optional[str] = None
    config_data: Optional[Dict[str, Any]] = None
    channel_data: List[Dict[str, Any]] = field(default_factory=list)
    
    # 扩展字段
    supported_currencies: List[str] = field(default_factory=lambda: ['CNY'])
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    
    @classmethod
    def from_model(cls, payment_method, include_config: bool = False) -> 'PaymentMethodDTO':
        """
        从数据库模型创建DTO对象
        
        Args:
            payment_method: PaymentMethod模型实例
            include_config: 是否包含配置信息
            
        Returns:
            PaymentMethodDTO: DTO对象
        """
        # 解析channel数据
        channel_data = []
        if hasattr(payment_method, 'data_json') and payment_method.data_json:
            try:
                data_dict = json.loads(payment_method.data_json)
                if isinstance(data_dict, dict) and 'channel' in data_dict:
                    channel_data = data_dict.get('channel', [])
                    if not isinstance(channel_data, list):
                        channel_data = []
            except json.JSONDecodeError:
                channel_data = []
        
        # 解析配置数据
        config_data = None
        if include_config and hasattr(payment_method, 'data_json') and payment_method.data_json:
            try:
                config_data = json.loads(payment_method.data_json)
            except json.JSONDecodeError:
                config_data = {}
        
        return cls(
            id=str(payment_method.id),
            name=payment_method.name,
            interface_type=payment_method.interface_type,
            fee=payment_method.fee,
            is_active=payment_method.is_active,
            created_at=payment_method.created_at,
            updated_at=getattr(payment_method, 'updated_at', None),
            description=getattr(payment_method, 'description', ''),
            config_data=config_data,
            channel_data=channel_data,
            min_amount=getattr(payment_method, 'min_amount', None),
            max_amount=getattr(payment_method, 'max_amount', None)
        )
    
    def to_dict(self, permission_level: str = 'user') -> Dict[str, Any]:
        """
        转换为字典格式
        
        Args:
            permission_level: 权限级别 ('user', 'admin', 'system')
            
        Returns:
            Dict[str, Any]: 格式化后的支付方式数据
        """
        base_data = {
            'id': self.id,
            'name': self.name,
            'interface_type': self.interface_type,
            'fee': str(self.fee),
            'is_active': self.is_active,
            'data': self.channel_data,
            'supported_currencies': self.supported_currencies
        }
        
        # 管理员级别数据
        if permission_level in ['admin', 'system']:
            base_data.update({
                'created_at': self.created_at.isoformat() if self.created_at else None,
                'updated_at': self.updated_at.isoformat() if self.updated_at else None,
                'description': self.description,
                'min_amount': str(self.min_amount) if self.min_amount else None,
                'max_amount': str(self.max_amount) if self.max_amount else None
            })
        
        # 系统级别数据（包含配置信息）
        if permission_level == 'system':
            base_data['config_data'] = self.config_data
        
        return base_data
    
    def to_admin_format(self) -> Dict[str, Any]:
        """
        转换为管理端格式
        
        Returns:
            Dict[str, Any]: 管理端格式的数据
        """
        return self.to_dict('admin')
    
    def to_user_format(self) -> Dict[str, Any]:
        """
        转换为用户端格式
        
        Returns:
            Dict[str, Any]: 用户端格式的数据
        """
        return self.to_dict('user')
    
    def to_api_format(self) -> Dict[str, Any]:
        """
        转换为API格式
        
        Returns:
            Dict[str, Any]: API格式的数据
        """
        return {
            'id': self.id,
            'name': self.name,
            'fee': str(self.fee),
            'data': self.channel_data,
            'interface_type': self.interface_type
        }


@dataclass
class PaymentTransactionDTO:
    """
    支付交易数据传输对象
    
    用于处理支付交易相关数据
    """
    
    # 基本字段
    transaction_id: str
    order_id: str
    payment_method_id: str
    amount: Decimal
    status: str
    created_at: datetime
    
    # 可选字段
    updated_at: Optional[datetime] = None
    payment_url: Optional[str] = None
    callback_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    external_transaction_id: Optional[str] = None
    
    # 扩展字段
    currency: str = 'CNY'
    payment_method_name: Optional[str] = None
    user_id: Optional[str] = None
    
    @classmethod
    def from_payment_request(cls, order_id: str, payment_method_id: str, 
                           amount: Decimal, user_id: Optional[str] = None) -> 'PaymentTransactionDTO':
        """
        从支付请求创建交易DTO
        
        Args:
            order_id: 订单ID
            payment_method_id: 支付方式ID
            amount: 支付金额
            user_id: 用户ID
            
        Returns:
            PaymentTransactionDTO: 交易DTO对象
        """
        import uuid
        
        return cls(
            transaction_id=str(uuid.uuid4()),
            order_id=order_id,
            payment_method_id=payment_method_id,
            amount=amount,
            status='pending',
            created_at=datetime.now(),
            user_id=user_id
        )
    
    def to_dict(self, permission_level: str = 'user') -> Dict[str, Any]:
        """
        转换为字典格式
        
        Args:
            permission_level: 权限级别
            
        Returns:
            Dict[str, Any]: 格式化后的交易数据
        """
        base_data = {
            'transaction_id': self.transaction_id,
            'order_id': self.order_id,
            'amount': str(self.amount),
            'currency': self.currency,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        
        # 用户级别数据
        if permission_level in ['user', 'admin', 'system']:
            base_data.update({
                'payment_method_id': self.payment_method_id,
                'payment_method_name': self.payment_method_name,
                'payment_url': self.payment_url
            })
        
        # 管理员级别数据
        if permission_level in ['admin', 'system']:
            base_data.update({
                'user_id': self.user_id,
                'updated_at': self.updated_at.isoformat() if self.updated_at else None,
                'external_transaction_id': self.external_transaction_id,
                'error_message': self.error_message
            })
        
        # 系统级别数据
        if permission_level == 'system':
            base_data['callback_data'] = self.callback_data
        
        return base_data
    
    def to_admin_format(self) -> Dict[str, Any]:
        """转换为管理端格式"""
        return self.to_dict('admin')
    
    def to_user_format(self) -> Dict[str, Any]:
        """转换为用户端格式"""
        return self.to_dict('user')


@dataclass
class PaymentCallbackDTO:
    """
    支付回调数据传输对象
    
    用于处理支付回调相关数据
    """
    
    # 基本字段
    transaction_id: str
    external_transaction_id: str
    status: str
    amount: Decimal
    callback_time: datetime
    
    # 可选字段
    order_id: Optional[str] = None
    payment_method_id: Optional[str] = None
    raw_data: Optional[Dict[str, Any]] = None
    signature: Optional[str] = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    
    # 扩展字段
    currency: str = 'CNY'
    verified: bool = False
    
    @classmethod
    def from_callback_data(cls, callback_data: Dict[str, Any], 
                          payment_method_id: str) -> 'PaymentCallbackDTO':
        """
        从回调数据创建DTO
        
        Args:
            callback_data: 回调原始数据
            payment_method_id: 支付方式ID
            
        Returns:
            PaymentCallbackDTO: 回调DTO对象
        """
        return cls(
            transaction_id=callback_data.get('transaction_id', ''),
            external_transaction_id=callback_data.get('external_transaction_id', ''),
            status=callback_data.get('status', 'unknown'),
            amount=Decimal(str(callback_data.get('amount', '0'))),
            callback_time=datetime.now(),
            order_id=callback_data.get('order_id'),
            payment_method_id=payment_method_id,
            raw_data=callback_data,
            signature=callback_data.get('signature'),
            error_code=callback_data.get('error_code'),
            error_message=callback_data.get('error_message'),
            currency=callback_data.get('currency', 'CNY')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 格式化后的回调数据
        """
        return {
            'transaction_id': self.transaction_id,
            'external_transaction_id': self.external_transaction_id,
            'order_id': self.order_id,
            'payment_method_id': self.payment_method_id,
            'status': self.status,
            'amount': str(self.amount),
            'currency': self.currency,
            'callback_time': self.callback_time.isoformat() if self.callback_time else None,
            'verified': self.verified,
            'error_code': self.error_code,
            'error_message': self.error_message,
            'signature': self.signature
        }


@dataclass
class PaymentFilter:
    """
    支付过滤条件DTO
    
    用于支付相关查询的过滤条件
    """
    
    # 基本过滤条件
    payment_method_id: Optional[str] = None
    status: Optional[str] = None
    interface_type: Optional[str] = None
    is_active: Optional[bool] = None
    
    # 时间范围过滤
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    
    # 金额范围过滤
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    
    # 搜索条件
    keyword: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 过滤条件字典
        """
        result = {}
        
        if self.payment_method_id:
            result['payment_method_id'] = self.payment_method_id
        if self.status:
            result['status'] = self.status
        if self.interface_type:
            result['interface_type'] = self.interface_type
        if self.is_active is not None:
            result['is_active'] = self.is_active
        if self.start_date:
            result['start_date'] = self.start_date
        if self.end_date:
            result['end_date'] = self.end_date
        if self.min_amount:
            result['min_amount'] = self.min_amount
        if self.max_amount:
            result['max_amount'] = self.max_amount
        if self.keyword:
            result['keyword'] = self.keyword
        
        return result


@dataclass
class PaymentSorter:
    """
    支付排序条件DTO
    
    用于支付相关查询的排序条件
    """
    
    field: str = 'created_at'
    direction: str = 'desc'  # 'asc' or 'desc'
    
    def get_order_by(self) -> str:
        """
        获取Django ORM的order_by参数
        
        Returns:
            str: order_by参数
        """
        prefix = '-' if self.direction == 'desc' else ''
        return f'{prefix}{self.field}'
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 排序条件字典
        """
        return {
            'field': self.field,
            'direction': self.direction
        }