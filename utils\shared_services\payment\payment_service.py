"""
支付服务 (PaymentService)

提供支付相关的业务逻辑处理，包括：
- 支付方式管理
- 支付链接生成
- 支付状态验证
- 支付回调处理
"""

import hashlib
import json
import uuid
from typing import Dict, Any, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime
from django.db import transaction
from django.core.paginator import Paginator
from django.utils import timezone

from ..base.base_service import BaseService
from ..base.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    PermissionDeniedException,
    BusinessLogicException
)
from .payment_dto import (
    PaymentMethodDTO, 
    PaymentTransactionDTO, 
    PaymentCallbackDTO,
    PaymentFilter, 
    PaymentSorter
)


class PaymentService(BaseService):
    """
    支付服务类
    
    提供支付相关的所有业务逻辑处理
    """
    
    def __init__(self, context: Optional[Dict[str, Any]] = None):
        """
        初始化支付服务
        
        Args:
            context: 请求上下文，包含用户信息和权限
        """
        super().__init__(context)
    
    def get_payment_methods(self, filters: Optional[PaymentFilter] = None,
                          format_type: str = 'user') -> List[Dict[str, Any]]:
        """
        获取支付方式列表
        
        Args:
            filters: 过滤条件
            format_type: 格式化类型 ('admin', 'user', 'api')
            
        Returns:
            List[Dict[str, Any]]: 支付方式列表
        """
        try:
            from api.models import PaymentMethod
            
            # 构建查询
            query = PaymentMethod.objects.all()
            
            # 应用过滤条件
            if filters:
                query = self._apply_payment_method_filters(query, filters)
            
            # 用户端只显示激活的支付方式
            if format_type == 'user':
                query = query.filter(is_active=True)
            
            # 排序
            query = query.order_by('-created_at')
            
            # 转换为DTO并格式化
            result = []
            for payment_method in query:
                payment_dto = PaymentMethodDTO.from_model(
                    payment_method, 
                    include_config=(format_type == 'admin')
                )
                
                if format_type == 'admin':
                    formatted_method = payment_dto.to_admin_format()
                elif format_type == 'api':
                    formatted_method = payment_dto.to_api_format()
                else:  # user
                    formatted_method = payment_dto.to_user_format()
                
                result.append(formatted_method)
            
            return result
            
        except Exception as e:
            self.logger.error(f'获取支付方式列表失败: {str(e)}')
            raise
    
    def get_payment_method_detail(self, payment_method_id: str,
                                format_type: str = 'user') -> Dict[str, Any]:
        """
        获取支付方式详情
        
        Args:
            payment_method_id: 支付方式ID
            format_type: 格式化类型
            
        Returns:
            Dict[str, Any]: 支付方式详情
            
        Raises:
            ResourceNotFoundException: 支付方式不存在
        """
        try:
            # 验证参数
            if not payment_method_id:
                raise ValidationException('支付方式ID不能为空')
            
            from api.models import PaymentMethod
            
            try:
                payment_method = PaymentMethod.objects.get(id=payment_method_id)
            except PaymentMethod.DoesNotExist:
                raise ResourceNotFoundException(
                    f'支付方式不存在',
                    resource_type='payment_method',
                    resource_id=payment_method_id
                )
            
            # 用户端只能查看激活的支付方式
            if format_type == 'user' and not payment_method.is_active:
                raise ResourceNotFoundException(
                    f'支付方式不可用',
                    resource_type='payment_method',
                    resource_id=payment_method_id
                )
            
            # 转换为DTO并格式化
            payment_dto = PaymentMethodDTO.from_model(
                payment_method,
                include_config=(format_type == 'admin')
            )
            
            if format_type == 'admin':
                return payment_dto.to_admin_format()
            elif format_type == 'api':
                return payment_dto.to_api_format()
            else:  # user
                return payment_dto.to_user_format()
                
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f'获取支付方式详情失败: {str(e)}')
            raise
    
    def create_payment_method(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建支付方式（仅管理端）
        
        Args:
            payment_data: 支付方式数据
            
        Returns:
            Dict[str, Any]: 创建后的支付方式数据
            
        Raises:
            PermissionDeniedException: 权限不足
            ValidationException: 数据验证失败
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException('需要管理员权限')
        
        try:
            # 验证数据
            validated_data = self._validate_payment_method_data(payment_data)
            
            with transaction.atomic():
                from api.models import PaymentMethod
                
                # 生成新ID
                new_id = str(uuid.uuid4())
                
                # 处理配置数据
                data_json = None
                if validated_data.get('config_data') or validated_data.get('channel_data'):
                    config = {}
                    if validated_data.get('config_data'):
                        config.update(validated_data['config_data'])
                    if validated_data.get('channel_data'):
                        config['channel'] = validated_data['channel_data']
                    data_json = json.dumps(config, ensure_ascii=False)
                
                # 创建支付方式
                payment_method = PaymentMethod(
                    id=new_id,
                    name=validated_data['name'],
                    interface_type=validated_data['interface_type'],
                    fee=validated_data.get('fee', Decimal('0')),
                    is_active=validated_data.get('is_active', True),
                    data_json=data_json
                )
                payment_method.save()
                
                # 转换为DTO并返回
                payment_dto = PaymentMethodDTO.from_model(payment_method, include_config=True)
                
                self.logger.info(f'支付方式创建成功: {new_id}')
                return payment_dto.to_admin_format()
                
        except (ValidationException, PermissionDeniedException):
            raise
        except Exception as e:
            self.logger.error(f'创建支付方式失败: {str(e)}')
            raise
    
    def update_payment_method(self, payment_method_id: str,
                            update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新支付方式（仅管理端）
        
        Args:
            payment_method_id: 支付方式ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新后的支付方式数据
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException('需要管理员权限')
        
        try:
            # 验证参数
            if not payment_method_id:
                raise ValidationException('支付方式ID不能为空')
            
            with transaction.atomic():
                from api.models import PaymentMethod
                
                try:
                    payment_method = PaymentMethod.objects.get(id=payment_method_id)
                except PaymentMethod.DoesNotExist:
                    raise ResourceNotFoundException(
                        '支付方式不存在',
                        resource_type='payment_method',
                        resource_id=payment_method_id
                    )
                
                # 更新字段
                if 'name' in update_data:
                    payment_method.name = update_data['name']
                
                if 'interface_type' in update_data:
                    payment_method.interface_type = update_data['interface_type']
                
                if 'fee' in update_data:
                    payment_method.fee = Decimal(str(update_data['fee']))
                
                if 'is_active' in update_data:
                    payment_method.is_active = bool(update_data['is_active'])
                
                # 更新配置数据
                if 'config_data' in update_data or 'channel_data' in update_data:
                    config = {}
                    
                    # 保留现有配置
                    if payment_method.data_json:
                        try:
                            config = json.loads(payment_method.data_json)
                        except json.JSONDecodeError:
                            config = {}
                    
                    # 更新配置
                    if 'config_data' in update_data:
                        config.update(update_data['config_data'])
                    
                    if 'channel_data' in update_data:
                        config['channel'] = update_data['channel_data']
                    
                    payment_method.data_json = json.dumps(config, ensure_ascii=False)
                
                payment_method.save()
                
                # 转换为DTO并返回
                payment_dto = PaymentMethodDTO.from_model(payment_method, include_config=True)
                
                self.logger.info(f'支付方式更新成功: {payment_method_id}')
                return payment_dto.to_admin_format()
                
        except (ValidationException, ResourceNotFoundException, PermissionDeniedException):
            raise
        except Exception as e:
            self.logger.error(f'更新支付方式失败: {str(e)}')
            raise
    
    def delete_payment_method(self, payment_method_id: str) -> bool:
        """
        删除支付方式（仅管理端）
        
        Args:
            payment_method_id: 支付方式ID
            
        Returns:
            bool: 是否删除成功
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException('需要管理员权限')
        
        try:
            # 验证参数
            if not payment_method_id:
                raise ValidationException('支付方式ID不能为空')
            
            with transaction.atomic():
                from api.models import PaymentMethod
                
                try:
                    payment_method = PaymentMethod.objects.get(id=payment_method_id)
                except PaymentMethod.DoesNotExist:
                    raise ResourceNotFoundException(
                        '支付方式不存在',
                        resource_type='payment_method',
                        resource_id=payment_method_id
                    )
                
                # 检查是否有订单使用该支付方式
                # 这里可以添加业务逻辑检查
                
                # 执行删除
                payment_method.delete()
                
                self.logger.info(f'支付方式删除成功: {payment_method_id}')
                return True
                
        except (ValidationException, ResourceNotFoundException, PermissionDeniedException):
            raise
        except Exception as e:
            self.logger.error(f'删除支付方式失败: {str(e)}')
            raise
    
    def generate_payment_url(self, payment_method_id: str, order_info: Dict[str, Any],
                           device_type: str = 'desktop') -> Dict[str, Any]:
        """
        生成支付链接
        
        Args:
            payment_method_id: 支付方式ID
            order_info: 订单信息
            device_type: 设备类型
            
        Returns:
            Dict[str, Any]: 包含支付URL和相关信息的字典
        """
        try:
            # 验证参数
            if not payment_method_id:
                raise ValidationException('支付方式ID不能为空')
            
            if not order_info or not order_info.get('order_id'):
                raise ValidationException('订单信息不完整')
            
            # 获取支付方式
            payment_method_data = self.get_payment_method_detail(
                payment_method_id, 
                format_type='admin'
            )
            
            # 验证支付方式是否可用
            if not payment_method_data.get('is_active'):
                raise BusinessLogicException('支付方式不可用')
            
            # 创建支付交易记录
            transaction_dto = PaymentTransactionDTO.from_payment_request(
                order_id=order_info['order_id'],
                payment_method_id=payment_method_id,
                amount=Decimal(str(order_info['amount'])),
                user_id=order_info.get('user_id')
            )
            
            # 根据支付方式类型生成支付URL
            interface_type = payment_method_data.get('interface_type')
            
            if interface_type == 'alipay':
                payment_url = self._generate_alipay_url(
                    payment_method_data, order_info, device_type
                )
            elif interface_type == 'wechat':
                payment_url = self._generate_wechat_url(
                    payment_method_data, order_info, device_type
                )
            elif interface_type == 'epay':
                payment_url = self._generate_epay_url(
                    payment_method_data, order_info, device_type
                )
            else:
                raise BusinessLogicException(f'不支持的支付方式类型: {interface_type}')
            
            # 更新交易记录
            transaction_dto.payment_url = payment_url
            transaction_dto.status = 'created'
            
            result = {
                'transaction_id': transaction_dto.transaction_id,
                'payment_url': payment_url,
                'payment_method': payment_method_data['name'],
                'amount': str(transaction_dto.amount),
                'currency': transaction_dto.currency,
                'expires_at': None  # 可以根据需要设置过期时间
            }
            
            self.logger.info(f'支付链接生成成功: {transaction_dto.transaction_id}')
            return result
            
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f'生成支付链接失败: {str(e)}')
            raise
    
    def check_payment_status(self, transaction_id: str) -> Dict[str, Any]:
        """
        检查支付状态
        
        Args:
            transaction_id: 交易ID
            
        Returns:
            Dict[str, Any]: 支付状态信息
        """
        try:
            # 验证参数
            if not transaction_id:
                raise ValidationException('交易ID不能为空')
            
            # 这里应该查询支付交易记录
            # 由于没有具体的交易表，这里返回模拟数据
            
            # 实际实现中应该从数据库查询交易状态
            status_info = {
                'transaction_id': transaction_id,
                'status': 'pending',  # pending, success, failed, cancelled
                'amount': '0.00',
                'currency': 'CNY',
                'payment_time': None,
                'error_message': None
            }
            
            return status_info
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f'检查支付状态失败: {str(e)}')
            raise
    
    def process_payment_callback(self, payment_method_id: str,
                               callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理支付回调
        
        Args:
            payment_method_id: 支付方式ID
            callback_data: 回调数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 验证参数
            if not payment_method_id:
                raise ValidationException('支付方式ID不能为空')
            
            if not callback_data:
                raise ValidationException('回调数据不能为空')
            
            # 创建回调DTO
            callback_dto = PaymentCallbackDTO.from_callback_data(
                callback_data, payment_method_id
            )
            
            # 获取支付方式配置
            payment_method_data = self.get_payment_method_detail(
                payment_method_id, format_type='admin'
            )
            
            # 验证回调签名
            is_valid = self._verify_callback_signature(
                callback_dto, payment_method_data
            )
            
            if not is_valid:
                raise BusinessLogicException('回调签名验证失败')
            
            callback_dto.verified = True
            
            # 处理支付结果
            result = self._process_payment_result(callback_dto)
            
            self.logger.info(f'支付回调处理成功: {callback_dto.transaction_id}')
            return result
            
        except (ValidationException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f'处理支付回调失败: {str(e)}')
            raise
    
    def get_payment_statistics(self, date_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取支付统计信息（仅管理端）
        
        Args:
            date_range: 日期范围
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException('需要管理员权限')
        
        try:
            # 这里应该实现具体的统计逻辑
            # 由于没有具体的交易表，返回模拟数据
            
            stats = {
                'total_transactions': 0,
                'successful_transactions': 0,
                'failed_transactions': 0,
                'total_amount': '0.00',
                'success_rate': '0.00%',
                'payment_methods': []
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f'获取支付统计失败: {str(e)}')
            raise
    
    def _apply_payment_method_filters(self, query, filters: PaymentFilter):
        """
        应用支付方式过滤条件
        
        Args:
            query: 查询对象
            filters: 过滤条件
            
        Returns:
            查询对象
        """
        filter_dict = filters.to_dict()
        
        if filter_dict.get('interface_type'):
            query = query.filter(interface_type=filter_dict['interface_type'])
        
        if filter_dict.get('is_active') is not None:
            query = query.filter(is_active=filter_dict['is_active'])
        
        if filter_dict.get('keyword'):
            keyword = filter_dict['keyword']
            query = query.filter(name__icontains=keyword)
        
        return query
    
    def _validate_payment_method_data(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证支付方式数据
        
        Args:
            payment_data: 支付方式数据
            
        Returns:
            Dict[str, Any]: 验证后的数据
        """
        # 必填字段检查
        if not payment_data.get('name'):
            raise ValidationException('支付方式名称不能为空')
        
        if not payment_data.get('interface_type'):
            raise ValidationException('接口类型不能为空')
        
        # 验证接口类型
        valid_types = ['alipay', 'wechat', 'epay', 'bank']
        if payment_data['interface_type'] not in valid_types:
            raise ValidationException(f'无效的接口类型: {payment_data["interface_type"]}')
        
        # 验证费率
        if 'fee' in payment_data:
            try:
                fee = Decimal(str(payment_data['fee']))
                if fee < 0:
                    raise ValidationException('费率不能为负数')
                payment_data['fee'] = fee
            except (ValueError, TypeError):
                raise ValidationException('无效的费率格式')
        
        return payment_data
    
    def _generate_alipay_url(self, payment_method_data: Dict[str, Any],
                           order_info: Dict[str, Any], device_type: str) -> str:
        """
        生成支付宝支付URL
        
        Args:
            payment_method_data: 支付方式数据
            order_info: 订单信息
            device_type: 设备类型
            
        Returns:
            str: 支付URL
        """
        # 这里应该实现具体的支付宝URL生成逻辑
        # 返回模拟URL
        return f"https://openapi.alipay.com/gateway.do?order_id={order_info['order_id']}"
    
    def _generate_wechat_url(self, payment_method_data: Dict[str, Any],
                           order_info: Dict[str, Any], device_type: str) -> str:
        """
        生成微信支付URL
        
        Args:
            payment_method_data: 支付方式数据
            order_info: 订单信息
            device_type: 设备类型
            
        Returns:
            str: 支付URL
        """
        # 这里应该实现具体的微信支付URL生成逻辑
        # 返回模拟URL
        return f"https://api.mch.weixin.qq.com/pay/unifiedorder?order_id={order_info['order_id']}"
    
    def _generate_epay_url(self, payment_method_data: Dict[str, Any],
                         order_info: Dict[str, Any], device_type: str) -> str:
        """
        生成易支付URL
        
        Args:
            payment_method_data: 支付方式数据
            order_info: 订单信息
            device_type: 设备类型
            
        Returns:
            str: 支付URL
        """
        # 这里应该实现具体的易支付URL生成逻辑
        # 返回模拟URL
        return f"https://epay.example.com/submit?order_id={order_info['order_id']}"
    
    def _verify_callback_signature(self, callback_dto: PaymentCallbackDTO,
                                 payment_method_data: Dict[str, Any]) -> bool:
        """
        验证回调签名
        
        Args:
            callback_dto: 回调DTO
            payment_method_data: 支付方式数据
            
        Returns:
            bool: 是否验证通过
        """
        # 这里应该实现具体的签名验证逻辑
        # 根据不同的支付方式使用不同的验证方法
        
        interface_type = payment_method_data.get('interface_type')
        
        if interface_type == 'alipay':
            return self._verify_alipay_signature(callback_dto, payment_method_data)
        elif interface_type == 'wechat':
            return self._verify_wechat_signature(callback_dto, payment_method_data)
        elif interface_type == 'epay':
            return self._verify_epay_signature(callback_dto, payment_method_data)
        
        # 默认返回True（实际应该根据具体情况实现）
        return True
    
    def _verify_alipay_signature(self, callback_dto: PaymentCallbackDTO,
                               payment_method_data: Dict[str, Any]) -> bool:
        """验证支付宝签名"""
        # 实现支付宝签名验证逻辑
        return True
    
    def _verify_wechat_signature(self, callback_dto: PaymentCallbackDTO,
                               payment_method_data: Dict[str, Any]) -> bool:
        """验证微信签名"""
        # 实现微信签名验证逻辑
        return True
    
    def _verify_epay_signature(self, callback_dto: PaymentCallbackDTO,
                             payment_method_data: Dict[str, Any]) -> bool:
        """验证易支付签名"""
        # 实现易支付签名验证逻辑
        return True
    
    def _process_payment_result(self, callback_dto: PaymentCallbackDTO) -> Dict[str, Any]:
        """
        处理支付结果
        
        Args:
            callback_dto: 回调DTO
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        # 这里应该实现具体的支付结果处理逻辑
        # 包括更新订单状态、发送通知等
        
        result = {
            'success': True,
            'transaction_id': callback_dto.transaction_id,
            'order_id': callback_dto.order_id,
            'status': callback_dto.status,
            'amount': str(callback_dto.amount),
            'processed_at': callback_dto.callback_time.isoformat()
        }
        
        return result
    
    def _check_admin_permission(self) -> bool:
        """
        检查管理员权限
        
        Returns:
            bool: 是否有管理员权限
        """
        return (
            self.request_type == 'admin' or
            'admin' in self.permissions or
            (self.user and getattr(self.user, 'is_staff', False))
        )