"""
商品数据传输对象模块

定义商品相关的数据传输对象，包括：
- ProductDTO: 商品数据传输对象
- 价格计算方法
- 数据格式化方法
- 数据验证方法
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
import json

from ..common.format_service import FormatService
from ..common.validation_service import ValidationService
from ..base.exceptions import ValidationException


@dataclass
class ProductDTO:
    """
    商品数据传输对象
    
    用于在服务层和API层之间传输商品数据
    """
    
    id: str
    name: str
    price: Decimal
    actual_price: Optional[Decimal] = None
    stock: int = 0
    status: str = '1'
    category_id: Optional[str] = None
    image: Optional[str] = None
    description: Optional[str] = None
    attach_data: Optional[List[Dict[str, Any]]] = field(default_factory=list)
    type: str = '1'
    sales_count: int = 0
    price_template_id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后验证"""
        self.validate()
    
    def validate(self) -> None:
        """
        验证商品数据
        
        Raises:
            ValidationException: 验证失败时抛出
        """
        # 验证必填字段
        ValidationService.validate_required(self.id, "商品ID")
        ValidationService.validate_required(self.name, "商品名称")
        ValidationService.validate_required(self.price, "商品价格")
        
        # 验证数据类型和格式
        ValidationService.validate_string(self.id, "商品ID", min_length=1, max_length=50)
        ValidationService.validate_string(self.name, "商品名称", min_length=1, max_length=200)
        ValidationService.validate_price(self.price, "商品价格")
        ValidationService.validate_stock(self.stock, "库存数量")
        ValidationService.validate_status(self.status, "商品状态")
        
        # 验证实际价格（如果提供）
        if self.actual_price is not None:
            ValidationService.validate_price(self.actual_price, "实际价格")
        
        # 验证分类ID（如果提供）
        if self.category_id:
            ValidationService.validate_string(self.category_id, "分类ID", min_length=1, max_length=50)
        
        # 验证图片URL（如果提供）
        if self.image:
            ValidationService.validate_string(self.image, "商品图片", max_length=500)
        
        # 验证描述（如果提供）
        if self.description:
            ValidationService.validate_string(self.description, "商品描述", max_length=2000)
    
    @classmethod
    def from_model(cls, product_model, user_membership: str = 'NormalUser') -> 'ProductDTO':
        """
        从Django模型创建DTO对象
        
        Args:
            product_model: Django商品模型实例
            user_membership: 用户会员等级
            
        Returns:
            ProductDTO: 商品DTO对象
        """
        # 处理附加数据
        attach_data = []
        if hasattr(product_model, 'attach') and product_model.attach:
            try:
                if isinstance(product_model.attach, str):
                    attach_data = json.loads(product_model.attach)
                elif isinstance(product_model.attach, list):
                    attach_data = product_model.attach
            except (json.JSONDecodeError, TypeError):
                attach_data = []
        
        # 计算实际价格
        actual_price = cls._calculate_actual_price(
            product_model.price,
            getattr(product_model, 'price_template', None),
            user_membership
        )
        
        return cls(
            id=str(product_model.id),
            name=product_model.name,
            price=Decimal(str(product_model.price)),
            actual_price=actual_price,
            stock=getattr(product_model, 'stock', 0),
            status=getattr(product_model, 'status', '1'),
            category_id=str(product_model.category_id) if product_model.category_id else None,
            image=getattr(product_model, 'image', None),
            description=getattr(product_model, 'info', None),
            attach_data=attach_data,
            type=getattr(product_model, 'type', '1'),
            sales_count=getattr(product_model, 'sales_count', 0),
            price_template_id=str(product_model.price_template.id) if hasattr(product_model, 'price_template') and product_model.price_template else None,
            created_at=getattr(product_model, 'created_at', None),
            updated_at=getattr(product_model, 'updated_at', None)
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], user_membership: str = 'NormalUser') -> 'ProductDTO':
        """
        从字典创建DTO对象
        
        Args:
            data: 商品数据字典
            user_membership: 用户会员等级
            
        Returns:
            ProductDTO: 商品DTO对象
        """
        # 处理附加数据
        attach_data = []
        if 'attach_data' in data and data['attach_data']:
            if isinstance(data['attach_data'], str):
                try:
                    attach_data = json.loads(data['attach_data'])
                except json.JSONDecodeError:
                    attach_data = []
            elif isinstance(data['attach_data'], list):
                attach_data = data['attach_data']
        elif 'attach' in data and data['attach']:
            if isinstance(data['attach'], str):
                try:
                    attach_data = json.loads(data['attach'])
                except json.JSONDecodeError:
                    attach_data = []
            elif isinstance(data['attach'], list):
                attach_data = data['attach']
        
        # 处理价格
        price = Decimal(str(data.get('price', 0)))
        actual_price = None
        if 'actual_price' in data and data['actual_price'] is not None:
            actual_price = Decimal(str(data['actual_price']))
        
        # 处理日期时间
        created_at = cls._parse_datetime(data.get('created_at'))
        updated_at = cls._parse_datetime(data.get('updated_at'))
        
        return cls(
            id=str(data['id']),
            name=data['name'],
            price=price,
            actual_price=actual_price,
            stock=int(data.get('stock', 0)),
            status=str(data.get('status', '1')),
            category_id=str(data['category_id']) if data.get('category_id') else None,
            image=data.get('image'),
            description=data.get('description') or data.get('info'),
            attach_data=attach_data,
            type=str(data.get('type', '1')),
            price_template_id=str(data['price_template_id']) if data.get('price_template_id') else None,
            created_at=created_at,
            updated_at=updated_at
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict: 商品数据字典
        """
        result = {
            'id': self.id,
            'name': self.name,
            'price': str(self.price),
            'stock': self.stock,
            'status': self.status,
            'image': self.image or '/static/images/product-placeholder.svg',
            'category_id': self.category_id or '',
            'type': self.type,
            'attach_data': self.attach_data or []
        }
        
        if self.actual_price is not None:
            result['actual_price'] = str(self.actual_price)
        
        if self.description:
            result['description'] = self.description
        
        if self.price_template_id:
            result['price_template_id'] = self.price_template_id
        
        if self.created_at:
            result['created_at'] = self.created_at.isoformat()
        
        if self.updated_at:
            result['updated_at'] = self.updated_at.isoformat()
        
        return result
    
    def to_admin_dict(self) -> Dict[str, Any]:
        """
        转换为管理端格式
        
        Returns:
            Dict: 管理端格式的商品数据
        """
        result = {
            'id': self.id,
            'name': self.name,
            'price': str(self.price),
            'stock': self.stock,
            'status': self.status,
            'image': self.image or '/static/images/product-placeholder.svg',
            'category_id': self.category_id or '',
            'info': self.description or '',
            'type': self.type,
            'sales_count': self.sales_count,
            'created_at': str(self.created_at),
            'updated_at': str(self.updated_at)
        }
        
        if self.actual_price is not None:
            result['actual_price'] = str(self.actual_price)
        
        if self.price_template_id:
            result['price_template'] = self.price_template_id
        
        # 管理端显示原始附加数据
        if self.attach_data:
            result['attach'] = json.dumps(self.attach_data, ensure_ascii=False)
        
        return result
    
    def to_user_dict(self, user_membership: str = 'NormalUser') -> Dict[str, Any]:
        """
        转换为用户端格式
        
        Args:
            user_membership: 用户会员等级
            
        Returns:
            Dict: 用户端格式的商品数据
        """
        # 如果没有计算实际价格，则计算一次
        if self.actual_price is None:
            actual_price = self._calculate_membership_price(user_membership)
        else:
            actual_price = self.actual_price
        
        result = {
            'id': self.id,
            'name': self.name,
            'price': str(self.price),
            'actual_price': str(actual_price),
            'type': self.type,
            'sales_count': self.sales_count,
            'stock': self.stock,
            'status': self.status,
            'attach': self.attach_data,
            'price_template_id': self.price_template_id,
            'info': self.description,
            'image': self.image or '/static/images/product-placeholder.svg',
            'category_id': self.category_id or '',
            'description': self.description or '',
        }
        
        # 处理商品参数
        if self.attach_data:
            result['params'] = self.attach_data
        
        return result
    
    def calculate_membership_price(self, user_membership: str = 'NormalUser') -> Decimal:
        """
        根据用户会员等级计算价格
        
        Args:
            user_membership: 用户会员等级
            
        Returns:
            Decimal: 计算后的价格
        """
        return self._calculate_membership_price(user_membership)
    
    def _calculate_membership_price(self, user_membership: str) -> Decimal:
        """
        内部价格计算方法
        
        Args:
            user_membership: 用户会员等级
            
        Returns:
            Decimal: 计算后的价格
        """
        # 如果已经有实际价格，直接返回
        if self.actual_price is not None:
            return self.actual_price
        
        # 基础价格处理
        base_price = self.price
        
        # 获取价格模板
        price_template = None
        if self.price_template_id:
            try:
                from api.models import PriceTemplate
                price_template = PriceTemplate.objects.get(id=self.price_template_id)
            except:
                price_template = None
        
        # 使用静态方法计算实际价格
        calculated_price = self._calculate_actual_price(base_price, price_template, user_membership)
        
        return calculated_price if calculated_price is not None else base_price
    
    @staticmethod
    def _handle_price_precision(price: Decimal) -> Decimal:
        """
        处理价格精度，确保不超过两位小数
        
        Args:
            price: 原始价格
            
        Returns:
            Decimal: 处理后的价格
        """
        try:
            # 检查是否需要处理超过两位小数的情况
            price_times_100 = price * Decimal('100')
            if price_times_100 % 1 != 0:
                # 存在两位小数以上的精度，保留两位小数并加0.01防止亏本
                price = price.quantize(Decimal('0.01'), rounding='ROUND_UP') + Decimal('0.01')
            
            return price
        except:
            return price
    
    @staticmethod
    def _calculate_actual_price(base_price: Decimal, price_template, user_membership: str) -> Optional[Decimal]:
        """
        根据价格模板计算实际价格
        
        Args:
            base_price: 基础价格
            price_template: 价格模板对象
            user_membership: 用户会员等级
            
        Returns:
            Optional[Decimal]: 计算后的实际价格
        """
        try:
            # 确保基础价格为Decimal类型
            base_price = Decimal(str(base_price))
            
            # 如果没有价格模板，返回原价（处理小数精度）
            if not price_template:
                return ProductDTO._handle_price_precision(base_price)
            
            # 解析价格模板的JSON数据
            try:
                pricing_data = json.loads(price_template.data_json)
            except json.JSONDecodeError:
                return ProductDTO._handle_price_precision(base_price)
            
            # 获取用户等级对应的加价值
            markup_value = pricing_data.get(user_membership)
            
            # 如果找不到用户等级对应的加价值，尝试使用普通用户的加价值
            if markup_value is None:
                markup_value = pricing_data.get("NormalUser", 0)
            
            # 转换为数值类型
            markup_value = Decimal(str(markup_value))
            
            # 根据模板类型计算实际价格
            if price_template.type == "1":
                # 固定金额加价
                actual_price = base_price + markup_value
            elif price_template.type == "2":
                # 百分比加价
                actual_price = base_price * (Decimal('1') + markup_value / Decimal('100'))
            else:
                # 未知的模板类型，使用原价
                actual_price = base_price
            
            # 确保价格不为负数
            if actual_price < 0:
                actual_price = Decimal('0.01')

            # 处理小数精度
            return ProductDTO._handle_price_precision(actual_price)
            
        except Exception as e:
            # 异常情况下返回处理过精度的基础价格
            return ProductDTO._handle_price_precision(Decimal(str(base_price)))
    
    @staticmethod
    def _parse_datetime(dt_str: Union[str, datetime, None]) -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            dt_str: 日期时间字符串或对象
            
        Returns:
            Optional[datetime]: 解析后的日期时间对象
        """
        if dt_str is None:
            return None
        
        if isinstance(dt_str, datetime):
            return dt_str
        
        if isinstance(dt_str, str):
            try:
                if 'T' in dt_str:
                    return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                else:
                    return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return None
        
        return None
    
    def is_in_stock(self) -> bool:
        """
        检查是否有库存
        
        Returns:
            bool: 是否有库存
        """
        return self.stock > 0
    
    def is_active(self) -> bool:
        """
        检查商品是否激活
        
        Returns:
            bool: 是否激活
        """
        return self.status == '1'
    
    def is_available(self) -> bool:
        """
        检查商品是否可用（激活且有库存）
        
        Returns:
            bool: 是否可用
        """
        return self.is_active() and self.is_in_stock()
    
    def get_discount_percentage(self, user_membership: str = 'NormalUser') -> float:
        """
        获取折扣百分比
        
        Args:
            user_membership: 用户会员等级
            
        Returns:
            float: 折扣百分比（0-100）
        """
        if self.actual_price is None:
            actual_price = self._calculate_membership_price(user_membership)
        else:
            actual_price = self.actual_price
        
        if self.price <= 0:
            return 0.0
        
        discount = (self.price - actual_price) / self.price * 100
        return max(0.0, float(discount))
    
    def get_savings_amount(self, user_membership: str = 'NormalUser') -> Decimal:
        """
        获取节省金额
        
        Args:
            user_membership: 用户会员等级
            
        Returns:
            Decimal: 节省的金额
        """
        if self.actual_price is None:
            actual_price = self._calculate_membership_price(user_membership)
        else:
            actual_price = self.actual_price
        
        savings = self.price - actual_price
        return max(Decimal('0'), savings)
    
    def update_stock(self, quantity: int) -> None:
        """
        更新库存数量
        
        Args:
            quantity: 新的库存数量
            
        Raises:
            ValidationException: 库存数量无效时抛出
        """
        if quantity < 0:
            raise ValidationException("库存数量不能为负数")
        
        self.stock = quantity
    
    def reduce_stock(self, quantity: int) -> bool:
        """
        减少库存
        
        Args:
            quantity: 要减少的数量
            
        Returns:
            bool: 是否成功减少库存
            
        Raises:
            ValidationException: 参数无效时抛出
        """
        if quantity <= 0:
            raise ValidationException("减少数量必须大于0")
        
        if self.stock < quantity:
            return False
        
        self.stock -= quantity
        return True
    
    def add_stock(self, quantity: int) -> None:
        """
        增加库存
        
        Args:
            quantity: 要增加的数量
            
        Raises:
            ValidationException: 参数无效时抛出
        """
        if quantity <= 0:
            raise ValidationException("增加数量必须大于0")
        
        self.stock += quantity
    
    def clone(self, new_id: Optional[str] = None) -> 'ProductDTO':
        """
        克隆商品对象
        
        Args:
            new_id: 新的商品ID（可选）
            
        Returns:
            ProductDTO: 克隆的商品对象
        """
        return ProductDTO(
            id=new_id or self.id,
            name=self.name,
            price=self.price,
            actual_price=self.actual_price,
            stock=self.stock,
            status=self.status,
            category_id=self.category_id,
            image=self.image,
            description=self.description,
            attach_data=self.attach_data.copy() if self.attach_data else [],
            type=self.type,
            price_template_id=self.price_template_id,
            created_at=self.created_at,
            updated_at=self.updated_at
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ProductDTO(id={self.id}, name={self.name}, price={self.price})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"ProductDTO(id={self.id}, name={self.name}, price={self.price}, "
                f"stock={self.stock}, status={self.status})")


class ProductFilter:
    """
    商品过滤器
    
    用于过滤和搜索商品
    """
    
    @staticmethod
    def filter_by_category(products: List[ProductDTO], category_id: str) -> List[ProductDTO]:
        """
        按分类过滤商品
        
        Args:
            products: 商品列表
            category_id: 分类ID
            
        Returns:
            List[ProductDTO]: 过滤后的商品列表
        """
        return [p for p in products if p.category_id == category_id]
    
    @staticmethod
    def filter_by_status(products: List[ProductDTO], status: str = '1') -> List[ProductDTO]:
        """
        按状态过滤商品
        
        Args:
            products: 商品列表
            status: 商品状态
            
        Returns:
            List[ProductDTO]: 过滤后的商品列表
        """
        return [p for p in products if p.status == status]
    
    @staticmethod
    def filter_in_stock(products: List[ProductDTO]) -> List[ProductDTO]:
        """
        过滤有库存的商品
        
        Args:
            products: 商品列表
            
        Returns:
            List[ProductDTO]: 有库存的商品列表
        """
        return [p for p in products if p.is_in_stock()]
    
    @staticmethod
    def filter_available(products: List[ProductDTO]) -> List[ProductDTO]:
        """
        过滤可用的商品（激活且有库存）
        
        Args:
            products: 商品列表
            
        Returns:
            List[ProductDTO]: 可用的商品列表
        """
        return [p for p in products if p.is_available()]
    
    @staticmethod
    def search_by_name(products: List[ProductDTO], keyword: str) -> List[ProductDTO]:
        """
        按名称搜索商品
        
        Args:
            products: 商品列表
            keyword: 搜索关键词
            
        Returns:
            List[ProductDTO]: 搜索结果列表
        """
        if not keyword:
            return products
        
        keyword_lower = keyword.lower()
        return [p for p in products if keyword_lower in p.name.lower()]
    
    @staticmethod
    def filter_by_price_range(products: List[ProductDTO], 
                            min_price: Optional[Decimal] = None,
                            max_price: Optional[Decimal] = None) -> List[ProductDTO]:
        """
        按价格范围过滤商品
        
        Args:
            products: 商品列表
            min_price: 最低价格
            max_price: 最高价格
            
        Returns:
            List[ProductDTO]: 过滤后的商品列表
        """
        result = products
        
        if min_price is not None:
            result = [p for p in result if p.price >= min_price]
        
        if max_price is not None:
            result = [p for p in result if p.price <= max_price]
        
        return result


class ProductSorter:
    """
    商品排序器
    
    提供各种商品排序功能
    """
    
    @staticmethod
    def sort_by_price(products: List[ProductDTO], ascending: bool = True) -> List[ProductDTO]:
        """
        按价格排序
        
        Args:
            products: 商品列表
            ascending: 是否升序
            
        Returns:
            List[ProductDTO]: 排序后的商品列表
        """
        return sorted(products, key=lambda p: p.price, reverse=not ascending)
    
    @staticmethod
    def sort_by_stock(products: List[ProductDTO], ascending: bool = False) -> List[ProductDTO]:
        """
        按库存排序
        
        Args:
            products: 商品列表
            ascending: 是否升序
            
        Returns:
            List[ProductDTO]: 排序后的商品列表
        """
        return sorted(products, key=lambda p: p.stock, reverse=not ascending)
    
    @staticmethod
    def sort_by_name(products: List[ProductDTO], ascending: bool = True) -> List[ProductDTO]:
        """
        按名称排序
        
        Args:
            products: 商品列表
            ascending: 是否升序
            
        Returns:
            List[ProductDTO]: 排序后的商品列表
        """
        return sorted(products, key=lambda p: p.name, reverse=not ascending)
    
    @staticmethod
    def sort_by_created_time(products: List[ProductDTO], ascending: bool = False) -> List[ProductDTO]:
        """
        按创建时间排序
        
        Args:
            products: 商品列表
            ascending: 是否升序（默认最新的在前）
            
        Returns:
            List[ProductDTO]: 排序后的商品列表
        """
        return sorted(
            products, 
            key=lambda p: p.created_at or datetime.min, 
            reverse=not ascending
        )


class ProductStatistics:
    """
    商品统计器
    
    提供商品相关的统计功能
    """
    
    @staticmethod
    def get_product_stats(products: List[ProductDTO]) -> Dict[str, Any]:
        """
        获取商品统计信息
        
        Args:
            products: 商品列表
            
        Returns:
            Dict: 统计信息
        """
        if not products:
            return {
                'total_count': 0,
                'active_count': 0,
                'in_stock_count': 0,
                'out_of_stock_count': 0,
                'total_stock': 0,
                'avg_price': 0,
                'min_price': 0,
                'max_price': 0,
                'category_distribution': {}
            }
        
        active_products = [p for p in products if p.is_active()]
        in_stock_products = [p for p in products if p.is_in_stock()]
        
        prices = [float(p.price) for p in products]
        total_stock = sum(p.stock for p in products)
        
        # 分类分布统计
        category_distribution = {}
        for product in products:
            category_id = product.category_id or 'uncategorized'
            category_distribution[category_id] = category_distribution.get(category_id, 0) + 1
        
        return {
            'total_count': len(products),
            'active_count': len(active_products),
            'in_stock_count': len(in_stock_products),
            'out_of_stock_count': len(products) - len(in_stock_products),
            'total_stock': total_stock,
            'avg_price': sum(prices) / len(prices) if prices else 0,
            'min_price': min(prices) if prices else 0,
            'max_price': max(prices) if prices else 0,
            'category_distribution': category_distribution
        }