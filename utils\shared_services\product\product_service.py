"""
商品服务模块

提供商品相关的业务逻辑处理，包括：
- 商品查询、创建、更新、删除
- 价格计算和库存检查
- 对接商品更新逻辑
- 数据格式化和验证
"""

import uuid
import json
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime
from django.db import transaction
from django.core.paginator import Paginator

from ..base.base_service import BaseService
from ..base.decorators import (
    log_service_call, 
    require_permission, 
    monitor_performance,
    handle_exceptions,
    cache_result
)
from ..base.exceptions import (
    ValidationException,
    ResourceNotFoundException,
    PermissionDeniedException,
    BusinessLogicException
)
from ..common.validation_service import ValidationService
from ..common.format_service import FormatService
from .product_dto import ProductDTO, ProductFilter, ProductSorter


class ProductService(BaseService):
    """
    商品服务类
    
    提供商品相关的业务逻辑处理
    """
    
    @log_service_call(include_params=True)
    @monitor_performance(threshold_seconds=1.0)
    def get_product_list(self, filters: Optional[Dict[str, Any]] = None, 
                        pagination: Optional[Dict[str, int]] = None,
                        format_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取商品列表
        
        Args:
            filters: 过滤条件
            pagination: 分页参数
            format_type: 格式化类型
            
        Returns:
            Dict: 格式化后的商品列表数据
        """
        try:
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 验证和处理过滤条件
            if filters:
                filters = ValidationService.validate_filter_params(filters)
            else:
                filters = {}
            
            # 验证和处理分页参数
            if pagination:
                pagination = ValidationService.validate_pagination(
                    pagination.get('page', 1),
                    pagination.get('page_size', 20)
                )
            else:
                pagination = ValidationService.validate_pagination()
            
            # 从数据库获取商品数据
            from api.models import Goods
            
            # 构建查询条件
            query = Goods.objects.all()
            
            # 应用过滤条件
            if 'category_id' in filters:
                query = query.filter(category_id=filters['category_id'])
            
            if 'status' in filters:
                query = query.filter(status=filters['status'])
            else:
                # 默认只显示激活的商品
                query = query.filter(status='1')
            
            if 'name' in filters:
                query = query.filter(name__icontains=filters['name'])
            
            # 排序
            query = query.order_by('-created_at')
            
            # 分页处理
            paginator = Paginator(query, pagination['page_size'])
            page_obj = paginator.get_page(pagination['page'])
            
            # 转换为DTO并格式化
            user_membership = self._get_user_membership()
            product_dtos = []
            
            for product in page_obj:
                product_dto = ProductDTO.from_model(product, user_membership)
                product_dtos.append(product_dto)
            
            # 格式化输出
            formatted_products = []
            for dto in product_dtos:
                if format_type == 'admin':
                    formatted_products.append(dto.to_admin_dict())
                else:
                    formatted_products.append(dto.to_user_dict(user_membership))
            
            # 构建分页响应
            result = FormatService.format_list_data(
                formatted_products,
                format_type,
                pagination['page'],
                pagination['page_size'],
                paginator.count
            )
            
            return result
            
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f"获取商品列表失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def get_product_detail(self, product_id: str, 
                          format_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取商品详情
        
        Args:
            product_id: 商品ID
            format_type: 格式化类型
            
        Returns:
            Dict: 商品详情数据
            
        Raises:
            ResourceNotFoundException: 商品不存在时抛出
        """
        try:
            # 验证参数
            ValidationService.validate_required(product_id, "商品ID")
            ValidationService.validate_string(product_id, "商品ID", min_length=1)
            
            # 确定格式化类型
            if format_type is None:
                format_type = self.request_type
            
            # 从数据库获取商品
            from api.models import Goods
            
            try:
                product = Goods.objects.get(id=product_id)
            except Goods.DoesNotExist:
                raise ResourceNotFoundException(
                    f"商品不存在",
                    resource_type="product",
                    resource_id=product_id
                )
            
            # 转换为DTO并格式化
            user_membership = self._get_user_membership()
            # print("用户会员等级: " + user_membership)
            product_dto = ProductDTO.from_model(product, user_membership)
            
            if format_type == 'admin':
                return product_dto.to_admin_dict()
            else:
                return product_dto.to_user_dict(user_membership)
                
        except (ValidationException, ResourceNotFoundException):
            raise
        except Exception as e:
            self.logger.error(f"获取商品详情失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建商品（仅管理端）
        
        Args:
            product_data: 商品数据
            
        Returns:
            Dict: 创建后的商品数据
            
        Raises:
            ValidationException: 数据验证失败时抛出
            BusinessLogicException: 业务逻辑错误时抛出
        """
        try:
            # 使用商品验证器验证数据
            validator = ValidationService.create_product_validator()
            validated_data = validator(product_data)
            
            # 开始事务创建商品
            with transaction.atomic():
                from api.models import Goods
                
                # 生成新商品ID
                new_id = str(uuid.uuid4())
                
                # 处理附加数据
                attach_data = validated_data.get('attach_data', [])
                if attach_data and isinstance(attach_data, list):
                    attach_json = json.dumps(attach_data, ensure_ascii=False)
                else:
                    attach_json = None
                
                # 创建商品
                product = Goods(
                    id=new_id,
                    name=validated_data['name'],
                    price=validated_data['price'],
                    stock=validated_data.get('stock', 0),
                    status=validated_data.get('status', '1'),
                    category_id=validated_data.get('category_id'),
                    image=validated_data.get('image'),
                    info=validated_data.get('description', ''),
                    attach=attach_json,
                    type=validated_data.get('type', '1')
                )
                product.save()
                
                # 转换为DTO并返回
                product_dto = ProductDTO.from_model(product)
                
                self.logger.info(f"商品创建成功: {new_id}")
                return product_dto.to_admin_dict()
                
        except (ValidationException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"创建商品失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def update_product(self, product_id: str, 
                      update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新商品（仅管理端）
        
        Args:
            product_id: 商品ID
            update_data: 更新数据
            
        Returns:
            Dict: 更新后的商品数据
            
        Raises:
            ResourceNotFoundException: 商品不存在时抛出
            ValidationException: 数据验证失败时抛出
        """
        try:
            # 验证商品ID
            ValidationService.validate_required(product_id, "商品ID")
            ValidationService.validate_string(product_id, "商品ID", min_length=1)
            
            # 检查是否有可更新的字段
            updatable_fields = ['name', 'price', 'stock', 'status', 'category_id', 'image', 'description', 'attach_data']
            has_updates = any(field in update_data for field in updatable_fields)
            
            if not has_updates:
                raise ValidationException("没有提供可更新的字段")
            
            # 开始事务更新
            with transaction.atomic():
                from api.models import Goods
                
                # 获取要更新的商品
                try:
                    product = Goods.objects.get(id=product_id)
                except Goods.DoesNotExist:
                    raise ResourceNotFoundException(
                        "商品不存在",
                        resource_type="product",
                        resource_id=product_id
                    )
                
                # 更新字段
                if 'name' in update_data:
                    name = ValidationService.validate_string(
                        update_data['name'], "商品名称", min_length=1, max_length=200
                    )
                    product.name = name
                
                if 'price' in update_data:
                    price = ValidationService.validate_price(update_data['price'], "商品价格")
                    product.price = price
                
                if 'stock' in update_data:
                    stock = ValidationService.validate_stock(update_data['stock'], "库存数量")
                    product.stock = stock
                
                if 'status' in update_data:
                    status = ValidationService.validate_status(update_data['status'], "商品状态")
                    product.status = status
                
                if 'category_id' in update_data and update_data['category_id']:
                    category_id = ValidationService.validate_string(
                        update_data['category_id'], "分类ID", min_length=1
                    )
                    product.category_id = category_id
                
                if 'image' in update_data:
                    image = update_data['image']
                    if image:
                        ValidationService.validate_string(image, "商品图片", max_length=500)
                    product.image = image
                
                if 'description' in update_data:
                    description = update_data['description']
                    if description:
                        ValidationService.validate_string(description, "商品描述", max_length=2000)
                    product.info = description
                
                if 'attach_data' in update_data:
                    attach_data = update_data['attach_data']
                    if attach_data and isinstance(attach_data, list):
                        product.attach = json.dumps(attach_data, ensure_ascii=False)
                    else:
                        product.attach = None
                
                # 保存更新
                product.save()
                
                # 转换为DTO并返回
                product_dto = ProductDTO.from_model(product)
                
                self.logger.info(f"商品更新成功: {product_id}")
                return product_dto.to_admin_dict()
                
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"更新商品失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def delete_product(self, product_id: str) -> bool:
        """
        删除商品（仅管理端）
        
        Args:
            product_id: 商品ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            ResourceNotFoundException: 商品不存在时抛出
            BusinessLogicException: 业务逻辑错误时抛出
        """
        try:
            # 验证商品ID
            ValidationService.validate_required(product_id, "商品ID")
            ValidationService.validate_string(product_id, "商品ID", min_length=1)
            
            # 开始事务删除
            with transaction.atomic():
                from api.models import Goods
                
                # 查找要删除的商品
                try:
                    product = Goods.objects.get(id=product_id)
                except Goods.DoesNotExist:
                    raise ResourceNotFoundException(
                        "商品不存在",
                        resource_type="product",
                        resource_id=product_id
                    )
                
                # 检查是否有订单使用该商品
                from api.models import Order
                orders_using_product = Order.objects.filter(data__contains=product_id)
                if orders_using_product.exists():
                    raise BusinessLogicException("该商品已有订单，无法删除")
                
                # 执行删除
                product.delete()
                
                self.logger.info(f"商品删除成功: {product_id}")
                return True
                
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"删除商品失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def calculate_actual_price(self, product_id: str, 
                              user_membership: str = 'NormalUser') -> Decimal:
        """
        计算商品实际价格（考虑会员等级）
        
        Args:
            product_id: 商品ID
            user_membership: 用户会员等级
            
        Returns:
            Decimal: 实际价格
        """
        try:
            # 获取商品信息
            product_data = self.get_product_detail(product_id, 'admin')
            base_price = Decimal(str(product_data['price'].replace('¥', '')))
            
            # 获取价格模板
            price_template = None
            if 'price_template' in product_data:
                from api.models import PriceTemplate
                try:
                    price_template = PriceTemplate.objects.get(id=product_data['price_template'])
                except PriceTemplate.DoesNotExist:
                    price_template = None
            
            # 计算实际价格
            actual_price = ProductDTO._calculate_actual_price(
                base_price, price_template, user_membership
            )
            
            return actual_price
            
        except Exception as e:
            self.logger.error(f"计算商品价格失败: {str(e)}")
            # 返回基础价格作为fallback
            try:
                product_data = self.get_product_detail(product_id, 'admin')
                return Decimal(str(product_data['price'].replace('¥', '')))
            except:
                return Decimal('0')
    
    @log_service_call(include_params=True)
    @require_permission('admin')
    def update_docking_product(self, product_id: str) -> Dict[str, Any]:
        """
        更新对接商品信息
        
        Args:
            product_id: 商品ID
            
        Returns:
            Dict: 更新结果
        """
        try:
            # 验证商品ID
            ValidationService.validate_required(product_id, "商品ID")
            
            # 获取商品信息
            from api.models import Goods
            
            try:
                product = Goods.objects.get(id=product_id)
            except Goods.DoesNotExist:
                raise ResourceNotFoundException(
                    "商品不存在",
                    resource_type="product",
                    resource_id=product_id
                )
            
            # 检查是否为对接商品
            if product.type != '2':  # 假设type=2表示对接商品
                raise BusinessLogicException("该商品不是对接商品")
            
            # 这里应该调用对接商品的更新逻辑
            # 由于具体的对接逻辑可能很复杂，这里只做基本框架
            
            # 模拟对接更新过程
            update_result = {
                'product_id': product_id,
                'updated_at': FormatService.format_datetime(datetime.now()),
                'status': 'success',
                'message': '对接商品更新成功'
            }
            
            self.logger.info(f"对接商品更新成功: {product_id}")
            return update_result
            
        except (ValidationException, ResourceNotFoundException, BusinessLogicException):
            raise
        except Exception as e:
            self.logger.error(f"更新对接商品失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def check_stock_status(self, product_id: str) -> Dict[str, Any]:
        """
        检查商品库存状态
        
        Args:
            product_id: 商品ID
            
        Returns:
            Dict: 库存状态信息
        """
        try:
            # 获取商品信息
            product_data = self.get_product_detail(product_id)
            
            stock = product_data.get('stock', 0)
            status = product_data.get('status', '0')
            
            # 判断库存状态
            stock_status = {
                'product_id': product_id,
                'stock': stock,
                'status': status,
                'is_available': status == '1' and stock > 0,
                'is_in_stock': stock > 0,
                'is_active': status == '1',
                'stock_level': self._get_stock_level(stock)
            }
            
            return stock_status
            
        except Exception as e:
            self.logger.error(f"检查库存状态失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def search_products(self, keyword: str, filters: Optional[Dict[str, Any]] = None,
                       format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        搜索商品
        
        Args:
            keyword: 搜索关键词
            filters: 额外过滤条件
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            # 验证搜索关键词
            ValidationService.validate_required(keyword, "搜索关键词")
            keyword = ValidationService.validate_string(keyword, "搜索关键词", min_length=1)
            
            # 合并搜索条件
            search_filters = filters or {}
            search_filters['name'] = keyword
            
            # 调用商品列表获取方法
            result = self.get_product_list(
                filters=search_filters,
                format_type=format_type
            )
            
            return result['list']
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"搜索商品失败: {str(e)}")
            raise
    
    @log_service_call(include_params=True)
    def get_products_by_category(self, category_id: str,
                               format_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据分类获取商品列表
        
        Args:
            category_id: 分类ID
            format_type: 格式化类型
            
        Returns:
            List[Dict]: 分类下的商品列表
        """
        try:
            # 验证分类ID
            ValidationService.validate_required(category_id, "分类ID")
            ValidationService.validate_string(category_id, "分类ID", min_length=1)
            
            # 调用商品列表获取方法
            result = self.get_product_list(
                filters={'category_id': category_id},
                format_type=format_type
            )
            
            return result['list']
            
        except ValidationException:
            raise
        except Exception as e:
            self.logger.error(f"根据分类获取商品失败: {str(e)}")
            raise
    
    def _get_user_membership(self) -> str:
        """
        获取用户会员等级
        
        Returns:
            str: 用户会员等级
        """
        try:
            # 从认证服务获取会员等级
            from ..common.auth_service import AuthService
            return AuthService.get_user_membership_level(self.context)
        except:
            return 'NormalUser'
    
    def _get_stock_level(self, stock: int) -> str:
        """
        获取库存等级描述
        
        Args:
            stock: 库存数量
            
        Returns:
            str: 库存等级
        """
        if stock <= 0:
            return 'out_of_stock'
        elif stock <= 10:
            return 'low_stock'
        elif stock <= 50:
            return 'medium_stock'
        else:
            return 'high_stock'
    
    def _format_product_data(self, product: ProductDTO, 
                           format_type: str, user_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        格式化单个商品数据（私有方法）
        
        Args:
            product: 商品DTO对象
            format_type: 格式化类型
            user_context: 用户上下文
            
        Returns:
            Dict: 格式化后的商品数据
        """
        user_membership = 'NormalUser'
        if user_context:
            user_membership = user_context.get('membership_level', 'NormalUser')
        
        if format_type == 'admin':
            return product.to_admin_dict()
        else:
            return product.to_user_dict(user_membership)