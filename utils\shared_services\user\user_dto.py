"""
用户数据传输对象 (UserDTO)

提供用户数据的结构化表示和格式化功能，包括：
- 用户基本信息
- 会员等级信息
- 权限信息
- 不同端的数据格式化
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal


@dataclass
class UserDTO:
    """
    用户数据传输对象
    
    用于在服务层和API层之间传输用户数据
    """
    
    # 基本信息
    user_id: str
    username: str
    email: str
    user_key: str
    
    # 状态信息
    is_active: bool = True
    is_staff: bool = False
    
    # 时间信息
    date_joined: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    # 会员信息
    membership_level: str = '普通用户'
    balance: Decimal = Decimal('0.00')
    total_spent: Decimal = Decimal('0.00')
    
    # 权限信息
    permissions: List[str] = field(default_factory=list)
    groups: List[str] = field(default_factory=list)
    
    # 扩展信息
    profile_data: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_model(cls, user_model, include_permissions: bool = False) -> 'UserDTO':
        """
        从Django用户模型创建DTO对象
        
        Args:
            user_model: Django用户模型实例
            include_permissions: 是否包含权限信息
            
        Returns:
            UserDTO: 用户DTO对象
        """
        permissions = []
        groups = []
        
        if include_permissions:
            # 获取用户权限
            if hasattr(user_model, 'user_permissions'):
                permissions = [
                    perm.codename for perm in user_model.user_permissions.all()
                ]
            
            # 获取用户组
            if hasattr(user_model, 'groups'):
                groups = [group.name for group in user_model.groups.all()]
        
        return cls(
            user_id=str(user_model.user_id),
            username=user_model.username,
            email=user_model.email,
            user_key=str(user_model.user_key),
            is_active=user_model.is_active,
            is_staff=user_model.is_staff,
            date_joined=user_model.date_joined,
            last_login=user_model.last_login,
            membership_level=user_model.membership_level,
            balance=user_model.balance,
            total_spent=user_model.total_spent,
            permissions=permissions,
            groups=groups
        )
    
    def to_admin_dict(self) -> Dict[str, Any]:
        """
        转换为管理端格式的字典
        
        Returns:
            Dict: 管理端格式的用户数据
        """
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'user_key': self.user_key,
            'is_active': self.is_active,
            'is_staff': self.is_staff,
            'date_joined': self.date_joined.isoformat() if self.date_joined else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'membership_level': self.membership_level,
            'balance': str(self.balance),
            'total_spent': str(self.total_spent),
            'permissions': self.permissions,
            'groups': self.groups,
            'profile_data': self.profile_data
        }
    
    def to_user_dict(self) -> Dict[str, Any]:
        """
        转换为用户端格式的字典
        
        Returns:
            Dict: 用户端格式的用户数据
        """
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'user_key': self.user_key,
            'is_active': self.is_active,
            'date_joined': self.date_joined.isoformat() if self.date_joined else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'membership_level': self.membership_level,
            'balance': str(self.balance),
            'total_spent': str(self.total_spent)
        }
    
    def to_public_dict(self) -> Dict[str, Any]:
        """
        转换为公开格式的字典（隐藏敏感信息）
        
        Returns:
            Dict: 公开格式的用户数据
        """
        return {
            'user_id': self.user_id,
            'username': self.username,
            'membership_level': self.membership_level,
            'date_joined': self.date_joined.isoformat() if self.date_joined else None
        }
    
    def to_api_dict(self) -> Dict[str, Any]:
        """
        转换为API格式的字典
        
        Returns:
            Dict: API格式的用户数据
        """
        return {
            'id': self.user_id,
            'username': self.username,
            'email': self.email,
            'userKey': self.user_key,
            'isActive': self.is_active,
            'isStaff': self.is_staff,
            'dateJoined': self.date_joined.isoformat() if self.date_joined else None,
            'lastLogin': self.last_login.isoformat() if self.last_login else None,
            'membershipLevel': self.membership_level,
            'balance': float(self.balance),
            'totalSpent': float(self.total_spent),
            'permissions': self.permissions,
            'groups': self.groups
        }
    
    def get_membership_display(self) -> str:
        """
        获取会员等级的显示名称
        
        Returns:
            str: 会员等级显示名称
        """
        membership_mapping = {
            'NormalUser': '普通用户',
            '普通用户': '普通用户',
            'VipUser': 'VIP用户',
            'PremiumUser': '高级用户',
            'DiamondUser': '钻石用户'
        }
        
        return membership_mapping.get(self.membership_level, self.membership_level)
    
    def has_permission(self, permission: str) -> bool:
        """
        检查用户是否有指定权限
        
        Args:
            permission: 权限名称
            
        Returns:
            bool: 是否有权限
        """
        return permission in self.permissions or self.is_staff
    
    def is_vip_member(self) -> bool:
        """
        检查是否为VIP会员
        
        Returns:
            bool: 是否为VIP会员
        """
        vip_levels = ['VipUser', 'PremiumUser', 'DiamondUser']
        return self.membership_level in vip_levels
    
    def get_balance_display(self) -> str:
        """
        获取余额的显示格式
        
        Returns:
            str: 格式化的余额显示
        """
        return f"¥{self.balance:.2f}"
    
    def get_total_spent_display(self) -> str:
        """
        获取累计消费的显示格式
        
        Returns:
            str: 格式化的累计消费显示
        """
        return f"¥{self.total_spent:.2f}"
    
    def update_balance(self, amount: Decimal, operation: str = 'add') -> None:
        """
        更新用户余额
        
        Args:
            amount: 金额
            operation: 操作类型 ('add' 或 'subtract')
        """
        if operation == 'add':
            self.balance += amount
        elif operation == 'subtract':
            self.balance -= amount
            if self.balance < 0:
                self.balance = Decimal('0.00')
    
    def add_spending(self, amount: Decimal) -> None:
        """
        增加累计消费
        
        Args:
            amount: 消费金额
        """
        self.total_spent += amount
    
    def get_user_level_benefits(self) -> Dict[str, Any]:
        """
        获取用户等级对应的权益
        
        Returns:
            Dict: 用户权益信息
        """
        benefits_mapping = {
            '普通用户': {
                'discount_rate': 1.0,
                'max_orders_per_day': 10,
                'priority_support': False,
                'exclusive_products': False
            },
            'VipUser': {
                'discount_rate': 0.95,
                'max_orders_per_day': 20,
                'priority_support': True,
                'exclusive_products': False
            },
            'PremiumUser': {
                'discount_rate': 0.90,
                'max_orders_per_day': 50,
                'priority_support': True,
                'exclusive_products': True
            },
            'DiamondUser': {
                'discount_rate': 0.85,
                'max_orders_per_day': 100,
                'priority_support': True,
                'exclusive_products': True
            }
        }
        
        return benefits_mapping.get(self.membership_level, benefits_mapping['普通用户'])
    
    def can_access_admin(self) -> bool:
        """
        检查是否可以访问管理后台
        
        Returns:
            bool: 是否可以访问管理后台
        """
        return self.is_staff and self.is_active
    
    def get_security_info(self) -> Dict[str, Any]:
        """
        获取安全相关信息（用于管理端）
        
        Returns:
            Dict: 安全信息
        """
        return {
            'user_key': self.user_key,
            'is_active': self.is_active,
            'is_staff': self.is_staff,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'permissions_count': len(self.permissions),
            'groups_count': len(self.groups)
        }


@dataclass
class UserListFilter:
    """
    用户列表过滤器
    """
    
    # 基本过滤
    username: Optional[str] = None
    email: Optional[str] = None
    membership_level: Optional[str] = None
    is_active: Optional[bool] = None
    is_staff: Optional[bool] = None
    
    # 时间范围过滤
    date_joined_start: Optional[datetime] = None
    date_joined_end: Optional[datetime] = None
    last_login_start: Optional[datetime] = None
    last_login_end: Optional[datetime] = None
    
    # 金额范围过滤
    balance_min: Optional[Decimal] = None
    balance_max: Optional[Decimal] = None
    total_spent_min: Optional[Decimal] = None
    total_spent_max: Optional[Decimal] = None
    
    # 搜索关键词
    search_keyword: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict: 过滤条件字典
        """
        result = {}
        
        for field_name, field_value in self.__dict__.items():
            if field_value is not None:
                if isinstance(field_value, datetime):
                    result[field_name] = field_value.isoformat()
                elif isinstance(field_value, Decimal):
                    result[field_name] = str(field_value)
                else:
                    result[field_name] = field_value
        
        return result


@dataclass
class UserSorter:
    """
    用户列表排序器
    """
    
    field: str = 'date_joined'  # 排序字段
    direction: str = 'desc'     # 排序方向 ('asc' 或 'desc')
    
    VALID_FIELDS = [
        'username', 'email', 'date_joined', 'last_login',
        'membership_level', 'balance', 'total_spent', 'is_active'
    ]
    
    def __post_init__(self):
        """验证排序参数"""
        if self.field not in self.VALID_FIELDS:
            self.field = 'date_joined'
        
        if self.direction not in ['asc', 'desc']:
            self.direction = 'desc'
    
    def get_order_by(self) -> str:
        """
        获取Django ORM的order_by参数
        
        Returns:
            str: order_by参数
        """
        prefix = '-' if self.direction == 'desc' else ''
        return f'{prefix}{self.field}'