"""
用户服务 (UserService)

提供用户相关的业务逻辑处理，包括：
- 用户信息查询和更新
- 会员等级管理
- 权限验证
- 用户列表管理（管理端）
"""

from typing import Optional, Dict, Any, List, Tuple
from decimal import Decimal
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q, F
from django.db import transaction
from django.utils import timezone
from datetime import datetime

from utils.shared_services.base.base_service import BaseService
from utils.shared_services.base.exceptions import (
    ServiceException, 
    PermissionDeniedException, 
    ValidationException,
    ResourceNotFoundException
)
from utils.shared_services.user.user_dto import UserDTO, UserListFilter, UserSorter
from user.models import User


class UserService(BaseService):
    """
    用户服务类
    
    提供用户相关的业务逻辑处理
    """
    
    def __init__(self, context=None):
        """
        初始化用户服务
        
        Args:
            context: 请求上下文，包含用户信息、权限等
        """
        super().__init__(context)
        self.model = User
    
    def get_user_info(self, user_id: Optional[str] = None, format_type: str = 'user', 
                     include_permissions: bool = False) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID，如果为None则获取当前用户信息
            format_type: 格式化类型 ('admin', 'user', 'public', 'api')
            include_permissions: 是否包含权限信息
            
        Returns:
            Dict: 格式化后的用户信息
            
        Raises:
            ResourceNotFoundException: 用户不存在
            PermissionDeniedException: 权限不足
        """
        try:
            # 确定要查询的用户ID
            target_user_id = user_id
            if not target_user_id:
                if not self.user:
                    raise PermissionDeniedException("未登录用户无法获取用户信息")
                target_user_id = str(self.user.user_id)
            
            # 权限检查
            if format_type == 'admin' and not self._check_admin_permission():
                raise PermissionDeniedException("需要管理员权限")
            
            # 如果不是管理员且查询其他用户信息，只允许公开格式
            if (self.user and str(self.user.user_id) != target_user_id and 
                not self._check_admin_permission() and format_type not in ['public']):
                format_type = 'public'
            
            # 查询用户
            user = self.model.objects.get(user_id=target_user_id)
            
            # 创建DTO并格式化
            user_dto = UserDTO.from_model(user, include_permissions=include_permissions)
            
            return self._format_user_response(user_dto, format_type)
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {target_user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"获取用户信息失败: {str(e)}")
    
    def get_user_list(self, filters: Optional[UserListFilter] = None, 
                     sorter: Optional[UserSorter] = None,
                     page: int = 1, page_size: int = 20,
                     format_type: str = 'admin') -> Dict[str, Any]:
        """
        获取用户列表（管理端功能）
        
        Args:
            filters: 过滤条件
            sorter: 排序条件
            page: 页码
            page_size: 每页数量
            format_type: 格式化类型
            
        Returns:
            Dict: 包含用户列表和分页信息的字典
            
        Raises:
            PermissionDeniedException: 权限不足
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException("需要管理员权限")
        
        try:
            # 构建查询
            queryset = self.model.objects.all()
            
            # 应用过滤条件
            if filters:
                queryset = self._apply_user_filters(queryset, filters)
            
            # 应用排序
            if sorter:
                queryset = queryset.order_by(sorter.get_order_by())
            else:
                queryset = queryset.order_by('-date_joined')
            
            # 计算总数
            total_count = queryset.count()
            
            # 应用分页
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            users = queryset[start_index:end_index]
            
            # 格式化数据
            user_list = []
            for user in users:
                user_dto = UserDTO.from_model(user, include_permissions=True)
                user_list.append(self._format_user_response(user_dto, format_type))
            
            # 计算分页信息
            total_pages = (total_count + page_size - 1) // page_size
            
            return {
                'users': user_list,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
            
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"获取用户列表失败: {str(e)}")
    
    def update_user_info(self, user_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            update_data: 更新数据
            
        Returns:
            Dict: 更新后的用户信息
            
        Raises:
            ResourceNotFoundException: 用户不存在
            PermissionDeniedException: 权限不足
            ValidationException: 数据验证失败
        """
        try:
            # 权限检查
            if not self._check_update_permission(user_id):
                raise PermissionDeniedException("无权限更新此用户信息")
            
            # 验证更新数据
            validated_data = self._validate_update_data(update_data)
            
            # 查询用户
            user = self.model.objects.get(user_id=user_id)
            
            # 更新用户信息
            with transaction.atomic():
                for field, value in validated_data.items():
                    if hasattr(user, field):
                        setattr(user, field, value)
                
                user.save()
            
            # 返回更新后的用户信息
            user_dto = UserDTO.from_model(user)
            format_type = 'admin' if self._check_admin_permission() else 'user'
            
            return self._format_user_response(user_dto, format_type)
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"更新用户信息失败: {str(e)}")
    
    def update_user_balance(self, user_id: str, amount: Decimal, 
                           operation: str = 'add', reason: str = '') -> Dict[str, Any]:
        """
        更新用户余额
        
        Args:
            user_id: 用户ID
            amount: 金额
            operation: 操作类型 ('add', 'subtract', 'set')
            reason: 操作原因
            
        Returns:
            Dict: 更新后的余额信息
            
        Raises:
            ResourceNotFoundException: 用户不存在
            PermissionDeniedException: 权限不足
            ValidationException: 操作无效
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException("需要管理员权限")
        
        try:
            # 验证参数
            if amount < 0:
                raise ValidationException("金额不能为负数")
            
            if operation not in ['add', 'subtract', 'set']:
                raise ValidationException("无效的操作类型")
            
            # 查询用户
            user = self.model.objects.get(user_id=user_id)
            
            # 计算新余额
            old_balance = user.balance
            if operation == 'add':
                new_balance = old_balance + amount
            elif operation == 'subtract':
                new_balance = max(Decimal('0.00'), old_balance - amount)
            else:  # set
                new_balance = amount
            
            # 更新余额
            with transaction.atomic():
                user.balance = new_balance
                user.save()
            
            return {
                'user_id': user_id,
                'old_balance': str(old_balance),
                'new_balance': str(new_balance),
                'amount': str(amount),
                'operation': operation,
                'reason': reason,
                'updated_at': timezone.now().isoformat()
            }
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"更新用户余额失败: {str(e)}")
    
    def get_user_membership_benefits(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取用户会员权益
        
        Args:
            user_id: 用户ID，如果为None则获取当前用户
            
        Returns:
            Dict: 会员权益信息
        """
        try:
            # 确定用户ID
            target_user_id = user_id or (str(self.user.user_id) if self.user else None)
            if not target_user_id:
                raise PermissionDeniedException("未登录用户无法获取会员权益")
            
            # 获取用户信息
            user = self.model.objects.get(user_id=target_user_id)
            user_dto = UserDTO.from_model(user)
            
            # 获取会员权益
            benefits = user_dto.get_user_level_benefits()
            
            return {
                'user_id': target_user_id,
                'membership_level': user_dto.membership_level,
                'membership_display': user_dto.get_membership_display(),
                'is_vip': user_dto.is_vip_member(),
                'benefits': benefits
            }
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {target_user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"获取会员权益失败: {str(e)}")
    
    def calculate_member_discount(self, user_id: str, base_price: Decimal) -> Dict[str, Any]:
        """
        计算会员折扣价格
        
        Args:
            user_id: 用户ID
            base_price: 基础价格
            
        Returns:
            Dict: 折扣信息
        """
        try:
            # 获取用户会员权益
            benefits_info = self.get_user_membership_benefits(user_id)
            discount_rate = benefits_info['benefits']['discount_rate']
            
            # 计算折扣价格
            discounted_price = base_price * Decimal(str(discount_rate))
            discount_amount = base_price - discounted_price
            
            return {
                'user_id': user_id,
                'membership_level': benefits_info['membership_level'],
                'base_price': str(base_price),
                'discount_rate': discount_rate,
                'discounted_price': str(discounted_price),
                'discount_amount': str(discount_amount),
                'is_discounted': discount_rate < 1.0
            }
            
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"计算会员折扣失败: {str(e)}")
    
    def validate_user_permissions(self, user_id: str, required_permissions: List[str]) -> Dict[str, Any]:
        """
        验证用户权限
        
        Args:
            user_id: 用户ID
            required_permissions: 需要的权限列表
            
        Returns:
            Dict: 权限验证结果
        """
        try:
            # 获取用户信息
            user = self.model.objects.get(user_id=user_id)
            user_dto = UserDTO.from_model(user, include_permissions=True)
            
            # 检查每个权限
            permission_results = {}
            all_granted = True
            
            for permission in required_permissions:
                has_permission = user_dto.has_permission(permission)
                permission_results[permission] = has_permission
                if not has_permission:
                    all_granted = False
            
            return {
                'user_id': user_id,
                'all_permissions_granted': all_granted,
                'permission_results': permission_results,
                'is_staff': user_dto.is_staff,
                'is_active': user_dto.is_active
            }
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"验证用户权限失败: {str(e)}")
    
    def upgrade_membership(self, user_id: str, new_level: str, reason: str = '') -> Dict[str, Any]:
        """
        升级用户会员等级
        
        Args:
            user_id: 用户ID
            new_level: 新的会员等级
            reason: 升级原因
            
        Returns:
            Dict: 升级结果
        """
        # 权限检查
        if not self._check_admin_permission():
            raise PermissionDeniedException("需要管理员权限")
        
        try:
            # 验证会员等级
            valid_levels = ['普通用户', 'VipUser', 'PremiumUser', 'DiamondUser']
            if new_level not in valid_levels:
                raise ValidationException(f"无效的会员等级: {new_level}")
            
            # 查询用户
            user = self.model.objects.get(user_id=user_id)
            old_level = user.membership_level
            
            # 更新会员等级
            with transaction.atomic():
                user.membership_level = new_level
                user.save()
            
            return {
                'user_id': user_id,
                'old_level': old_level,
                'new_level': new_level,
                'reason': reason,
                'upgraded_at': timezone.now().isoformat()
            }
            
        except ObjectDoesNotExist:
            raise ResourceNotFoundException(f"用户不存在: {user_id}")
        except Exception as e:
            if isinstance(e, (ServiceException,)):
                raise
            raise ServiceException(f"升级会员等级失败: {str(e)}")
    
    def _check_admin_permission(self) -> bool:
        """检查管理员权限"""
        return (self.user and 
                hasattr(self.user, 'is_staff') and 
                self.user.is_staff and 
                self.user.is_active)
    
    def _check_update_permission(self, target_user_id: str) -> bool:
        """检查更新权限"""
        if not self.user:
            return False
        
        # 管理员可以更新任何用户
        if self._check_admin_permission():
            return True
        
        # 用户只能更新自己的信息
        return str(self.user.user_id) == target_user_id
    
    def _validate_update_data(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证更新数据"""
        validated_data = {}
        
        # 允许更新的字段
        allowed_fields = {
            'username': str,
            'email': str,
            'membership_level': str,
            'balance': Decimal,
            'is_active': bool
        }
        
        # 管理员专用字段
        admin_only_fields = {'membership_level', 'balance', 'is_active'}
        
        for field, value in update_data.items():
            if field not in allowed_fields:
                continue
            
            # 检查管理员专用字段
            if field in admin_only_fields and not self._check_admin_permission():
                continue
            
            # 类型验证
            expected_type = allowed_fields[field]
            if expected_type == Decimal:
                try:
                    validated_data[field] = Decimal(str(value))
                except:
                    raise ValidationException(f"字段 {field} 必须是有效的数字")
            elif not isinstance(value, expected_type):
                raise ValidationException(f"字段 {field} 类型错误")
            else:
                validated_data[field] = value
        
        return validated_data
    
    def _apply_user_filters(self, queryset, filters: UserListFilter):
        """应用用户过滤条件"""
        if filters.username:
            queryset = queryset.filter(username__icontains=filters.username)
        
        if filters.email:
            queryset = queryset.filter(email__icontains=filters.email)
        
        if filters.membership_level:
            queryset = queryset.filter(membership_level=filters.membership_level)
        
        if filters.is_active is not None:
            queryset = queryset.filter(is_active=filters.is_active)
        
        if filters.is_staff is not None:
            queryset = queryset.filter(is_staff=filters.is_staff)
        
        if filters.date_joined_start:
            queryset = queryset.filter(date_joined__gte=filters.date_joined_start)
        
        if filters.date_joined_end:
            queryset = queryset.filter(date_joined__lte=filters.date_joined_end)
        
        if filters.last_login_start:
            queryset = queryset.filter(last_login__gte=filters.last_login_start)
        
        if filters.last_login_end:
            queryset = queryset.filter(last_login__lte=filters.last_login_end)
        
        if filters.balance_min:
            queryset = queryset.filter(balance__gte=filters.balance_min)
        
        if filters.balance_max:
            queryset = queryset.filter(balance__lte=filters.balance_max)
        
        if filters.total_spent_min:
            queryset = queryset.filter(total_spent__gte=filters.total_spent_min)
        
        if filters.total_spent_max:
            queryset = queryset.filter(total_spent__lte=filters.total_spent_max)
        
        if filters.search_keyword:
            queryset = queryset.filter(
                Q(username__icontains=filters.search_keyword) |
                Q(email__icontains=filters.search_keyword)
            )
        
        return queryset
    
    def _format_user_response(self, user_dto: UserDTO, format_type: str) -> Dict[str, Any]:
        """格式化用户响应数据"""
        if format_type == 'admin':
            return user_dto.to_admin_dict()
        elif format_type == 'user':
            return user_dto.to_user_dict()
        elif format_type == 'public':
            return user_dto.to_public_dict()
        elif format_type == 'api':
            return user_dto.to_api_dict()
        else:
            return user_dto.to_user_dict()