"""
Vue模板缓存系统
提供内存缓存和文件缓存机制
"""
import os
import json
import hashlib
import pickle
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class VueTemplateCache:
    """Vue模板缓存管理器"""
    
    def __init__(self):
        self.vue_settings = getattr(settings, 'VUE_SETTINGS', {})
        self.cache_settings = self.vue_settings.get('PRODUCTION', {})
        self.cache_dir = self.vue_settings.get('COMPILER', {}).get('CACHE_DIR')
        self.cache_enabled = self.cache_settings.get('CACHE_ENABLED', True)
        self.cache_timeout = self.cache_settings.get('CACHE_TIMEOUT', 3600)
        
        # 确保缓存目录存在
        if self.cache_dir and not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)
    
    def get_cached_template(self, template_path):
        """
        获取缓存的模板编译结果
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            dict or None: 缓存的编译结果，如果不存在则返回None
        """
        if not self.cache_enabled:
            return None
        
        try:
            cache_key = self._get_cache_key(template_path)
            
            if settings.DEBUG:
                # 开发环境：检查文件修改时间
                return self._get_dev_cache(template_path, cache_key)
            else:
                # 生产环境：使用持久化缓存
                return self._get_prod_cache(template_path, cache_key)
                
        except Exception as e:
            logger.warning(f"获取缓存失败: {template_path} - {str(e)}")
            return None
    
    def cache_template(self, template_path, compiled_result):
        """
        缓存模板编译结果
        
        Args:
            template_path: 模板文件路径
            compiled_result: 编译结果
        """
        if not self.cache_enabled:
            return
        
        try:
            cache_key = self._get_cache_key(template_path)
            
            if settings.DEBUG:
                # 开发环境：使用内存缓存
                self._cache_dev(cache_key, template_path, compiled_result)
            else:
                # 生产环境：使用文件缓存
                self._cache_prod(cache_key, template_path, compiled_result)
                
        except Exception as e:
            logger.warning(f"缓存保存失败: {template_path} - {str(e)}")
    
    def clear_cache(self, template_path=None):
        """
        清除缓存
        
        Args:
            template_path: 特定模板路径，如果为None则清除所有缓存
        """
        try:
            if template_path:
                # 清除特定模板的缓存
                cache_key = self._get_cache_key(template_path)
                cache.delete(cache_key)
                
                # 删除文件缓存
                if self.cache_dir:
                    cache_file = os.path.join(self.cache_dir, f"{cache_key}.cache")
                    if os.path.exists(cache_file):
                        os.remove(cache_file)
            else:
                # 清除所有缓存
                cache.clear()
                
                # 清除文件缓存目录
                if self.cache_dir and os.path.exists(self.cache_dir):
                    for filename in os.listdir(self.cache_dir):
                        if filename.endswith('.cache'):
                            os.remove(os.path.join(self.cache_dir, filename))
                            
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
    
    def get_cache_stats(self):
        """
        获取缓存统计信息
        
        Returns:
            dict: 缓存统计信息
        """
        try:
            stats = {
                'cache_enabled': self.cache_enabled,
                'cache_timeout': self.cache_timeout,
                'cache_dir': self.cache_dir,
                'total_cached_files': 0,
                'cache_size': 0,
                'oldest_cache': None,
                'newest_cache': None
            }
            
            if self.cache_dir and os.path.exists(self.cache_dir):
                cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.cache')]
                stats['total_cached_files'] = len(cache_files)
                
                if cache_files:
                    file_stats = []
                    total_size = 0
                    
                    for filename in cache_files:
                        filepath = os.path.join(self.cache_dir, filename)
                        file_stat = os.stat(filepath)
                        file_stats.append(file_stat.st_mtime)
                        total_size += file_stat.st_size
                    
                    stats['cache_size'] = total_size
                    stats['oldest_cache'] = datetime.fromtimestamp(min(file_stats))
                    stats['newest_cache'] = datetime.fromtimestamp(max(file_stats))
            
            return stats
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {'error': str(e)}
    
    def _get_cache_key(self, template_path):
        """生成缓存键"""
        try:
            # 获取文件信息
            file_stat = os.stat(template_path)
            
            # 组合文件路径、修改时间和大小生成唯一键
            key_content = f"{template_path}_{file_stat.st_mtime}_{file_stat.st_size}"
            
            # 生成MD5哈希
            return hashlib.md5(key_content.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.warning(f"生成缓存键失败: {template_path} - {str(e)}")
            return hashlib.md5(template_path.encode('utf-8')).hexdigest()
    
    def _get_dev_cache(self, template_path, cache_key):
        """开发环境缓存获取"""
        try:
            # 检查内存缓存
            cached_data = cache.get(cache_key)
            if cached_data:
                # 验证文件是否被修改
                if self._is_file_modified(template_path, cached_data.get('file_mtime')):
                    # 文件已修改，删除缓存
                    cache.delete(cache_key)
                    return None
                
                return cached_data.get('compiled_result')
            
            return None
            
        except Exception as e:
            logger.warning(f"开发环境缓存获取失败: {str(e)}")
            return None
    
    def _get_prod_cache(self, template_path, cache_key):
        """生产环境缓存获取"""
        try:
            # 首先检查内存缓存
            cached_data = cache.get(cache_key)
            if cached_data:
                return cached_data.get('compiled_result')
            
            # 检查文件缓存
            if self.cache_dir:
                cache_file = os.path.join(self.cache_dir, f"{cache_key}.cache")
                if os.path.exists(cache_file):
                    with open(cache_file, 'rb') as f:
                        cached_data = pickle.load(f)
                    
                    # 验证缓存是否过期
                    if not self._is_cache_expired(cached_data.get('cached_at')):
                        compiled_result = cached_data.get('compiled_result')
                        
                        # 重新加载到内存缓存
                        cache.set(cache_key, cached_data, self.cache_timeout)
                        
                        return compiled_result
                    else:
                        # 缓存已过期，删除文件
                        os.remove(cache_file)
            
            return None
            
        except Exception as e:
            logger.warning(f"生产环境缓存获取失败: {str(e)}")
            return None
    
    def _cache_dev(self, cache_key, template_path, compiled_result):
        """开发环境缓存保存"""
        try:
            file_stat = os.stat(template_path)
            cached_data = {
                'compiled_result': compiled_result,
                'file_mtime': file_stat.st_mtime,
                'cached_at': datetime.now()
            }
            
            # 保存到内存缓存，较短的超时时间
            cache.set(cache_key, cached_data, 300)  # 5分钟
            
        except Exception as e:
            logger.warning(f"开发环境缓存保存失败: {str(e)}")
    
    def _cache_prod(self, cache_key, template_path, compiled_result):
        """生产环境缓存保存"""
        try:
            file_stat = os.stat(template_path)
            cached_data = {
                'compiled_result': compiled_result,
                'file_mtime': file_stat.st_mtime,
                'cached_at': datetime.now()
            }
            
            # 保存到内存缓存
            cache.set(cache_key, cached_data, self.cache_timeout)
            
            # 保存到文件缓存
            if self.cache_dir:
                cache_file = os.path.join(self.cache_dir, f"{cache_key}.cache")
                with open(cache_file, 'wb') as f:
                    pickle.dump(cached_data, f)
                    
        except Exception as e:
            logger.warning(f"生产环境缓存保存失败: {str(e)}")
    
    def _is_file_modified(self, template_path, cached_mtime):
        """检查文件是否被修改"""
        try:
            current_mtime = os.stat(template_path).st_mtime
            return current_mtime != cached_mtime
        except Exception:
            return True
    
    def _is_cache_expired(self, cached_at):
        """检查缓存是否过期"""
        try:
            if not cached_at:
                return True
            
            expiry_time = cached_at + timedelta(seconds=self.cache_timeout)
            return datetime.now() > expiry_time
            
        except Exception:
            return True


# 全局缓存实例
vue_cache = VueTemplateCache()