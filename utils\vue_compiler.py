"""
Vue模板编译器
负责将Vue单文件组件编译为可执行的HTML、CSS和JavaScript
"""
import os
import re
import json
import hashlib
import subprocess
import tempfile
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class VueCompiler:
    """Vue单文件组件编译器"""
    
    def __init__(self):
        self.vue_settings = getattr(settings, 'VUE_SETTINGS', {})
        self.compiler_settings = self.vue_settings.get('COMPILER', {})
        self.template_settings = self.vue_settings.get('TEMPLATES', {})
        
    def compile_vue_file(self, vue_file_path, context=None):
        """
        编译Vue单文件组件
        
        Args:
            vue_file_path: Vue文件的完整路径
            context: Django上下文数据
            
        Returns:
            dict: 编译结果，包含html、javascript、css
        """
        try:
            # 读取Vue文件内容
            vue_content = self._read_vue_file(vue_file_path)
            
            # 解析Vue单文件组件
            parsed_components = self._parse_vue_file(vue_content)
            
            # 编译各个部分
            compiled_result = {
                'html': self._compile_template(parsed_components.get('template', ''), context),
                'javascript': self._compile_script(parsed_components.get('script', ''), context),
                'css': self._compile_style(parsed_components.get('style', ''))
            }
            
            return compiled_result
            
        except Exception as e:
            logger.error(f"Vue文件编译失败: {vue_file_path} - {str(e)}")
            raise
    
    def _read_vue_file(self, vue_file_path):
        """读取Vue文件内容"""
        try:
            with open(vue_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取Vue文件失败: {vue_file_path} - {str(e)}")
            raise
    
    def _parse_vue_file(self, vue_content):
        """
        解析Vue单文件组件，提取template、script、style部分
        
        Args:
            vue_content: Vue文件内容
            
        Returns:
            dict: 包含template、script、style的字典
        """
        result = {
            'template': '',
            'script': '',
            'style': ''
        }
        
        try:
            # 提取template部分
            template_match = re.search(r'<template[^>]*>(.*?)</template>', vue_content, re.DOTALL)
            if template_match:
                result['template'] = template_match.group(1).strip()
            
            # 提取script部分
            script_match = re.search(r'<script[^>]*>(.*?)</script>', vue_content, re.DOTALL)
            if script_match:
                result['script'] = script_match.group(1).strip()
            
            # 提取style部分
            style_match = re.search(r'<style[^>]*>(.*?)</style>', vue_content, re.DOTALL)
            if style_match:
                result['style'] = style_match.group(1).strip()
            
            return result
            
        except Exception as e:
            logger.error(f"Vue文件解析失败: {str(e)}")
            raise
    
    def _compile_template(self, template_content, context):
        """
        编译Vue模板部分

        Args:
            template_content: 模板内容
            context: Django上下文数据

        Returns:
            str: 编译后的HTML
        """
        if not template_content:
            return ''

        try:
            # 将Vue的自定义分隔符 [[ ]] 转换为标准的 {{ }}
            # 这样Vue 3可以正常解析模板
            compiled_html = template_content.replace('[[', '{{').replace(']]', '}}')

            return compiled_html

        except Exception as e:
            logger.error(f"模板编译失败: {str(e)}")
            return template_content
    
    def _compile_script(self, script_content, context):
        """
        编译Vue脚本部分

        Args:
            script_content: 脚本内容
            context: Django上下文数据

        Returns:
            str: 编译后的JavaScript
        """
        if not script_content:
            print("[Vue编译器] 脚本内容为空，使用默认Vue应用")
            return self._get_default_vue_app()

        try:
            # 处理Vue组件定义
            compiled_js = script_content
            print(f"[Vue编译器] 开始编译脚本，长度: {len(script_content)} 字符")
            print(f"[Vue编译器] 脚本内容预览: {script_content[:200]}...")

            # 检查是否包含async/await语法
            if 'async ' in script_content or 'await ' in script_content:
                print("[Vue编译器] 检测到async/await语法")

            # 检查是否包含复杂的方法定义
            method_count = script_content.count('async ') + script_content.count('function')
            print(f"[Vue编译器] 检测到 {method_count} 个方法定义")
            
            # 如果脚本包含export default，转换为Vue应用
            if 'export default' in compiled_js:
                # 使用改进的方法提取组件配置
                config_content = self._extract_export_default_content(compiled_js)
                
                if config_content:
                    print(f"[Vue编译器] 成功提取组件配置，长度: {len(config_content)} 字符")
                    print(f"[Vue编译器] 提取内容预览: {config_content[:300]}...")

                    # 验证提取的内容是否为有效的JavaScript对象
                    print("[Vue编译器] 开始验证JavaScript对象")
                    if self._validate_javascript_object(config_content):
                        print("[Vue编译器] JavaScript对象验证通过，开始生成Vue应用代码")
                        # 创建Vue应用
                        vue_app_js = f"""
                        console.log('[Vue Compiler] Starting Vue app creation');
                        const {{ createApp }} = Vue;

                        // Component configuration object
                        console.log('[Vue Compiler] Parsing component config');
                        const componentConfig = {config_content};
                        console.log('[Vue Compiler] Component config created:', componentConfig);

                        // Add Django data to component
                        if (window.__DJANGO_CONTEXT__) {{
                            console.log('[Vue Compiler] Injecting Django data');
                            if (componentConfig.data) {{
                                const originalData = componentConfig.data;
                                componentConfig.data = function() {{
                                    const data = originalData.call(this);
                                    return {{
                                        ...data,
                                        djangoContext: window.__DJANGO_CONTEXT__
                                    }};
                                }};
                            }} else {{
                                componentConfig.data = function() {{
                                    return {{
                                        djangoContext: window.__DJANGO_CONTEXT__
                                    }};
                                }};
                            }}
                        }}

                        // Create and mount Vue app
                        console.log('[Vue Compiler] Creating Vue app instance');
                        const app = createApp(componentConfig);
                        
                        // Ensure DOM element exists before mounting
                        const appElement = document.getElementById('app');
                        if (appElement) {{
                            console.log('[Vue Compiler] Found mount point, mounting Vue app');
                            app.mount('#app');
                            console.log('[Vue Compiler] Vue app mounted successfully');
                        }} else {{
                            console.error('[Vue Compiler] Vue mount point #app not found');
                        }}
                        """
                        
                        print("[Vue编译器] Vue应用代码生成完成")
                        compiled_js = vue_app_js
                    else:
                        print("[Vue编译器] 提取的组件配置不是有效的JavaScript对象")
                        compiled_js = self._get_default_vue_app()
                else:
                    print("[Vue编译器] 无法提取export default内容")
                    compiled_js = self._get_default_vue_app()
            else:
                # 如果不是标准的Vue组件，包装为Vue应用
                compiled_js = f"""
                const {{ createApp }} = Vue;
                
                const app = createApp({{
                    data() {{
                        return {{
                            djangoContext: window.__DJANGO_CONTEXT__ || {{}}
                        }};
                    }},
                    mounted() {{
                        {compiled_js}
                    }}
                }});
                
                // 确保DOM元素存在后再挂载
                if (document.getElementById('app')) {{
                    app.mount('#app');
                }} else {{
                    console.error('Vue挂载点 #app 不存在');
                }}
                """
            
            return compiled_js
            
        except Exception as e:
            logger.error(f"脚本编译失败: {str(e)}")
            return self._get_default_vue_app()
    
    def _compile_style(self, style_content):
        """
        编译Vue样式部分
        
        Args:
            style_content: 样式内容
            
        Returns:
            str: 编译后的CSS
        """
        if not style_content:
            return ''
        
        try:
            # 处理scoped样式（简单实现）
            compiled_css = style_content
            
            # 这里可以添加CSS预处理器支持（如Sass、Less）
            # 目前只是简单返回原始CSS
            
            return compiled_css
            
        except Exception as e:
            logger.error(f"样式编译失败: {str(e)}")
            return style_content
    
    def _extract_export_default_content(self, script_content):
        """
        改进的export default内容提取方法（增强版本）

        Args:
            script_content: 脚本内容

        Returns:
            str: 提取的组件配置内容，如果失败返回None
        """
        try:
            print(f"[Vue编译器] 开始提取export default内容，脚本长度: {len(script_content)}")

            # 预处理：移除注释和多余的空白
            cleaned_content = self._clean_script_content(script_content)
            print(f"[Vue编译器] 清理后的脚本长度: {len(cleaned_content)}")

            # 找到 export default 的位置
            export_patterns = [
                r'export\s+default\s+',
                r'export\s*{\s*default\s*}\s*=\s*',
                r'module\.exports\s*=\s*'
            ]

            export_match = None
            for pattern in export_patterns:
                export_match = re.search(pattern, cleaned_content)
                if export_match:
                    print(f"[Vue编译器] 找到导出语句，模式: {pattern}")
                    break

            if not export_match:
                print("[Vue编译器] 未找到任何导出语句")
                return None

            start_pos = export_match.end()
            remaining_content = cleaned_content[start_pos:].strip()
            print(f"[Vue编译器] 导出语句后的内容长度: {len(remaining_content)}")

            if not remaining_content:
                print("[Vue编译器] 导出语句后没有内容")
                return None
            
            # 如果以 { 开始，使用改进的括号匹配
            if remaining_content.startswith('{'):
                print("[Vue编译器] 检测到对象字面量，开始提取")
                extracted = self._extract_object_content(remaining_content)
                if extracted:
                    print(f"[Vue编译器] 成功提取对象内容，长度: {len(extracted)}")
                    # 记录前100个字符用于调试
                    preview = extracted[:100].replace('\n', '\\n')
                    print(f"[Vue编译器] 提取内容预览: {preview}...")
                else:
                    print("[Vue编译器] 对象内容提取失败")
                return extracted
            else:
                # 如果不是对象字面量，可能是变量引用或其他形式
                print("[Vue编译器] 检测到非对象字面量形式")
                lines = remaining_content.split('\n')
                first_line = lines[0].strip()
                if first_line.endswith(';'):
                    first_line = first_line[:-1]
                print(f"[Vue编译器] 提取的第一行内容: {first_line}")
                return first_line
                
        except Exception as e:
            logger.error(f"提取export default内容失败: {str(e)}")
            return None

    def _clean_script_content(self, content):
        """
        清理脚本内容，移除注释和多余空白

        Args:
            content: 原始脚本内容

        Returns:
            str: 清理后的内容
        """
        try:
            # 移除单行注释（但保留字符串中的//）
            lines = content.split('\n')
            cleaned_lines = []

            for line in lines:
                # 简单的注释移除（不处理字符串中的注释）
                comment_pos = line.find('//')
                if comment_pos != -1:
                    # 检查是否在字符串中
                    before_comment = line[:comment_pos]
                    single_quotes = before_comment.count("'") - before_comment.count("\\'")
                    double_quotes = before_comment.count('"') - before_comment.count('\\"')

                    # 如果引号数量是偶数，说明注释不在字符串中
                    if single_quotes % 2 == 0 and double_quotes % 2 == 0:
                        line = line[:comment_pos]

                cleaned_lines.append(line.rstrip())

            cleaned_content = '\n'.join(cleaned_lines)

            # 移除多行注释
            cleaned_content = re.sub(r'/\*.*?\*/', '', cleaned_content, flags=re.DOTALL)

            # 移除多余的空白行
            cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)

            return cleaned_content.strip()

        except Exception as e:
            print(f"[Vue编译器] 清理脚本内容时发生错误: {e}")
            return content

    def _extract_object_content(self, content):
        """
        提取JavaScript对象内容，支持嵌套和字符串
        
        Args:
            content: 以{开始的内容
            
        Returns:
            str: 提取的对象内容
        """
        try:
            brace_count = 0
            end_pos = 0
            i = 0
            in_string = False
            string_char = None
            in_regex = False
            escape_next = False
            in_template_literal = False
            template_depth = 0
            
            while i < len(content):
                char = content[i]
                
                # 处理转义字符
                if escape_next:
                    escape_next = False
                    i += 1
                    continue
                
                if char == '\\':
                    escape_next = True
                    i += 1
                    continue
                
                # 处理模板字面量
                if char == '`' and not in_string and not in_regex:
                    if not in_template_literal:
                        in_template_literal = True
                        template_depth = 0
                    else:
                        in_template_literal = False
                    i += 1
                    continue
                
                # 在模板字面量中处理 ${}
                if in_template_literal:
                    if char == '$' and i + 1 < len(content) and content[i + 1] == '{':
                        template_depth += 1
                        i += 2
                        continue
                    elif char == '}' and template_depth > 0:
                        template_depth -= 1
                        i += 1
                        continue
                    i += 1
                    continue
                
                # 处理字符串
                if char in ['"', "'"] and not in_regex:
                    if not in_string:
                        in_string = True
                        string_char = char
                    elif char == string_char:
                        in_string = False
                        string_char = None
                    i += 1
                    continue
                
                # 处理正则表达式
                if char == '/' and not in_string:
                    # 简单的正则表达式检测
                    if i > 0 and content[i-1] in '=([,;:!&|':
                        in_regex = True
                    i += 1
                    continue
                
                if in_regex and char == '/':
                    in_regex = False
                    i += 1
                    continue
                
                # 只在非字符串、非正则、非模板字面量中计算括号
                if not in_string and not in_regex and not in_template_literal:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                
                i += 1
            
            if end_pos > 0:
                return content[:end_pos]
            else:
                # 如果没有找到匹配的括号，返回整个内容
                logger.warning("未找到匹配的右括号，使用全部内容")
                return content
                
        except Exception as e:
            logger.error(f"对象内容提取失败: {str(e)}")
            return content
    
    def _validate_javascript_object(self, content):
        """
        验证JavaScript对象语法是否有效（改进版本）

        Args:
            content: 要验证的内容

        Returns:
            bool: 是否有效
        """
        try:
            print(f"[Vue编译器] 开始验证JavaScript对象，内容长度: {len(content) if content else 0}")

            # 基本的语法检查
            if not content or not content.strip():
                print("[Vue编译器] 内容为空，验证失败")
                return False

            content = content.strip()

            # 检查是否以{开始和}结束
            if not (content.startswith('{') and content.endswith('}')):
                print(f"[Vue编译器] 对象格式不正确，开始字符: '{content[0] if content else 'N/A'}', 结束字符: '{content[-1] if content else 'N/A'}'")
                return False

            # 检查是否包含Vue组件的基本结构
            vue_component_indicators = ['name:', 'data()', 'methods:', 'mounted()', 'data:', 'methods', 'mounted']
            has_vue_structure = any(indicator in content for indicator in vue_component_indicators)

            if has_vue_structure:
                print("[Vue编译器] 检测到Vue组件结构")
            else:
                print("[Vue编译器] 警告：未检测到明显的Vue组件结构")
            
            # 检查括号是否匹配
            brace_count = 0
            paren_count = 0
            bracket_count = 0
            in_string = False
            string_char = None
            escape_next = False
            
            for i, char in enumerate(content):
                if escape_next:
                    escape_next = False
                    continue
                
                if char == '\\':
                    escape_next = True
                    continue
                
                if char in ['"', "'", '`'] and not in_string:
                    in_string = True
                    string_char = char
                elif char == string_char and in_string:
                    in_string = False
                    string_char = None
                elif not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                    elif char == '(':
                        paren_count += 1
                    elif char == ')':
                        paren_count -= 1
                    elif char == '[':
                        bracket_count += 1
                    elif char == ']':
                        bracket_count -= 1
            
            # 检查所有括号是否匹配
            is_valid = brace_count == 0 and paren_count == 0 and bracket_count == 0
            
            if not is_valid:
                print(f"[Vue编译器] 括号不匹配 - 大括号: {brace_count}, 圆括号: {paren_count}, 方括号: {bracket_count}")
            else:
                print("[Vue编译器] JavaScript对象验证通过")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"JavaScript对象验证失败: {str(e)}")
            return False
    
    def _get_default_vue_app(self):
        """获取默认的Vue应用代码"""
        return """
        const { createApp } = Vue;
        
        const app = createApp({
            data() {
                return {
                    djangoContext: window.__DJANGO_CONTEXT__ || {}
                };
            }
        });
        
        // 确保DOM元素存在后再挂载
        if (document.getElementById('app')) {
            app.mount('#app');
        } else {
            console.error('Vue挂载点 #app 不存在');
        }
        """
    
    def _get_cache_key(self, vue_file_path):
        """生成缓存键"""
        file_stat = os.stat(vue_file_path)
        content = f"{vue_file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def compile_with_node(self, vue_file_path):
        """
        使用Node.js编译Vue文件（高级功能，需要Node.js环境）
        
        Args:
            vue_file_path: Vue文件路径
            
        Returns:
            dict: 编译结果
        """
        try:
            node_executable = self.compiler_settings.get('NODE_EXECUTABLE', 'node')
            compile_timeout = self.compiler_settings.get('COMPILE_TIMEOUT', 30)
            
            # 创建临时编译脚本
            compile_script = self._create_node_compile_script()
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(compile_script)
                script_path = f.name
            
            try:
                # 执行Node.js编译
                result = subprocess.run(
                    [node_executable, script_path, vue_file_path],
                    capture_output=True,
                    text=True,
                    timeout=compile_timeout
                )
                
                if result.returncode == 0:
                    return json.loads(result.stdout)
                else:
                    logger.error(f"Node.js编译失败: {result.stderr}")
                    raise Exception(f"Node.js编译失败: {result.stderr}")
                    
            finally:
                # 清理临时文件
                os.unlink(script_path)
                
        except Exception as e:
            logger.error(f"Node.js编译异常: {str(e)}")
            raise
    
    def _create_node_compile_script(self):
        """创建Node.js编译脚本"""
        return """
        const fs = require('fs');
        const path = require('path');
        
        // 简单的Vue文件解析器
        function parseVueFile(content) {
            const templateMatch = content.match(/<template[^>]*>([\\s\\S]*?)<\\/template>/);
            const scriptMatch = content.match(/<script[^>]*>([\\s\\S]*?)<\\/script>/);
            const styleMatch = content.match(/<style[^>]*>([\\s\\S]*?)<\\/style>/);
            
            return {
                template: templateMatch ? templateMatch[1].trim() : '',
                script: scriptMatch ? scriptMatch[1].trim() : '',
                style: styleMatch ? styleMatch[1].trim() : ''
            };
        }
        
        // 主编译函数
        function compileVueFile(filePath) {
            const content = fs.readFileSync(filePath, 'utf-8');
            const parsed = parseVueFile(content);
            
            return {
                html: parsed.template,
                javascript: parsed.script,
                css: parsed.style
            };
        }
        
        // 命令行参数处理
        const filePath = process.argv[2];
        if (!filePath) {
            console.error('请提供Vue文件路径');
            process.exit(1);
        }
        
        try {
            const result = compileVueFile(filePath);
            console.log(JSON.stringify(result));
        } catch (error) {
            console.error('编译失败:', error.message);
            process.exit(1);
        }
        """