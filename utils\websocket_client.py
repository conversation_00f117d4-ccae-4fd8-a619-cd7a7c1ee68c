#!/usr/bin/env python
# -*- coding: utf-8 -*-

import socket
import threading
import time
import json
import logging
from typing import Optional, Callable, Dict, Any

# 配置日志
logger = logging.getLogger(__name__)

class WebSocketClient:
    """
    WebSocket客户端类
    支持自动重连、消息发送接收、连接状态管理
    """
    
    def __init__(self, host: str = 'localhost', port: int = 9000, auto_reconnect: bool = True):
        """
        初始化WebSocket客户端
        
        Args:
            host: WebSocket服务器地址
            port: WebSocket服务器端口
            auto_reconnect: 是否自动重连
        """
        self.host = host
        self.port = port
        self.auto_reconnect = auto_reconnect
        
        # 连接状态
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.running = False
        
        # 线程管理
        self.receive_thread: Optional[threading.Thread] = None
        self.reconnect_thread: Optional[threading.Thread] = None
        
        # 回调函数
        self.on_message: Optional[Callable[[str], None]] = None
        self.on_connect: Optional[Callable[[], None]] = None
        self.on_disconnect: Optional[Callable[[], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 重连参数
        self.reconnect_interval = 5  # 重连间隔（秒）
        self.max_reconnect_attempts = 10  # 最大重连次数，0表示无限重连
        self.reconnect_attempts = 0
    
    def connect(self) -> bool:
        """
        连接到WebSocket服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            with self.lock:
                if self.connected:
                    logger.warning("WebSocket客户端已连接")
                    return True
                
                # 创建socket连接
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(10)  # 设置连接超时
                
                # 连接到服务器
                self.socket.connect((self.host, self.port))
                
                # 发送WebSocket握手请求
                handshake_request = (
                    f"GET / HTTP/1.1\r\n"
                    f"Host: {self.host}:{self.port}\r\n"
                    f"Upgrade: websocket\r\n"
                    f"Connection: Upgrade\r\n"
                    f"Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\n"
                    f"Sec-WebSocket-Version: 13\r\n\r\n"
                )
                
                self.socket.send(handshake_request.encode('utf-8'))
                
                # 接收握手响应
                response = self.socket.recv(4096).decode('utf-8')
                
                if "101 Switching Protocols" in response:
                    self.connected = True
                    self.running = True
                    self.reconnect_attempts = 0  # 重置重连计数
                    
                    # 启动接收线程
                    self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
                    self.receive_thread.start()
                    
                    logger.info(f"WebSocket客户端已连接到 {self.host}:{self.port}")
                    
                    # 触发连接回调
                    if self.on_connect:
                        try:
                            self.on_connect()
                        except Exception as e:
                            logger.error(f"连接回调执行错误: {e}")
                    
                    return True
                else:
                    logger.error(f"WebSocket握手失败: {response}")
                    self._cleanup_connection()
                    return False
                    
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            self._cleanup_connection()
            
            # 触发错误回调
            if self.on_error:
                try:
                    self.on_error(e)
                except Exception as callback_error:
                    logger.error(f"错误回调执行错误: {callback_error}")
            
            return False
    
    def disconnect(self):
        """
        断开WebSocket连接
        """
        with self.lock:
            self.running = False
            self._cleanup_connection()
            
            # 触发断开连接回调
            if self.on_disconnect:
                try:
                    self.on_disconnect()
                except Exception as e:
                    logger.error(f"断开连接回调执行错误: {e}")
        
        logger.info("WebSocket客户端已断开连接")
    
    def send_message(self, message: str) -> bool:
        """
        发送消息到WebSocket服务器
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 发送是否成功
        """
        try:
            with self.lock:
                if not self.connected or not self.socket:
                    logger.warning("WebSocket未连接，无法发送消息")
                    return False
                
                # 发送消息（简化版，实际应该按WebSocket协议格式化）
                self.socket.send(message.encode('utf-8'))
                logger.debug(f"已发送消息: {message}")
                return True
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            self._handle_connection_error(e)
            return False
    
    def send_json(self, data: Dict[str, Any]) -> bool:
        """
        发送JSON格式消息
        
        Args:
            data: 要发送的数据字典
            
        Returns:
            bool: 发送是否成功
        """
        try:
            message = json.dumps(data, ensure_ascii=False)
            return self.send_message(message)
        except Exception as e:
            logger.error(f"发送JSON消息失败: {e}")
            return False
    
    def _receive_loop(self):
        """
        消息接收循环（在独立线程中运行）
        """
        while self.running and self.connected:
            try:
                if not self.socket:
                    break
                
                # 接收数据
                data = self.socket.recv(4096)
                
                if not data:
                    logger.warning("WebSocket连接已关闭")
                    break
                
                # 解码消息
                message = data.decode('utf-8')
                logger.debug(f"收到消息: {message}")
                
                # 触发消息回调
                if self.on_message:
                    try:
                        self.on_message(message)
                    except Exception as e:
                        logger.error(f"消息回调执行错误: {e}")
                
            except socket.timeout:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"接收消息错误: {e}")
                self._handle_connection_error(e)
                break
        
        logger.info("消息接收循环已结束")
    
    def _handle_connection_error(self, error: Exception):
        """
        处理连接错误
        
        Args:
            error: 错误异常
        """
        logger.error(f"连接错误: {error}")
        
        with self.lock:
            self.connected = False
            self._cleanup_connection()
        
        # 触发错误回调
        if self.on_error:
            try:
                self.on_error(error)
            except Exception as callback_error:
                logger.error(f"错误回调执行错误: {callback_error}")
        
        # 启动自动重连
        if self.auto_reconnect and self.running:
            self._start_reconnect()
    
    def _start_reconnect(self):
        """
        启动重连线程
        """
        if self.reconnect_thread and self.reconnect_thread.is_alive():
            return  # 重连线程已在运行
        
        self.reconnect_thread = threading.Thread(target=self._reconnect_loop, daemon=True)
        self.reconnect_thread.start()
    
    def _reconnect_loop(self):
        """
        重连循环
        """
        while self.running and not self.connected:
            # 检查重连次数限制
            if self.max_reconnect_attempts > 0 and self.reconnect_attempts >= self.max_reconnect_attempts:
                logger.error(f"达到最大重连次数 {self.max_reconnect_attempts}，停止重连")
                break
            
            self.reconnect_attempts += 1
            logger.info(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts if self.max_reconnect_attempts > 0 else '∞'})")
            
            # 等待重连间隔
            time.sleep(self.reconnect_interval)
            
            # 尝试重连
            if self.connect():
                logger.info("重连成功")
                break
            else:
                logger.warning(f"重连失败，{self.reconnect_interval}秒后重试")
    
    def _cleanup_connection(self):
        """
        清理连接资源
        """
        self.connected = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
    
    def is_connected(self) -> bool:
        """
        检查连接状态
        
        Returns:
            bool: 是否已连接
        """
        return self.connected
    
    def set_callbacks(self, 
                     on_message: Optional[Callable[[str], None]] = None,
                     on_connect: Optional[Callable[[], None]] = None,
                     on_disconnect: Optional[Callable[[], None]] = None,
                     on_error: Optional[Callable[[Exception], None]] = None):
        """
        设置回调函数
        
        Args:
            on_message: 消息接收回调
            on_connect: 连接成功回调
            on_disconnect: 断开连接回调
            on_error: 错误回调
        """
        if on_message:
            self.on_message = on_message
        if on_connect:
            self.on_connect = on_connect
        if on_disconnect:
            self.on_disconnect = on_disconnect
        if on_error:
            self.on_error = on_error
