#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import logging
from typing import Optional, Callable, Dict, Any
from django.conf import settings
from .websocket_client import WebSocketClient

# 配置日志
logger = logging.getLogger(__name__)

class WebSocketManager:
    """
    WebSocket管理器 - 单例模式
    提供全局WebSocket客户端实例管理
    """
    
    _instance: Optional['WebSocketManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(WebSocketManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """
        初始化WebSocket管理器
        """
        if self._initialized:
            return
        
        self._initialized = True
        self.client: Optional[WebSocketClient] = None
        self._connection_lock = threading.Lock()
        
        # 从Django设置中获取配置
        self.host = getattr(settings, 'WEBSOCKET_HOST', 'localhost')
        self.port = getattr(settings, 'WEBSOCKET_PORT', 9000)
        self.auto_reconnect = getattr(settings, 'WEBSOCKET_AUTO_RECONNECT', True)
        self.enabled = getattr(settings, 'WEBSOCKET_ENABLED', True)
        
        logger.info(f"WebSocket管理器初始化完成 - Host: {self.host}, Port: {self.port}, 自动重连: {self.auto_reconnect}, 启用状态: {self.enabled}")
    
    def initialize(self) -> bool:
        """
        初始化WebSocket连接
        
        Returns:
            bool: 初始化是否成功
        """
        if not self.enabled:
            logger.info("WebSocket功能已禁用，跳过初始化")
            return False
        
        with self._connection_lock:
            if self.client is not None:
                logger.warning("WebSocket客户端已初始化")
                return self.client.is_connected()
            
            try:
                # 创建WebSocket客户端
                self.client = WebSocketClient(
                    host=self.host,
                    port=self.port,
                    auto_reconnect=self.auto_reconnect
                )
                
                # 设置默认回调函数
                self.client.set_callbacks(
                    on_connect=self._on_connect,
                    on_disconnect=self._on_disconnect,
                    on_message=self._on_message,
                    on_error=self._on_error
                )
                
                # 尝试连接
                success = self.client.connect()
                
                if success:
                    logger.info("WebSocket管理器初始化成功")
                else:
                    logger.error("WebSocket管理器初始化失败")
                
                return success
                
            except Exception as e:
                logger.error(f"WebSocket管理器初始化异常: {e}")
                return False
    
    def get_client(self) -> Optional[WebSocketClient]:
        """
        获取WebSocket客户端实例
        
        Returns:
            Optional[WebSocketClient]: WebSocket客户端实例，如果未初始化则返回None
        """
        return self.client
    
    def is_connected(self) -> bool:
        """
        检查WebSocket连接状态
        
        Returns:
            bool: 是否已连接
        """
        if not self.enabled or self.client is None:
            return False
        return self.client.is_connected()
    
    def send_message(self, message: str) -> bool:
        """
        发送消息
        
        Args:
            message: 要发送的消息
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled or self.client is None:
            logger.warning("WebSocket未启用或未初始化，无法发送消息")
            return False
        
        return self.client.send_message(message)
    
    def send_json(self, data: Dict[str, Any]) -> bool:
        """
        发送JSON格式消息
        
        Args:
            data: 要发送的数据字典
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled or self.client is None:
            logger.warning("WebSocket未启用或未初始化，无法发送JSON消息")
            return False
        
        return self.client.send_json(data)
    
    def disconnect(self):
        """
        断开WebSocket连接
        """
        with self._connection_lock:
            if self.client is not None:
                self.client.disconnect()
                self.client = None
                logger.info("WebSocket连接已断开")
    
    def set_message_callback(self, callback: Callable[[str], None]):
        """
        设置消息接收回调函数
        
        Args:
            callback: 消息回调函数
        """
        if self.client is not None:
            self.client.on_message = callback
    
    def set_connect_callback(self, callback: Callable[[], None]):
        """
        设置连接成功回调函数
        
        Args:
            callback: 连接回调函数
        """
        if self.client is not None:
            self.client.on_connect = callback
    
    def set_disconnect_callback(self, callback: Callable[[], None]):
        """
        设置断开连接回调函数
        
        Args:
            callback: 断开连接回调函数
        """
        if self.client is not None:
            self.client.on_disconnect = callback
    
    def set_error_callback(self, callback: Callable[[Exception], None]):
        """
        设置错误回调函数
        
        Args:
            callback: 错误回调函数
        """
        if self.client is not None:
            self.client.on_error = callback
    
    # 默认回调函数
    def _on_connect(self):
        """
        默认连接成功回调
        """
        logger.info("WebSocket连接已建立")
        
        # 发送初始化消息
        self.send_json({
            "type": "django_client_init",
            "message": "Django WebSocket客户端已连接",
            "timestamp": str(threading.current_thread().ident)
        })
    
    def _on_disconnect(self):
        """
        默认断开连接回调
        """
        logger.info("WebSocket连接已断开")
    
    def _on_message(self, message: str):
        """
        默认消息接收回调
        
        Args:
            message: 接收到的消息
        """
        logger.info(f"收到WebSocket消息: {message}")
        
        # 这里可以添加默认的消息处理逻辑
        # 例如：解析JSON消息，分发到不同的处理器等
        try:
            import json
            data = json.loads(message)
            
            # 处理不同类型的消息
            msg_type = data.get('type', 'unknown')
            
            if msg_type == 'welcome':
                logger.info(f"收到服务器欢迎消息: {data.get('message', '')}")
            elif msg_type == 'echo':
                logger.info(f"收到服务器回显消息: {data.get('message', '')}")
            else:
                logger.info(f"收到未知类型消息: {msg_type}")
                
        except json.JSONDecodeError:
            logger.warning(f"收到非JSON格式消息: {message}")
        except Exception as e:
            logger.error(f"处理WebSocket消息时出错: {e}")
    
    def _on_error(self, error: Exception):
        """
        默认错误回调
        
        Args:
            error: 错误异常
        """
        logger.error(f"WebSocket错误: {error}")


# 全局WebSocket管理器实例
_websocket_manager = None

def get_websocket_manager() -> WebSocketManager:
    """
    获取全局WebSocket管理器实例
    
    Returns:
        WebSocketManager: WebSocket管理器实例
    """
    global _websocket_manager
    if _websocket_manager is None:
        _websocket_manager = WebSocketManager()
    return _websocket_manager

def init_websocket() -> bool:
    """
    初始化全局WebSocket连接
    
    Returns:
        bool: 初始化是否成功
    """
    manager = get_websocket_manager()
    return manager.initialize()

def send_websocket_message(message: str) -> bool:
    """
    发送WebSocket消息的便捷函数
    
    Args:
        message: 要发送的消息
        
    Returns:
        bool: 发送是否成功
    """
    manager = get_websocket_manager()
    return manager.send_message(message)

def send_websocket_json(data: Dict[str, Any]) -> bool:
    """
    发送WebSocket JSON消息的便捷函数
    
    Args:
        data: 要发送的数据字典
        
    Returns:
        bool: 发送是否成功
    """
    manager = get_websocket_manager()
    return manager.send_json(data)

def is_websocket_connected() -> bool:
    """
    检查WebSocket连接状态的便捷函数
    
    Returns:
        bool: 是否已连接
    """
    manager = get_websocket_manager()
    return manager.is_connected()

def disconnect_websocket():
    """
    断开WebSocket连接的便捷函数
    """
    manager = get_websocket_manager()
    manager.disconnect()
