# 前端调用 get_categories 接口完整指南

## 接口概述

`get_categories` 是一个用于获取商品分类数据的用户端API接口，返回包含主分类和子分类的完整层级结构。

## 接口基本信息

- **接口路径**: `/user/api/categories/`
- **请求方法**: `POST`
- **认证方式**: JWT Token + MD5签名双重验证
- **内容类型**: `application/x-www-form-urlencoded` 或 `multipart/form-data`

## 前端调用流程

### 1. 前置条件检查

在调用接口前，前端需要检查浏览器缓存中是否存在token userId userKey 三个值

```javascript
// 检查必需的用户认证信息
const userId = localStorage.getItem('userId');        // 用户ID
const userKey = localStorage.getItem('userKey');      // 用户密钥
const token = localStorage.getItem('token');          // JWT访问令牌
```
#### 处理userId和userKey以及token缺失的情况

解决方法：需要在请求API前请求/user/api/GetUser/接口
取响应头中的token作为请求附带的token
取响应体中的user.id作为userId
取响应体中的user.user_key作为userKey

响应体返回json格式
获取到的token userId userKey 保存到浏览器缓存

### 2. MD5签名生成

接口需要MD5签名验证，签名算法为：`MD5(userId + userKey)`

```javascript
// MD5签名生成函数（需要引入可用MD5库）
// 该md5函数为全局内部函数 后续的接口请求还会使用到
function generateSignature(userId, userKey) {
    return MD5(userId + userKey);
}

// 生成签名
const sign = generateSignature(userId, userKey);
```

### 3. 请求参数构建

```javascript
// 构建POST请求参数
const formData = new FormData();
formData.append('userId', userId);    // 必需：用户ID
formData.append('sign', sign);        // 必需：MD5签名

// 或者使用URLSearchParams（推荐）
const params = new URLSearchParams();
params.append('userId', userId);
params.append('sign', sign);
```

### 4. 请求头配置

```javascript
const requestOptions = {
    method: 'POST',
    body: formData, // 或 params
    headers: {
        'Token': token  // JWT令牌放在请求头中
    }
};
```

### 5. 发送请求

```javascript
fetch('/user/api/categories/', requestOptions)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP请求错误! 状态: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 处理响应数据
        handleResponse(data);
    })
    .catch(error => {
        console.error('请求失败:', error);
        // 处理错误
    });
```

## 响应数据格式

### 成功响应 (HTTP 200)

**最小单位为二级分类**

```json
{
    "code": 200,
    "data": [
        {
            "name": "电子产品",
            "id": 1,
            "image": "/media/category/electronics.jpg",
            "children": [
                {
                    "name": "手机",
                    "id": 11,
                    "parentId": 1,
                    "image": "/media/category/phone.jpg"
                },
                {
                    "name": "电脑",
                    "id": 12,
                    "parentId": 1,
                    "image": "/media/category/computer.jpg"
                }
            ]
        },
        {
            "name": "服装",
            "id": 2,
            "image": "/media/category/clothing.jpg",
            "children": [
                {
                    "name": "男装",
                    "id": 21,
                    "parentId": 2,
                    "image": "/media/category/mens.jpg"
                }
            ]
        }
    ]
}
```

### 错误响应

#### 参数不完整 (HTTP 400)
```json
{
    "code": 400,
    "message": "参数不完整"
}
```

#### 签名验证失败 (HTTP 403)
```json
{
    "code": 403,
    "message": "签名验证失败"
}
```

#### Token相关错误 (HTTP 401)
```json
{
    "code": 401,
    "msg": "未授权访问，缺少Token"
}
```

```json
{
    "code": 401,
    "msg": "Token已过期"
}
```

```json
{
    "code": 401,
    "msg": "无效的Token"
}
```

#### 服务器错误 (HTTP 500)
```json
{
    "code": 500,
    "message": "服务器错误: [具体错误信息]"
}
```

## 数据结构说明

### 主分类对象
```typescript
interface Category {
    name: string;        // 分类名称
    id: number;          // 分类ID
    image: string;       // 分类图片URL
    children: SubCategory[];  // 子分类数组
}
```

### 子分类对象
```typescript
interface SubCategory {
    name: string;        // 子分类名称
    id: number;          // 子分类ID
    parentId: number;    // 父分类ID
    image: string;       // 子分类图片URL
}
```

## 注意事项

1. **认证信息管理**: 确保userId、userKey和token的同步更新
2. **签名算法**: 严格按照 `MD5(userId + userKey)` 生成签名
3. **请求头**: Token必须放在请求头的'Token'字段中
4. **错误处理**: 根据不同错误码进行相应处理
5. **数据缓存**: 可考虑缓存分类数据以减少请求频率
6. **安全性**: userKey等敏感信息应妥善保管，避免泄露

## 依赖库

前端需要引入MD5加密库，推荐使用：
- crypto-js
- js-md5

```html
<!-- 使用CDN引入MD5库 -->
<script src="https://unpkg.com/js-md5@0.7.3/build/md5.min.js"></script>
```
